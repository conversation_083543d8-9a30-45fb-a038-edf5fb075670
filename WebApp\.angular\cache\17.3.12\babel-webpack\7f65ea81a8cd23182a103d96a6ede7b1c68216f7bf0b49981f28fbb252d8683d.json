{"ast": null, "code": "/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeJoin = arrayProto.join;\n\n/**\n * Converts all elements in `array` into a string separated by `separator`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to convert.\n * @param {string} [separator=','] The element separator.\n * @returns {string} Returns the joined string.\n * @example\n *\n * _.join(['a', 'b', 'c'], '~');\n * // => 'a~b~c'\n */\nfunction join(array, separator) {\n  return array == null ? '' : nativeJoin.call(array, separator);\n}\nexport default join;", "map": {"version": 3, "names": ["arrayProto", "Array", "prototype", "nativeJoin", "join", "array", "separator", "call"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/join.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeJoin = arrayProto.join;\n\n/**\n * Converts all elements in `array` into a string separated by `separator`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to convert.\n * @param {string} [separator=','] The element separator.\n * @returns {string} Returns the joined string.\n * @example\n *\n * _.join(['a', 'b', 'c'], '~');\n * // => 'a~b~c'\n */\nfunction join(array, separator) {\n  return array == null ? '' : nativeJoin.call(array, separator);\n}\n\nexport default join;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAGC,KAAK,CAACC,SAAS;;AAEhC;AACA,IAAIC,UAAU,GAAGH,UAAU,CAACI,IAAI;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAE;EAC9B,OAAOD,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGF,UAAU,CAACI,IAAI,CAACF,KAAK,EAAEC,SAAS,CAAC;AAC/D;AAEA,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}