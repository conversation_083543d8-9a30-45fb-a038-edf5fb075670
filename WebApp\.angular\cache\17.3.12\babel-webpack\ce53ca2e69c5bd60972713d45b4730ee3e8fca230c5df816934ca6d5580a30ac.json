{"ast": null, "code": "/**\n * This library modifies the diff-patch-match library by <PERSON> by removing the patch and match functionality and certain advanced\n * options in the diff function. The original license is as follows:\n *\n * ===\n *\n * Diff Match and Patch\n *\n * Copyright 2006 Google Inc.\n * http://code.google.com/p/google-diff-match-patch/\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The data structure representing a diff is an array of tuples:\n * [[DIFF_DELETE, 'Hello'], [DIFF_INSERT, 'Goodbye'], [DIFF_EQUAL, ' world.']]\n * which means: delete 'Hello', add 'Goodbye' and keep ' world.'\n */\nvar DIFF_DELETE = -1;\nvar DIFF_INSERT = 1;\nvar DIFF_EQUAL = 0;\n\n/**\n * Find the differences between two texts.  Simplifies the problem by stripping\n * any common prefix or suffix off the texts before diffing.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {Int|Object} [cursor_pos] Edit position in text1 or object with more info\n * @param {boolean} [cleanup] Apply semantic cleanup before returning.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_main(text1, text2, cursor_pos, cleanup, _fix_unicode) {\n  // Check for equality\n  if (text1 === text2) {\n    if (text1) {\n      return [[DIFF_EQUAL, text1]];\n    }\n    return [];\n  }\n  if (cursor_pos != null) {\n    var editdiff = find_cursor_edit_diff(text1, text2, cursor_pos);\n    if (editdiff) {\n      return editdiff;\n    }\n  }\n\n  // Trim off common prefix (speedup).\n  var commonlength = diff_commonPrefix(text1, text2);\n  var commonprefix = text1.substring(0, commonlength);\n  text1 = text1.substring(commonlength);\n  text2 = text2.substring(commonlength);\n\n  // Trim off common suffix (speedup).\n  commonlength = diff_commonSuffix(text1, text2);\n  var commonsuffix = text1.substring(text1.length - commonlength);\n  text1 = text1.substring(0, text1.length - commonlength);\n  text2 = text2.substring(0, text2.length - commonlength);\n\n  // Compute the diff on the middle block.\n  var diffs = diff_compute_(text1, text2);\n\n  // Restore the prefix and suffix.\n  if (commonprefix) {\n    diffs.unshift([DIFF_EQUAL, commonprefix]);\n  }\n  if (commonsuffix) {\n    diffs.push([DIFF_EQUAL, commonsuffix]);\n  }\n  diff_cleanupMerge(diffs, _fix_unicode);\n  if (cleanup) {\n    diff_cleanupSemantic(diffs);\n  }\n  return diffs;\n}\n\n/**\n * Find the differences between two texts.  Assumes that the texts do not\n * have any common prefix or suffix.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_compute_(text1, text2) {\n  var diffs;\n  if (!text1) {\n    // Just add some text (speedup).\n    return [[DIFF_INSERT, text2]];\n  }\n  if (!text2) {\n    // Just delete some text (speedup).\n    return [[DIFF_DELETE, text1]];\n  }\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  var i = longtext.indexOf(shorttext);\n  if (i !== -1) {\n    // Shorter text is inside the longer text (speedup).\n    diffs = [[DIFF_INSERT, longtext.substring(0, i)], [DIFF_EQUAL, shorttext], [DIFF_INSERT, longtext.substring(i + shorttext.length)]];\n    // Swap insertions for deletions if diff is reversed.\n    if (text1.length > text2.length) {\n      diffs[0][0] = diffs[2][0] = DIFF_DELETE;\n    }\n    return diffs;\n  }\n  if (shorttext.length === 1) {\n    // Single character string.\n    // After the previous speedup, the character can't be an equality.\n    return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];\n  }\n\n  // Check to see if the problem can be split in two.\n  var hm = diff_halfMatch_(text1, text2);\n  if (hm) {\n    // A half-match was found, sort out the return data.\n    var text1_a = hm[0];\n    var text1_b = hm[1];\n    var text2_a = hm[2];\n    var text2_b = hm[3];\n    var mid_common = hm[4];\n    // Send both pairs off for separate processing.\n    var diffs_a = diff_main(text1_a, text2_a);\n    var diffs_b = diff_main(text1_b, text2_b);\n    // Merge the results.\n    return diffs_a.concat([[DIFF_EQUAL, mid_common]], diffs_b);\n  }\n  return diff_bisect_(text1, text2);\n}\n\n/**\n * Find the 'middle snake' of a diff, split the problem in two\n * and return the recursively constructed diff.\n * See Myers 1986 paper: An O(ND) Difference Algorithm and Its Variations.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @return {Array} Array of diff tuples.\n * @private\n */\nfunction diff_bisect_(text1, text2) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  var max_d = Math.ceil((text1_length + text2_length) / 2);\n  var v_offset = max_d;\n  var v_length = 2 * max_d;\n  var v1 = new Array(v_length);\n  var v2 = new Array(v_length);\n  // Setting all elements to -1 is faster in Chrome & Firefox than mixing\n  // integers and undefined.\n  for (var x = 0; x < v_length; x++) {\n    v1[x] = -1;\n    v2[x] = -1;\n  }\n  v1[v_offset + 1] = 0;\n  v2[v_offset + 1] = 0;\n  var delta = text1_length - text2_length;\n  // If the total number of characters is odd, then the front path will collide\n  // with the reverse path.\n  var front = delta % 2 !== 0;\n  // Offsets for start and end of k loop.\n  // Prevents mapping of space beyond the grid.\n  var k1start = 0;\n  var k1end = 0;\n  var k2start = 0;\n  var k2end = 0;\n  for (var d = 0; d < max_d; d++) {\n    // Walk the front path one step.\n    for (var k1 = -d + k1start; k1 <= d - k1end; k1 += 2) {\n      var k1_offset = v_offset + k1;\n      var x1;\n      if (k1 === -d || k1 !== d && v1[k1_offset - 1] < v1[k1_offset + 1]) {\n        x1 = v1[k1_offset + 1];\n      } else {\n        x1 = v1[k1_offset - 1] + 1;\n      }\n      var y1 = x1 - k1;\n      while (x1 < text1_length && y1 < text2_length && text1.charAt(x1) === text2.charAt(y1)) {\n        x1++;\n        y1++;\n      }\n      v1[k1_offset] = x1;\n      if (x1 > text1_length) {\n        // Ran off the right of the graph.\n        k1end += 2;\n      } else if (y1 > text2_length) {\n        // Ran off the bottom of the graph.\n        k1start += 2;\n      } else if (front) {\n        var k2_offset = v_offset + delta - k1;\n        if (k2_offset >= 0 && k2_offset < v_length && v2[k2_offset] !== -1) {\n          // Mirror x2 onto top-left coordinate system.\n          var x2 = text1_length - v2[k2_offset];\n          if (x1 >= x2) {\n            // Overlap detected.\n            return diff_bisectSplit_(text1, text2, x1, y1);\n          }\n        }\n      }\n    }\n\n    // Walk the reverse path one step.\n    for (var k2 = -d + k2start; k2 <= d - k2end; k2 += 2) {\n      var k2_offset = v_offset + k2;\n      var x2;\n      if (k2 === -d || k2 !== d && v2[k2_offset - 1] < v2[k2_offset + 1]) {\n        x2 = v2[k2_offset + 1];\n      } else {\n        x2 = v2[k2_offset - 1] + 1;\n      }\n      var y2 = x2 - k2;\n      while (x2 < text1_length && y2 < text2_length && text1.charAt(text1_length - x2 - 1) === text2.charAt(text2_length - y2 - 1)) {\n        x2++;\n        y2++;\n      }\n      v2[k2_offset] = x2;\n      if (x2 > text1_length) {\n        // Ran off the left of the graph.\n        k2end += 2;\n      } else if (y2 > text2_length) {\n        // Ran off the top of the graph.\n        k2start += 2;\n      } else if (!front) {\n        var k1_offset = v_offset + delta - k2;\n        if (k1_offset >= 0 && k1_offset < v_length && v1[k1_offset] !== -1) {\n          var x1 = v1[k1_offset];\n          var y1 = v_offset + x1 - k1_offset;\n          // Mirror x2 onto top-left coordinate system.\n          x2 = text1_length - x2;\n          if (x1 >= x2) {\n            // Overlap detected.\n            return diff_bisectSplit_(text1, text2, x1, y1);\n          }\n        }\n      }\n    }\n  }\n  // Diff took too long and hit the deadline or\n  // number of diffs equals number of characters, no commonality at all.\n  return [[DIFF_DELETE, text1], [DIFF_INSERT, text2]];\n}\n\n/**\n * Given the location of the 'middle snake', split the diff in two parts\n * and recurse.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {number} x Index of split point in text1.\n * @param {number} y Index of split point in text2.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_bisectSplit_(text1, text2, x, y) {\n  var text1a = text1.substring(0, x);\n  var text2a = text2.substring(0, y);\n  var text1b = text1.substring(x);\n  var text2b = text2.substring(y);\n\n  // Compute both diffs serially.\n  var diffs = diff_main(text1a, text2a);\n  var diffsb = diff_main(text1b, text2b);\n  return diffs.concat(diffsb);\n}\n\n/**\n * Determine the common prefix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the start of each\n *     string.\n */\nfunction diff_commonPrefix(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 || text1.charAt(0) !== text2.charAt(0)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerstart = 0;\n  while (pointermin < pointermid) {\n    if (text1.substring(pointerstart, pointermid) == text2.substring(pointerstart, pointermid)) {\n      pointermin = pointermid;\n      pointerstart = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n  if (is_surrogate_pair_start(text1.charCodeAt(pointermid - 1))) {\n    pointermid--;\n  }\n  return pointermid;\n}\n\n/**\n * Determine if the suffix of one string is the prefix of another.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of the first\n *     string and the start of the second string.\n * @private\n */\nfunction diff_commonOverlap_(text1, text2) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  // Eliminate the null case.\n  if (text1_length == 0 || text2_length == 0) {\n    return 0;\n  }\n  // Truncate the longer string.\n  if (text1_length > text2_length) {\n    text1 = text1.substring(text1_length - text2_length);\n  } else if (text1_length < text2_length) {\n    text2 = text2.substring(0, text1_length);\n  }\n  var text_length = Math.min(text1_length, text2_length);\n  // Quick check for the worst case.\n  if (text1 == text2) {\n    return text_length;\n  }\n\n  // Start by looking for a single character match\n  // and increase length until no match is found.\n  // Performance analysis: http://neil.fraser.name/news/2010/11/04/\n  var best = 0;\n  var length = 1;\n  while (true) {\n    var pattern = text1.substring(text_length - length);\n    var found = text2.indexOf(pattern);\n    if (found == -1) {\n      return best;\n    }\n    length += found;\n    if (found == 0 || text1.substring(text_length - length) == text2.substring(0, length)) {\n      best = length;\n      length++;\n    }\n  }\n}\n\n/**\n * Determine the common suffix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of each string.\n */\nfunction diff_commonSuffix(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 || text1.slice(-1) !== text2.slice(-1)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerend = 0;\n  while (pointermin < pointermid) {\n    if (text1.substring(text1.length - pointermid, text1.length - pointerend) == text2.substring(text2.length - pointermid, text2.length - pointerend)) {\n      pointermin = pointermid;\n      pointerend = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n  if (is_surrogate_pair_end(text1.charCodeAt(text1.length - pointermid))) {\n    pointermid--;\n  }\n  return pointermid;\n}\n\n/**\n * Do the two texts share a substring which is at least half the length of the\n * longer text?\n * This speedup can produce non-minimal diffs.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {Array.<string>} Five element Array, containing the prefix of\n *     text1, the suffix of text1, the prefix of text2, the suffix of\n *     text2 and the common middle.  Or null if there was no match.\n */\nfunction diff_halfMatch_(text1, text2) {\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  if (longtext.length < 4 || shorttext.length * 2 < longtext.length) {\n    return null; // Pointless.\n  }\n\n  /**\n   * Does a substring of shorttext exist within longtext such that the substring\n   * is at least half the length of longtext?\n   * Closure, but does not reference any external variables.\n   * @param {string} longtext Longer string.\n   * @param {string} shorttext Shorter string.\n   * @param {number} i Start index of quarter length substring within longtext.\n   * @return {Array.<string>} Five element Array, containing the prefix of\n   *     longtext, the suffix of longtext, the prefix of shorttext, the suffix\n   *     of shorttext and the common middle.  Or null if there was no match.\n   * @private\n   */\n  function diff_halfMatchI_(longtext, shorttext, i) {\n    // Start with a 1/4 length substring at position i as a seed.\n    var seed = longtext.substring(i, i + Math.floor(longtext.length / 4));\n    var j = -1;\n    var best_common = \"\";\n    var best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b;\n    while ((j = shorttext.indexOf(seed, j + 1)) !== -1) {\n      var prefixLength = diff_commonPrefix(longtext.substring(i), shorttext.substring(j));\n      var suffixLength = diff_commonSuffix(longtext.substring(0, i), shorttext.substring(0, j));\n      if (best_common.length < suffixLength + prefixLength) {\n        best_common = shorttext.substring(j - suffixLength, j) + shorttext.substring(j, j + prefixLength);\n        best_longtext_a = longtext.substring(0, i - suffixLength);\n        best_longtext_b = longtext.substring(i + prefixLength);\n        best_shorttext_a = shorttext.substring(0, j - suffixLength);\n        best_shorttext_b = shorttext.substring(j + prefixLength);\n      }\n    }\n    if (best_common.length * 2 >= longtext.length) {\n      return [best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b, best_common];\n    } else {\n      return null;\n    }\n  }\n\n  // First check if the second quarter is the seed for a half-match.\n  var hm1 = diff_halfMatchI_(longtext, shorttext, Math.ceil(longtext.length / 4));\n  // Check again based on the third quarter.\n  var hm2 = diff_halfMatchI_(longtext, shorttext, Math.ceil(longtext.length / 2));\n  var hm;\n  if (!hm1 && !hm2) {\n    return null;\n  } else if (!hm2) {\n    hm = hm1;\n  } else if (!hm1) {\n    hm = hm2;\n  } else {\n    // Both matched.  Select the longest.\n    hm = hm1[4].length > hm2[4].length ? hm1 : hm2;\n  }\n\n  // A half-match was found, sort out the return data.\n  var text1_a, text1_b, text2_a, text2_b;\n  if (text1.length > text2.length) {\n    text1_a = hm[0];\n    text1_b = hm[1];\n    text2_a = hm[2];\n    text2_b = hm[3];\n  } else {\n    text2_a = hm[0];\n    text2_b = hm[1];\n    text1_a = hm[2];\n    text1_b = hm[3];\n  }\n  var mid_common = hm[4];\n  return [text1_a, text1_b, text2_a, text2_b, mid_common];\n}\n\n/**\n * Reduce the number of edits by eliminating semantically trivial equalities.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\nfunction diff_cleanupSemantic(diffs) {\n  var changes = false;\n  var equalities = []; // Stack of indices where equalities are found.\n  var equalitiesLength = 0; // Keeping our own length var is faster in JS.\n  /** @type {?string} */\n  var lastequality = null;\n  // Always equal to diffs[equalities[equalitiesLength - 1]][1]\n  var pointer = 0; // Index of current position.\n  // Number of characters that changed prior to the equality.\n  var length_insertions1 = 0;\n  var length_deletions1 = 0;\n  // Number of characters that changed after the equality.\n  var length_insertions2 = 0;\n  var length_deletions2 = 0;\n  while (pointer < diffs.length) {\n    if (diffs[pointer][0] == DIFF_EQUAL) {\n      // Equality found.\n      equalities[equalitiesLength++] = pointer;\n      length_insertions1 = length_insertions2;\n      length_deletions1 = length_deletions2;\n      length_insertions2 = 0;\n      length_deletions2 = 0;\n      lastequality = diffs[pointer][1];\n    } else {\n      // An insertion or deletion.\n      if (diffs[pointer][0] == DIFF_INSERT) {\n        length_insertions2 += diffs[pointer][1].length;\n      } else {\n        length_deletions2 += diffs[pointer][1].length;\n      }\n      // Eliminate an equality that is smaller or equal to the edits on both\n      // sides of it.\n      if (lastequality && lastequality.length <= Math.max(length_insertions1, length_deletions1) && lastequality.length <= Math.max(length_insertions2, length_deletions2)) {\n        // Duplicate record.\n        diffs.splice(equalities[equalitiesLength - 1], 0, [DIFF_DELETE, lastequality]);\n        // Change second copy to insert.\n        diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT;\n        // Throw away the equality we just deleted.\n        equalitiesLength--;\n        // Throw away the previous equality (it needs to be reevaluated).\n        equalitiesLength--;\n        pointer = equalitiesLength > 0 ? equalities[equalitiesLength - 1] : -1;\n        length_insertions1 = 0; // Reset the counters.\n        length_deletions1 = 0;\n        length_insertions2 = 0;\n        length_deletions2 = 0;\n        lastequality = null;\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n\n  // Normalize the diff.\n  if (changes) {\n    diff_cleanupMerge(diffs);\n  }\n  diff_cleanupSemanticLossless(diffs);\n\n  // Find any overlaps between deletions and insertions.\n  // e.g: <del>abcxxx</del><ins>xxxdef</ins>\n  //   -> <del>abc</del>xxx<ins>def</ins>\n  // e.g: <del>xxxabc</del><ins>defxxx</ins>\n  //   -> <ins>def</ins>xxx<del>abc</del>\n  // Only extract an overlap if it is as big as the edit ahead or behind it.\n  pointer = 1;\n  while (pointer < diffs.length) {\n    if (diffs[pointer - 1][0] == DIFF_DELETE && diffs[pointer][0] == DIFF_INSERT) {\n      var deletion = diffs[pointer - 1][1];\n      var insertion = diffs[pointer][1];\n      var overlap_length1 = diff_commonOverlap_(deletion, insertion);\n      var overlap_length2 = diff_commonOverlap_(insertion, deletion);\n      if (overlap_length1 >= overlap_length2) {\n        if (overlap_length1 >= deletion.length / 2 || overlap_length1 >= insertion.length / 2) {\n          // Overlap found.  Insert an equality and trim the surrounding edits.\n          diffs.splice(pointer, 0, [DIFF_EQUAL, insertion.substring(0, overlap_length1)]);\n          diffs[pointer - 1][1] = deletion.substring(0, deletion.length - overlap_length1);\n          diffs[pointer + 1][1] = insertion.substring(overlap_length1);\n          pointer++;\n        }\n      } else {\n        if (overlap_length2 >= deletion.length / 2 || overlap_length2 >= insertion.length / 2) {\n          // Reverse overlap found.\n          // Insert an equality and swap and trim the surrounding edits.\n          diffs.splice(pointer, 0, [DIFF_EQUAL, deletion.substring(0, overlap_length2)]);\n          diffs[pointer - 1][0] = DIFF_INSERT;\n          diffs[pointer - 1][1] = insertion.substring(0, insertion.length - overlap_length2);\n          diffs[pointer + 1][0] = DIFF_DELETE;\n          diffs[pointer + 1][1] = deletion.substring(overlap_length2);\n          pointer++;\n        }\n      }\n      pointer++;\n    }\n    pointer++;\n  }\n}\nvar nonAlphaNumericRegex_ = /[^a-zA-Z0-9]/;\nvar whitespaceRegex_ = /\\s/;\nvar linebreakRegex_ = /[\\r\\n]/;\nvar blanklineEndRegex_ = /\\n\\r?\\n$/;\nvar blanklineStartRegex_ = /^\\r?\\n\\r?\\n/;\n\n/**\n * Look for single edits surrounded on both sides by equalities\n * which can be shifted sideways to align the edit to a word boundary.\n * e.g: The c<ins>at c</ins>ame. -> The <ins>cat </ins>came.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\nfunction diff_cleanupSemanticLossless(diffs) {\n  /**\n   * Given two strings, compute a score representing whether the internal\n   * boundary falls on logical boundaries.\n   * Scores range from 6 (best) to 0 (worst).\n   * Closure, but does not reference any external variables.\n   * @param {string} one First string.\n   * @param {string} two Second string.\n   * @return {number} The score.\n   * @private\n   */\n  function diff_cleanupSemanticScore_(one, two) {\n    if (!one || !two) {\n      // Edges are the best.\n      return 6;\n    }\n\n    // Each port of this function behaves slightly differently due to\n    // subtle differences in each language's definition of things like\n    // 'whitespace'.  Since this function's purpose is largely cosmetic,\n    // the choice has been made to use each language's native features\n    // rather than force total conformity.\n    var char1 = one.charAt(one.length - 1);\n    var char2 = two.charAt(0);\n    var nonAlphaNumeric1 = char1.match(nonAlphaNumericRegex_);\n    var nonAlphaNumeric2 = char2.match(nonAlphaNumericRegex_);\n    var whitespace1 = nonAlphaNumeric1 && char1.match(whitespaceRegex_);\n    var whitespace2 = nonAlphaNumeric2 && char2.match(whitespaceRegex_);\n    var lineBreak1 = whitespace1 && char1.match(linebreakRegex_);\n    var lineBreak2 = whitespace2 && char2.match(linebreakRegex_);\n    var blankLine1 = lineBreak1 && one.match(blanklineEndRegex_);\n    var blankLine2 = lineBreak2 && two.match(blanklineStartRegex_);\n    if (blankLine1 || blankLine2) {\n      // Five points for blank lines.\n      return 5;\n    } else if (lineBreak1 || lineBreak2) {\n      // Four points for line breaks.\n      return 4;\n    } else if (nonAlphaNumeric1 && !whitespace1 && whitespace2) {\n      // Three points for end of sentences.\n      return 3;\n    } else if (whitespace1 || whitespace2) {\n      // Two points for whitespace.\n      return 2;\n    } else if (nonAlphaNumeric1 || nonAlphaNumeric2) {\n      // One point for non-alphanumeric.\n      return 1;\n    }\n    return 0;\n  }\n  var pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (diffs[pointer - 1][0] == DIFF_EQUAL && diffs[pointer + 1][0] == DIFF_EQUAL) {\n      // This is a single edit surrounded by equalities.\n      var equality1 = diffs[pointer - 1][1];\n      var edit = diffs[pointer][1];\n      var equality2 = diffs[pointer + 1][1];\n\n      // First, shift the edit as far left as possible.\n      var commonOffset = diff_commonSuffix(equality1, edit);\n      if (commonOffset) {\n        var commonString = edit.substring(edit.length - commonOffset);\n        equality1 = equality1.substring(0, equality1.length - commonOffset);\n        edit = commonString + edit.substring(0, edit.length - commonOffset);\n        equality2 = commonString + equality2;\n      }\n\n      // Second, step character by character right, looking for the best fit.\n      var bestEquality1 = equality1;\n      var bestEdit = edit;\n      var bestEquality2 = equality2;\n      var bestScore = diff_cleanupSemanticScore_(equality1, edit) + diff_cleanupSemanticScore_(edit, equality2);\n      while (edit.charAt(0) === equality2.charAt(0)) {\n        equality1 += edit.charAt(0);\n        edit = edit.substring(1) + equality2.charAt(0);\n        equality2 = equality2.substring(1);\n        var score = diff_cleanupSemanticScore_(equality1, edit) + diff_cleanupSemanticScore_(edit, equality2);\n        // The >= encourages trailing rather than leading whitespace on edits.\n        if (score >= bestScore) {\n          bestScore = score;\n          bestEquality1 = equality1;\n          bestEdit = edit;\n          bestEquality2 = equality2;\n        }\n      }\n      if (diffs[pointer - 1][1] != bestEquality1) {\n        // We have an improvement, save it back to the diff.\n        if (bestEquality1) {\n          diffs[pointer - 1][1] = bestEquality1;\n        } else {\n          diffs.splice(pointer - 1, 1);\n          pointer--;\n        }\n        diffs[pointer][1] = bestEdit;\n        if (bestEquality2) {\n          diffs[pointer + 1][1] = bestEquality2;\n        } else {\n          diffs.splice(pointer + 1, 1);\n          pointer--;\n        }\n      }\n    }\n    pointer++;\n  }\n}\n\n/**\n * Reorder and merge like edit sections.  Merge equalities.\n * Any edit section can move as long as it doesn't cross an equality.\n * @param {Array} diffs Array of diff tuples.\n * @param {boolean} fix_unicode Whether to normalize to a unicode-correct diff\n */\nfunction diff_cleanupMerge(diffs, fix_unicode) {\n  diffs.push([DIFF_EQUAL, \"\"]); // Add a dummy entry at the end.\n  var pointer = 0;\n  var count_delete = 0;\n  var count_insert = 0;\n  var text_delete = \"\";\n  var text_insert = \"\";\n  var commonlength;\n  while (pointer < diffs.length) {\n    if (pointer < diffs.length - 1 && !diffs[pointer][1]) {\n      diffs.splice(pointer, 1);\n      continue;\n    }\n    switch (diffs[pointer][0]) {\n      case DIFF_INSERT:\n        count_insert++;\n        text_insert += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_DELETE:\n        count_delete++;\n        text_delete += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_EQUAL:\n        var previous_equality = pointer - count_insert - count_delete - 1;\n        if (fix_unicode) {\n          // prevent splitting of unicode surrogate pairs.  when fix_unicode is true,\n          // we assume that the old and new text in the diff are complete and correct\n          // unicode-encoded JS strings, but the tuple boundaries may fall between\n          // surrogate pairs.  we fix this by shaving off stray surrogates from the end\n          // of the previous equality and the beginning of this equality.  this may create\n          // empty equalities or a common prefix or suffix.  for example, if AB and AC are\n          // emojis, `[[0, 'A'], [-1, 'BA'], [0, 'C']]` would turn into deleting 'ABAC' and\n          // inserting 'AC', and then the common suffix 'AC' will be eliminated.  in this\n          // particular case, both equalities go away, we absorb any previous inequalities,\n          // and we keep scanning for the next equality before rewriting the tuples.\n          if (previous_equality >= 0 && ends_with_pair_start(diffs[previous_equality][1])) {\n            var stray = diffs[previous_equality][1].slice(-1);\n            diffs[previous_equality][1] = diffs[previous_equality][1].slice(0, -1);\n            text_delete = stray + text_delete;\n            text_insert = stray + text_insert;\n            if (!diffs[previous_equality][1]) {\n              // emptied out previous equality, so delete it and include previous delete/insert\n              diffs.splice(previous_equality, 1);\n              pointer--;\n              var k = previous_equality - 1;\n              if (diffs[k] && diffs[k][0] === DIFF_INSERT) {\n                count_insert++;\n                text_insert = diffs[k][1] + text_insert;\n                k--;\n              }\n              if (diffs[k] && diffs[k][0] === DIFF_DELETE) {\n                count_delete++;\n                text_delete = diffs[k][1] + text_delete;\n                k--;\n              }\n              previous_equality = k;\n            }\n          }\n          if (starts_with_pair_end(diffs[pointer][1])) {\n            var stray = diffs[pointer][1].charAt(0);\n            diffs[pointer][1] = diffs[pointer][1].slice(1);\n            text_delete += stray;\n            text_insert += stray;\n          }\n        }\n        if (pointer < diffs.length - 1 && !diffs[pointer][1]) {\n          // for empty equality not at end, wait for next equality\n          diffs.splice(pointer, 1);\n          break;\n        }\n        if (text_delete.length > 0 || text_insert.length > 0) {\n          // note that diff_commonPrefix and diff_commonSuffix are unicode-aware\n          if (text_delete.length > 0 && text_insert.length > 0) {\n            // Factor out any common prefixes.\n            commonlength = diff_commonPrefix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              if (previous_equality >= 0) {\n                diffs[previous_equality][1] += text_insert.substring(0, commonlength);\n              } else {\n                diffs.splice(0, 0, [DIFF_EQUAL, text_insert.substring(0, commonlength)]);\n                pointer++;\n              }\n              text_insert = text_insert.substring(commonlength);\n              text_delete = text_delete.substring(commonlength);\n            }\n            // Factor out any common suffixes.\n            commonlength = diff_commonSuffix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              diffs[pointer][1] = text_insert.substring(text_insert.length - commonlength) + diffs[pointer][1];\n              text_insert = text_insert.substring(0, text_insert.length - commonlength);\n              text_delete = text_delete.substring(0, text_delete.length - commonlength);\n            }\n          }\n          // Delete the offending records and add the merged ones.\n          var n = count_insert + count_delete;\n          if (text_delete.length === 0 && text_insert.length === 0) {\n            diffs.splice(pointer - n, n);\n            pointer = pointer - n;\n          } else if (text_delete.length === 0) {\n            diffs.splice(pointer - n, n, [DIFF_INSERT, text_insert]);\n            pointer = pointer - n + 1;\n          } else if (text_insert.length === 0) {\n            diffs.splice(pointer - n, n, [DIFF_DELETE, text_delete]);\n            pointer = pointer - n + 1;\n          } else {\n            diffs.splice(pointer - n, n, [DIFF_DELETE, text_delete], [DIFF_INSERT, text_insert]);\n            pointer = pointer - n + 2;\n          }\n        }\n        if (pointer !== 0 && diffs[pointer - 1][0] === DIFF_EQUAL) {\n          // Merge this equality with the previous one.\n          diffs[pointer - 1][1] += diffs[pointer][1];\n          diffs.splice(pointer, 1);\n        } else {\n          pointer++;\n        }\n        count_insert = 0;\n        count_delete = 0;\n        text_delete = \"\";\n        text_insert = \"\";\n        break;\n    }\n  }\n  if (diffs[diffs.length - 1][1] === \"\") {\n    diffs.pop(); // Remove the dummy entry at the end.\n  }\n\n  // Second pass: look for single edits surrounded on both sides by equalities\n  // which can be shifted sideways to eliminate an equality.\n  // e.g: A<ins>BA</ins>C -> <ins>AB</ins>AC\n  var changes = false;\n  pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (diffs[pointer - 1][0] === DIFF_EQUAL && diffs[pointer + 1][0] === DIFF_EQUAL) {\n      // This is a single edit surrounded by equalities.\n      if (diffs[pointer][1].substring(diffs[pointer][1].length - diffs[pointer - 1][1].length) === diffs[pointer - 1][1]) {\n        // Shift the edit over the previous equality.\n        diffs[pointer][1] = diffs[pointer - 1][1] + diffs[pointer][1].substring(0, diffs[pointer][1].length - diffs[pointer - 1][1].length);\n        diffs[pointer + 1][1] = diffs[pointer - 1][1] + diffs[pointer + 1][1];\n        diffs.splice(pointer - 1, 1);\n        changes = true;\n      } else if (diffs[pointer][1].substring(0, diffs[pointer + 1][1].length) == diffs[pointer + 1][1]) {\n        // Shift the edit over the next equality.\n        diffs[pointer - 1][1] += diffs[pointer + 1][1];\n        diffs[pointer][1] = diffs[pointer][1].substring(diffs[pointer + 1][1].length) + diffs[pointer + 1][1];\n        diffs.splice(pointer + 1, 1);\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n  // If shifts were made, the diff needs reordering and another shift sweep.\n  if (changes) {\n    diff_cleanupMerge(diffs, fix_unicode);\n  }\n}\nfunction is_surrogate_pair_start(charCode) {\n  return charCode >= 0xd800 && charCode <= 0xdbff;\n}\nfunction is_surrogate_pair_end(charCode) {\n  return charCode >= 0xdc00 && charCode <= 0xdfff;\n}\nfunction starts_with_pair_end(str) {\n  return is_surrogate_pair_end(str.charCodeAt(0));\n}\nfunction ends_with_pair_start(str) {\n  return is_surrogate_pair_start(str.charCodeAt(str.length - 1));\n}\nfunction remove_empty_tuples(tuples) {\n  var ret = [];\n  for (var i = 0; i < tuples.length; i++) {\n    if (tuples[i][1].length > 0) {\n      ret.push(tuples[i]);\n    }\n  }\n  return ret;\n}\nfunction make_edit_splice(before, oldMiddle, newMiddle, after) {\n  if (ends_with_pair_start(before) || starts_with_pair_end(after)) {\n    return null;\n  }\n  return remove_empty_tuples([[DIFF_EQUAL, before], [DIFF_DELETE, oldMiddle], [DIFF_INSERT, newMiddle], [DIFF_EQUAL, after]]);\n}\nfunction find_cursor_edit_diff(oldText, newText, cursor_pos) {\n  // note: this runs after equality check has ruled out exact equality\n  var oldRange = typeof cursor_pos === \"number\" ? {\n    index: cursor_pos,\n    length: 0\n  } : cursor_pos.oldRange;\n  var newRange = typeof cursor_pos === \"number\" ? null : cursor_pos.newRange;\n  // take into account the old and new selection to generate the best diff\n  // possible for a text edit.  for example, a text change from \"xxx\" to \"xx\"\n  // could be a delete or forwards-delete of any one of the x's, or the\n  // result of selecting two of the x's and typing \"x\".\n  var oldLength = oldText.length;\n  var newLength = newText.length;\n  if (oldRange.length === 0 && (newRange === null || newRange.length === 0)) {\n    // see if we have an insert or delete before or after cursor\n    var oldCursor = oldRange.index;\n    var oldBefore = oldText.slice(0, oldCursor);\n    var oldAfter = oldText.slice(oldCursor);\n    var maybeNewCursor = newRange ? newRange.index : null;\n    editBefore: {\n      // is this an insert or delete right before oldCursor?\n      var newCursor = oldCursor + newLength - oldLength;\n      if (maybeNewCursor !== null && maybeNewCursor !== newCursor) {\n        break editBefore;\n      }\n      if (newCursor < 0 || newCursor > newLength) {\n        break editBefore;\n      }\n      var newBefore = newText.slice(0, newCursor);\n      var newAfter = newText.slice(newCursor);\n      if (newAfter !== oldAfter) {\n        break editBefore;\n      }\n      var prefixLength = Math.min(oldCursor, newCursor);\n      var oldPrefix = oldBefore.slice(0, prefixLength);\n      var newPrefix = newBefore.slice(0, prefixLength);\n      if (oldPrefix !== newPrefix) {\n        break editBefore;\n      }\n      var oldMiddle = oldBefore.slice(prefixLength);\n      var newMiddle = newBefore.slice(prefixLength);\n      return make_edit_splice(oldPrefix, oldMiddle, newMiddle, oldAfter);\n    }\n    editAfter: {\n      // is this an insert or delete right after oldCursor?\n      if (maybeNewCursor !== null && maybeNewCursor !== oldCursor) {\n        break editAfter;\n      }\n      var cursor = oldCursor;\n      var newBefore = newText.slice(0, cursor);\n      var newAfter = newText.slice(cursor);\n      if (newBefore !== oldBefore) {\n        break editAfter;\n      }\n      var suffixLength = Math.min(oldLength - cursor, newLength - cursor);\n      var oldSuffix = oldAfter.slice(oldAfter.length - suffixLength);\n      var newSuffix = newAfter.slice(newAfter.length - suffixLength);\n      if (oldSuffix !== newSuffix) {\n        break editAfter;\n      }\n      var oldMiddle = oldAfter.slice(0, oldAfter.length - suffixLength);\n      var newMiddle = newAfter.slice(0, newAfter.length - suffixLength);\n      return make_edit_splice(oldBefore, oldMiddle, newMiddle, oldSuffix);\n    }\n  }\n  if (oldRange.length > 0 && newRange && newRange.length === 0) {\n    replaceRange: {\n      // see if diff could be a splice of the old selection range\n      var oldPrefix = oldText.slice(0, oldRange.index);\n      var oldSuffix = oldText.slice(oldRange.index + oldRange.length);\n      var prefixLength = oldPrefix.length;\n      var suffixLength = oldSuffix.length;\n      if (newLength < prefixLength + suffixLength) {\n        break replaceRange;\n      }\n      var newPrefix = newText.slice(0, prefixLength);\n      var newSuffix = newText.slice(newLength - suffixLength);\n      if (oldPrefix !== newPrefix || oldSuffix !== newSuffix) {\n        break replaceRange;\n      }\n      var oldMiddle = oldText.slice(prefixLength, oldLength - suffixLength);\n      var newMiddle = newText.slice(prefixLength, newLength - suffixLength);\n      return make_edit_splice(oldPrefix, oldMiddle, newMiddle, oldSuffix);\n    }\n  }\n  return null;\n}\nfunction diff(text1, text2, cursor_pos, cleanup) {\n  // only pass fix_unicode=true at the top level, not when diff_main is\n  // recursively invoked\n  return diff_main(text1, text2, cursor_pos, cleanup, true);\n}\ndiff.INSERT = DIFF_INSERT;\ndiff.DELETE = DIFF_DELETE;\ndiff.EQUAL = DIFF_EQUAL;\nmodule.exports = diff;", "map": {"version": 3, "names": ["DIFF_DELETE", "DIFF_INSERT", "DIFF_EQUAL", "diff_main", "text1", "text2", "cursor_pos", "cleanup", "_fix_unicode", "editdiff", "find_cursor_edit_diff", "commonlength", "diff_commonPrefix", "commonprefix", "substring", "diff_commonSuffix", "commonsuffix", "length", "diffs", "diff_compute_", "unshift", "push", "diff_cleanupMerge", "diff_cleanupSemantic", "longtext", "shorttext", "i", "indexOf", "hm", "diff_halfMatch_", "text1_a", "text1_b", "text2_a", "text2_b", "mid_common", "diffs_a", "diffs_b", "concat", "diff_bisect_", "text1_length", "text2_length", "max_d", "Math", "ceil", "v_offset", "v_length", "v1", "Array", "v2", "x", "delta", "front", "k1start", "k1end", "k2start", "k2end", "d", "k1", "k1_offset", "x1", "y1", "char<PERSON>t", "k2_offset", "x2", "diff_bisectSplit_", "k2", "y2", "y", "text1a", "text2a", "text1b", "text2b", "diffsb", "pointermin", "pointermax", "min", "pointermid", "pointerstart", "floor", "is_surrogate_pair_start", "charCodeAt", "diff_commonOverlap_", "text_length", "best", "pattern", "found", "slice", "pointerend", "is_surrogate_pair_end", "diff_halfMatchI_", "seed", "j", "best_common", "best_longtext_a", "best_longtext_b", "best_shorttext_a", "best_shorttext_b", "prefixLength", "suffixLength", "hm1", "hm2", "changes", "equalities", "equalitiesLength", "lastequality", "pointer", "length_insertions1", "length_deletions1", "length_insertions2", "length_deletions2", "max", "splice", "diff_cleanupSemanticLossless", "deletion", "insertion", "overlap_length1", "overlap_length2", "nonAlphaNumericRegex_", "whitespaceRegex_", "linebreakRegex_", "blanklineEndRegex_", "blanklineStartRegex_", "diff_cleanupSemanticScore_", "one", "two", "char1", "char2", "nonAlphaNumeric1", "match", "nonAlphaNumeric2", "whitespace1", "whitespace2", "lineBreak1", "lineBreak2", "blankLine1", "blankLine2", "equality1", "edit", "equality2", "commonOffset", "commonString", "bestEquality1", "bestEdit", "bestEquality2", "bestScore", "score", "fix_unicode", "count_delete", "count_insert", "text_delete", "text_insert", "previous_equality", "ends_with_pair_start", "stray", "k", "starts_with_pair_end", "n", "pop", "charCode", "str", "remove_empty_tuples", "tuples", "ret", "make_edit_splice", "before", "oldMiddle", "newMiddle", "after", "oldText", "newText", "oldRange", "index", "newRange", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "old<PERSON>ursor", "oldBefore", "oldAfter", "maybeNewCursor", "editBefore", "newCursor", "newBefore", "newAfter", "oldPrefix", "newPrefix", "editAfter", "cursor", "oldSuffix", "newSuffix", "replaceRange", "diff", "INSERT", "DELETE", "EQUAL", "module", "exports"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/fast-diff/diff.js"], "sourcesContent": ["/**\n * This library modifies the diff-patch-match library by <PERSON> by removing the patch and match functionality and certain advanced\n * options in the diff function. The original license is as follows:\n *\n * ===\n *\n * Diff Match and Patch\n *\n * Copyright 2006 Google Inc.\n * http://code.google.com/p/google-diff-match-patch/\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *   http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/**\n * The data structure representing a diff is an array of tuples:\n * [[DIFF_DELETE, 'Hello'], [DIFF_INSERT, 'Goodbye'], [DIFF_EQUAL, ' world.']]\n * which means: delete 'Hello', add 'Goodbye' and keep ' world.'\n */\nvar DIFF_DELETE = -1;\nvar DIFF_INSERT = 1;\nvar DIFF_EQUAL = 0;\n\n/**\n * Find the differences between two texts.  Simplifies the problem by stripping\n * any common prefix or suffix off the texts before diffing.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {Int|Object} [cursor_pos] Edit position in text1 or object with more info\n * @param {boolean} [cleanup] Apply semantic cleanup before returning.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_main(text1, text2, cursor_pos, cleanup, _fix_unicode) {\n  // Check for equality\n  if (text1 === text2) {\n    if (text1) {\n      return [[DIFF_EQUAL, text1]];\n    }\n    return [];\n  }\n\n  if (cursor_pos != null) {\n    var editdiff = find_cursor_edit_diff(text1, text2, cursor_pos);\n    if (editdiff) {\n      return editdiff;\n    }\n  }\n\n  // Trim off common prefix (speedup).\n  var commonlength = diff_commonPrefix(text1, text2);\n  var commonprefix = text1.substring(0, commonlength);\n  text1 = text1.substring(commonlength);\n  text2 = text2.substring(commonlength);\n\n  // Trim off common suffix (speedup).\n  commonlength = diff_commonSuffix(text1, text2);\n  var commonsuffix = text1.substring(text1.length - commonlength);\n  text1 = text1.substring(0, text1.length - commonlength);\n  text2 = text2.substring(0, text2.length - commonlength);\n\n  // Compute the diff on the middle block.\n  var diffs = diff_compute_(text1, text2);\n\n  // Restore the prefix and suffix.\n  if (commonprefix) {\n    diffs.unshift([DIFF_EQUAL, commonprefix]);\n  }\n  if (commonsuffix) {\n    diffs.push([DIFF_EQUAL, commonsuffix]);\n  }\n  diff_cleanupMerge(diffs, _fix_unicode);\n  if (cleanup) {\n    diff_cleanupSemantic(diffs);\n  }\n  return diffs;\n}\n\n/**\n * Find the differences between two texts.  Assumes that the texts do not\n * have any common prefix or suffix.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_compute_(text1, text2) {\n  var diffs;\n\n  if (!text1) {\n    // Just add some text (speedup).\n    return [[DIFF_INSERT, text2]];\n  }\n\n  if (!text2) {\n    // Just delete some text (speedup).\n    return [[DIFF_DELETE, text1]];\n  }\n\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  var i = longtext.indexOf(shorttext);\n  if (i !== -1) {\n    // Shorter text is inside the longer text (speedup).\n    diffs = [\n      [DIFF_INSERT, longtext.substring(0, i)],\n      [DIFF_EQUAL, shorttext],\n      [DIFF_INSERT, longtext.substring(i + shorttext.length)],\n    ];\n    // Swap insertions for deletions if diff is reversed.\n    if (text1.length > text2.length) {\n      diffs[0][0] = diffs[2][0] = DIFF_DELETE;\n    }\n    return diffs;\n  }\n\n  if (shorttext.length === 1) {\n    // Single character string.\n    // After the previous speedup, the character can't be an equality.\n    return [\n      [DIFF_DELETE, text1],\n      [DIFF_INSERT, text2],\n    ];\n  }\n\n  // Check to see if the problem can be split in two.\n  var hm = diff_halfMatch_(text1, text2);\n  if (hm) {\n    // A half-match was found, sort out the return data.\n    var text1_a = hm[0];\n    var text1_b = hm[1];\n    var text2_a = hm[2];\n    var text2_b = hm[3];\n    var mid_common = hm[4];\n    // Send both pairs off for separate processing.\n    var diffs_a = diff_main(text1_a, text2_a);\n    var diffs_b = diff_main(text1_b, text2_b);\n    // Merge the results.\n    return diffs_a.concat([[DIFF_EQUAL, mid_common]], diffs_b);\n  }\n\n  return diff_bisect_(text1, text2);\n}\n\n/**\n * Find the 'middle snake' of a diff, split the problem in two\n * and return the recursively constructed diff.\n * See Myers 1986 paper: An O(ND) Difference Algorithm and Its Variations.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @return {Array} Array of diff tuples.\n * @private\n */\nfunction diff_bisect_(text1, text2) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  var max_d = Math.ceil((text1_length + text2_length) / 2);\n  var v_offset = max_d;\n  var v_length = 2 * max_d;\n  var v1 = new Array(v_length);\n  var v2 = new Array(v_length);\n  // Setting all elements to -1 is faster in Chrome & Firefox than mixing\n  // integers and undefined.\n  for (var x = 0; x < v_length; x++) {\n    v1[x] = -1;\n    v2[x] = -1;\n  }\n  v1[v_offset + 1] = 0;\n  v2[v_offset + 1] = 0;\n  var delta = text1_length - text2_length;\n  // If the total number of characters is odd, then the front path will collide\n  // with the reverse path.\n  var front = delta % 2 !== 0;\n  // Offsets for start and end of k loop.\n  // Prevents mapping of space beyond the grid.\n  var k1start = 0;\n  var k1end = 0;\n  var k2start = 0;\n  var k2end = 0;\n  for (var d = 0; d < max_d; d++) {\n    // Walk the front path one step.\n    for (var k1 = -d + k1start; k1 <= d - k1end; k1 += 2) {\n      var k1_offset = v_offset + k1;\n      var x1;\n      if (k1 === -d || (k1 !== d && v1[k1_offset - 1] < v1[k1_offset + 1])) {\n        x1 = v1[k1_offset + 1];\n      } else {\n        x1 = v1[k1_offset - 1] + 1;\n      }\n      var y1 = x1 - k1;\n      while (\n        x1 < text1_length &&\n        y1 < text2_length &&\n        text1.charAt(x1) === text2.charAt(y1)\n      ) {\n        x1++;\n        y1++;\n      }\n      v1[k1_offset] = x1;\n      if (x1 > text1_length) {\n        // Ran off the right of the graph.\n        k1end += 2;\n      } else if (y1 > text2_length) {\n        // Ran off the bottom of the graph.\n        k1start += 2;\n      } else if (front) {\n        var k2_offset = v_offset + delta - k1;\n        if (k2_offset >= 0 && k2_offset < v_length && v2[k2_offset] !== -1) {\n          // Mirror x2 onto top-left coordinate system.\n          var x2 = text1_length - v2[k2_offset];\n          if (x1 >= x2) {\n            // Overlap detected.\n            return diff_bisectSplit_(text1, text2, x1, y1);\n          }\n        }\n      }\n    }\n\n    // Walk the reverse path one step.\n    for (var k2 = -d + k2start; k2 <= d - k2end; k2 += 2) {\n      var k2_offset = v_offset + k2;\n      var x2;\n      if (k2 === -d || (k2 !== d && v2[k2_offset - 1] < v2[k2_offset + 1])) {\n        x2 = v2[k2_offset + 1];\n      } else {\n        x2 = v2[k2_offset - 1] + 1;\n      }\n      var y2 = x2 - k2;\n      while (\n        x2 < text1_length &&\n        y2 < text2_length &&\n        text1.charAt(text1_length - x2 - 1) ===\n          text2.charAt(text2_length - y2 - 1)\n      ) {\n        x2++;\n        y2++;\n      }\n      v2[k2_offset] = x2;\n      if (x2 > text1_length) {\n        // Ran off the left of the graph.\n        k2end += 2;\n      } else if (y2 > text2_length) {\n        // Ran off the top of the graph.\n        k2start += 2;\n      } else if (!front) {\n        var k1_offset = v_offset + delta - k2;\n        if (k1_offset >= 0 && k1_offset < v_length && v1[k1_offset] !== -1) {\n          var x1 = v1[k1_offset];\n          var y1 = v_offset + x1 - k1_offset;\n          // Mirror x2 onto top-left coordinate system.\n          x2 = text1_length - x2;\n          if (x1 >= x2) {\n            // Overlap detected.\n            return diff_bisectSplit_(text1, text2, x1, y1);\n          }\n        }\n      }\n    }\n  }\n  // Diff took too long and hit the deadline or\n  // number of diffs equals number of characters, no commonality at all.\n  return [\n    [DIFF_DELETE, text1],\n    [DIFF_INSERT, text2],\n  ];\n}\n\n/**\n * Given the location of the 'middle snake', split the diff in two parts\n * and recurse.\n * @param {string} text1 Old string to be diffed.\n * @param {string} text2 New string to be diffed.\n * @param {number} x Index of split point in text1.\n * @param {number} y Index of split point in text2.\n * @return {Array} Array of diff tuples.\n */\nfunction diff_bisectSplit_(text1, text2, x, y) {\n  var text1a = text1.substring(0, x);\n  var text2a = text2.substring(0, y);\n  var text1b = text1.substring(x);\n  var text2b = text2.substring(y);\n\n  // Compute both diffs serially.\n  var diffs = diff_main(text1a, text2a);\n  var diffsb = diff_main(text1b, text2b);\n\n  return diffs.concat(diffsb);\n}\n\n/**\n * Determine the common prefix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the start of each\n *     string.\n */\nfunction diff_commonPrefix(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 || text1.charAt(0) !== text2.charAt(0)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerstart = 0;\n  while (pointermin < pointermid) {\n    if (\n      text1.substring(pointerstart, pointermid) ==\n      text2.substring(pointerstart, pointermid)\n    ) {\n      pointermin = pointermid;\n      pointerstart = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n\n  if (is_surrogate_pair_start(text1.charCodeAt(pointermid - 1))) {\n    pointermid--;\n  }\n\n  return pointermid;\n}\n\n/**\n * Determine if the suffix of one string is the prefix of another.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of the first\n *     string and the start of the second string.\n * @private\n */\nfunction diff_commonOverlap_(text1, text2) {\n  // Cache the text lengths to prevent multiple calls.\n  var text1_length = text1.length;\n  var text2_length = text2.length;\n  // Eliminate the null case.\n  if (text1_length == 0 || text2_length == 0) {\n    return 0;\n  }\n  // Truncate the longer string.\n  if (text1_length > text2_length) {\n    text1 = text1.substring(text1_length - text2_length);\n  } else if (text1_length < text2_length) {\n    text2 = text2.substring(0, text1_length);\n  }\n  var text_length = Math.min(text1_length, text2_length);\n  // Quick check for the worst case.\n  if (text1 == text2) {\n    return text_length;\n  }\n\n  // Start by looking for a single character match\n  // and increase length until no match is found.\n  // Performance analysis: http://neil.fraser.name/news/2010/11/04/\n  var best = 0;\n  var length = 1;\n  while (true) {\n    var pattern = text1.substring(text_length - length);\n    var found = text2.indexOf(pattern);\n    if (found == -1) {\n      return best;\n    }\n    length += found;\n    if (\n      found == 0 ||\n      text1.substring(text_length - length) == text2.substring(0, length)\n    ) {\n      best = length;\n      length++;\n    }\n  }\n}\n\n/**\n * Determine the common suffix of two strings.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {number} The number of characters common to the end of each string.\n */\nfunction diff_commonSuffix(text1, text2) {\n  // Quick check for common null cases.\n  if (!text1 || !text2 || text1.slice(-1) !== text2.slice(-1)) {\n    return 0;\n  }\n  // Binary search.\n  // Performance analysis: http://neil.fraser.name/news/2007/10/09/\n  var pointermin = 0;\n  var pointermax = Math.min(text1.length, text2.length);\n  var pointermid = pointermax;\n  var pointerend = 0;\n  while (pointermin < pointermid) {\n    if (\n      text1.substring(text1.length - pointermid, text1.length - pointerend) ==\n      text2.substring(text2.length - pointermid, text2.length - pointerend)\n    ) {\n      pointermin = pointermid;\n      pointerend = pointermin;\n    } else {\n      pointermax = pointermid;\n    }\n    pointermid = Math.floor((pointermax - pointermin) / 2 + pointermin);\n  }\n\n  if (is_surrogate_pair_end(text1.charCodeAt(text1.length - pointermid))) {\n    pointermid--;\n  }\n\n  return pointermid;\n}\n\n/**\n * Do the two texts share a substring which is at least half the length of the\n * longer text?\n * This speedup can produce non-minimal diffs.\n * @param {string} text1 First string.\n * @param {string} text2 Second string.\n * @return {Array.<string>} Five element Array, containing the prefix of\n *     text1, the suffix of text1, the prefix of text2, the suffix of\n *     text2 and the common middle.  Or null if there was no match.\n */\nfunction diff_halfMatch_(text1, text2) {\n  var longtext = text1.length > text2.length ? text1 : text2;\n  var shorttext = text1.length > text2.length ? text2 : text1;\n  if (longtext.length < 4 || shorttext.length * 2 < longtext.length) {\n    return null; // Pointless.\n  }\n\n  /**\n   * Does a substring of shorttext exist within longtext such that the substring\n   * is at least half the length of longtext?\n   * Closure, but does not reference any external variables.\n   * @param {string} longtext Longer string.\n   * @param {string} shorttext Shorter string.\n   * @param {number} i Start index of quarter length substring within longtext.\n   * @return {Array.<string>} Five element Array, containing the prefix of\n   *     longtext, the suffix of longtext, the prefix of shorttext, the suffix\n   *     of shorttext and the common middle.  Or null if there was no match.\n   * @private\n   */\n  function diff_halfMatchI_(longtext, shorttext, i) {\n    // Start with a 1/4 length substring at position i as a seed.\n    var seed = longtext.substring(i, i + Math.floor(longtext.length / 4));\n    var j = -1;\n    var best_common = \"\";\n    var best_longtext_a, best_longtext_b, best_shorttext_a, best_shorttext_b;\n    while ((j = shorttext.indexOf(seed, j + 1)) !== -1) {\n      var prefixLength = diff_commonPrefix(\n        longtext.substring(i),\n        shorttext.substring(j)\n      );\n      var suffixLength = diff_commonSuffix(\n        longtext.substring(0, i),\n        shorttext.substring(0, j)\n      );\n      if (best_common.length < suffixLength + prefixLength) {\n        best_common =\n          shorttext.substring(j - suffixLength, j) +\n          shorttext.substring(j, j + prefixLength);\n        best_longtext_a = longtext.substring(0, i - suffixLength);\n        best_longtext_b = longtext.substring(i + prefixLength);\n        best_shorttext_a = shorttext.substring(0, j - suffixLength);\n        best_shorttext_b = shorttext.substring(j + prefixLength);\n      }\n    }\n    if (best_common.length * 2 >= longtext.length) {\n      return [\n        best_longtext_a,\n        best_longtext_b,\n        best_shorttext_a,\n        best_shorttext_b,\n        best_common,\n      ];\n    } else {\n      return null;\n    }\n  }\n\n  // First check if the second quarter is the seed for a half-match.\n  var hm1 = diff_halfMatchI_(\n    longtext,\n    shorttext,\n    Math.ceil(longtext.length / 4)\n  );\n  // Check again based on the third quarter.\n  var hm2 = diff_halfMatchI_(\n    longtext,\n    shorttext,\n    Math.ceil(longtext.length / 2)\n  );\n  var hm;\n  if (!hm1 && !hm2) {\n    return null;\n  } else if (!hm2) {\n    hm = hm1;\n  } else if (!hm1) {\n    hm = hm2;\n  } else {\n    // Both matched.  Select the longest.\n    hm = hm1[4].length > hm2[4].length ? hm1 : hm2;\n  }\n\n  // A half-match was found, sort out the return data.\n  var text1_a, text1_b, text2_a, text2_b;\n  if (text1.length > text2.length) {\n    text1_a = hm[0];\n    text1_b = hm[1];\n    text2_a = hm[2];\n    text2_b = hm[3];\n  } else {\n    text2_a = hm[0];\n    text2_b = hm[1];\n    text1_a = hm[2];\n    text1_b = hm[3];\n  }\n  var mid_common = hm[4];\n  return [text1_a, text1_b, text2_a, text2_b, mid_common];\n}\n\n/**\n * Reduce the number of edits by eliminating semantically trivial equalities.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\nfunction diff_cleanupSemantic(diffs) {\n  var changes = false;\n  var equalities = []; // Stack of indices where equalities are found.\n  var equalitiesLength = 0; // Keeping our own length var is faster in JS.\n  /** @type {?string} */\n  var lastequality = null;\n  // Always equal to diffs[equalities[equalitiesLength - 1]][1]\n  var pointer = 0; // Index of current position.\n  // Number of characters that changed prior to the equality.\n  var length_insertions1 = 0;\n  var length_deletions1 = 0;\n  // Number of characters that changed after the equality.\n  var length_insertions2 = 0;\n  var length_deletions2 = 0;\n  while (pointer < diffs.length) {\n    if (diffs[pointer][0] == DIFF_EQUAL) {\n      // Equality found.\n      equalities[equalitiesLength++] = pointer;\n      length_insertions1 = length_insertions2;\n      length_deletions1 = length_deletions2;\n      length_insertions2 = 0;\n      length_deletions2 = 0;\n      lastequality = diffs[pointer][1];\n    } else {\n      // An insertion or deletion.\n      if (diffs[pointer][0] == DIFF_INSERT) {\n        length_insertions2 += diffs[pointer][1].length;\n      } else {\n        length_deletions2 += diffs[pointer][1].length;\n      }\n      // Eliminate an equality that is smaller or equal to the edits on both\n      // sides of it.\n      if (\n        lastequality &&\n        lastequality.length <=\n          Math.max(length_insertions1, length_deletions1) &&\n        lastequality.length <= Math.max(length_insertions2, length_deletions2)\n      ) {\n        // Duplicate record.\n        diffs.splice(equalities[equalitiesLength - 1], 0, [\n          DIFF_DELETE,\n          lastequality,\n        ]);\n        // Change second copy to insert.\n        diffs[equalities[equalitiesLength - 1] + 1][0] = DIFF_INSERT;\n        // Throw away the equality we just deleted.\n        equalitiesLength--;\n        // Throw away the previous equality (it needs to be reevaluated).\n        equalitiesLength--;\n        pointer = equalitiesLength > 0 ? equalities[equalitiesLength - 1] : -1;\n        length_insertions1 = 0; // Reset the counters.\n        length_deletions1 = 0;\n        length_insertions2 = 0;\n        length_deletions2 = 0;\n        lastequality = null;\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n\n  // Normalize the diff.\n  if (changes) {\n    diff_cleanupMerge(diffs);\n  }\n  diff_cleanupSemanticLossless(diffs);\n\n  // Find any overlaps between deletions and insertions.\n  // e.g: <del>abcxxx</del><ins>xxxdef</ins>\n  //   -> <del>abc</del>xxx<ins>def</ins>\n  // e.g: <del>xxxabc</del><ins>defxxx</ins>\n  //   -> <ins>def</ins>xxx<del>abc</del>\n  // Only extract an overlap if it is as big as the edit ahead or behind it.\n  pointer = 1;\n  while (pointer < diffs.length) {\n    if (\n      diffs[pointer - 1][0] == DIFF_DELETE &&\n      diffs[pointer][0] == DIFF_INSERT\n    ) {\n      var deletion = diffs[pointer - 1][1];\n      var insertion = diffs[pointer][1];\n      var overlap_length1 = diff_commonOverlap_(deletion, insertion);\n      var overlap_length2 = diff_commonOverlap_(insertion, deletion);\n      if (overlap_length1 >= overlap_length2) {\n        if (\n          overlap_length1 >= deletion.length / 2 ||\n          overlap_length1 >= insertion.length / 2\n        ) {\n          // Overlap found.  Insert an equality and trim the surrounding edits.\n          diffs.splice(pointer, 0, [\n            DIFF_EQUAL,\n            insertion.substring(0, overlap_length1),\n          ]);\n          diffs[pointer - 1][1] = deletion.substring(\n            0,\n            deletion.length - overlap_length1\n          );\n          diffs[pointer + 1][1] = insertion.substring(overlap_length1);\n          pointer++;\n        }\n      } else {\n        if (\n          overlap_length2 >= deletion.length / 2 ||\n          overlap_length2 >= insertion.length / 2\n        ) {\n          // Reverse overlap found.\n          // Insert an equality and swap and trim the surrounding edits.\n          diffs.splice(pointer, 0, [\n            DIFF_EQUAL,\n            deletion.substring(0, overlap_length2),\n          ]);\n          diffs[pointer - 1][0] = DIFF_INSERT;\n          diffs[pointer - 1][1] = insertion.substring(\n            0,\n            insertion.length - overlap_length2\n          );\n          diffs[pointer + 1][0] = DIFF_DELETE;\n          diffs[pointer + 1][1] = deletion.substring(overlap_length2);\n          pointer++;\n        }\n      }\n      pointer++;\n    }\n    pointer++;\n  }\n}\n\nvar nonAlphaNumericRegex_ = /[^a-zA-Z0-9]/;\nvar whitespaceRegex_ = /\\s/;\nvar linebreakRegex_ = /[\\r\\n]/;\nvar blanklineEndRegex_ = /\\n\\r?\\n$/;\nvar blanklineStartRegex_ = /^\\r?\\n\\r?\\n/;\n\n/**\n * Look for single edits surrounded on both sides by equalities\n * which can be shifted sideways to align the edit to a word boundary.\n * e.g: The c<ins>at c</ins>ame. -> The <ins>cat </ins>came.\n * @param {!Array.<!diff_match_patch.Diff>} diffs Array of diff tuples.\n */\nfunction diff_cleanupSemanticLossless(diffs) {\n  /**\n   * Given two strings, compute a score representing whether the internal\n   * boundary falls on logical boundaries.\n   * Scores range from 6 (best) to 0 (worst).\n   * Closure, but does not reference any external variables.\n   * @param {string} one First string.\n   * @param {string} two Second string.\n   * @return {number} The score.\n   * @private\n   */\n  function diff_cleanupSemanticScore_(one, two) {\n    if (!one || !two) {\n      // Edges are the best.\n      return 6;\n    }\n\n    // Each port of this function behaves slightly differently due to\n    // subtle differences in each language's definition of things like\n    // 'whitespace'.  Since this function's purpose is largely cosmetic,\n    // the choice has been made to use each language's native features\n    // rather than force total conformity.\n    var char1 = one.charAt(one.length - 1);\n    var char2 = two.charAt(0);\n    var nonAlphaNumeric1 = char1.match(nonAlphaNumericRegex_);\n    var nonAlphaNumeric2 = char2.match(nonAlphaNumericRegex_);\n    var whitespace1 = nonAlphaNumeric1 && char1.match(whitespaceRegex_);\n    var whitespace2 = nonAlphaNumeric2 && char2.match(whitespaceRegex_);\n    var lineBreak1 = whitespace1 && char1.match(linebreakRegex_);\n    var lineBreak2 = whitespace2 && char2.match(linebreakRegex_);\n    var blankLine1 = lineBreak1 && one.match(blanklineEndRegex_);\n    var blankLine2 = lineBreak2 && two.match(blanklineStartRegex_);\n\n    if (blankLine1 || blankLine2) {\n      // Five points for blank lines.\n      return 5;\n    } else if (lineBreak1 || lineBreak2) {\n      // Four points for line breaks.\n      return 4;\n    } else if (nonAlphaNumeric1 && !whitespace1 && whitespace2) {\n      // Three points for end of sentences.\n      return 3;\n    } else if (whitespace1 || whitespace2) {\n      // Two points for whitespace.\n      return 2;\n    } else if (nonAlphaNumeric1 || nonAlphaNumeric2) {\n      // One point for non-alphanumeric.\n      return 1;\n    }\n    return 0;\n  }\n\n  var pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (\n      diffs[pointer - 1][0] == DIFF_EQUAL &&\n      diffs[pointer + 1][0] == DIFF_EQUAL\n    ) {\n      // This is a single edit surrounded by equalities.\n      var equality1 = diffs[pointer - 1][1];\n      var edit = diffs[pointer][1];\n      var equality2 = diffs[pointer + 1][1];\n\n      // First, shift the edit as far left as possible.\n      var commonOffset = diff_commonSuffix(equality1, edit);\n      if (commonOffset) {\n        var commonString = edit.substring(edit.length - commonOffset);\n        equality1 = equality1.substring(0, equality1.length - commonOffset);\n        edit = commonString + edit.substring(0, edit.length - commonOffset);\n        equality2 = commonString + equality2;\n      }\n\n      // Second, step character by character right, looking for the best fit.\n      var bestEquality1 = equality1;\n      var bestEdit = edit;\n      var bestEquality2 = equality2;\n      var bestScore =\n        diff_cleanupSemanticScore_(equality1, edit) +\n        diff_cleanupSemanticScore_(edit, equality2);\n      while (edit.charAt(0) === equality2.charAt(0)) {\n        equality1 += edit.charAt(0);\n        edit = edit.substring(1) + equality2.charAt(0);\n        equality2 = equality2.substring(1);\n        var score =\n          diff_cleanupSemanticScore_(equality1, edit) +\n          diff_cleanupSemanticScore_(edit, equality2);\n        // The >= encourages trailing rather than leading whitespace on edits.\n        if (score >= bestScore) {\n          bestScore = score;\n          bestEquality1 = equality1;\n          bestEdit = edit;\n          bestEquality2 = equality2;\n        }\n      }\n\n      if (diffs[pointer - 1][1] != bestEquality1) {\n        // We have an improvement, save it back to the diff.\n        if (bestEquality1) {\n          diffs[pointer - 1][1] = bestEquality1;\n        } else {\n          diffs.splice(pointer - 1, 1);\n          pointer--;\n        }\n        diffs[pointer][1] = bestEdit;\n        if (bestEquality2) {\n          diffs[pointer + 1][1] = bestEquality2;\n        } else {\n          diffs.splice(pointer + 1, 1);\n          pointer--;\n        }\n      }\n    }\n    pointer++;\n  }\n}\n\n/**\n * Reorder and merge like edit sections.  Merge equalities.\n * Any edit section can move as long as it doesn't cross an equality.\n * @param {Array} diffs Array of diff tuples.\n * @param {boolean} fix_unicode Whether to normalize to a unicode-correct diff\n */\nfunction diff_cleanupMerge(diffs, fix_unicode) {\n  diffs.push([DIFF_EQUAL, \"\"]); // Add a dummy entry at the end.\n  var pointer = 0;\n  var count_delete = 0;\n  var count_insert = 0;\n  var text_delete = \"\";\n  var text_insert = \"\";\n  var commonlength;\n  while (pointer < diffs.length) {\n    if (pointer < diffs.length - 1 && !diffs[pointer][1]) {\n      diffs.splice(pointer, 1);\n      continue;\n    }\n    switch (diffs[pointer][0]) {\n      case DIFF_INSERT:\n        count_insert++;\n        text_insert += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_DELETE:\n        count_delete++;\n        text_delete += diffs[pointer][1];\n        pointer++;\n        break;\n      case DIFF_EQUAL:\n        var previous_equality = pointer - count_insert - count_delete - 1;\n        if (fix_unicode) {\n          // prevent splitting of unicode surrogate pairs.  when fix_unicode is true,\n          // we assume that the old and new text in the diff are complete and correct\n          // unicode-encoded JS strings, but the tuple boundaries may fall between\n          // surrogate pairs.  we fix this by shaving off stray surrogates from the end\n          // of the previous equality and the beginning of this equality.  this may create\n          // empty equalities or a common prefix or suffix.  for example, if AB and AC are\n          // emojis, `[[0, 'A'], [-1, 'BA'], [0, 'C']]` would turn into deleting 'ABAC' and\n          // inserting 'AC', and then the common suffix 'AC' will be eliminated.  in this\n          // particular case, both equalities go away, we absorb any previous inequalities,\n          // and we keep scanning for the next equality before rewriting the tuples.\n          if (\n            previous_equality >= 0 &&\n            ends_with_pair_start(diffs[previous_equality][1])\n          ) {\n            var stray = diffs[previous_equality][1].slice(-1);\n            diffs[previous_equality][1] = diffs[previous_equality][1].slice(\n              0,\n              -1\n            );\n            text_delete = stray + text_delete;\n            text_insert = stray + text_insert;\n            if (!diffs[previous_equality][1]) {\n              // emptied out previous equality, so delete it and include previous delete/insert\n              diffs.splice(previous_equality, 1);\n              pointer--;\n              var k = previous_equality - 1;\n              if (diffs[k] && diffs[k][0] === DIFF_INSERT) {\n                count_insert++;\n                text_insert = diffs[k][1] + text_insert;\n                k--;\n              }\n              if (diffs[k] && diffs[k][0] === DIFF_DELETE) {\n                count_delete++;\n                text_delete = diffs[k][1] + text_delete;\n                k--;\n              }\n              previous_equality = k;\n            }\n          }\n          if (starts_with_pair_end(diffs[pointer][1])) {\n            var stray = diffs[pointer][1].charAt(0);\n            diffs[pointer][1] = diffs[pointer][1].slice(1);\n            text_delete += stray;\n            text_insert += stray;\n          }\n        }\n        if (pointer < diffs.length - 1 && !diffs[pointer][1]) {\n          // for empty equality not at end, wait for next equality\n          diffs.splice(pointer, 1);\n          break;\n        }\n        if (text_delete.length > 0 || text_insert.length > 0) {\n          // note that diff_commonPrefix and diff_commonSuffix are unicode-aware\n          if (text_delete.length > 0 && text_insert.length > 0) {\n            // Factor out any common prefixes.\n            commonlength = diff_commonPrefix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              if (previous_equality >= 0) {\n                diffs[previous_equality][1] += text_insert.substring(\n                  0,\n                  commonlength\n                );\n              } else {\n                diffs.splice(0, 0, [\n                  DIFF_EQUAL,\n                  text_insert.substring(0, commonlength),\n                ]);\n                pointer++;\n              }\n              text_insert = text_insert.substring(commonlength);\n              text_delete = text_delete.substring(commonlength);\n            }\n            // Factor out any common suffixes.\n            commonlength = diff_commonSuffix(text_insert, text_delete);\n            if (commonlength !== 0) {\n              diffs[pointer][1] =\n                text_insert.substring(text_insert.length - commonlength) +\n                diffs[pointer][1];\n              text_insert = text_insert.substring(\n                0,\n                text_insert.length - commonlength\n              );\n              text_delete = text_delete.substring(\n                0,\n                text_delete.length - commonlength\n              );\n            }\n          }\n          // Delete the offending records and add the merged ones.\n          var n = count_insert + count_delete;\n          if (text_delete.length === 0 && text_insert.length === 0) {\n            diffs.splice(pointer - n, n);\n            pointer = pointer - n;\n          } else if (text_delete.length === 0) {\n            diffs.splice(pointer - n, n, [DIFF_INSERT, text_insert]);\n            pointer = pointer - n + 1;\n          } else if (text_insert.length === 0) {\n            diffs.splice(pointer - n, n, [DIFF_DELETE, text_delete]);\n            pointer = pointer - n + 1;\n          } else {\n            diffs.splice(\n              pointer - n,\n              n,\n              [DIFF_DELETE, text_delete],\n              [DIFF_INSERT, text_insert]\n            );\n            pointer = pointer - n + 2;\n          }\n        }\n        if (pointer !== 0 && diffs[pointer - 1][0] === DIFF_EQUAL) {\n          // Merge this equality with the previous one.\n          diffs[pointer - 1][1] += diffs[pointer][1];\n          diffs.splice(pointer, 1);\n        } else {\n          pointer++;\n        }\n        count_insert = 0;\n        count_delete = 0;\n        text_delete = \"\";\n        text_insert = \"\";\n        break;\n    }\n  }\n  if (diffs[diffs.length - 1][1] === \"\") {\n    diffs.pop(); // Remove the dummy entry at the end.\n  }\n\n  // Second pass: look for single edits surrounded on both sides by equalities\n  // which can be shifted sideways to eliminate an equality.\n  // e.g: A<ins>BA</ins>C -> <ins>AB</ins>AC\n  var changes = false;\n  pointer = 1;\n  // Intentionally ignore the first and last element (don't need checking).\n  while (pointer < diffs.length - 1) {\n    if (\n      diffs[pointer - 1][0] === DIFF_EQUAL &&\n      diffs[pointer + 1][0] === DIFF_EQUAL\n    ) {\n      // This is a single edit surrounded by equalities.\n      if (\n        diffs[pointer][1].substring(\n          diffs[pointer][1].length - diffs[pointer - 1][1].length\n        ) === diffs[pointer - 1][1]\n      ) {\n        // Shift the edit over the previous equality.\n        diffs[pointer][1] =\n          diffs[pointer - 1][1] +\n          diffs[pointer][1].substring(\n            0,\n            diffs[pointer][1].length - diffs[pointer - 1][1].length\n          );\n        diffs[pointer + 1][1] = diffs[pointer - 1][1] + diffs[pointer + 1][1];\n        diffs.splice(pointer - 1, 1);\n        changes = true;\n      } else if (\n        diffs[pointer][1].substring(0, diffs[pointer + 1][1].length) ==\n        diffs[pointer + 1][1]\n      ) {\n        // Shift the edit over the next equality.\n        diffs[pointer - 1][1] += diffs[pointer + 1][1];\n        diffs[pointer][1] =\n          diffs[pointer][1].substring(diffs[pointer + 1][1].length) +\n          diffs[pointer + 1][1];\n        diffs.splice(pointer + 1, 1);\n        changes = true;\n      }\n    }\n    pointer++;\n  }\n  // If shifts were made, the diff needs reordering and another shift sweep.\n  if (changes) {\n    diff_cleanupMerge(diffs, fix_unicode);\n  }\n}\n\nfunction is_surrogate_pair_start(charCode) {\n  return charCode >= 0xd800 && charCode <= 0xdbff;\n}\n\nfunction is_surrogate_pair_end(charCode) {\n  return charCode >= 0xdc00 && charCode <= 0xdfff;\n}\n\nfunction starts_with_pair_end(str) {\n  return is_surrogate_pair_end(str.charCodeAt(0));\n}\n\nfunction ends_with_pair_start(str) {\n  return is_surrogate_pair_start(str.charCodeAt(str.length - 1));\n}\n\nfunction remove_empty_tuples(tuples) {\n  var ret = [];\n  for (var i = 0; i < tuples.length; i++) {\n    if (tuples[i][1].length > 0) {\n      ret.push(tuples[i]);\n    }\n  }\n  return ret;\n}\n\nfunction make_edit_splice(before, oldMiddle, newMiddle, after) {\n  if (ends_with_pair_start(before) || starts_with_pair_end(after)) {\n    return null;\n  }\n  return remove_empty_tuples([\n    [DIFF_EQUAL, before],\n    [DIFF_DELETE, oldMiddle],\n    [DIFF_INSERT, newMiddle],\n    [DIFF_EQUAL, after],\n  ]);\n}\n\nfunction find_cursor_edit_diff(oldText, newText, cursor_pos) {\n  // note: this runs after equality check has ruled out exact equality\n  var oldRange =\n    typeof cursor_pos === \"number\"\n      ? { index: cursor_pos, length: 0 }\n      : cursor_pos.oldRange;\n  var newRange = typeof cursor_pos === \"number\" ? null : cursor_pos.newRange;\n  // take into account the old and new selection to generate the best diff\n  // possible for a text edit.  for example, a text change from \"xxx\" to \"xx\"\n  // could be a delete or forwards-delete of any one of the x's, or the\n  // result of selecting two of the x's and typing \"x\".\n  var oldLength = oldText.length;\n  var newLength = newText.length;\n  if (oldRange.length === 0 && (newRange === null || newRange.length === 0)) {\n    // see if we have an insert or delete before or after cursor\n    var oldCursor = oldRange.index;\n    var oldBefore = oldText.slice(0, oldCursor);\n    var oldAfter = oldText.slice(oldCursor);\n    var maybeNewCursor = newRange ? newRange.index : null;\n    editBefore: {\n      // is this an insert or delete right before oldCursor?\n      var newCursor = oldCursor + newLength - oldLength;\n      if (maybeNewCursor !== null && maybeNewCursor !== newCursor) {\n        break editBefore;\n      }\n      if (newCursor < 0 || newCursor > newLength) {\n        break editBefore;\n      }\n      var newBefore = newText.slice(0, newCursor);\n      var newAfter = newText.slice(newCursor);\n      if (newAfter !== oldAfter) {\n        break editBefore;\n      }\n      var prefixLength = Math.min(oldCursor, newCursor);\n      var oldPrefix = oldBefore.slice(0, prefixLength);\n      var newPrefix = newBefore.slice(0, prefixLength);\n      if (oldPrefix !== newPrefix) {\n        break editBefore;\n      }\n      var oldMiddle = oldBefore.slice(prefixLength);\n      var newMiddle = newBefore.slice(prefixLength);\n      return make_edit_splice(oldPrefix, oldMiddle, newMiddle, oldAfter);\n    }\n    editAfter: {\n      // is this an insert or delete right after oldCursor?\n      if (maybeNewCursor !== null && maybeNewCursor !== oldCursor) {\n        break editAfter;\n      }\n      var cursor = oldCursor;\n      var newBefore = newText.slice(0, cursor);\n      var newAfter = newText.slice(cursor);\n      if (newBefore !== oldBefore) {\n        break editAfter;\n      }\n      var suffixLength = Math.min(oldLength - cursor, newLength - cursor);\n      var oldSuffix = oldAfter.slice(oldAfter.length - suffixLength);\n      var newSuffix = newAfter.slice(newAfter.length - suffixLength);\n      if (oldSuffix !== newSuffix) {\n        break editAfter;\n      }\n      var oldMiddle = oldAfter.slice(0, oldAfter.length - suffixLength);\n      var newMiddle = newAfter.slice(0, newAfter.length - suffixLength);\n      return make_edit_splice(oldBefore, oldMiddle, newMiddle, oldSuffix);\n    }\n  }\n  if (oldRange.length > 0 && newRange && newRange.length === 0) {\n    replaceRange: {\n      // see if diff could be a splice of the old selection range\n      var oldPrefix = oldText.slice(0, oldRange.index);\n      var oldSuffix = oldText.slice(oldRange.index + oldRange.length);\n      var prefixLength = oldPrefix.length;\n      var suffixLength = oldSuffix.length;\n      if (newLength < prefixLength + suffixLength) {\n        break replaceRange;\n      }\n      var newPrefix = newText.slice(0, prefixLength);\n      var newSuffix = newText.slice(newLength - suffixLength);\n      if (oldPrefix !== newPrefix || oldSuffix !== newSuffix) {\n        break replaceRange;\n      }\n      var oldMiddle = oldText.slice(prefixLength, oldLength - suffixLength);\n      var newMiddle = newText.slice(prefixLength, newLength - suffixLength);\n      return make_edit_splice(oldPrefix, oldMiddle, newMiddle, oldSuffix);\n    }\n  }\n\n  return null;\n}\n\nfunction diff(text1, text2, cursor_pos, cleanup) {\n  // only pass fix_unicode=true at the top level, not when diff_main is\n  // recursively invoked\n  return diff_main(text1, text2, cursor_pos, cleanup, true);\n}\n\ndiff.INSERT = DIFF_INSERT;\ndiff.DELETE = DIFF_DELETE;\ndiff.EQUAL = DIFF_EQUAL;\n\nmodule.exports = diff;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIA,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,WAAW,GAAG,CAAC;AACnB,IAAIC,UAAU,GAAG,CAAC;;AAElB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAEC,YAAY,EAAE;EAClE;EACA,IAAIJ,KAAK,KAAKC,KAAK,EAAE;IACnB,IAAID,KAAK,EAAE;MACT,OAAO,CAAC,CAACF,UAAU,EAAEE,KAAK,CAAC,CAAC;IAC9B;IACA,OAAO,EAAE;EACX;EAEA,IAAIE,UAAU,IAAI,IAAI,EAAE;IACtB,IAAIG,QAAQ,GAAGC,qBAAqB,CAACN,KAAK,EAAEC,KAAK,EAAEC,UAAU,CAAC;IAC9D,IAAIG,QAAQ,EAAE;MACZ,OAAOA,QAAQ;IACjB;EACF;;EAEA;EACA,IAAIE,YAAY,GAAGC,iBAAiB,CAACR,KAAK,EAAEC,KAAK,CAAC;EAClD,IAAIQ,YAAY,GAAGT,KAAK,CAACU,SAAS,CAAC,CAAC,EAAEH,YAAY,CAAC;EACnDP,KAAK,GAAGA,KAAK,CAACU,SAAS,CAACH,YAAY,CAAC;EACrCN,KAAK,GAAGA,KAAK,CAACS,SAAS,CAACH,YAAY,CAAC;;EAErC;EACAA,YAAY,GAAGI,iBAAiB,CAACX,KAAK,EAAEC,KAAK,CAAC;EAC9C,IAAIW,YAAY,GAAGZ,KAAK,CAACU,SAAS,CAACV,KAAK,CAACa,MAAM,GAAGN,YAAY,CAAC;EAC/DP,KAAK,GAAGA,KAAK,CAACU,SAAS,CAAC,CAAC,EAAEV,KAAK,CAACa,MAAM,GAAGN,YAAY,CAAC;EACvDN,KAAK,GAAGA,KAAK,CAACS,SAAS,CAAC,CAAC,EAAET,KAAK,CAACY,MAAM,GAAGN,YAAY,CAAC;;EAEvD;EACA,IAAIO,KAAK,GAAGC,aAAa,CAACf,KAAK,EAAEC,KAAK,CAAC;;EAEvC;EACA,IAAIQ,YAAY,EAAE;IAChBK,KAAK,CAACE,OAAO,CAAC,CAAClB,UAAU,EAAEW,YAAY,CAAC,CAAC;EAC3C;EACA,IAAIG,YAAY,EAAE;IAChBE,KAAK,CAACG,IAAI,CAAC,CAACnB,UAAU,EAAEc,YAAY,CAAC,CAAC;EACxC;EACAM,iBAAiB,CAACJ,KAAK,EAAEV,YAAY,CAAC;EACtC,IAAID,OAAO,EAAE;IACXgB,oBAAoB,CAACL,KAAK,CAAC;EAC7B;EACA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACf,KAAK,EAAEC,KAAK,EAAE;EACnC,IAAIa,KAAK;EAET,IAAI,CAACd,KAAK,EAAE;IACV;IACA,OAAO,CAAC,CAACH,WAAW,EAAEI,KAAK,CAAC,CAAC;EAC/B;EAEA,IAAI,CAACA,KAAK,EAAE;IACV;IACA,OAAO,CAAC,CAACL,WAAW,EAAEI,KAAK,CAAC,CAAC;EAC/B;EAEA,IAAIoB,QAAQ,GAAGpB,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,GAAGb,KAAK,GAAGC,KAAK;EAC1D,IAAIoB,SAAS,GAAGrB,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,GAAGZ,KAAK,GAAGD,KAAK;EAC3D,IAAIsB,CAAC,GAAGF,QAAQ,CAACG,OAAO,CAACF,SAAS,CAAC;EACnC,IAAIC,CAAC,KAAK,CAAC,CAAC,EAAE;IACZ;IACAR,KAAK,GAAG,CACN,CAACjB,WAAW,EAAEuB,QAAQ,CAACV,SAAS,CAAC,CAAC,EAAEY,CAAC,CAAC,CAAC,EACvC,CAACxB,UAAU,EAAEuB,SAAS,CAAC,EACvB,CAACxB,WAAW,EAAEuB,QAAQ,CAACV,SAAS,CAACY,CAAC,GAAGD,SAAS,CAACR,MAAM,CAAC,CAAC,CACxD;IACD;IACA,IAAIb,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,EAAE;MAC/BC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGlB,WAAW;IACzC;IACA,OAAOkB,KAAK;EACd;EAEA,IAAIO,SAAS,CAACR,MAAM,KAAK,CAAC,EAAE;IAC1B;IACA;IACA,OAAO,CACL,CAACjB,WAAW,EAAEI,KAAK,CAAC,EACpB,CAACH,WAAW,EAAEI,KAAK,CAAC,CACrB;EACH;;EAEA;EACA,IAAIuB,EAAE,GAAGC,eAAe,CAACzB,KAAK,EAAEC,KAAK,CAAC;EACtC,IAAIuB,EAAE,EAAE;IACN;IACA,IAAIE,OAAO,GAAGF,EAAE,CAAC,CAAC,CAAC;IACnB,IAAIG,OAAO,GAAGH,EAAE,CAAC,CAAC,CAAC;IACnB,IAAII,OAAO,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACnB,IAAIK,OAAO,GAAGL,EAAE,CAAC,CAAC,CAAC;IACnB,IAAIM,UAAU,GAAGN,EAAE,CAAC,CAAC,CAAC;IACtB;IACA,IAAIO,OAAO,GAAGhC,SAAS,CAAC2B,OAAO,EAAEE,OAAO,CAAC;IACzC,IAAII,OAAO,GAAGjC,SAAS,CAAC4B,OAAO,EAAEE,OAAO,CAAC;IACzC;IACA,OAAOE,OAAO,CAACE,MAAM,CAAC,CAAC,CAACnC,UAAU,EAAEgC,UAAU,CAAC,CAAC,EAAEE,OAAO,CAAC;EAC5D;EAEA,OAAOE,YAAY,CAAClC,KAAK,EAAEC,KAAK,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiC,YAAYA,CAAClC,KAAK,EAAEC,KAAK,EAAE;EAClC;EACA,IAAIkC,YAAY,GAAGnC,KAAK,CAACa,MAAM;EAC/B,IAAIuB,YAAY,GAAGnC,KAAK,CAACY,MAAM;EAC/B,IAAIwB,KAAK,GAAGC,IAAI,CAACC,IAAI,CAAC,CAACJ,YAAY,GAAGC,YAAY,IAAI,CAAC,CAAC;EACxD,IAAII,QAAQ,GAAGH,KAAK;EACpB,IAAII,QAAQ,GAAG,CAAC,GAAGJ,KAAK;EACxB,IAAIK,EAAE,GAAG,IAAIC,KAAK,CAACF,QAAQ,CAAC;EAC5B,IAAIG,EAAE,GAAG,IAAID,KAAK,CAACF,QAAQ,CAAC;EAC5B;EACA;EACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,QAAQ,EAAEI,CAAC,EAAE,EAAE;IACjCH,EAAE,CAACG,CAAC,CAAC,GAAG,CAAC,CAAC;IACVD,EAAE,CAACC,CAAC,CAAC,GAAG,CAAC,CAAC;EACZ;EACAH,EAAE,CAACF,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EACpBI,EAAE,CAACJ,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EACpB,IAAIM,KAAK,GAAGX,YAAY,GAAGC,YAAY;EACvC;EACA;EACA,IAAIW,KAAK,GAAGD,KAAK,GAAG,CAAC,KAAK,CAAC;EAC3B;EACA;EACA,IAAIE,OAAO,GAAG,CAAC;EACf,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,OAAO,GAAG,CAAC;EACf,IAAIC,KAAK,GAAG,CAAC;EACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,KAAK,EAAEe,CAAC,EAAE,EAAE;IAC9B;IACA,KAAK,IAAIC,EAAE,GAAG,CAACD,CAAC,GAAGJ,OAAO,EAAEK,EAAE,IAAID,CAAC,GAAGH,KAAK,EAAEI,EAAE,IAAI,CAAC,EAAE;MACpD,IAAIC,SAAS,GAAGd,QAAQ,GAAGa,EAAE;MAC7B,IAAIE,EAAE;MACN,IAAIF,EAAE,KAAK,CAACD,CAAC,IAAKC,EAAE,KAAKD,CAAC,IAAIV,EAAE,CAACY,SAAS,GAAG,CAAC,CAAC,GAAGZ,EAAE,CAACY,SAAS,GAAG,CAAC,CAAE,EAAE;QACpEC,EAAE,GAAGb,EAAE,CAACY,SAAS,GAAG,CAAC,CAAC;MACxB,CAAC,MAAM;QACLC,EAAE,GAAGb,EAAE,CAACY,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;MAC5B;MACA,IAAIE,EAAE,GAAGD,EAAE,GAAGF,EAAE;MAChB,OACEE,EAAE,GAAGpB,YAAY,IACjBqB,EAAE,GAAGpB,YAAY,IACjBpC,KAAK,CAACyD,MAAM,CAACF,EAAE,CAAC,KAAKtD,KAAK,CAACwD,MAAM,CAACD,EAAE,CAAC,EACrC;QACAD,EAAE,EAAE;QACJC,EAAE,EAAE;MACN;MACAd,EAAE,CAACY,SAAS,CAAC,GAAGC,EAAE;MAClB,IAAIA,EAAE,GAAGpB,YAAY,EAAE;QACrB;QACAc,KAAK,IAAI,CAAC;MACZ,CAAC,MAAM,IAAIO,EAAE,GAAGpB,YAAY,EAAE;QAC5B;QACAY,OAAO,IAAI,CAAC;MACd,CAAC,MAAM,IAAID,KAAK,EAAE;QAChB,IAAIW,SAAS,GAAGlB,QAAQ,GAAGM,KAAK,GAAGO,EAAE;QACrC,IAAIK,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAGjB,QAAQ,IAAIG,EAAE,CAACc,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;UAClE;UACA,IAAIC,EAAE,GAAGxB,YAAY,GAAGS,EAAE,CAACc,SAAS,CAAC;UACrC,IAAIH,EAAE,IAAII,EAAE,EAAE;YACZ;YACA,OAAOC,iBAAiB,CAAC5D,KAAK,EAAEC,KAAK,EAAEsD,EAAE,EAAEC,EAAE,CAAC;UAChD;QACF;MACF;IACF;;IAEA;IACA,KAAK,IAAIK,EAAE,GAAG,CAACT,CAAC,GAAGF,OAAO,EAAEW,EAAE,IAAIT,CAAC,GAAGD,KAAK,EAAEU,EAAE,IAAI,CAAC,EAAE;MACpD,IAAIH,SAAS,GAAGlB,QAAQ,GAAGqB,EAAE;MAC7B,IAAIF,EAAE;MACN,IAAIE,EAAE,KAAK,CAACT,CAAC,IAAKS,EAAE,KAAKT,CAAC,IAAIR,EAAE,CAACc,SAAS,GAAG,CAAC,CAAC,GAAGd,EAAE,CAACc,SAAS,GAAG,CAAC,CAAE,EAAE;QACpEC,EAAE,GAAGf,EAAE,CAACc,SAAS,GAAG,CAAC,CAAC;MACxB,CAAC,MAAM;QACLC,EAAE,GAAGf,EAAE,CAACc,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;MAC5B;MACA,IAAII,EAAE,GAAGH,EAAE,GAAGE,EAAE;MAChB,OACEF,EAAE,GAAGxB,YAAY,IACjB2B,EAAE,GAAG1B,YAAY,IACjBpC,KAAK,CAACyD,MAAM,CAACtB,YAAY,GAAGwB,EAAE,GAAG,CAAC,CAAC,KACjC1D,KAAK,CAACwD,MAAM,CAACrB,YAAY,GAAG0B,EAAE,GAAG,CAAC,CAAC,EACrC;QACAH,EAAE,EAAE;QACJG,EAAE,EAAE;MACN;MACAlB,EAAE,CAACc,SAAS,CAAC,GAAGC,EAAE;MAClB,IAAIA,EAAE,GAAGxB,YAAY,EAAE;QACrB;QACAgB,KAAK,IAAI,CAAC;MACZ,CAAC,MAAM,IAAIW,EAAE,GAAG1B,YAAY,EAAE;QAC5B;QACAc,OAAO,IAAI,CAAC;MACd,CAAC,MAAM,IAAI,CAACH,KAAK,EAAE;QACjB,IAAIO,SAAS,GAAGd,QAAQ,GAAGM,KAAK,GAAGe,EAAE;QACrC,IAAIP,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAGb,QAAQ,IAAIC,EAAE,CAACY,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;UAClE,IAAIC,EAAE,GAAGb,EAAE,CAACY,SAAS,CAAC;UACtB,IAAIE,EAAE,GAAGhB,QAAQ,GAAGe,EAAE,GAAGD,SAAS;UAClC;UACAK,EAAE,GAAGxB,YAAY,GAAGwB,EAAE;UACtB,IAAIJ,EAAE,IAAII,EAAE,EAAE;YACZ;YACA,OAAOC,iBAAiB,CAAC5D,KAAK,EAAEC,KAAK,EAAEsD,EAAE,EAAEC,EAAE,CAAC;UAChD;QACF;MACF;IACF;EACF;EACA;EACA;EACA,OAAO,CACL,CAAC5D,WAAW,EAAEI,KAAK,CAAC,EACpB,CAACH,WAAW,EAAEI,KAAK,CAAC,CACrB;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2D,iBAAiBA,CAAC5D,KAAK,EAAEC,KAAK,EAAE4C,CAAC,EAAEkB,CAAC,EAAE;EAC7C,IAAIC,MAAM,GAAGhE,KAAK,CAACU,SAAS,CAAC,CAAC,EAAEmC,CAAC,CAAC;EAClC,IAAIoB,MAAM,GAAGhE,KAAK,CAACS,SAAS,CAAC,CAAC,EAAEqD,CAAC,CAAC;EAClC,IAAIG,MAAM,GAAGlE,KAAK,CAACU,SAAS,CAACmC,CAAC,CAAC;EAC/B,IAAIsB,MAAM,GAAGlE,KAAK,CAACS,SAAS,CAACqD,CAAC,CAAC;;EAE/B;EACA,IAAIjD,KAAK,GAAGf,SAAS,CAACiE,MAAM,EAAEC,MAAM,CAAC;EACrC,IAAIG,MAAM,GAAGrE,SAAS,CAACmE,MAAM,EAAEC,MAAM,CAAC;EAEtC,OAAOrD,KAAK,CAACmB,MAAM,CAACmC,MAAM,CAAC;AAC7B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS5D,iBAAiBA,CAACR,KAAK,EAAEC,KAAK,EAAE;EACvC;EACA,IAAI,CAACD,KAAK,IAAI,CAACC,KAAK,IAAID,KAAK,CAACyD,MAAM,CAAC,CAAC,CAAC,KAAKxD,KAAK,CAACwD,MAAM,CAAC,CAAC,CAAC,EAAE;IAC3D,OAAO,CAAC;EACV;EACA;EACA;EACA,IAAIY,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAGhC,IAAI,CAACiC,GAAG,CAACvE,KAAK,CAACa,MAAM,EAAEZ,KAAK,CAACY,MAAM,CAAC;EACrD,IAAI2D,UAAU,GAAGF,UAAU;EAC3B,IAAIG,YAAY,GAAG,CAAC;EACpB,OAAOJ,UAAU,GAAGG,UAAU,EAAE;IAC9B,IACExE,KAAK,CAACU,SAAS,CAAC+D,YAAY,EAAED,UAAU,CAAC,IACzCvE,KAAK,CAACS,SAAS,CAAC+D,YAAY,EAAED,UAAU,CAAC,EACzC;MACAH,UAAU,GAAGG,UAAU;MACvBC,YAAY,GAAGJ,UAAU;IAC3B,CAAC,MAAM;MACLC,UAAU,GAAGE,UAAU;IACzB;IACAA,UAAU,GAAGlC,IAAI,CAACoC,KAAK,CAAC,CAACJ,UAAU,GAAGD,UAAU,IAAI,CAAC,GAAGA,UAAU,CAAC;EACrE;EAEA,IAAIM,uBAAuB,CAAC3E,KAAK,CAAC4E,UAAU,CAACJ,UAAU,GAAG,CAAC,CAAC,CAAC,EAAE;IAC7DA,UAAU,EAAE;EACd;EAEA,OAAOA,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,mBAAmBA,CAAC7E,KAAK,EAAEC,KAAK,EAAE;EACzC;EACA,IAAIkC,YAAY,GAAGnC,KAAK,CAACa,MAAM;EAC/B,IAAIuB,YAAY,GAAGnC,KAAK,CAACY,MAAM;EAC/B;EACA,IAAIsB,YAAY,IAAI,CAAC,IAAIC,YAAY,IAAI,CAAC,EAAE;IAC1C,OAAO,CAAC;EACV;EACA;EACA,IAAID,YAAY,GAAGC,YAAY,EAAE;IAC/BpC,KAAK,GAAGA,KAAK,CAACU,SAAS,CAACyB,YAAY,GAAGC,YAAY,CAAC;EACtD,CAAC,MAAM,IAAID,YAAY,GAAGC,YAAY,EAAE;IACtCnC,KAAK,GAAGA,KAAK,CAACS,SAAS,CAAC,CAAC,EAAEyB,YAAY,CAAC;EAC1C;EACA,IAAI2C,WAAW,GAAGxC,IAAI,CAACiC,GAAG,CAACpC,YAAY,EAAEC,YAAY,CAAC;EACtD;EACA,IAAIpC,KAAK,IAAIC,KAAK,EAAE;IAClB,OAAO6E,WAAW;EACpB;;EAEA;EACA;EACA;EACA,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIlE,MAAM,GAAG,CAAC;EACd,OAAO,IAAI,EAAE;IACX,IAAImE,OAAO,GAAGhF,KAAK,CAACU,SAAS,CAACoE,WAAW,GAAGjE,MAAM,CAAC;IACnD,IAAIoE,KAAK,GAAGhF,KAAK,CAACsB,OAAO,CAACyD,OAAO,CAAC;IAClC,IAAIC,KAAK,IAAI,CAAC,CAAC,EAAE;MACf,OAAOF,IAAI;IACb;IACAlE,MAAM,IAAIoE,KAAK;IACf,IACEA,KAAK,IAAI,CAAC,IACVjF,KAAK,CAACU,SAAS,CAACoE,WAAW,GAAGjE,MAAM,CAAC,IAAIZ,KAAK,CAACS,SAAS,CAAC,CAAC,EAAEG,MAAM,CAAC,EACnE;MACAkE,IAAI,GAAGlE,MAAM;MACbA,MAAM,EAAE;IACV;EACF;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASF,iBAAiBA,CAACX,KAAK,EAAEC,KAAK,EAAE;EACvC;EACA,IAAI,CAACD,KAAK,IAAI,CAACC,KAAK,IAAID,KAAK,CAACkF,KAAK,CAAC,CAAC,CAAC,CAAC,KAAKjF,KAAK,CAACiF,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;IAC3D,OAAO,CAAC;EACV;EACA;EACA;EACA,IAAIb,UAAU,GAAG,CAAC;EAClB,IAAIC,UAAU,GAAGhC,IAAI,CAACiC,GAAG,CAACvE,KAAK,CAACa,MAAM,EAAEZ,KAAK,CAACY,MAAM,CAAC;EACrD,IAAI2D,UAAU,GAAGF,UAAU;EAC3B,IAAIa,UAAU,GAAG,CAAC;EAClB,OAAOd,UAAU,GAAGG,UAAU,EAAE;IAC9B,IACExE,KAAK,CAACU,SAAS,CAACV,KAAK,CAACa,MAAM,GAAG2D,UAAU,EAAExE,KAAK,CAACa,MAAM,GAAGsE,UAAU,CAAC,IACrElF,KAAK,CAACS,SAAS,CAACT,KAAK,CAACY,MAAM,GAAG2D,UAAU,EAAEvE,KAAK,CAACY,MAAM,GAAGsE,UAAU,CAAC,EACrE;MACAd,UAAU,GAAGG,UAAU;MACvBW,UAAU,GAAGd,UAAU;IACzB,CAAC,MAAM;MACLC,UAAU,GAAGE,UAAU;IACzB;IACAA,UAAU,GAAGlC,IAAI,CAACoC,KAAK,CAAC,CAACJ,UAAU,GAAGD,UAAU,IAAI,CAAC,GAAGA,UAAU,CAAC;EACrE;EAEA,IAAIe,qBAAqB,CAACpF,KAAK,CAAC4E,UAAU,CAAC5E,KAAK,CAACa,MAAM,GAAG2D,UAAU,CAAC,CAAC,EAAE;IACtEA,UAAU,EAAE;EACd;EAEA,OAAOA,UAAU;AACnB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS/C,eAAeA,CAACzB,KAAK,EAAEC,KAAK,EAAE;EACrC,IAAImB,QAAQ,GAAGpB,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,GAAGb,KAAK,GAAGC,KAAK;EAC1D,IAAIoB,SAAS,GAAGrB,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,GAAGZ,KAAK,GAAGD,KAAK;EAC3D,IAAIoB,QAAQ,CAACP,MAAM,GAAG,CAAC,IAAIQ,SAAS,CAACR,MAAM,GAAG,CAAC,GAAGO,QAAQ,CAACP,MAAM,EAAE;IACjE,OAAO,IAAI,CAAC,CAAC;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwE,gBAAgBA,CAACjE,QAAQ,EAAEC,SAAS,EAAEC,CAAC,EAAE;IAChD;IACA,IAAIgE,IAAI,GAAGlE,QAAQ,CAACV,SAAS,CAACY,CAAC,EAAEA,CAAC,GAAGgB,IAAI,CAACoC,KAAK,CAACtD,QAAQ,CAACP,MAAM,GAAG,CAAC,CAAC,CAAC;IACrE,IAAI0E,CAAC,GAAG,CAAC,CAAC;IACV,IAAIC,WAAW,GAAG,EAAE;IACpB,IAAIC,eAAe,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB;IACxE,OAAO,CAACL,CAAC,GAAGlE,SAAS,CAACE,OAAO,CAAC+D,IAAI,EAAEC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE;MAClD,IAAIM,YAAY,GAAGrF,iBAAiB,CAClCY,QAAQ,CAACV,SAAS,CAACY,CAAC,CAAC,EACrBD,SAAS,CAACX,SAAS,CAAC6E,CAAC,CACvB,CAAC;MACD,IAAIO,YAAY,GAAGnF,iBAAiB,CAClCS,QAAQ,CAACV,SAAS,CAAC,CAAC,EAAEY,CAAC,CAAC,EACxBD,SAAS,CAACX,SAAS,CAAC,CAAC,EAAE6E,CAAC,CAC1B,CAAC;MACD,IAAIC,WAAW,CAAC3E,MAAM,GAAGiF,YAAY,GAAGD,YAAY,EAAE;QACpDL,WAAW,GACTnE,SAAS,CAACX,SAAS,CAAC6E,CAAC,GAAGO,YAAY,EAAEP,CAAC,CAAC,GACxClE,SAAS,CAACX,SAAS,CAAC6E,CAAC,EAAEA,CAAC,GAAGM,YAAY,CAAC;QAC1CJ,eAAe,GAAGrE,QAAQ,CAACV,SAAS,CAAC,CAAC,EAAEY,CAAC,GAAGwE,YAAY,CAAC;QACzDJ,eAAe,GAAGtE,QAAQ,CAACV,SAAS,CAACY,CAAC,GAAGuE,YAAY,CAAC;QACtDF,gBAAgB,GAAGtE,SAAS,CAACX,SAAS,CAAC,CAAC,EAAE6E,CAAC,GAAGO,YAAY,CAAC;QAC3DF,gBAAgB,GAAGvE,SAAS,CAACX,SAAS,CAAC6E,CAAC,GAAGM,YAAY,CAAC;MAC1D;IACF;IACA,IAAIL,WAAW,CAAC3E,MAAM,GAAG,CAAC,IAAIO,QAAQ,CAACP,MAAM,EAAE;MAC7C,OAAO,CACL4E,eAAe,EACfC,eAAe,EACfC,gBAAgB,EAChBC,gBAAgB,EAChBJ,WAAW,CACZ;IACH,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;;EAEA;EACA,IAAIO,GAAG,GAAGV,gBAAgB,CACxBjE,QAAQ,EACRC,SAAS,EACTiB,IAAI,CAACC,IAAI,CAACnB,QAAQ,CAACP,MAAM,GAAG,CAAC,CAC/B,CAAC;EACD;EACA,IAAImF,GAAG,GAAGX,gBAAgB,CACxBjE,QAAQ,EACRC,SAAS,EACTiB,IAAI,CAACC,IAAI,CAACnB,QAAQ,CAACP,MAAM,GAAG,CAAC,CAC/B,CAAC;EACD,IAAIW,EAAE;EACN,IAAI,CAACuE,GAAG,IAAI,CAACC,GAAG,EAAE;IAChB,OAAO,IAAI;EACb,CAAC,MAAM,IAAI,CAACA,GAAG,EAAE;IACfxE,EAAE,GAAGuE,GAAG;EACV,CAAC,MAAM,IAAI,CAACA,GAAG,EAAE;IACfvE,EAAE,GAAGwE,GAAG;EACV,CAAC,MAAM;IACL;IACAxE,EAAE,GAAGuE,GAAG,CAAC,CAAC,CAAC,CAAClF,MAAM,GAAGmF,GAAG,CAAC,CAAC,CAAC,CAACnF,MAAM,GAAGkF,GAAG,GAAGC,GAAG;EAChD;;EAEA;EACA,IAAItE,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO;EACtC,IAAI7B,KAAK,CAACa,MAAM,GAAGZ,KAAK,CAACY,MAAM,EAAE;IAC/Ba,OAAO,GAAGF,EAAE,CAAC,CAAC,CAAC;IACfG,OAAO,GAAGH,EAAE,CAAC,CAAC,CAAC;IACfI,OAAO,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACfK,OAAO,GAAGL,EAAE,CAAC,CAAC,CAAC;EACjB,CAAC,MAAM;IACLI,OAAO,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACfK,OAAO,GAAGL,EAAE,CAAC,CAAC,CAAC;IACfE,OAAO,GAAGF,EAAE,CAAC,CAAC,CAAC;IACfG,OAAO,GAAGH,EAAE,CAAC,CAAC,CAAC;EACjB;EACA,IAAIM,UAAU,GAAGN,EAAE,CAAC,CAAC,CAAC;EACtB,OAAO,CAACE,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,OAAO,EAAEC,UAAU,CAAC;AACzD;;AAEA;AACA;AACA;AACA;AACA,SAASX,oBAAoBA,CAACL,KAAK,EAAE;EACnC,IAAImF,OAAO,GAAG,KAAK;EACnB,IAAIC,UAAU,GAAG,EAAE,CAAC,CAAC;EACrB,IAAIC,gBAAgB,GAAG,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIC,YAAY,GAAG,IAAI;EACvB;EACA,IAAIC,OAAO,GAAG,CAAC,CAAC,CAAC;EACjB;EACA,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,iBAAiB,GAAG,CAAC;EACzB;EACA,IAAIC,kBAAkB,GAAG,CAAC;EAC1B,IAAIC,iBAAiB,GAAG,CAAC;EACzB,OAAOJ,OAAO,GAAGvF,KAAK,CAACD,MAAM,EAAE;IAC7B,IAAIC,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIvG,UAAU,EAAE;MACnC;MACAoG,UAAU,CAACC,gBAAgB,EAAE,CAAC,GAAGE,OAAO;MACxCC,kBAAkB,GAAGE,kBAAkB;MACvCD,iBAAiB,GAAGE,iBAAiB;MACrCD,kBAAkB,GAAG,CAAC;MACtBC,iBAAiB,GAAG,CAAC;MACrBL,YAAY,GAAGtF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;IAClC,CAAC,MAAM;MACL;MACA,IAAIvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIxG,WAAW,EAAE;QACpC2G,kBAAkB,IAAI1F,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM;MAChD,CAAC,MAAM;QACL4F,iBAAiB,IAAI3F,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM;MAC/C;MACA;MACA;MACA,IACEuF,YAAY,IACZA,YAAY,CAACvF,MAAM,IACjByB,IAAI,CAACoE,GAAG,CAACJ,kBAAkB,EAAEC,iBAAiB,CAAC,IACjDH,YAAY,CAACvF,MAAM,IAAIyB,IAAI,CAACoE,GAAG,CAACF,kBAAkB,EAAEC,iBAAiB,CAAC,EACtE;QACA;QACA3F,KAAK,CAAC6F,MAAM,CAACT,UAAU,CAACC,gBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAChDvG,WAAW,EACXwG,YAAY,CACb,CAAC;QACF;QACAtF,KAAK,CAACoF,UAAU,CAACC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGtG,WAAW;QAC5D;QACAsG,gBAAgB,EAAE;QAClB;QACAA,gBAAgB,EAAE;QAClBE,OAAO,GAAGF,gBAAgB,GAAG,CAAC,GAAGD,UAAU,CAACC,gBAAgB,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACtEG,kBAAkB,GAAG,CAAC,CAAC,CAAC;QACxBC,iBAAiB,GAAG,CAAC;QACrBC,kBAAkB,GAAG,CAAC;QACtBC,iBAAiB,GAAG,CAAC;QACrBL,YAAY,GAAG,IAAI;QACnBH,OAAO,GAAG,IAAI;MAChB;IACF;IACAI,OAAO,EAAE;EACX;;EAEA;EACA,IAAIJ,OAAO,EAAE;IACX/E,iBAAiB,CAACJ,KAAK,CAAC;EAC1B;EACA8F,4BAA4B,CAAC9F,KAAK,CAAC;;EAEnC;EACA;EACA;EACA;EACA;EACA;EACAuF,OAAO,GAAG,CAAC;EACX,OAAOA,OAAO,GAAGvF,KAAK,CAACD,MAAM,EAAE;IAC7B,IACEC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIzG,WAAW,IACpCkB,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,IAAIxG,WAAW,EAChC;MACA,IAAIgH,QAAQ,GAAG/F,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,IAAIS,SAAS,GAAGhG,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;MACjC,IAAIU,eAAe,GAAGlC,mBAAmB,CAACgC,QAAQ,EAAEC,SAAS,CAAC;MAC9D,IAAIE,eAAe,GAAGnC,mBAAmB,CAACiC,SAAS,EAAED,QAAQ,CAAC;MAC9D,IAAIE,eAAe,IAAIC,eAAe,EAAE;QACtC,IACED,eAAe,IAAIF,QAAQ,CAAChG,MAAM,GAAG,CAAC,IACtCkG,eAAe,IAAID,SAAS,CAACjG,MAAM,GAAG,CAAC,EACvC;UACA;UACAC,KAAK,CAAC6F,MAAM,CAACN,OAAO,EAAE,CAAC,EAAE,CACvBvG,UAAU,EACVgH,SAAS,CAACpG,SAAS,CAAC,CAAC,EAAEqG,eAAe,CAAC,CACxC,CAAC;UACFjG,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACnG,SAAS,CACxC,CAAC,EACDmG,QAAQ,CAAChG,MAAM,GAAGkG,eACpB,CAAC;UACDjG,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGS,SAAS,CAACpG,SAAS,CAACqG,eAAe,CAAC;UAC5DV,OAAO,EAAE;QACX;MACF,CAAC,MAAM;QACL,IACEW,eAAe,IAAIH,QAAQ,CAAChG,MAAM,GAAG,CAAC,IACtCmG,eAAe,IAAIF,SAAS,CAACjG,MAAM,GAAG,CAAC,EACvC;UACA;UACA;UACAC,KAAK,CAAC6F,MAAM,CAACN,OAAO,EAAE,CAAC,EAAE,CACvBvG,UAAU,EACV+G,QAAQ,CAACnG,SAAS,CAAC,CAAC,EAAEsG,eAAe,CAAC,CACvC,CAAC;UACFlG,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGxG,WAAW;UACnCiB,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGS,SAAS,CAACpG,SAAS,CACzC,CAAC,EACDoG,SAAS,CAACjG,MAAM,GAAGmG,eACrB,CAAC;UACDlG,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGzG,WAAW;UACnCkB,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGQ,QAAQ,CAACnG,SAAS,CAACsG,eAAe,CAAC;UAC3DX,OAAO,EAAE;QACX;MACF;MACAA,OAAO,EAAE;IACX;IACAA,OAAO,EAAE;EACX;AACF;AAEA,IAAIY,qBAAqB,GAAG,cAAc;AAC1C,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,eAAe,GAAG,QAAQ;AAC9B,IAAIC,kBAAkB,GAAG,UAAU;AACnC,IAAIC,oBAAoB,GAAG,aAAa;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA,SAAST,4BAA4BA,CAAC9F,KAAK,EAAE;EAC3C;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASwG,0BAA0BA,CAACC,GAAG,EAAEC,GAAG,EAAE;IAC5C,IAAI,CAACD,GAAG,IAAI,CAACC,GAAG,EAAE;MAChB;MACA,OAAO,CAAC;IACV;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAIC,KAAK,GAAGF,GAAG,CAAC9D,MAAM,CAAC8D,GAAG,CAAC1G,MAAM,GAAG,CAAC,CAAC;IACtC,IAAI6G,KAAK,GAAGF,GAAG,CAAC/D,MAAM,CAAC,CAAC,CAAC;IACzB,IAAIkE,gBAAgB,GAAGF,KAAK,CAACG,KAAK,CAACX,qBAAqB,CAAC;IACzD,IAAIY,gBAAgB,GAAGH,KAAK,CAACE,KAAK,CAACX,qBAAqB,CAAC;IACzD,IAAIa,WAAW,GAAGH,gBAAgB,IAAIF,KAAK,CAACG,KAAK,CAACV,gBAAgB,CAAC;IACnE,IAAIa,WAAW,GAAGF,gBAAgB,IAAIH,KAAK,CAACE,KAAK,CAACV,gBAAgB,CAAC;IACnE,IAAIc,UAAU,GAAGF,WAAW,IAAIL,KAAK,CAACG,KAAK,CAACT,eAAe,CAAC;IAC5D,IAAIc,UAAU,GAAGF,WAAW,IAAIL,KAAK,CAACE,KAAK,CAACT,eAAe,CAAC;IAC5D,IAAIe,UAAU,GAAGF,UAAU,IAAIT,GAAG,CAACK,KAAK,CAACR,kBAAkB,CAAC;IAC5D,IAAIe,UAAU,GAAGF,UAAU,IAAIT,GAAG,CAACI,KAAK,CAACP,oBAAoB,CAAC;IAE9D,IAAIa,UAAU,IAAIC,UAAU,EAAE;MAC5B;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAIH,UAAU,IAAIC,UAAU,EAAE;MACnC;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAIN,gBAAgB,IAAI,CAACG,WAAW,IAAIC,WAAW,EAAE;MAC1D;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAID,WAAW,IAAIC,WAAW,EAAE;MACrC;MACA,OAAO,CAAC;IACV,CAAC,MAAM,IAAIJ,gBAAgB,IAAIE,gBAAgB,EAAE;MAC/C;MACA,OAAO,CAAC;IACV;IACA,OAAO,CAAC;EACV;EAEA,IAAIxB,OAAO,GAAG,CAAC;EACf;EACA,OAAOA,OAAO,GAAGvF,KAAK,CAACD,MAAM,GAAG,CAAC,EAAE;IACjC,IACEC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIvG,UAAU,IACnCgB,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIvG,UAAU,EACnC;MACA;MACA,IAAIsI,SAAS,GAAGtH,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,IAAIgC,IAAI,GAAGvH,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5B,IAAIiC,SAAS,GAAGxH,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;MAErC;MACA,IAAIkC,YAAY,GAAG5H,iBAAiB,CAACyH,SAAS,EAAEC,IAAI,CAAC;MACrD,IAAIE,YAAY,EAAE;QAChB,IAAIC,YAAY,GAAGH,IAAI,CAAC3H,SAAS,CAAC2H,IAAI,CAACxH,MAAM,GAAG0H,YAAY,CAAC;QAC7DH,SAAS,GAAGA,SAAS,CAAC1H,SAAS,CAAC,CAAC,EAAE0H,SAAS,CAACvH,MAAM,GAAG0H,YAAY,CAAC;QACnEF,IAAI,GAAGG,YAAY,GAAGH,IAAI,CAAC3H,SAAS,CAAC,CAAC,EAAE2H,IAAI,CAACxH,MAAM,GAAG0H,YAAY,CAAC;QACnED,SAAS,GAAGE,YAAY,GAAGF,SAAS;MACtC;;MAEA;MACA,IAAIG,aAAa,GAAGL,SAAS;MAC7B,IAAIM,QAAQ,GAAGL,IAAI;MACnB,IAAIM,aAAa,GAAGL,SAAS;MAC7B,IAAIM,SAAS,GACXtB,0BAA0B,CAACc,SAAS,EAAEC,IAAI,CAAC,GAC3Cf,0BAA0B,CAACe,IAAI,EAAEC,SAAS,CAAC;MAC7C,OAAOD,IAAI,CAAC5E,MAAM,CAAC,CAAC,CAAC,KAAK6E,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC,EAAE;QAC7C2E,SAAS,IAAIC,IAAI,CAAC5E,MAAM,CAAC,CAAC,CAAC;QAC3B4E,IAAI,GAAGA,IAAI,CAAC3H,SAAS,CAAC,CAAC,CAAC,GAAG4H,SAAS,CAAC7E,MAAM,CAAC,CAAC,CAAC;QAC9C6E,SAAS,GAAGA,SAAS,CAAC5H,SAAS,CAAC,CAAC,CAAC;QAClC,IAAImI,KAAK,GACPvB,0BAA0B,CAACc,SAAS,EAAEC,IAAI,CAAC,GAC3Cf,0BAA0B,CAACe,IAAI,EAAEC,SAAS,CAAC;QAC7C;QACA,IAAIO,KAAK,IAAID,SAAS,EAAE;UACtBA,SAAS,GAAGC,KAAK;UACjBJ,aAAa,GAAGL,SAAS;UACzBM,QAAQ,GAAGL,IAAI;UACfM,aAAa,GAAGL,SAAS;QAC3B;MACF;MAEA,IAAIxH,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIoC,aAAa,EAAE;QAC1C;QACA,IAAIA,aAAa,EAAE;UACjB3H,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGoC,aAAa;QACvC,CAAC,MAAM;UACL3H,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;UAC5BA,OAAO,EAAE;QACX;QACAvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGqC,QAAQ;QAC5B,IAAIC,aAAa,EAAE;UACjB7H,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGsC,aAAa;QACvC,CAAC,MAAM;UACL7H,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;UAC5BA,OAAO,EAAE;QACX;MACF;IACF;IACAA,OAAO,EAAE;EACX;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASnF,iBAAiBA,CAACJ,KAAK,EAAEgI,WAAW,EAAE;EAC7ChI,KAAK,CAACG,IAAI,CAAC,CAACnB,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAIuG,OAAO,GAAG,CAAC;EACf,IAAI0C,YAAY,GAAG,CAAC;EACpB,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAIC,WAAW,GAAG,EAAE;EACpB,IAAI3I,YAAY;EAChB,OAAO8F,OAAO,GAAGvF,KAAK,CAACD,MAAM,EAAE;IAC7B,IAAIwF,OAAO,GAAGvF,KAAK,CAACD,MAAM,GAAG,CAAC,IAAI,CAACC,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;MACpDvF,KAAK,CAAC6F,MAAM,CAACN,OAAO,EAAE,CAAC,CAAC;MACxB;IACF;IACA,QAAQvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;MACvB,KAAKxG,WAAW;QACdmJ,YAAY,EAAE;QACdE,WAAW,IAAIpI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;QAChCA,OAAO,EAAE;QACT;MACF,KAAKzG,WAAW;QACdmJ,YAAY,EAAE;QACdE,WAAW,IAAInI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;QAChCA,OAAO,EAAE;QACT;MACF,KAAKvG,UAAU;QACb,IAAIqJ,iBAAiB,GAAG9C,OAAO,GAAG2C,YAAY,GAAGD,YAAY,GAAG,CAAC;QACjE,IAAID,WAAW,EAAE;UACf;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA,IACEK,iBAAiB,IAAI,CAAC,IACtBC,oBAAoB,CAACtI,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAAC,EACjD;YACA,IAAIE,KAAK,GAAGvI,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAACjE,KAAK,CAAC,CAAC,CAAC,CAAC;YACjDpE,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,GAAGrI,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,CAACjE,KAAK,CAC7D,CAAC,EACD,CAAC,CACH,CAAC;YACD+D,WAAW,GAAGI,KAAK,GAAGJ,WAAW;YACjCC,WAAW,GAAGG,KAAK,GAAGH,WAAW;YACjC,IAAI,CAACpI,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,EAAE;cAChC;cACArI,KAAK,CAAC6F,MAAM,CAACwC,iBAAiB,EAAE,CAAC,CAAC;cAClC9C,OAAO,EAAE;cACT,IAAIiD,CAAC,GAAGH,iBAAiB,GAAG,CAAC;cAC7B,IAAIrI,KAAK,CAACwI,CAAC,CAAC,IAAIxI,KAAK,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKzJ,WAAW,EAAE;gBAC3CmJ,YAAY,EAAE;gBACdE,WAAW,GAAGpI,KAAK,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGJ,WAAW;gBACvCI,CAAC,EAAE;cACL;cACA,IAAIxI,KAAK,CAACwI,CAAC,CAAC,IAAIxI,KAAK,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK1J,WAAW,EAAE;gBAC3CmJ,YAAY,EAAE;gBACdE,WAAW,GAAGnI,KAAK,CAACwI,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGL,WAAW;gBACvCK,CAAC,EAAE;cACL;cACAH,iBAAiB,GAAGG,CAAC;YACvB;UACF;UACA,IAAIC,oBAAoB,CAACzI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;YAC3C,IAAIgD,KAAK,GAAGvI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC5C,MAAM,CAAC,CAAC,CAAC;YACvC3C,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,GAAGvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAACnB,KAAK,CAAC,CAAC,CAAC;YAC9C+D,WAAW,IAAII,KAAK;YACpBH,WAAW,IAAIG,KAAK;UACtB;QACF;QACA,IAAIhD,OAAO,GAAGvF,KAAK,CAACD,MAAM,GAAG,CAAC,IAAI,CAACC,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;UACpD;UACAvF,KAAK,CAAC6F,MAAM,CAACN,OAAO,EAAE,CAAC,CAAC;UACxB;QACF;QACA,IAAI4C,WAAW,CAACpI,MAAM,GAAG,CAAC,IAAIqI,WAAW,CAACrI,MAAM,GAAG,CAAC,EAAE;UACpD;UACA,IAAIoI,WAAW,CAACpI,MAAM,GAAG,CAAC,IAAIqI,WAAW,CAACrI,MAAM,GAAG,CAAC,EAAE;YACpD;YACAN,YAAY,GAAGC,iBAAiB,CAAC0I,WAAW,EAAED,WAAW,CAAC;YAC1D,IAAI1I,YAAY,KAAK,CAAC,EAAE;cACtB,IAAI4I,iBAAiB,IAAI,CAAC,EAAE;gBAC1BrI,KAAK,CAACqI,iBAAiB,CAAC,CAAC,CAAC,CAAC,IAAID,WAAW,CAACxI,SAAS,CAClD,CAAC,EACDH,YACF,CAAC;cACH,CAAC,MAAM;gBACLO,KAAK,CAAC6F,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CACjB7G,UAAU,EACVoJ,WAAW,CAACxI,SAAS,CAAC,CAAC,EAAEH,YAAY,CAAC,CACvC,CAAC;gBACF8F,OAAO,EAAE;cACX;cACA6C,WAAW,GAAGA,WAAW,CAACxI,SAAS,CAACH,YAAY,CAAC;cACjD0I,WAAW,GAAGA,WAAW,CAACvI,SAAS,CAACH,YAAY,CAAC;YACnD;YACA;YACAA,YAAY,GAAGI,iBAAiB,CAACuI,WAAW,EAAED,WAAW,CAAC;YAC1D,IAAI1I,YAAY,KAAK,CAAC,EAAE;cACtBO,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,GACf6C,WAAW,CAACxI,SAAS,CAACwI,WAAW,CAACrI,MAAM,GAAGN,YAAY,CAAC,GACxDO,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;cACnB6C,WAAW,GAAGA,WAAW,CAACxI,SAAS,CACjC,CAAC,EACDwI,WAAW,CAACrI,MAAM,GAAGN,YACvB,CAAC;cACD0I,WAAW,GAAGA,WAAW,CAACvI,SAAS,CACjC,CAAC,EACDuI,WAAW,CAACpI,MAAM,GAAGN,YACvB,CAAC;YACH;UACF;UACA;UACA,IAAIiJ,CAAC,GAAGR,YAAY,GAAGD,YAAY;UACnC,IAAIE,WAAW,CAACpI,MAAM,KAAK,CAAC,IAAIqI,WAAW,CAACrI,MAAM,KAAK,CAAC,EAAE;YACxDC,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAGmD,CAAC,EAAEA,CAAC,CAAC;YAC5BnD,OAAO,GAAGA,OAAO,GAAGmD,CAAC;UACvB,CAAC,MAAM,IAAIP,WAAW,CAACpI,MAAM,KAAK,CAAC,EAAE;YACnCC,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAGmD,CAAC,EAAEA,CAAC,EAAE,CAAC3J,WAAW,EAAEqJ,WAAW,CAAC,CAAC;YACxD7C,OAAO,GAAGA,OAAO,GAAGmD,CAAC,GAAG,CAAC;UAC3B,CAAC,MAAM,IAAIN,WAAW,CAACrI,MAAM,KAAK,CAAC,EAAE;YACnCC,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAGmD,CAAC,EAAEA,CAAC,EAAE,CAAC5J,WAAW,EAAEqJ,WAAW,CAAC,CAAC;YACxD5C,OAAO,GAAGA,OAAO,GAAGmD,CAAC,GAAG,CAAC;UAC3B,CAAC,MAAM;YACL1I,KAAK,CAAC6F,MAAM,CACVN,OAAO,GAAGmD,CAAC,EACXA,CAAC,EACD,CAAC5J,WAAW,EAAEqJ,WAAW,CAAC,EAC1B,CAACpJ,WAAW,EAAEqJ,WAAW,CAC3B,CAAC;YACD7C,OAAO,GAAGA,OAAO,GAAGmD,CAAC,GAAG,CAAC;UAC3B;QACF;QACA,IAAInD,OAAO,KAAK,CAAC,IAAIvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKvG,UAAU,EAAE;UACzD;UACAgB,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC;UAC1CvF,KAAK,CAAC6F,MAAM,CAACN,OAAO,EAAE,CAAC,CAAC;QAC1B,CAAC,MAAM;UACLA,OAAO,EAAE;QACX;QACA2C,YAAY,GAAG,CAAC;QAChBD,YAAY,GAAG,CAAC;QAChBE,WAAW,GAAG,EAAE;QAChBC,WAAW,GAAG,EAAE;QAChB;IACJ;EACF;EACA,IAAIpI,KAAK,CAACA,KAAK,CAACD,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IACrCC,KAAK,CAAC2I,GAAG,CAAC,CAAC,CAAC,CAAC;EACf;;EAEA;EACA;EACA;EACA,IAAIxD,OAAO,GAAG,KAAK;EACnBI,OAAO,GAAG,CAAC;EACX;EACA,OAAOA,OAAO,GAAGvF,KAAK,CAACD,MAAM,GAAG,CAAC,EAAE;IACjC,IACEC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKvG,UAAU,IACpCgB,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKvG,UAAU,EACpC;MACA;MACA,IACEgB,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC3F,SAAS,CACzBI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM,GAAGC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACxF,MACnD,CAAC,KAAKC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3B;QACA;QACAvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,GACfvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GACrBvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC3F,SAAS,CACzB,CAAC,EACDI,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM,GAAGC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACxF,MACnD,CAAC;QACHC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACrEvF,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5BJ,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IACLnF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC3F,SAAS,CAAC,CAAC,EAAEI,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM,CAAC,IAC5DC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EACrB;QACA;QACAvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIvF,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9CvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,GACfvF,KAAK,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC3F,SAAS,CAACI,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAACxF,MAAM,CAAC,GACzDC,KAAK,CAACuF,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACvBvF,KAAK,CAAC6F,MAAM,CAACN,OAAO,GAAG,CAAC,EAAE,CAAC,CAAC;QAC5BJ,OAAO,GAAG,IAAI;MAChB;IACF;IACAI,OAAO,EAAE;EACX;EACA;EACA,IAAIJ,OAAO,EAAE;IACX/E,iBAAiB,CAACJ,KAAK,EAAEgI,WAAW,CAAC;EACvC;AACF;AAEA,SAASnE,uBAAuBA,CAAC+E,QAAQ,EAAE;EACzC,OAAOA,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,MAAM;AACjD;AAEA,SAAStE,qBAAqBA,CAACsE,QAAQ,EAAE;EACvC,OAAOA,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,MAAM;AACjD;AAEA,SAASH,oBAAoBA,CAACI,GAAG,EAAE;EACjC,OAAOvE,qBAAqB,CAACuE,GAAG,CAAC/E,UAAU,CAAC,CAAC,CAAC,CAAC;AACjD;AAEA,SAASwE,oBAAoBA,CAACO,GAAG,EAAE;EACjC,OAAOhF,uBAAuB,CAACgF,GAAG,CAAC/E,UAAU,CAAC+E,GAAG,CAAC9I,MAAM,GAAG,CAAC,CAAC,CAAC;AAChE;AAEA,SAAS+I,mBAAmBA,CAACC,MAAM,EAAE;EACnC,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIxI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuI,MAAM,CAAChJ,MAAM,EAAES,CAAC,EAAE,EAAE;IACtC,IAAIuI,MAAM,CAACvI,CAAC,CAAC,CAAC,CAAC,CAAC,CAACT,MAAM,GAAG,CAAC,EAAE;MAC3BiJ,GAAG,CAAC7I,IAAI,CAAC4I,MAAM,CAACvI,CAAC,CAAC,CAAC;IACrB;EACF;EACA,OAAOwI,GAAG;AACZ;AAEA,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC7D,IAAIf,oBAAoB,CAACY,MAAM,CAAC,IAAIT,oBAAoB,CAACY,KAAK,CAAC,EAAE;IAC/D,OAAO,IAAI;EACb;EACA,OAAOP,mBAAmB,CAAC,CACzB,CAAC9J,UAAU,EAAEkK,MAAM,CAAC,EACpB,CAACpK,WAAW,EAAEqK,SAAS,CAAC,EACxB,CAACpK,WAAW,EAAEqK,SAAS,CAAC,EACxB,CAACpK,UAAU,EAAEqK,KAAK,CAAC,CACpB,CAAC;AACJ;AAEA,SAAS7J,qBAAqBA,CAAC8J,OAAO,EAAEC,OAAO,EAAEnK,UAAU,EAAE;EAC3D;EACA,IAAIoK,QAAQ,GACV,OAAOpK,UAAU,KAAK,QAAQ,GAC1B;IAAEqK,KAAK,EAAErK,UAAU;IAAEW,MAAM,EAAE;EAAE,CAAC,GAChCX,UAAU,CAACoK,QAAQ;EACzB,IAAIE,QAAQ,GAAG,OAAOtK,UAAU,KAAK,QAAQ,GAAG,IAAI,GAAGA,UAAU,CAACsK,QAAQ;EAC1E;EACA;EACA;EACA;EACA,IAAIC,SAAS,GAAGL,OAAO,CAACvJ,MAAM;EAC9B,IAAI6J,SAAS,GAAGL,OAAO,CAACxJ,MAAM;EAC9B,IAAIyJ,QAAQ,CAACzJ,MAAM,KAAK,CAAC,KAAK2J,QAAQ,KAAK,IAAI,IAAIA,QAAQ,CAAC3J,MAAM,KAAK,CAAC,CAAC,EAAE;IACzE;IACA,IAAI8J,SAAS,GAAGL,QAAQ,CAACC,KAAK;IAC9B,IAAIK,SAAS,GAAGR,OAAO,CAAClF,KAAK,CAAC,CAAC,EAAEyF,SAAS,CAAC;IAC3C,IAAIE,QAAQ,GAAGT,OAAO,CAAClF,KAAK,CAACyF,SAAS,CAAC;IACvC,IAAIG,cAAc,GAAGN,QAAQ,GAAGA,QAAQ,CAACD,KAAK,GAAG,IAAI;IACrDQ,UAAU,EAAE;MACV;MACA,IAAIC,SAAS,GAAGL,SAAS,GAAGD,SAAS,GAAGD,SAAS;MACjD,IAAIK,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKE,SAAS,EAAE;QAC3D,MAAMD,UAAU;MAClB;MACA,IAAIC,SAAS,GAAG,CAAC,IAAIA,SAAS,GAAGN,SAAS,EAAE;QAC1C,MAAMK,UAAU;MAClB;MACA,IAAIE,SAAS,GAAGZ,OAAO,CAACnF,KAAK,CAAC,CAAC,EAAE8F,SAAS,CAAC;MAC3C,IAAIE,QAAQ,GAAGb,OAAO,CAACnF,KAAK,CAAC8F,SAAS,CAAC;MACvC,IAAIE,QAAQ,KAAKL,QAAQ,EAAE;QACzB,MAAME,UAAU;MAClB;MACA,IAAIlF,YAAY,GAAGvD,IAAI,CAACiC,GAAG,CAACoG,SAAS,EAAEK,SAAS,CAAC;MACjD,IAAIG,SAAS,GAAGP,SAAS,CAAC1F,KAAK,CAAC,CAAC,EAAEW,YAAY,CAAC;MAChD,IAAIuF,SAAS,GAAGH,SAAS,CAAC/F,KAAK,CAAC,CAAC,EAAEW,YAAY,CAAC;MAChD,IAAIsF,SAAS,KAAKC,SAAS,EAAE;QAC3B,MAAML,UAAU;MAClB;MACA,IAAId,SAAS,GAAGW,SAAS,CAAC1F,KAAK,CAACW,YAAY,CAAC;MAC7C,IAAIqE,SAAS,GAAGe,SAAS,CAAC/F,KAAK,CAACW,YAAY,CAAC;MAC7C,OAAOkE,gBAAgB,CAACoB,SAAS,EAAElB,SAAS,EAAEC,SAAS,EAAEW,QAAQ,CAAC;IACpE;IACAQ,SAAS,EAAE;MACT;MACA,IAAIP,cAAc,KAAK,IAAI,IAAIA,cAAc,KAAKH,SAAS,EAAE;QAC3D,MAAMU,SAAS;MACjB;MACA,IAAIC,MAAM,GAAGX,SAAS;MACtB,IAAIM,SAAS,GAAGZ,OAAO,CAACnF,KAAK,CAAC,CAAC,EAAEoG,MAAM,CAAC;MACxC,IAAIJ,QAAQ,GAAGb,OAAO,CAACnF,KAAK,CAACoG,MAAM,CAAC;MACpC,IAAIL,SAAS,KAAKL,SAAS,EAAE;QAC3B,MAAMS,SAAS;MACjB;MACA,IAAIvF,YAAY,GAAGxD,IAAI,CAACiC,GAAG,CAACkG,SAAS,GAAGa,MAAM,EAAEZ,SAAS,GAAGY,MAAM,CAAC;MACnE,IAAIC,SAAS,GAAGV,QAAQ,CAAC3F,KAAK,CAAC2F,QAAQ,CAAChK,MAAM,GAAGiF,YAAY,CAAC;MAC9D,IAAI0F,SAAS,GAAGN,QAAQ,CAAChG,KAAK,CAACgG,QAAQ,CAACrK,MAAM,GAAGiF,YAAY,CAAC;MAC9D,IAAIyF,SAAS,KAAKC,SAAS,EAAE;QAC3B,MAAMH,SAAS;MACjB;MACA,IAAIpB,SAAS,GAAGY,QAAQ,CAAC3F,KAAK,CAAC,CAAC,EAAE2F,QAAQ,CAAChK,MAAM,GAAGiF,YAAY,CAAC;MACjE,IAAIoE,SAAS,GAAGgB,QAAQ,CAAChG,KAAK,CAAC,CAAC,EAAEgG,QAAQ,CAACrK,MAAM,GAAGiF,YAAY,CAAC;MACjE,OAAOiE,gBAAgB,CAACa,SAAS,EAAEX,SAAS,EAAEC,SAAS,EAAEqB,SAAS,CAAC;IACrE;EACF;EACA,IAAIjB,QAAQ,CAACzJ,MAAM,GAAG,CAAC,IAAI2J,QAAQ,IAAIA,QAAQ,CAAC3J,MAAM,KAAK,CAAC,EAAE;IAC5D4K,YAAY,EAAE;MACZ;MACA,IAAIN,SAAS,GAAGf,OAAO,CAAClF,KAAK,CAAC,CAAC,EAAEoF,QAAQ,CAACC,KAAK,CAAC;MAChD,IAAIgB,SAAS,GAAGnB,OAAO,CAAClF,KAAK,CAACoF,QAAQ,CAACC,KAAK,GAAGD,QAAQ,CAACzJ,MAAM,CAAC;MAC/D,IAAIgF,YAAY,GAAGsF,SAAS,CAACtK,MAAM;MACnC,IAAIiF,YAAY,GAAGyF,SAAS,CAAC1K,MAAM;MACnC,IAAI6J,SAAS,GAAG7E,YAAY,GAAGC,YAAY,EAAE;QAC3C,MAAM2F,YAAY;MACpB;MACA,IAAIL,SAAS,GAAGf,OAAO,CAACnF,KAAK,CAAC,CAAC,EAAEW,YAAY,CAAC;MAC9C,IAAI2F,SAAS,GAAGnB,OAAO,CAACnF,KAAK,CAACwF,SAAS,GAAG5E,YAAY,CAAC;MACvD,IAAIqF,SAAS,KAAKC,SAAS,IAAIG,SAAS,KAAKC,SAAS,EAAE;QACtD,MAAMC,YAAY;MACpB;MACA,IAAIxB,SAAS,GAAGG,OAAO,CAAClF,KAAK,CAACW,YAAY,EAAE4E,SAAS,GAAG3E,YAAY,CAAC;MACrE,IAAIoE,SAAS,GAAGG,OAAO,CAACnF,KAAK,CAACW,YAAY,EAAE6E,SAAS,GAAG5E,YAAY,CAAC;MACrE,OAAOiE,gBAAgB,CAACoB,SAAS,EAAElB,SAAS,EAAEC,SAAS,EAAEqB,SAAS,CAAC;IACrE;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASG,IAAIA,CAAC1L,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE;EAC/C;EACA;EACA,OAAOJ,SAAS,CAACC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,EAAE,IAAI,CAAC;AAC3D;AAEAuL,IAAI,CAACC,MAAM,GAAG9L,WAAW;AACzB6L,IAAI,CAACE,MAAM,GAAGhM,WAAW;AACzB8L,IAAI,CAACG,KAAK,GAAG/L,UAAU;AAEvBgM,MAAM,CAACC,OAAO,GAAGL,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}