{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} milliseconds - the milliseconds of the new date\n * @returns {Date} the new date with the milliseconds set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport default function setMilliseconds(dirtyDate, dirtyMilliseconds) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = toInteger(dirtyMilliseconds);\n  date.setMilliseconds(milliseconds);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setMilliseconds", "dirtyDate", "dirtyMilliseconds", "arguments", "date", "milliseconds"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/setMilliseconds/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setMilliseconds\n * @category Millisecond Helpers\n * @summary Set the milliseconds to the given date.\n *\n * @description\n * Set the milliseconds to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} milliseconds - the milliseconds of the new date\n * @returns {Date} the new date with the milliseconds set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set 300 milliseconds to 1 September 2014 11:30:40.500:\n * const result = setMilliseconds(new Date(2014, 8, 1, 11, 30, 40, 500), 300)\n * //=> Mon Sep 01 2014 11:30:40.300\n */\nexport default function setMilliseconds(dirtyDate, dirtyMilliseconds) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var milliseconds = toInteger(dirtyMilliseconds);\n  date.setMilliseconds(milliseconds);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,iBAAiB,EAAE;EACpEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,YAAY,GAAGR,SAAS,CAACK,iBAAiB,CAAC;EAC/CE,IAAI,CAACJ,eAAe,CAACK,YAAY,CAAC;EAClC,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}