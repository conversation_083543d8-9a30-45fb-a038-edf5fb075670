{"ast": null, "code": "import createCompounder from './_createCompounder.js';\nimport upperFirst from './upperFirst.js';\n\n/**\n * Converts `string` to\n * [start case](https://en.wikipedia.org/wiki/Letter_case#Stylistic_or_specialised_usage).\n *\n * @static\n * @memberOf _\n * @since 3.1.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the start cased string.\n * @example\n *\n * _.startCase('--foo-bar--');\n * // => 'Foo Bar'\n *\n * _.startCase('fooBar');\n * // => 'Foo Bar'\n *\n * _.startCase('__FOO_BAR__');\n * // => 'FOO BAR'\n */\nvar startCase = createCompounder(function (result, word, index) {\n  return result + (index ? ' ' : '') + upperFirst(word);\n});\nexport default startCase;", "map": {"version": 3, "names": ["createCompounder", "upperFirst", "startCase", "result", "word", "index"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/startCase.js"], "sourcesContent": ["import createCompounder from './_createCompounder.js';\nimport upperFirst from './upperFirst.js';\n\n/**\n * Converts `string` to\n * [start case](https://en.wikipedia.org/wiki/Letter_case#Stylistic_or_specialised_usage).\n *\n * @static\n * @memberOf _\n * @since 3.1.0\n * @category String\n * @param {string} [string=''] The string to convert.\n * @returns {string} Returns the start cased string.\n * @example\n *\n * _.startCase('--foo-bar--');\n * // => 'Foo Bar'\n *\n * _.startCase('fooBar');\n * // => 'Foo Bar'\n *\n * _.startCase('__FOO_BAR__');\n * // => 'FOO BAR'\n */\nvar startCase = createCompounder(function(result, word, index) {\n  return result + (index ? ' ' : '') + upperFirst(word);\n});\n\nexport default startCase;\n"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,wBAAwB;AACrD,OAAOC,UAAU,MAAM,iBAAiB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGF,gBAAgB,CAAC,UAASG,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE;EAC7D,OAAOF,MAAM,IAAIE,KAAK,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGJ,UAAU,CAACG,IAAI,CAAC;AACvD,CAAC,CAAC;AAEF,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}