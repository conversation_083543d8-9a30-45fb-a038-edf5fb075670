{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param {Date|Number} dateLeft - the first date to compare\n * @param {Date|Number} dateRight - the second date to compare\n * @returns {Boolean} the dates are equal\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport default function isEqual(dirtyLeftDate, dirtyRightDate) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyLeftDate);\n  var dateRight = toDate(dirtyRightDate);\n  return dateLeft.getTime() === dateRight.getTime();\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "isEqual", "dirtyLeftDate", "dirtyRightDate", "arguments", "dateLeft", "dateRight", "getTime"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/isEqual/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param {Date|Number} dateLeft - the first date to compare\n * @param {Date|Number} dateRight - the second date to compare\n * @returns {Boolean} the dates are equal\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport default function isEqual(dirtyLeftDate, dirtyRightDate) {\n  requiredArgs(2, arguments);\n  var dateLeft = toDate(dirtyLeftDate);\n  var dateRight = toDate(dirtyRightDate);\n  return dateLeft.getTime() === dateRight.getTime();\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,OAAOA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC7DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGN,MAAM,CAACG,aAAa,CAAC;EACpC,IAAII,SAAS,GAAGP,MAAM,CAACI,cAAc,CAAC;EACtC,OAAOE,QAAQ,CAACE,OAAO,CAAC,CAAC,KAAKD,SAAS,CAACC,OAAO,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}