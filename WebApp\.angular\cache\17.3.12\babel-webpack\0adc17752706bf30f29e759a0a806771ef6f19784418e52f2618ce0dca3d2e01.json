{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Injectable } from '@angular/core';\nimport { BehaviorSubject, interval } from 'rxjs';\nimport { map } from 'rxjs/operators';\nlet TimeFormatService = class TimeFormatService {\n  constructor() {\n    this.currentTimeSubject = new BehaviorSubject(Date.now());\n    this.currentTime$ = this.currentTimeSubject.asObservable();\n    // Update the time every minute (60000 ms)\n    interval(60000).subscribe(() => {\n      this.currentTimeSubject.next(Date.now());\n    });\n  }\n  /**\n   * Formats a timestamp into a relative time string (e.g., \"2 minutes ago\")\n   * @param timestamp Timestamp in milliseconds\n   * @returns Formatted time string\n   */\n  getFormattedTime(timestamp) {\n    const now = Date.now();\n    const diff = now - timestamp;\n    const absDiff = Math.abs(diff);\n    // Calculate time units without seconds\n    const minutes = Math.floor(absDiff / 60000); // 60000 ms in a minute\n    const hours = Math.floor(minutes / 60);\n    const days = Math.floor(hours / 24);\n    // More precise month/year calculation using Date objects\n    const currentDate = new Date();\n    const pastDate = new Date(timestamp);\n    let months = (currentDate.getFullYear() - pastDate.getFullYear()) * 12 + (currentDate.getMonth() - pastDate.getMonth());\n    const years = Math.floor(months / 12);\n    // Handle negative differences (future timestamps)\n    const isFuture = diff < 0;\n    const suffix = isFuture ? ' from now' : ' ago';\n    if (years > 0) {\n      return `${years} year${years > 1 ? 's' : ''}${suffix}`;\n    } else if (months > 0) {\n      return `${months} month${months > 1 ? 's' : ''}${suffix}`;\n    } else if (days > 0) {\n      return `${days} day${days > 1 ? 's' : ''}${suffix}`;\n    } else if (hours > 0) {\n      return `${hours} hour${hours > 1 ? 's' : ''}${suffix}`;\n    } else if (minutes > 0) {\n      return `${minutes} minute${minutes > 1 ? 's' : ''}${suffix}`;\n    } else {\n      // If less than a minute has passed, show \"just now\"\n      return 'just now';\n    }\n  }\n  /**\n   * Returns an observable that emits the formatted time and updates every minute\n   * @param timestamp Timestamp in milliseconds\n   * @returns Observable that emits formatted time string\n   */\n  getFormattedTimeObservable(timestamp) {\n    return this.currentTime$.pipe(map(() => this.getFormattedTime(timestamp)));\n  }\n  static {\n    this.ctorParameters = () => [];\n  }\n};\nTimeFormatService = __decorate([Injectable({\n  providedIn: 'root'\n})], TimeFormatService);\nexport { TimeFormatService };", "map": {"version": 3, "names": ["Injectable", "BehaviorSubject", "interval", "map", "TimeFormatService", "constructor", "currentTimeSubject", "Date", "now", "currentTime$", "asObservable", "subscribe", "next", "getFormattedTime", "timestamp", "diff", "absDiff", "Math", "abs", "minutes", "floor", "hours", "days", "currentDate", "pastDate", "months", "getFullYear", "getMonth", "years", "isFuture", "suffix", "getFormattedTimeObservable", "pipe", "__decorate", "providedIn"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\services\\time-format.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { BehaviorSubject, Observable, interval } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class TimeFormatService {\r\n  private currentTimeSubject = new BehaviorSubject<number>(Date.now());\r\n  public currentTime$ = this.currentTimeSubject.asObservable();\r\n\r\n  constructor() {\r\n    // Update the time every minute (60000 ms)\r\n    interval(60000).subscribe(() => {\r\n      this.currentTimeSubject.next(Date.now());\r\n    });\r\n  }\r\n\r\n  /**\r\n   * Formats a timestamp into a relative time string (e.g., \"2 minutes ago\")\r\n   * @param timestamp Timestamp in milliseconds\r\n   * @returns Formatted time string\r\n   */  getFormattedTime(timestamp: number): string {\r\n    const now = Date.now();\r\n    const diff = now - timestamp;\r\n    const absDiff = Math.abs(diff);\r\n\r\n    // Calculate time units without seconds\r\n    const minutes = Math.floor(absDiff / 60000); // 60000 ms in a minute\r\n    const hours = Math.floor(minutes / 60);\r\n    const days = Math.floor(hours / 24);\r\n\r\n    // More precise month/year calculation using Date objects\r\n    const currentDate = new Date();\r\n    const pastDate = new Date(timestamp);\r\n    let months =\r\n      (currentDate.getFullYear() - pastDate.getFullYear()) * 12 +\r\n      (currentDate.getMonth() - pastDate.getMonth());\r\n    const years = Math.floor(months / 12);\r\n\r\n    // Handle negative differences (future timestamps)\r\n    const isFuture = diff < 0;\r\n    const suffix = isFuture ? ' from now' : ' ago';\r\n\r\n    if (years > 0) {\r\n      return `${years} year${years > 1 ? 's' : ''}${suffix}`;\r\n    } else if (months > 0) {\r\n      return `${months} month${months > 1 ? 's' : ''}${suffix}`;\r\n    } else if (days > 0) {\r\n      return `${days} day${days > 1 ? 's' : ''}${suffix}`;\r\n    } else if (hours > 0) {\r\n      return `${hours} hour${hours > 1 ? 's' : ''}${suffix}`;\r\n    } else if (minutes > 0) {\r\n      return `${minutes} minute${minutes > 1 ? 's' : ''}${suffix}`;\r\n    } else {\r\n      // If less than a minute has passed, show \"just now\"\r\n      return 'just now';\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns an observable that emits the formatted time and updates every minute\r\n   * @param timestamp Timestamp in milliseconds\r\n   * @returns Observable that emits formatted time string\r\n   */\r\n  getFormattedTimeObservable(timestamp: number): Observable<string> {\r\n    return this.currentTime$.pipe(\r\n      map(() => this.getFormattedTime(timestamp))\r\n    );\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,eAAe,EAAcC,QAAQ,QAAQ,MAAM;AAC5D,SAASC,GAAG,QAAQ,gBAAgB;AAK7B,IAAMC,iBAAiB,GAAvB,MAAMA,iBAAiB;EAI5BC,YAAA;IAHQ,KAAAC,kBAAkB,GAAG,IAAIL,eAAe,CAASM,IAAI,CAACC,GAAG,EAAE,CAAC;IAC7D,KAAAC,YAAY,GAAG,IAAI,CAACH,kBAAkB,CAACI,YAAY,EAAE;IAG1D;IACAR,QAAQ,CAAC,KAAK,CAAC,CAACS,SAAS,CAAC,MAAK;MAC7B,IAAI,CAACL,kBAAkB,CAACM,IAAI,CAACL,IAAI,CAACC,GAAG,EAAE,CAAC;IAC1C,CAAC,CAAC;EACJ;EAEA;;;;;EAIKK,gBAAgBA,CAACC,SAAiB;IACrC,MAAMN,GAAG,GAAGD,IAAI,CAACC,GAAG,EAAE;IACtB,MAAMO,IAAI,GAAGP,GAAG,GAAGM,SAAS;IAC5B,MAAME,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACH,IAAI,CAAC;IAE9B;IACA,MAAMI,OAAO,GAAGF,IAAI,CAACG,KAAK,CAACJ,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC;IAC7C,MAAMK,KAAK,GAAGJ,IAAI,CAACG,KAAK,CAACD,OAAO,GAAG,EAAE,CAAC;IACtC,MAAMG,IAAI,GAAGL,IAAI,CAACG,KAAK,CAACC,KAAK,GAAG,EAAE,CAAC;IAEnC;IACA,MAAME,WAAW,GAAG,IAAIhB,IAAI,EAAE;IAC9B,MAAMiB,QAAQ,GAAG,IAAIjB,IAAI,CAACO,SAAS,CAAC;IACpC,IAAIW,MAAM,GACR,CAACF,WAAW,CAACG,WAAW,EAAE,GAAGF,QAAQ,CAACE,WAAW,EAAE,IAAI,EAAE,IACxDH,WAAW,CAACI,QAAQ,EAAE,GAAGH,QAAQ,CAACG,QAAQ,EAAE,CAAC;IAChD,MAAMC,KAAK,GAAGX,IAAI,CAACG,KAAK,CAACK,MAAM,GAAG,EAAE,CAAC;IAErC;IACA,MAAMI,QAAQ,GAAGd,IAAI,GAAG,CAAC;IACzB,MAAMe,MAAM,GAAGD,QAAQ,GAAG,WAAW,GAAG,MAAM;IAE9C,IAAID,KAAK,GAAG,CAAC,EAAE;MACb,OAAO,GAAGA,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGE,MAAM,EAAE;KACvD,MAAM,IAAIL,MAAM,GAAG,CAAC,EAAE;MACrB,OAAO,GAAGA,MAAM,SAASA,MAAM,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGK,MAAM,EAAE;KAC1D,MAAM,IAAIR,IAAI,GAAG,CAAC,EAAE;MACnB,OAAO,GAAGA,IAAI,OAAOA,IAAI,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGQ,MAAM,EAAE;KACpD,MAAM,IAAIT,KAAK,GAAG,CAAC,EAAE;MACpB,OAAO,GAAGA,KAAK,QAAQA,KAAK,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGS,MAAM,EAAE;KACvD,MAAM,IAAIX,OAAO,GAAG,CAAC,EAAE;MACtB,OAAO,GAAGA,OAAO,UAAUA,OAAO,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAGW,MAAM,EAAE;KAC7D,MAAM;MACL;MACA,OAAO,UAAU;;EAErB;EAEA;;;;;EAKAC,0BAA0BA,CAACjB,SAAiB;IAC1C,OAAO,IAAI,CAACL,YAAY,CAACuB,IAAI,CAC3B7B,GAAG,CAAC,MAAM,IAAI,CAACU,gBAAgB,CAACC,SAAS,CAAC,CAAC,CAC5C;EACH;;;;;AA9DWV,iBAAiB,GAAA6B,UAAA,EAH7BjC,UAAU,CAAC;EACVkC,UAAU,EAAE;CACb,CAAC,C,EACW9B,iBAAiB,CA+D7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}