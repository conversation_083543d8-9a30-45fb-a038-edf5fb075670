{"ast": null, "code": "/**\n * Enables the wrapper to be iterable.\n *\n * @name Symbol.iterator\n * @memberOf _\n * @since 4.0.0\n * @category Seq\n * @returns {Object} Returns the wrapper object.\n * @example\n *\n * var wrapped = _([1, 2]);\n *\n * wrapped[Symbol.iterator]() === wrapped;\n * // => true\n *\n * Array.from(wrapped);\n * // => [1, 2]\n */\nfunction wrapperToIterator() {\n  return this;\n}\nexport default wrapperToIterator;", "map": {"version": 3, "names": ["wrapperToIterator"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/toIterator.js"], "sourcesContent": ["/**\n * Enables the wrapper to be iterable.\n *\n * @name Symbol.iterator\n * @memberOf _\n * @since 4.0.0\n * @category Seq\n * @returns {Object} Returns the wrapper object.\n * @example\n *\n * var wrapped = _([1, 2]);\n *\n * wrapped[Symbol.iterator]() === wrapped;\n * // => true\n *\n * Array.from(wrapped);\n * // => [1, 2]\n */\nfunction wrapperToIterator() {\n  return this;\n}\n\nexport default wrapperToIterator;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAiBA,CAAA,EAAG;EAC3B,OAAO,IAAI;AACb;AAEA,eAAeA,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}