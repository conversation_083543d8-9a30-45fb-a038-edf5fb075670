{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { NgClass, NgTemplateOutlet, NgStyle } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { isNotNil, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = a0 => ({\n  $implicit: a0\n});\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzType\", ctx_r0.icon);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const formatter_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", formatter_r2(ctx_r0.nzPercent), \" \");\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template, 2, 1, \"ng-container\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx_r0.formatter)(\"nzStringTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c0, ctx_r0.nzPercent));\n  }\n}\nfunction NzProgressComponent_ng_template_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵtemplate(1, NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template, 1, 1, \"span\", 4)(2, NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template, 1, 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, (ctx_r0.status === \"exception\" || ctx_r0.status === \"success\") && !ctx_r0.nzFormat ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Conditional_0_Template, 3, 1, \"span\", 3);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r0.nzShowInfo ? 0 : -1);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 7);\n  }\n  if (rf & 2) {\n    const step_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngStyle\", step_r3);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6);\n    i0.ɵɵrepeaterCreate(1, NzProgressComponent_Conditional_3_Conditional_1_For_2_Template, 1, 1, \"div\", 7, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.steps);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzSuccessPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"height\", ctx_r0.strokeWidth, \"px\");\n  }\n}\nfunction NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_3_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10);\n    i0.ɵɵelement(2, \"div\", 11);\n    i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template, 1, 6, \"div\", 12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(4, NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template, 0, 0, \"ng-template\", 8);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzPercent, \"%\")(\"border-radius\", ctx_r0.nzStrokeLinecap === \"round\" ? \"100px\" : \"0\")(\"background\", !ctx_r0.isGradient ? ctx_r0.nzStrokeColor : null)(\"background-image\", ctx_r0.isGradient ? ctx_r0.lineGradient : null)(\"height\", ctx_r0.strokeWidth, \"px\");\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(3, ctx_r0.nzSuccessPercent || ctx_r0.nzSuccessPercent === 0 ? 3 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction NzProgressComponent_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, NzProgressComponent_Conditional_3_Conditional_1_Template, 4, 1, \"div\", 6)(2, NzProgressComponent_Conditional_3_Conditional_2_Template, 5, 12);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.isSteps ? 1 : 2);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"stop\");\n  }\n  if (rf & 2) {\n    const i_r5 = ctx.$implicit;\n    i0.ɵɵattribute(\"offset\", i_r5.offset)(\"stop-color\", i_r5.color);\n  }\n}\nfunction NzProgressComponent_Conditional_4_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(0, \"defs\")(1, \"linearGradient\", 17);\n    i0.ɵɵrepeaterCreate(2, NzProgressComponent_Conditional_4_Conditional_2_For_3_Template, 1, 2, \":svg:stop\", null, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"id\", \"gradient-\" + ctx_r0.gradientId);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.circleGradient);\n  }\n}\nfunction NzProgressComponent_Conditional_4_For_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelement(0, \"path\", 16);\n  }\n  if (rf & 2) {\n    const p_r6 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", p_r6.strokePathStyle);\n    i0.ɵɵattribute(\"d\", ctx_r0.pathString)(\"stroke-linecap\", ctx_r0.nzStrokeLinecap)(\"stroke\", p_r6.stroke)(\"stroke-width\", ctx_r0.nzPercent ? ctx_r0.strokeWidth : 0);\n  }\n}\nfunction NzProgressComponent_Conditional_4_ng_template_6_Template(rf, ctx) {}\nfunction NzProgressComponent_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 14);\n    i0.ɵɵtemplate(2, NzProgressComponent_Conditional_4_Conditional_2_Template, 4, 1, \":svg:defs\");\n    i0.ɵɵelement(3, \"path\", 15);\n    i0.ɵɵrepeaterCreate(4, NzProgressComponent_Conditional_4_For_5_Template, 1, 5, \":svg:path\", 16, i0.ɵɵrepeaterTrackByIdentity);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, NzProgressComponent_Conditional_4_ng_template_6_Template, 0, 0, \"ng-template\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const progressInfoTemplate_r4 = i0.ɵɵreference(1);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.nzWidth, \"px\")(\"height\", ctx_r0.nzWidth, \"px\")(\"font-size\", ctx_r0.nzWidth * 0.15 + 6, \"px\");\n    i0.ɵɵclassProp(\"ant-progress-circle-gradient\", ctx_r0.isGradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, ctx_r0.isGradient ? 2 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.trailPathStyle);\n    i0.ɵɵattribute(\"stroke-width\", ctx_r0.strokeWidth)(\"d\", ctx_r0.pathString);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.progressCirclePath);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", progressInfoTemplate_r4);\n  }\n}\nfunction stripPercentToNumber(percent) {\n  return +percent.replace('%', '');\n}\nconst sortGradient = gradients => {\n  let tempArr = [];\n  Object.keys(gradients).forEach(key => {\n    const value = gradients[key];\n    const formatKey = stripPercentToNumber(key);\n    if (!isNaN(formatKey)) {\n      tempArr.push({\n        key: formatKey,\n        value\n      });\n    }\n  });\n  tempArr = tempArr.sort((a, b) => a.key - b.key);\n  return tempArr;\n};\nconst handleCircleGradient = strokeColor => sortGradient(strokeColor).map(({\n  key,\n  value\n}) => ({\n  offset: `${key}%`,\n  color: value\n}));\nconst handleLinearGradient = strokeColor => {\n  const {\n    from = '#1890ff',\n    to = '#1890ff',\n    direction = 'to right',\n    ...rest\n  } = strokeColor;\n  if (Object.keys(rest).length !== 0) {\n    const sortedGradients = sortGradient(rest).map(({\n      key,\n      value\n    }) => `${value} ${key}%`).join(', ');\n    return `linear-gradient(${direction}, ${sortedGradients})`;\n  }\n  return `linear-gradient(${direction}, ${from}, ${to})`;\n};\nlet gradientIdSeed = 0;\nconst NZ_CONFIG_MODULE_NAME = 'progress';\nconst statusIconNameMap = new Map([['success', 'check'], ['exception', 'close']]);\nconst statusColorMap = new Map([['normal', '#108ee9'], ['exception', '#ff5500'], ['success', '#87d068']]);\nconst defaultFormatter = p => `${p}%`;\nclass NzProgressComponent {\n  get formatter() {\n    return this.nzFormat || defaultFormatter;\n  }\n  get status() {\n    return this.nzStatus || this.inferredStatus;\n  }\n  get strokeWidth() {\n    return this.nzStrokeWidth || (this.nzType === 'line' && this.nzSize !== 'small' ? 8 : 6);\n  }\n  get isCircleStyle() {\n    return this.nzType === 'circle' || this.nzType === 'dashboard';\n  }\n  constructor(cdr, nzConfigService, directionality) {\n    this.cdr = cdr;\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzShowInfo = true;\n    this.nzWidth = 132;\n    this.nzStrokeColor = undefined;\n    this.nzSize = 'default';\n    this.nzPercent = 0;\n    this.nzStrokeWidth = undefined;\n    this.nzGapDegree = undefined;\n    this.nzType = 'line';\n    this.nzGapPosition = 'top';\n    this.nzStrokeLinecap = 'round';\n    this.nzSteps = 0;\n    this.steps = [];\n    /** Gradient style when `nzType` is `line`. */\n    this.lineGradient = null;\n    /** If user uses gradient color. */\n    this.isGradient = false;\n    /** If the linear progress is a step progress. */\n    this.isSteps = false;\n    /**\n     * Each progress whose `nzType` is circle or dashboard should have unique id to\n     * define `<linearGradient>`.\n     */\n    this.gradientId = gradientIdSeed++;\n    /** Paths to rendered in the template. */\n    this.progressCirclePath = [];\n    this.trailPathStyle = null;\n    this.dir = 'ltr';\n    this.cachedStatus = 'normal';\n    this.inferredStatus = 'normal';\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSteps,\n      nzGapPosition,\n      nzStrokeLinecap,\n      nzStrokeColor,\n      nzGapDegree,\n      nzType,\n      nzStatus,\n      nzPercent,\n      nzSuccessPercent,\n      nzStrokeWidth\n    } = changes;\n    if (nzStatus) {\n      this.cachedStatus = this.nzStatus || this.cachedStatus;\n    }\n    if (nzPercent || nzSuccessPercent) {\n      const fillAll = parseInt(this.nzPercent.toString(), 10) >= 100;\n      if (fillAll) {\n        if (isNotNil(this.nzSuccessPercent) && this.nzSuccessPercent >= 100 || this.nzSuccessPercent === undefined) {\n          this.inferredStatus = 'success';\n        }\n      } else {\n        this.inferredStatus = this.cachedStatus;\n      }\n    }\n    if (nzStatus || nzPercent || nzSuccessPercent || nzStrokeColor) {\n      this.updateIcon();\n    }\n    if (nzStrokeColor) {\n      this.setStrokeColor();\n    }\n    if (nzGapPosition || nzStrokeLinecap || nzGapDegree || nzType || nzPercent || nzStrokeColor || nzStrokeColor) {\n      this.getCirclePaths();\n    }\n    if (nzPercent || nzSteps || nzStrokeWidth) {\n      this.isSteps = this.nzSteps > 0;\n      if (this.isSteps) {\n        this.getSteps();\n      }\n    }\n  }\n  ngOnInit() {\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.updateIcon();\n      this.setStrokeColor();\n      this.getCirclePaths();\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  updateIcon() {\n    const ret = statusIconNameMap.get(this.status);\n    this.icon = ret ? ret + (this.isCircleStyle ? '-o' : '-circle-fill') : '';\n  }\n  /**\n   * Calculate step render configs.\n   */\n  getSteps() {\n    const current = Math.floor(this.nzSteps * (this.nzPercent / 100));\n    const stepWidth = this.nzSize === 'small' ? 2 : 14;\n    const steps = [];\n    for (let i = 0; i < this.nzSteps; i++) {\n      let color;\n      if (i <= current - 1) {\n        color = this.nzStrokeColor;\n      }\n      const stepStyle = {\n        backgroundColor: `${color}`,\n        width: `${stepWidth}px`,\n        height: `${this.strokeWidth}px`\n      };\n      steps.push(stepStyle);\n    }\n    this.steps = steps;\n  }\n  /**\n   * Calculate paths when the type is circle or dashboard.\n   */\n  getCirclePaths() {\n    if (!this.isCircleStyle) {\n      return;\n    }\n    const values = isNotNil(this.nzSuccessPercent) ? [this.nzSuccessPercent, this.nzPercent] : [this.nzPercent];\n    // Calculate shared styles.\n    const radius = 50 - this.strokeWidth / 2;\n    const gapPosition = this.nzGapPosition || (this.nzType === 'circle' ? 'top' : 'bottom');\n    const len = Math.PI * 2 * radius;\n    const gapDegree = this.nzGapDegree || (this.nzType === 'circle' ? 0 : 75);\n    let beginPositionX = 0;\n    let beginPositionY = -radius;\n    let endPositionX = 0;\n    let endPositionY = radius * -2;\n    switch (gapPosition) {\n      case 'left':\n        beginPositionX = -radius;\n        beginPositionY = 0;\n        endPositionX = radius * 2;\n        endPositionY = 0;\n        break;\n      case 'right':\n        beginPositionX = radius;\n        beginPositionY = 0;\n        endPositionX = radius * -2;\n        endPositionY = 0;\n        break;\n      case 'bottom':\n        beginPositionY = radius;\n        endPositionY = radius * 2;\n        break;\n      default:\n    }\n    this.pathString = `M 50,50 m ${beginPositionX},${beginPositionY}\n       a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}\n       a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;\n    this.trailPathStyle = {\n      strokeDasharray: `${len - gapDegree}px ${len}px`,\n      strokeDashoffset: `-${gapDegree / 2}px`,\n      transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s'\n    };\n    // Calculate styles for each path.\n    this.progressCirclePath = values.map((value, index) => {\n      const isSuccessPercent = values.length === 2 && index === 0;\n      return {\n        stroke: this.isGradient && !isSuccessPercent ? `url(#gradient-${this.gradientId})` : null,\n        strokePathStyle: {\n          stroke: !this.isGradient ? isSuccessPercent ? statusColorMap.get('success') : this.nzStrokeColor : null,\n          transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s',\n          strokeDasharray: `${(value || 0) / 100 * (len - gapDegree)}px ${len}px`,\n          strokeDashoffset: `-${gapDegree / 2}px`\n        }\n      };\n    }).reverse();\n  }\n  setStrokeColor() {\n    const color = this.nzStrokeColor;\n    const isGradient = this.isGradient = !!color && typeof color !== 'string';\n    if (isGradient && !this.isCircleStyle) {\n      this.lineGradient = handleLinearGradient(color);\n    } else if (isGradient && this.isCircleStyle) {\n      this.circleGradient = handleCircleGradient(this.nzStrokeColor);\n    } else {\n      this.lineGradient = null;\n      this.circleGradient = [];\n    }\n  }\n  static {\n    this.ɵfac = function NzProgressComponent_Factory(t) {\n      return new (t || NzProgressComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzProgressComponent,\n      selectors: [[\"nz-progress\"]],\n      inputs: {\n        nzShowInfo: \"nzShowInfo\",\n        nzWidth: \"nzWidth\",\n        nzStrokeColor: \"nzStrokeColor\",\n        nzSize: \"nzSize\",\n        nzFormat: \"nzFormat\",\n        nzSuccessPercent: \"nzSuccessPercent\",\n        nzPercent: \"nzPercent\",\n        nzStrokeWidth: \"nzStrokeWidth\",\n        nzGapDegree: \"nzGapDegree\",\n        nzStatus: \"nzStatus\",\n        nzType: \"nzType\",\n        nzGapPosition: \"nzGapPosition\",\n        nzStrokeLinecap: \"nzStrokeLinecap\",\n        nzSteps: \"nzSteps\"\n      },\n      exportAs: [\"nzProgress\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 17,\n      consts: [[\"progressInfoTemplate\", \"\"], [3, \"ngClass\"], [1, \"ant-progress-inner\", 3, \"width\", \"height\", \"fontSize\", \"ant-progress-circle-gradient\"], [1, \"ant-progress-text\"], [\"nz-icon\", \"\", 3, \"nzType\"], [4, \"nzStringTemplateOutlet\", \"nzStringTemplateOutletContext\"], [1, \"ant-progress-steps-outer\"], [1, \"ant-progress-steps-item\", 3, \"ngStyle\"], [3, \"ngTemplateOutlet\"], [1, \"ant-progress-outer\"], [1, \"ant-progress-inner\"], [1, \"ant-progress-bg\"], [1, \"ant-progress-success-bg\", 3, \"width\", \"border-radius\", \"height\"], [1, \"ant-progress-success-bg\"], [\"viewBox\", \"0 0 100 100\", 1, \"ant-progress-circle\"], [\"stroke\", \"#f3f3f3\", \"fill-opacity\", \"0\", 1, \"ant-progress-circle-trail\", 3, \"ngStyle\"], [\"fill-opacity\", \"0\", 1, \"ant-progress-circle-path\", 3, \"ngStyle\"], [\"x1\", \"100%\", \"y1\", \"0%\", \"x2\", \"0%\", \"y2\", \"0%\", 3, \"id\"]],\n      template: function NzProgressComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzProgressComponent_ng_template_0_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n          i0.ɵɵelementStart(2, \"div\", 1);\n          i0.ɵɵtemplate(3, NzProgressComponent_Conditional_3_Template, 3, 1, \"div\")(4, NzProgressComponent_Conditional_4_Template, 7, 13, \"div\", 2);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"ant-progress-line\", ctx.nzType === \"line\")(\"ant-progress-small\", ctx.nzSize === \"small\")(\"ant-progress-default\", ctx.nzSize === \"default\")(\"ant-progress-show-info\", ctx.nzShowInfo)(\"ant-progress-circle\", ctx.isCircleStyle)(\"ant-progress-steps\", ctx.isSteps)(\"ant-progress-rtl\", ctx.dir === \"rtl\");\n          i0.ɵɵproperty(\"ngClass\", \"ant-progress ant-progress-status-\" + ctx.status);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(3, ctx.nzType === \"line\" ? 3 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(4, ctx.isCircleStyle ? 4 : -1);\n        }\n      },\n      dependencies: [NzIconModule, i3.NzIconDirective, NzOutletModule, i4.NzStringTemplateOutletDirective, NgClass, NgTemplateOutlet, NgStyle],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzShowInfo\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzStrokeColor\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzSize\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzSuccessPercent\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzPercent\", void 0);\n__decorate([WithConfig(), InputNumber()], NzProgressComponent.prototype, \"nzStrokeWidth\", void 0);\n__decorate([WithConfig(), InputNumber()], NzProgressComponent.prototype, \"nzGapDegree\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzGapPosition\", void 0);\n__decorate([WithConfig()], NzProgressComponent.prototype, \"nzStrokeLinecap\", void 0);\n__decorate([InputNumber()], NzProgressComponent.prototype, \"nzSteps\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressComponent, [{\n    type: Component,\n    args: [{\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      selector: 'nz-progress',\n      exportAs: 'nzProgress',\n      preserveWhitespaces: false,\n      standalone: true,\n      imports: [NzIconModule, NzOutletModule, NgClass, NgTemplateOutlet, NgStyle],\n      template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <span nz-icon [nzType]=\"icon\"></span>\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [ngClass]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track step) {\n                <div class=\"ant-progress-steps-item\" [ngStyle]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track i) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [ngStyle]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track p) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [ngStyle]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzShowInfo: [{\n      type: Input\n    }],\n    nzWidth: [{\n      type: Input\n    }],\n    nzStrokeColor: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzFormat: [{\n      type: Input\n    }],\n    nzSuccessPercent: [{\n      type: Input\n    }],\n    nzPercent: [{\n      type: Input\n    }],\n    nzStrokeWidth: [{\n      type: Input\n    }],\n    nzGapDegree: [{\n      type: Input\n    }],\n    nzStatus: [{\n      type: Input\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzGapPosition: [{\n      type: Input\n    }],\n    nzStrokeLinecap: [{\n      type: Input\n    }],\n    nzSteps: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzProgressModule {\n  static {\n    this.ɵfac = function NzProgressModule_Factory(t) {\n      return new (t || NzProgressModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzProgressModule,\n      imports: [NzProgressComponent],\n      exports: [NzProgressComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzProgressComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzProgressModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzProgressComponent],\n      exports: [NzProgressComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzProgressComponent, NzProgressModule };", "map": {"version": 3, "names": ["i0", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Input", "NgModule", "__decorate", "Ng<PERSON><PERSON>", "NgTemplateOutlet", "NgStyle", "Subject", "takeUntil", "i1", "WithConfig", "i4", "NzOutletModule", "isNotNil", "InputNumber", "i3", "NzIconModule", "i2", "_c0", "a0", "$implicit", "NzProgressComponent_ng_template_0_Conditional_0_Conditional_1_Template", "rf", "ctx", "ɵɵelement", "ctx_r0", "ɵɵnextContext", "ɵɵproperty", "icon", "NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_ng_container_0_Template", "ɵɵelementContainerStart", "ɵɵtext", "ɵɵelementContainerEnd", "formatter_r2", "ɵɵadvance", "ɵɵtextInterpolate1", "nzPercent", "NzProgressComponent_ng_template_0_Conditional_0_Conditional_2_Template", "ɵɵtemplate", "formatter", "ɵɵpureFunction1", "NzProgressComponent_ng_template_0_Conditional_0_Template", "ɵɵelementStart", "ɵɵelementEnd", "ɵɵconditional", "status", "nzFormat", "NzProgressComponent_ng_template_0_Template", "nzShowInfo", "NzProgressComponent_Conditional_3_Conditional_1_For_2_Template", "step_r3", "NzProgressComponent_Conditional_3_Conditional_1_ng_template_3_Template", "NzProgressComponent_Conditional_3_Conditional_1_Template", "ɵɵrepeaterCreate", "ɵɵrepeaterTrackByIdentity", "progressInfoTemplate_r4", "ɵɵreference", "ɵɵrepeater", "steps", "NzProgressComponent_Conditional_3_Conditional_2_Conditional_3_Template", "ɵɵstyleProp", "nzSuccessPercent", "nzStrokeLinecap", "strokeWidth", "NzProgressComponent_Conditional_3_Conditional_2_ng_template_4_Template", "NzProgressComponent_Conditional_3_Conditional_2_Template", "isGradient", "nzStrokeColor", "lineGradient", "NzProgressComponent_Conditional_3_Template", "isSteps", "NzProgressComponent_Conditional_4_Conditional_2_For_3_Template", "ɵɵnamespaceSVG", "i_r5", "ɵɵattribute", "offset", "color", "NzProgressComponent_Conditional_4_Conditional_2_Template", "gradientId", "circleGradient", "NzProgressComponent_Conditional_4_For_5_Template", "p_r6", "strokePathStyle", "pathString", "stroke", "NzProgressComponent_Conditional_4_ng_template_6_Template", "NzProgressComponent_Conditional_4_Template", "nzWidth", "ɵɵclassProp", "trailPathStyle", "progressCirclePath", "stripPercentToNumber", "percent", "replace", "sortGradient", "gradients", "tempArr", "Object", "keys", "for<PERSON>ach", "key", "value", "formatKey", "isNaN", "push", "sort", "a", "b", "handleCircleGradient", "strokeColor", "map", "handleLinearGradient", "from", "to", "direction", "rest", "length", "sortedGradients", "join", "gradientIdSeed", "NZ_CONFIG_MODULE_NAME", "statusIconNameMap", "Map", "statusColorMap", "defaultFormatter", "p", "NzProgressComponent", "nzStatus", "inferredStatus", "nzStrokeWidth", "nzType", "nzSize", "isCircleStyle", "constructor", "cdr", "nzConfigService", "directionality", "_nzModuleName", "undefined", "nzGapDegree", "nzGapPosition", "nzSteps", "dir", "cachedStatus", "destroy$", "ngOnChanges", "changes", "fillAll", "parseInt", "toString", "updateIcon", "setStrokeColor", "getCirclePaths", "getSteps", "ngOnInit", "getConfigChangeEventForComponent", "pipe", "subscribe", "change", "detectChanges", "ngOnDestroy", "next", "complete", "ret", "get", "current", "Math", "floor", "<PERSON><PERSON><PERSON><PERSON>", "i", "step<PERSON>tyle", "backgroundColor", "width", "height", "values", "radius", "gapPosition", "len", "PI", "gapDegree", "beginPositionX", "beginPositionY", "endPositionX", "endPositionY", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "transition", "index", "isSuccessPercent", "reverse", "ɵfac", "NzProgressComponent_Factory", "t", "ɵɵdirectiveInject", "ChangeDetectorRef", "NzConfigService", "Directionality", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "inputs", "exportAs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NzProgressComponent_Template", "ɵɵtemplateRefExtractor", "dependencies", "NzIconDirective", "NzStringTemplateOutletDirective", "encapsulation", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "OnPush", "None", "selector", "preserveWhitespaces", "imports", "decorators", "NzProgressModule", "NzProgressModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-progress.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { NgClass, NgTemplateOutlet, NgStyle } from '@angular/common';\nimport { Subject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport { isNotNil, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nfunction stripPercentToNumber(percent) {\n    return +percent.replace('%', '');\n}\nconst sortGradient = (gradients) => {\n    let tempArr = [];\n    Object.keys(gradients).forEach(key => {\n        const value = gradients[key];\n        const formatKey = stripPercentToNumber(key);\n        if (!isNaN(formatKey)) {\n            tempArr.push({\n                key: formatKey,\n                value\n            });\n        }\n    });\n    tempArr = tempArr.sort((a, b) => a.key - b.key);\n    return tempArr;\n};\nconst handleCircleGradient = (strokeColor) => sortGradient(strokeColor).map(({ key, value }) => ({ offset: `${key}%`, color: value }));\nconst handleLinearGradient = (strokeColor) => {\n    const { from = '#1890ff', to = '#1890ff', direction = 'to right', ...rest } = strokeColor;\n    if (Object.keys(rest).length !== 0) {\n        const sortedGradients = sortGradient(rest)\n            .map(({ key, value }) => `${value} ${key}%`)\n            .join(', ');\n        return `linear-gradient(${direction}, ${sortedGradients})`;\n    }\n    return `linear-gradient(${direction}, ${from}, ${to})`;\n};\n\nlet gradientIdSeed = 0;\nconst NZ_CONFIG_MODULE_NAME = 'progress';\nconst statusIconNameMap = new Map([\n    ['success', 'check'],\n    ['exception', 'close']\n]);\nconst statusColorMap = new Map([\n    ['normal', '#108ee9'],\n    ['exception', '#ff5500'],\n    ['success', '#87d068']\n]);\nconst defaultFormatter = (p) => `${p}%`;\nclass NzProgressComponent {\n    get formatter() {\n        return this.nzFormat || defaultFormatter;\n    }\n    get status() {\n        return this.nzStatus || this.inferredStatus;\n    }\n    get strokeWidth() {\n        return this.nzStrokeWidth || (this.nzType === 'line' && this.nzSize !== 'small' ? 8 : 6);\n    }\n    get isCircleStyle() {\n        return this.nzType === 'circle' || this.nzType === 'dashboard';\n    }\n    constructor(cdr, nzConfigService, directionality) {\n        this.cdr = cdr;\n        this.nzConfigService = nzConfigService;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.nzShowInfo = true;\n        this.nzWidth = 132;\n        this.nzStrokeColor = undefined;\n        this.nzSize = 'default';\n        this.nzPercent = 0;\n        this.nzStrokeWidth = undefined;\n        this.nzGapDegree = undefined;\n        this.nzType = 'line';\n        this.nzGapPosition = 'top';\n        this.nzStrokeLinecap = 'round';\n        this.nzSteps = 0;\n        this.steps = [];\n        /** Gradient style when `nzType` is `line`. */\n        this.lineGradient = null;\n        /** If user uses gradient color. */\n        this.isGradient = false;\n        /** If the linear progress is a step progress. */\n        this.isSteps = false;\n        /**\n         * Each progress whose `nzType` is circle or dashboard should have unique id to\n         * define `<linearGradient>`.\n         */\n        this.gradientId = gradientIdSeed++;\n        /** Paths to rendered in the template. */\n        this.progressCirclePath = [];\n        this.trailPathStyle = null;\n        this.dir = 'ltr';\n        this.cachedStatus = 'normal';\n        this.inferredStatus = 'normal';\n        this.destroy$ = new Subject();\n    }\n    ngOnChanges(changes) {\n        const { nzSteps, nzGapPosition, nzStrokeLinecap, nzStrokeColor, nzGapDegree, nzType, nzStatus, nzPercent, nzSuccessPercent, nzStrokeWidth } = changes;\n        if (nzStatus) {\n            this.cachedStatus = this.nzStatus || this.cachedStatus;\n        }\n        if (nzPercent || nzSuccessPercent) {\n            const fillAll = parseInt(this.nzPercent.toString(), 10) >= 100;\n            if (fillAll) {\n                if ((isNotNil(this.nzSuccessPercent) && this.nzSuccessPercent >= 100) || this.nzSuccessPercent === undefined) {\n                    this.inferredStatus = 'success';\n                }\n            }\n            else {\n                this.inferredStatus = this.cachedStatus;\n            }\n        }\n        if (nzStatus || nzPercent || nzSuccessPercent || nzStrokeColor) {\n            this.updateIcon();\n        }\n        if (nzStrokeColor) {\n            this.setStrokeColor();\n        }\n        if (nzGapPosition || nzStrokeLinecap || nzGapDegree || nzType || nzPercent || nzStrokeColor || nzStrokeColor) {\n            this.getCirclePaths();\n        }\n        if (nzPercent || nzSteps || nzStrokeWidth) {\n            this.isSteps = this.nzSteps > 0;\n            if (this.isSteps) {\n                this.getSteps();\n            }\n        }\n    }\n    ngOnInit() {\n        this.nzConfigService\n            .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(() => {\n            this.updateIcon();\n            this.setStrokeColor();\n            this.getCirclePaths();\n        });\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    updateIcon() {\n        const ret = statusIconNameMap.get(this.status);\n        this.icon = ret ? ret + (this.isCircleStyle ? '-o' : '-circle-fill') : '';\n    }\n    /**\n     * Calculate step render configs.\n     */\n    getSteps() {\n        const current = Math.floor(this.nzSteps * (this.nzPercent / 100));\n        const stepWidth = this.nzSize === 'small' ? 2 : 14;\n        const steps = [];\n        for (let i = 0; i < this.nzSteps; i++) {\n            let color;\n            if (i <= current - 1) {\n                color = this.nzStrokeColor;\n            }\n            const stepStyle = {\n                backgroundColor: `${color}`,\n                width: `${stepWidth}px`,\n                height: `${this.strokeWidth}px`\n            };\n            steps.push(stepStyle);\n        }\n        this.steps = steps;\n    }\n    /**\n     * Calculate paths when the type is circle or dashboard.\n     */\n    getCirclePaths() {\n        if (!this.isCircleStyle) {\n            return;\n        }\n        const values = isNotNil(this.nzSuccessPercent) ? [this.nzSuccessPercent, this.nzPercent] : [this.nzPercent];\n        // Calculate shared styles.\n        const radius = 50 - this.strokeWidth / 2;\n        const gapPosition = this.nzGapPosition || (this.nzType === 'circle' ? 'top' : 'bottom');\n        const len = Math.PI * 2 * radius;\n        const gapDegree = this.nzGapDegree || (this.nzType === 'circle' ? 0 : 75);\n        let beginPositionX = 0;\n        let beginPositionY = -radius;\n        let endPositionX = 0;\n        let endPositionY = radius * -2;\n        switch (gapPosition) {\n            case 'left':\n                beginPositionX = -radius;\n                beginPositionY = 0;\n                endPositionX = radius * 2;\n                endPositionY = 0;\n                break;\n            case 'right':\n                beginPositionX = radius;\n                beginPositionY = 0;\n                endPositionX = radius * -2;\n                endPositionY = 0;\n                break;\n            case 'bottom':\n                beginPositionY = radius;\n                endPositionY = radius * 2;\n                break;\n            default:\n        }\n        this.pathString = `M 50,50 m ${beginPositionX},${beginPositionY}\n       a ${radius},${radius} 0 1 1 ${endPositionX},${-endPositionY}\n       a ${radius},${radius} 0 1 1 ${-endPositionX},${endPositionY}`;\n        this.trailPathStyle = {\n            strokeDasharray: `${len - gapDegree}px ${len}px`,\n            strokeDashoffset: `-${gapDegree / 2}px`,\n            transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s'\n        };\n        // Calculate styles for each path.\n        this.progressCirclePath = values\n            .map((value, index) => {\n            const isSuccessPercent = values.length === 2 && index === 0;\n            return {\n                stroke: this.isGradient && !isSuccessPercent ? `url(#gradient-${this.gradientId})` : null,\n                strokePathStyle: {\n                    stroke: !this.isGradient\n                        ? isSuccessPercent\n                            ? statusColorMap.get('success')\n                            : this.nzStrokeColor\n                        : null,\n                    transition: 'stroke-dashoffset .3s ease 0s, stroke-dasharray .3s ease 0s, stroke .3s, stroke-width .06s ease .3s',\n                    strokeDasharray: `${((value || 0) / 100) * (len - gapDegree)}px ${len}px`,\n                    strokeDashoffset: `-${gapDegree / 2}px`\n                }\n            };\n        })\n            .reverse();\n    }\n    setStrokeColor() {\n        const color = this.nzStrokeColor;\n        const isGradient = (this.isGradient = !!color && typeof color !== 'string');\n        if (isGradient && !this.isCircleStyle) {\n            this.lineGradient = handleLinearGradient(color);\n        }\n        else if (isGradient && this.isCircleStyle) {\n            this.circleGradient = handleCircleGradient(this.nzStrokeColor);\n        }\n        else {\n            this.lineGradient = null;\n            this.circleGradient = [];\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i1.NzConfigService }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzProgressComponent, isStandalone: true, selector: \"nz-progress\", inputs: { nzShowInfo: \"nzShowInfo\", nzWidth: \"nzWidth\", nzStrokeColor: \"nzStrokeColor\", nzSize: \"nzSize\", nzFormat: \"nzFormat\", nzSuccessPercent: \"nzSuccessPercent\", nzPercent: \"nzPercent\", nzStrokeWidth: \"nzStrokeWidth\", nzGapDegree: \"nzGapDegree\", nzStatus: \"nzStatus\", nzType: \"nzType\", nzGapPosition: \"nzGapPosition\", nzStrokeLinecap: \"nzStrokeLinecap\", nzSteps: \"nzSteps\" }, exportAs: [\"nzProgress\"], usesOnChanges: true, ngImport: i0, template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <span nz-icon [nzType]=\"icon\"></span>\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [ngClass]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track step) {\n                <div class=\"ant-progress-steps-item\" [ngStyle]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track i) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [ngStyle]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track p) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [ngStyle]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i3.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i4.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    WithConfig()\n], NzProgressComponent.prototype, \"nzShowInfo\", void 0);\n__decorate([\n    WithConfig()\n], NzProgressComponent.prototype, \"nzStrokeColor\", void 0);\n__decorate([\n    WithConfig()\n], NzProgressComponent.prototype, \"nzSize\", void 0);\n__decorate([\n    InputNumber()\n], NzProgressComponent.prototype, \"nzSuccessPercent\", void 0);\n__decorate([\n    InputNumber()\n], NzProgressComponent.prototype, \"nzPercent\", void 0);\n__decorate([\n    WithConfig(),\n    InputNumber()\n], NzProgressComponent.prototype, \"nzStrokeWidth\", void 0);\n__decorate([\n    WithConfig(),\n    InputNumber()\n], NzProgressComponent.prototype, \"nzGapDegree\", void 0);\n__decorate([\n    WithConfig()\n], NzProgressComponent.prototype, \"nzGapPosition\", void 0);\n__decorate([\n    WithConfig()\n], NzProgressComponent.prototype, \"nzStrokeLinecap\", void 0);\n__decorate([\n    InputNumber()\n], NzProgressComponent.prototype, \"nzSteps\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressComponent, decorators: [{\n            type: Component,\n            args: [{\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    selector: 'nz-progress',\n                    exportAs: 'nzProgress',\n                    preserveWhitespaces: false,\n                    standalone: true,\n                    imports: [NzIconModule, NzOutletModule, NgClass, NgTemplateOutlet, NgStyle],\n                    template: `\n    <ng-template #progressInfoTemplate>\n      @if (nzShowInfo) {\n        <span class=\"ant-progress-text\">\n          @if ((status === 'exception' || status === 'success') && !nzFormat) {\n            <span nz-icon [nzType]=\"icon\"></span>\n          } @else {\n            <ng-container *nzStringTemplateOutlet=\"formatter; context: { $implicit: nzPercent }; let formatter\">\n              {{ formatter(nzPercent) }}\n            </ng-container>\n          }\n        </span>\n      }\n    </ng-template>\n\n    <div\n      [ngClass]=\"'ant-progress ant-progress-status-' + status\"\n      [class.ant-progress-line]=\"nzType === 'line'\"\n      [class.ant-progress-small]=\"nzSize === 'small'\"\n      [class.ant-progress-default]=\"nzSize === 'default'\"\n      [class.ant-progress-show-info]=\"nzShowInfo\"\n      [class.ant-progress-circle]=\"isCircleStyle\"\n      [class.ant-progress-steps]=\"isSteps\"\n      [class.ant-progress-rtl]=\"dir === 'rtl'\"\n    >\n      @if (nzType === 'line') {\n        <div>\n          <!-- normal line style -->\n          @if (isSteps) {\n            <div class=\"ant-progress-steps-outer\">\n              @for (step of steps; track step) {\n                <div class=\"ant-progress-steps-item\" [ngStyle]=\"step\"></div>\n              }\n              <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n            </div>\n          } @else {\n            <div class=\"ant-progress-outer\">\n              <div class=\"ant-progress-inner\">\n                <div\n                  class=\"ant-progress-bg\"\n                  [style.width.%]=\"nzPercent\"\n                  [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                  [style.background]=\"!isGradient ? nzStrokeColor : null\"\n                  [style.background-image]=\"isGradient ? lineGradient : null\"\n                  [style.height.px]=\"strokeWidth\"\n                ></div>\n                @if (nzSuccessPercent || nzSuccessPercent === 0) {\n                  <div\n                    class=\"ant-progress-success-bg\"\n                    [style.width.%]=\"nzSuccessPercent\"\n                    [style.border-radius]=\"nzStrokeLinecap === 'round' ? '100px' : '0'\"\n                    [style.height.px]=\"strokeWidth\"\n                  ></div>\n                }\n              </div>\n            </div>\n            <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n          }\n        </div>\n      }\n      <!-- line progress -->\n\n      <!-- circle / dashboard progress -->\n\n      @if (isCircleStyle) {\n        <div\n          [style.width.px]=\"this.nzWidth\"\n          [style.height.px]=\"this.nzWidth\"\n          [style.fontSize.px]=\"this.nzWidth * 0.15 + 6\"\n          class=\"ant-progress-inner\"\n          [class.ant-progress-circle-gradient]=\"isGradient\"\n        >\n          <svg class=\"ant-progress-circle \" viewBox=\"0 0 100 100\">\n            @if (isGradient) {\n              <defs>\n                <linearGradient [id]=\"'gradient-' + gradientId\" x1=\"100%\" y1=\"0%\" x2=\"0%\" y2=\"0%\">\n                  @for (i of circleGradient; track i) {\n                    <stop [attr.offset]=\"i.offset\" [attr.stop-color]=\"i.color\"></stop>\n                  }\n                </linearGradient>\n              </defs>\n            }\n\n            <path\n              class=\"ant-progress-circle-trail\"\n              stroke=\"#f3f3f3\"\n              fill-opacity=\"0\"\n              [attr.stroke-width]=\"strokeWidth\"\n              [attr.d]=\"pathString\"\n              [ngStyle]=\"trailPathStyle\"\n            ></path>\n            @for (p of progressCirclePath; track p) {\n              <path\n                class=\"ant-progress-circle-path\"\n                fill-opacity=\"0\"\n                [attr.d]=\"pathString\"\n                [attr.stroke-linecap]=\"nzStrokeLinecap\"\n                [attr.stroke]=\"p.stroke\"\n                [attr.stroke-width]=\"nzPercent ? strokeWidth : 0\"\n                [ngStyle]=\"p.strokePathStyle\"\n              ></path>\n            }\n          </svg>\n          <ng-template [ngTemplateOutlet]=\"progressInfoTemplate\" />\n        </div>\n      }\n    </div>\n  `\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i1.NzConfigService }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzShowInfo: [{\n                type: Input\n            }], nzWidth: [{\n                type: Input\n            }], nzStrokeColor: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzFormat: [{\n                type: Input\n            }], nzSuccessPercent: [{\n                type: Input\n            }], nzPercent: [{\n                type: Input\n            }], nzStrokeWidth: [{\n                type: Input\n            }], nzGapDegree: [{\n                type: Input\n            }], nzStatus: [{\n                type: Input\n            }], nzType: [{\n                type: Input\n            }], nzGapPosition: [{\n                type: Input\n            }], nzStrokeLinecap: [{\n                type: Input\n            }], nzSteps: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzProgressModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressModule, imports: [NzProgressComponent], exports: [NzProgressComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressModule, imports: [NzProgressComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzProgressModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzProgressComponent],\n                    exports: [NzProgressComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzProgressComponent, NzProgressModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAChH,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,QAAQ,iBAAiB;AACpE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,SAASC,QAAQ,EAAEC,WAAW,QAAQ,yBAAyB;AAC/D,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;;AAEvC;AACA;AACA;AACA;AAHA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,SAAAE,uEAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAyPoG1B,EAAE,CAAA4B,SAAA,aAMtD,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GANmD7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,UAAA,WAAAF,MAAA,CAAAG,IAM9D,CAAC;EAAA;AAAA;AAAA,SAAAC,sFAAAP,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAN2D1B,EAAE,CAAAkC,uBAAA,EAQS,CAAC;IARZlC,EAAE,CAAAmC,MAAA,EAU3F,CAAC;IAVwFnC,EAAE,CAAAoC,qBAAA;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAAW,YAAA,GAAAV,GAAA,CAAAH,SAAA;IAAA,MAAAK,MAAA,GAAF7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAsC,SAAA,CAU3F,CAAC;IAVwFtC,EAAE,CAAAuC,kBAAA,MAAAF,YAAA,CAAAR,MAAA,CAAAW,SAAA,MAU3F,CAAC;EAAA;AAAA;AAAA,SAAAC,uEAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAVwF1B,EAAE,CAAA0C,UAAA,IAAAT,qFAAA,yBAQS,CAAC;EAAA;EAAA,IAAAP,EAAA;IAAA,MAAAG,MAAA,GARZ7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,UAAA,2BAAAF,MAAA,CAAAc,SAQzC,CAAC,kCARsC3C,EAAE,CAAA4C,eAAA,IAAAtB,GAAA,EAAAO,MAAA,CAAAW,SAAA,CAQN,CAAC;EAAA;AAAA;AAAA,SAAAK,yDAAAnB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IARG1B,EAAE,CAAA8C,cAAA,aAI/D,CAAC;IAJ4D9C,EAAE,CAAA0C,UAAA,IAAAjB,sEAAA,iBAKxB,CAAC,IAAAgB,sEAAA,MAE7D,CAAC;IAPiFzC,EAAE,CAAA+C,YAAA,CAYxF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAZqF7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAsC,SAAA,CAW5F,CAAC;IAXyFtC,EAAE,CAAAgD,aAAA,KAAAnB,MAAA,CAAAoB,MAAA,oBAAApB,MAAA,CAAAoB,MAAA,oBAAApB,MAAA,CAAAqB,QAAA,QAW5F,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAzB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAXyF1B,EAAE,CAAA0C,UAAA,IAAAG,wDAAA,iBAG/E,CAAC;EAAA;EAAA,IAAAnB,EAAA;IAAA,MAAAG,MAAA,GAH4E7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAgD,aAAA,IAAAnB,MAAA,CAAAuB,UAAA,SAahG,CAAC;EAAA;AAAA;AAAA,SAAAC,+DAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAb6F1B,EAAE,CAAA4B,SAAA,YAgC3B,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAA4B,OAAA,GAAA3B,GAAA,CAAAH,SAAA;IAhCwBxB,EAAE,CAAA+B,UAAA,YAAAuB,OAgClC,CAAC;EAAA;AAAA;AAAA,SAAAC,uEAAA7B,EAAA,EAAAC,GAAA;AAAA,SAAA6B,yDAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhC+B1B,EAAE,CAAA8C,cAAA,YA8BrD,CAAC;IA9BkD9C,EAAE,CAAAyD,gBAAA,IAAAJ,8DAAA,kBAAFrD,EAAE,CAAA0D,yBAiCxF,CAAC;IAjCqF1D,EAAE,CAAA0C,UAAA,IAAAa,sEAAA,wBAkChC,CAAC;IAlC6BvD,EAAE,CAAA+C,YAAA,CAmCrF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAnCkF7B,EAAE,CAAA8B,aAAA;IAAA,MAAA6B,uBAAA,GAAF3D,EAAE,CAAA4D,WAAA;IAAF5D,EAAE,CAAAsC,SAAA,CAiCxF,CAAC;IAjCqFtC,EAAE,CAAA6D,UAAA,CAAAhC,MAAA,CAAAiC,KAiCxF,CAAC;IAjCqF9D,EAAE,CAAAsC,SAAA,EAkCnC,CAAC;IAlCgCtC,EAAE,CAAA+B,UAAA,qBAAA4B,uBAkCnC,CAAC;EAAA;AAAA;AAAA,SAAAI,uEAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAlCgC1B,EAAE,CAAA4B,SAAA,aAqD9E,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAG,MAAA,GArD2E7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAgE,WAAA,UAAAnC,MAAA,CAAAoC,gBAAA,KAkDjD,CAAC,kBAAApC,MAAA,CAAAqC,eAAA,4BACgC,CAAC,WAAArC,MAAA,CAAAsC,WAAA,MACrC,CAAC;EAAA;AAAA;AAAA,SAAAC,uEAAA1C,EAAA,EAAAC,GAAA;AAAA,SAAA0C,yDAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApDiD1B,EAAE,CAAA8C,cAAA,YAqC3D,CAAC,aACC,CAAC;IAtCsD9C,EAAE,CAAA4B,SAAA,aA8ChF,CAAC;IA9C6E5B,EAAE,CAAA0C,UAAA,IAAAqB,sEAAA,iBA+CrC,CAAC;IA/CkC/D,EAAE,CAAA+C,YAAA,CAuDnF,CAAC,CACH,CAAC;IAxDkF/C,EAAE,CAAA0C,UAAA,IAAA0B,sEAAA,wBAyDlC,CAAC;EAAA;EAAA,IAAA1C,EAAA;IAAA,MAAAG,MAAA,GAzD+B7B,EAAE,CAAA8B,aAAA;IAAA,MAAA6B,uBAAA,GAAF3D,EAAE,CAAA4D,WAAA;IAAF5D,EAAE,CAAAsC,SAAA,EAyC1D,CAAC;IAzCuDtC,EAAE,CAAAgE,WAAA,UAAAnC,MAAA,CAAAW,SAAA,KAyC1D,CAAC,kBAAAX,MAAA,CAAAqC,eAAA,4BACuC,CAAC,gBAAArC,MAAA,CAAAyC,UAAA,GAAAzC,MAAA,CAAA0C,aAAA,OACb,CAAC,qBAAA1C,MAAA,CAAAyC,UAAA,GAAAzC,MAAA,CAAA2C,YAAA,OACG,CAAC,WAAA3C,MAAA,CAAAsC,WAAA,MAC7B,CAAC;IA7CmDnE,EAAE,CAAAsC,SAAA,CAsDtF,CAAC;IAtDmFtC,EAAE,CAAAgD,aAAA,IAAAnB,MAAA,CAAAoC,gBAAA,IAAApC,MAAA,CAAAoC,gBAAA,eAsDtF,CAAC;IAtDmFjE,EAAE,CAAAsC,SAAA,CAyDrC,CAAC;IAzDkCtC,EAAE,CAAA+B,UAAA,qBAAA4B,uBAyDrC,CAAC;EAAA;AAAA;AAAA,SAAAc,2CAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzDkC1B,EAAE,CAAA8C,cAAA,SA2B1F,CAAC;IA3BuF9C,EAAE,CAAA0C,UAAA,IAAAc,wDAAA,gBA6B9E,CAAC,IAAAa,wDAAA,OAOP,CAAC;IApCiFrE,EAAE,CAAA+C,YAAA,CA2DzF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GA3DsF7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAsC,SAAA,CA0D5F,CAAC;IA1DyFtC,EAAE,CAAAgD,aAAA,IAAAnB,MAAA,CAAA6C,OAAA,QA0D5F,CAAC;EAAA;AAAA;AAAA,SAAAC,+DAAAjD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1DyF1B,EAAE,CAAA4E,cAAA;IAAF5E,EAAE,CAAA4B,SAAA,UA8EjB,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAAmD,IAAA,GAAAlD,GAAA,CAAAH,SAAA;IA9EcxB,EAAE,CAAA8E,WAAA,WAAAD,IAAA,CAAAE,MAAA,gBAAAF,IAAA,CAAAG,KAAA;EAAA;AAAA;AAAA,SAAAC,yDAAAvD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF1B,EAAE,CAAA4E,cAAA;IAAF5E,EAAE,CAAA8C,cAAA,UA2EnF,CAAC,wBAC6E,CAAC;IA5EE9C,EAAE,CAAAyD,gBAAA,IAAAkB,8DAAA,2BAAF3E,EAAE,CAAA0D,yBA+EpF,CAAC;IA/EiF1D,EAAE,CAAA+C,YAAA,CAgFtE,CAAC,CACb,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAjF+E7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAAsC,SAAA,CA4ExC,CAAC;IA5EqCtC,EAAE,CAAA+B,UAAA,qBAAAF,MAAA,CAAAqD,UA4ExC,CAAC;IA5EqClF,EAAE,CAAAsC,SAAA,CA+EpF,CAAC;IA/EiFtC,EAAE,CAAA6D,UAAA,CAAAhC,MAAA,CAAAsD,cA+EpF,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/EiF1B,EAAE,CAAA4E,cAAA;IAAF5E,EAAE,CAAA4B,SAAA,cAqGjF,CAAC;EAAA;EAAA,IAAAF,EAAA;IAAA,MAAA2D,IAAA,GAAA1D,GAAA,CAAAH,SAAA;IAAA,MAAAK,MAAA,GArG8E7B,EAAE,CAAA8B,aAAA;IAAF9B,EAAE,CAAA+B,UAAA,YAAAsD,IAAA,CAAAC,eAoG1D,CAAC;IApGuDtF,EAAE,CAAA8E,WAAA,MAAAjD,MAAA,CAAA0D,UAAA,oBAAA1D,MAAA,CAAAqC,eAAA,YAAAmB,IAAA,CAAAG,MAAA,kBAAA3D,MAAA,CAAAW,SAAA,GAAAX,MAAA,CAAAsC,WAAA;EAAA;AAAA;AAAA,SAAAsB,yDAAA/D,EAAA,EAAAC,GAAA;AAAA,SAAA+D,2CAAAhE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF1B,EAAE,CAAA8C,cAAA,aAwE9F,CAAC;IAxE2F9C,EAAE,CAAA4E,cAAA;IAAF5E,EAAE,CAAA8C,cAAA,aAyErC,CAAC;IAzEkC9C,EAAE,CAAA0C,UAAA,IAAAuC,wDAAA,mBA0EzE,CAAC;IA1EsEjF,EAAE,CAAA4B,SAAA,cA2FnF,CAAC;IA3FgF5B,EAAE,CAAAyD,gBAAA,IAAA2B,gDAAA,yBAAFpF,EAAE,CAAA0D,yBAsG1F,CAAC;IAtGuF1D,EAAE,CAAA+C,YAAA,CAuGvF,CAAC;IAvGoF/C,EAAE,CAAA0C,UAAA,IAAA+C,wDAAA,wBAwGpC,CAAC;IAxGiCzF,EAAE,CAAA+C,YAAA,CAyGzF,CAAC;EAAA;EAAA,IAAArB,EAAA;IAAA,MAAAG,MAAA,GAzGsF7B,EAAE,CAAA8B,aAAA;IAAA,MAAA6B,uBAAA,GAAF3D,EAAE,CAAA4D,WAAA;IAAF5D,EAAE,CAAAgE,WAAA,UAAAnC,MAAA,CAAA8D,OAAA,MAmE9D,CAAC,WAAA9D,MAAA,CAAA8D,OAAA,MACA,CAAC,cAAA9D,MAAA,CAAA8D,OAAA,iBACY,CAAC;IArE6C3F,EAAE,CAAA4F,WAAA,iCAAA/D,MAAA,CAAAyC,UAuE5C,CAAC;IAvEyCtE,EAAE,CAAAsC,SAAA,EAkF1F,CAAC;IAlFuFtC,EAAE,CAAAgD,aAAA,IAAAnB,MAAA,CAAAyC,UAAA,SAkF1F,CAAC;IAlFuFtE,EAAE,CAAAsC,SAAA,CA0F/D,CAAC;IA1F4DtC,EAAE,CAAA+B,UAAA,YAAAF,MAAA,CAAAgE,cA0F/D,CAAC;IA1F4D7F,EAAE,CAAA8E,WAAA,iBAAAjD,MAAA,CAAAsC,WAAA,OAAAtC,MAAA,CAAA0D,UAAA;IAAFvF,EAAE,CAAAsC,SAAA,CAsG1F,CAAC;IAtGuFtC,EAAE,CAAA6D,UAAA,CAAAhC,MAAA,CAAAiE,kBAsG1F,CAAC;IAtGuF9F,EAAE,CAAAsC,SAAA,EAwGvC,CAAC;IAxGoCtC,EAAE,CAAA+B,UAAA,qBAAA4B,uBAwGvC,CAAC;EAAA;AAAA;AA7VhE,SAASoC,oBAAoBA,CAACC,OAAO,EAAE;EACnC,OAAO,CAACA,OAAO,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;AACpC;AACA,MAAMC,YAAY,GAAIC,SAAS,IAAK;EAChC,IAAIC,OAAO,GAAG,EAAE;EAChBC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IAClC,MAAMC,KAAK,GAAGN,SAAS,CAACK,GAAG,CAAC;IAC5B,MAAME,SAAS,GAAGX,oBAAoB,CAACS,GAAG,CAAC;IAC3C,IAAI,CAACG,KAAK,CAACD,SAAS,CAAC,EAAE;MACnBN,OAAO,CAACQ,IAAI,CAAC;QACTJ,GAAG,EAAEE,SAAS;QACdD;MACJ,CAAC,CAAC;IACN;EACJ,CAAC,CAAC;EACFL,OAAO,GAAGA,OAAO,CAACS,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACN,GAAG,GAAGO,CAAC,CAACP,GAAG,CAAC;EAC/C,OAAOJ,OAAO;AAClB,CAAC;AACD,MAAMY,oBAAoB,GAAIC,WAAW,IAAKf,YAAY,CAACe,WAAW,CAAC,CAACC,GAAG,CAAC,CAAC;EAAEV,GAAG;EAAEC;AAAM,CAAC,MAAM;EAAE1B,MAAM,EAAG,GAAEyB,GAAI,GAAE;EAAExB,KAAK,EAAEyB;AAAM,CAAC,CAAC,CAAC;AACtI,MAAMU,oBAAoB,GAAIF,WAAW,IAAK;EAC1C,MAAM;IAAEG,IAAI,GAAG,SAAS;IAAEC,EAAE,GAAG,SAAS;IAAEC,SAAS,GAAG,UAAU;IAAE,GAAGC;EAAK,CAAC,GAAGN,WAAW;EACzF,IAAIZ,MAAM,CAACC,IAAI,CAACiB,IAAI,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IAChC,MAAMC,eAAe,GAAGvB,YAAY,CAACqB,IAAI,CAAC,CACrCL,GAAG,CAAC,CAAC;MAAEV,GAAG;MAAEC;IAAM,CAAC,KAAM,GAAEA,KAAM,IAAGD,GAAI,GAAE,CAAC,CAC3CkB,IAAI,CAAC,IAAI,CAAC;IACf,OAAQ,mBAAkBJ,SAAU,KAAIG,eAAgB,GAAE;EAC9D;EACA,OAAQ,mBAAkBH,SAAU,KAAIF,IAAK,KAAIC,EAAG,GAAE;AAC1D,CAAC;AAED,IAAIM,cAAc,GAAG,CAAC;AACtB,MAAMC,qBAAqB,GAAG,UAAU;AACxC,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAC,CAC9B,CAAC,SAAS,EAAE,OAAO,CAAC,EACpB,CAAC,WAAW,EAAE,OAAO,CAAC,CACzB,CAAC;AACF,MAAMC,cAAc,GAAG,IAAID,GAAG,CAAC,CAC3B,CAAC,QAAQ,EAAE,SAAS,CAAC,EACrB,CAAC,WAAW,EAAE,SAAS,CAAC,EACxB,CAAC,SAAS,EAAE,SAAS,CAAC,CACzB,CAAC;AACF,MAAME,gBAAgB,GAAIC,CAAC,IAAM,GAAEA,CAAE,GAAE;AACvC,MAAMC,mBAAmB,CAAC;EACtB,IAAIvF,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACO,QAAQ,IAAI8E,gBAAgB;EAC5C;EACA,IAAI/E,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAACkF,QAAQ,IAAI,IAAI,CAACC,cAAc;EAC/C;EACA,IAAIjE,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACkE,aAAa,KAAK,IAAI,CAACC,MAAM,KAAK,MAAM,IAAI,IAAI,CAACC,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;EAC5F;EACA,IAAIC,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACF,MAAM,KAAK,QAAQ,IAAI,IAAI,CAACA,MAAM,KAAK,WAAW;EAClE;EACAG,WAAWA,CAACC,GAAG,EAAEC,eAAe,EAAEC,cAAc,EAAE;IAC9C,IAAI,CAACF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGjB,qBAAqB;IAC1C,IAAI,CAACxE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACuC,OAAO,GAAG,GAAG;IAClB,IAAI,CAACpB,aAAa,GAAGuE,SAAS;IAC9B,IAAI,CAACP,MAAM,GAAG,SAAS;IACvB,IAAI,CAAC/F,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC6F,aAAa,GAAGS,SAAS;IAC9B,IAAI,CAACC,WAAW,GAAGD,SAAS;IAC5B,IAAI,CAACR,MAAM,GAAG,MAAM;IACpB,IAAI,CAACU,aAAa,GAAG,KAAK;IAC1B,IAAI,CAAC9E,eAAe,GAAG,OAAO;IAC9B,IAAI,CAAC+E,OAAO,GAAG,CAAC;IAChB,IAAI,CAACnF,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACU,YAAY,GAAG,IAAI;IACxB;IACA,IAAI,CAACF,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACI,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACQ,UAAU,GAAGyC,cAAc,EAAE;IAClC;IACA,IAAI,CAAC7B,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACD,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACqD,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,YAAY,GAAG,QAAQ;IAC5B,IAAI,CAACf,cAAc,GAAG,QAAQ;IAC9B,IAAI,CAACgB,QAAQ,GAAG,IAAIzI,OAAO,CAAC,CAAC;EACjC;EACA0I,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEL,OAAO;MAAED,aAAa;MAAE9E,eAAe;MAAEK,aAAa;MAAEwE,WAAW;MAAET,MAAM;MAAEH,QAAQ;MAAE3F,SAAS;MAAEyB,gBAAgB;MAAEoE;IAAc,CAAC,GAAGiB,OAAO;IACrJ,IAAInB,QAAQ,EAAE;MACV,IAAI,CAACgB,YAAY,GAAG,IAAI,CAAChB,QAAQ,IAAI,IAAI,CAACgB,YAAY;IAC1D;IACA,IAAI3G,SAAS,IAAIyB,gBAAgB,EAAE;MAC/B,MAAMsF,OAAO,GAAGC,QAAQ,CAAC,IAAI,CAAChH,SAAS,CAACiH,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG;MAC9D,IAAIF,OAAO,EAAE;QACT,IAAKtI,QAAQ,CAAC,IAAI,CAACgD,gBAAgB,CAAC,IAAI,IAAI,CAACA,gBAAgB,IAAI,GAAG,IAAK,IAAI,CAACA,gBAAgB,KAAK6E,SAAS,EAAE;UAC1G,IAAI,CAACV,cAAc,GAAG,SAAS;QACnC;MACJ,CAAC,MACI;QACD,IAAI,CAACA,cAAc,GAAG,IAAI,CAACe,YAAY;MAC3C;IACJ;IACA,IAAIhB,QAAQ,IAAI3F,SAAS,IAAIyB,gBAAgB,IAAIM,aAAa,EAAE;MAC5D,IAAI,CAACmF,UAAU,CAAC,CAAC;IACrB;IACA,IAAInF,aAAa,EAAE;MACf,IAAI,CAACoF,cAAc,CAAC,CAAC;IACzB;IACA,IAAIX,aAAa,IAAI9E,eAAe,IAAI6E,WAAW,IAAIT,MAAM,IAAI9F,SAAS,IAAI+B,aAAa,IAAIA,aAAa,EAAE;MAC1G,IAAI,CAACqF,cAAc,CAAC,CAAC;IACzB;IACA,IAAIpH,SAAS,IAAIyG,OAAO,IAAIZ,aAAa,EAAE;MACvC,IAAI,CAAC3D,OAAO,GAAG,IAAI,CAACuE,OAAO,GAAG,CAAC;MAC/B,IAAI,IAAI,CAACvE,OAAO,EAAE;QACd,IAAI,CAACmF,QAAQ,CAAC,CAAC;MACnB;IACJ;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACnB,eAAe,CACfoB,gCAAgC,CAACnC,qBAAqB,CAAC,CACvDoC,IAAI,CAACpJ,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAC9Ba,SAAS,CAAC,MAAM;MACjB,IAAI,CAACP,UAAU,CAAC,CAAC;MACjB,IAAI,CAACC,cAAc,CAAC,CAAC;MACrB,IAAI,CAACC,cAAc,CAAC,CAAC;IACzB,CAAC,CAAC;IACF,IAAI,CAAChB,cAAc,CAACsB,MAAM,EAAEF,IAAI,CAACpJ,SAAS,CAAC,IAAI,CAACwI,QAAQ,CAAC,CAAC,CAACa,SAAS,CAAE3C,SAAS,IAAK;MAChF,IAAI,CAAC4B,GAAG,GAAG5B,SAAS;MACpB,IAAI,CAACoB,GAAG,CAACyB,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACjB,GAAG,GAAG,IAAI,CAACN,cAAc,CAACnC,KAAK;EACxC;EACA2D,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,CAAC;IACpB,IAAI,CAACjB,QAAQ,CAACkB,QAAQ,CAAC,CAAC;EAC5B;EACAZ,UAAUA,CAAA,EAAG;IACT,MAAMa,GAAG,GAAG1C,iBAAiB,CAAC2C,GAAG,CAAC,IAAI,CAACvH,MAAM,CAAC;IAC9C,IAAI,CAACjB,IAAI,GAAGuI,GAAG,GAAGA,GAAG,IAAI,IAAI,CAAC/B,aAAa,GAAG,IAAI,GAAG,cAAc,CAAC,GAAG,EAAE;EAC7E;EACA;AACJ;AACA;EACIqB,QAAQA,CAAA,EAAG;IACP,MAAMY,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC1B,OAAO,IAAI,IAAI,CAACzG,SAAS,GAAG,GAAG,CAAC,CAAC;IACjE,MAAMoI,SAAS,GAAG,IAAI,CAACrC,MAAM,KAAK,OAAO,GAAG,CAAC,GAAG,EAAE;IAClD,MAAMzE,KAAK,GAAG,EAAE;IAChB,KAAK,IAAI+G,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5B,OAAO,EAAE4B,CAAC,EAAE,EAAE;MACnC,IAAI7F,KAAK;MACT,IAAI6F,CAAC,IAAIJ,OAAO,GAAG,CAAC,EAAE;QAClBzF,KAAK,GAAG,IAAI,CAACT,aAAa;MAC9B;MACA,MAAMuG,SAAS,GAAG;QACdC,eAAe,EAAG,GAAE/F,KAAM,EAAC;QAC3BgG,KAAK,EAAG,GAAEJ,SAAU,IAAG;QACvBK,MAAM,EAAG,GAAE,IAAI,CAAC9G,WAAY;MAChC,CAAC;MACDL,KAAK,CAAC8C,IAAI,CAACkE,SAAS,CAAC;IACzB;IACA,IAAI,CAAChH,KAAK,GAAGA,KAAK;EACtB;EACA;AACJ;AACA;EACI8F,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE;MACrB;IACJ;IACA,MAAM0C,MAAM,GAAGjK,QAAQ,CAAC,IAAI,CAACgD,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAACA,gBAAgB,EAAE,IAAI,CAACzB,SAAS,CAAC,GAAG,CAAC,IAAI,CAACA,SAAS,CAAC;IAC3G;IACA,MAAM2I,MAAM,GAAG,EAAE,GAAG,IAAI,CAAChH,WAAW,GAAG,CAAC;IACxC,MAAMiH,WAAW,GAAG,IAAI,CAACpC,aAAa,KAAK,IAAI,CAACV,MAAM,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC;IACvF,MAAM+C,GAAG,GAAGX,IAAI,CAACY,EAAE,GAAG,CAAC,GAAGH,MAAM;IAChC,MAAMI,SAAS,GAAG,IAAI,CAACxC,WAAW,KAAK,IAAI,CAACT,MAAM,KAAK,QAAQ,GAAG,CAAC,GAAG,EAAE,CAAC;IACzE,IAAIkD,cAAc,GAAG,CAAC;IACtB,IAAIC,cAAc,GAAG,CAACN,MAAM;IAC5B,IAAIO,YAAY,GAAG,CAAC;IACpB,IAAIC,YAAY,GAAGR,MAAM,GAAG,CAAC,CAAC;IAC9B,QAAQC,WAAW;MACf,KAAK,MAAM;QACPI,cAAc,GAAG,CAACL,MAAM;QACxBM,cAAc,GAAG,CAAC;QAClBC,YAAY,GAAGP,MAAM,GAAG,CAAC;QACzBQ,YAAY,GAAG,CAAC;QAChB;MACJ,KAAK,OAAO;QACRH,cAAc,GAAGL,MAAM;QACvBM,cAAc,GAAG,CAAC;QAClBC,YAAY,GAAGP,MAAM,GAAG,CAAC,CAAC;QAC1BQ,YAAY,GAAG,CAAC;QAChB;MACJ,KAAK,QAAQ;QACTF,cAAc,GAAGN,MAAM;QACvBQ,YAAY,GAAGR,MAAM,GAAG,CAAC;QACzB;MACJ;IACJ;IACA,IAAI,CAAC5F,UAAU,GAAI,aAAYiG,cAAe,IAAGC,cAAe;AACxE,WAAWN,MAAO,IAAGA,MAAO,UAASO,YAAa,IAAG,CAACC,YAAa;AACnE,WAAWR,MAAO,IAAGA,MAAO,UAAS,CAACO,YAAa,IAAGC,YAAa,EAAC;IAC5D,IAAI,CAAC9F,cAAc,GAAG;MAClB+F,eAAe,EAAG,GAAEP,GAAG,GAAGE,SAAU,MAAKF,GAAI,IAAG;MAChDQ,gBAAgB,EAAG,IAAGN,SAAS,GAAG,CAAE,IAAG;MACvCO,UAAU,EAAE;IAChB,CAAC;IACD;IACA,IAAI,CAAChG,kBAAkB,GAAGoF,MAAM,CAC3BhE,GAAG,CAAC,CAACT,KAAK,EAAEsF,KAAK,KAAK;MACvB,MAAMC,gBAAgB,GAAGd,MAAM,CAAC1D,MAAM,KAAK,CAAC,IAAIuE,KAAK,KAAK,CAAC;MAC3D,OAAO;QACHvG,MAAM,EAAE,IAAI,CAAClB,UAAU,IAAI,CAAC0H,gBAAgB,GAAI,iBAAgB,IAAI,CAAC9G,UAAW,GAAE,GAAG,IAAI;QACzFI,eAAe,EAAE;UACbE,MAAM,EAAE,CAAC,IAAI,CAAClB,UAAU,GAClB0H,gBAAgB,GACZjE,cAAc,CAACyC,GAAG,CAAC,SAAS,CAAC,GAC7B,IAAI,CAACjG,aAAa,GACtB,IAAI;UACVuH,UAAU,EAAE,qGAAqG;UACjHF,eAAe,EAAG,GAAG,CAACnF,KAAK,IAAI,CAAC,IAAI,GAAG,IAAK4E,GAAG,GAAGE,SAAS,CAAE,MAAKF,GAAI,IAAG;UACzEQ,gBAAgB,EAAG,IAAGN,SAAS,GAAG,CAAE;QACxC;MACJ,CAAC;IACL,CAAC,CAAC,CACGU,OAAO,CAAC,CAAC;EAClB;EACAtC,cAAcA,CAAA,EAAG;IACb,MAAM3E,KAAK,GAAG,IAAI,CAACT,aAAa;IAChC,MAAMD,UAAU,GAAI,IAAI,CAACA,UAAU,GAAG,CAAC,CAACU,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAS;IAC3E,IAAIV,UAAU,IAAI,CAAC,IAAI,CAACkE,aAAa,EAAE;MACnC,IAAI,CAAChE,YAAY,GAAG2C,oBAAoB,CAACnC,KAAK,CAAC;IACnD,CAAC,MACI,IAAIV,UAAU,IAAI,IAAI,CAACkE,aAAa,EAAE;MACvC,IAAI,CAACrD,cAAc,GAAG6B,oBAAoB,CAAC,IAAI,CAACzC,aAAa,CAAC;IAClE,CAAC,MACI;MACD,IAAI,CAACC,YAAY,GAAG,IAAI;MACxB,IAAI,CAACW,cAAc,GAAG,EAAE;IAC5B;EACJ;EACA;IAAS,IAAI,CAAC+G,IAAI,YAAAC,4BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFlE,mBAAmB,EAA7BlI,EAAE,CAAAqM,iBAAA,CAA6CrM,EAAE,CAACsM,iBAAiB,GAAnEtM,EAAE,CAAAqM,iBAAA,CAA8ExL,EAAE,CAAC0L,eAAe,GAAlGvM,EAAE,CAAAqM,iBAAA,CAA6GhL,EAAE,CAACmL,cAAc;IAAA,CAA4D;EAAE;EAC9R;IAAS,IAAI,CAACC,IAAI,kBAD8EzM,EAAE,CAAA0M,iBAAA;MAAAC,IAAA,EACJzE,mBAAmB;MAAA0E,SAAA;MAAAC,MAAA;QAAAzJ,UAAA;QAAAuC,OAAA;QAAApB,aAAA;QAAAgE,MAAA;QAAArF,QAAA;QAAAe,gBAAA;QAAAzB,SAAA;QAAA6F,aAAA;QAAAU,WAAA;QAAAZ,QAAA;QAAAG,MAAA;QAAAU,aAAA;QAAA9E,eAAA;QAAA+E,OAAA;MAAA;MAAA6D,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADjBhN,EAAE,CAAAiN,oBAAA,EAAFjN,EAAE,CAAAkN,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,6BAAA7L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1B,EAAE,CAAA0C,UAAA,IAAAS,0CAAA,gCAAFnD,EAAE,CAAAwN,sBAEhE,CAAC;UAF6DxN,EAAE,CAAA8C,cAAA,YAyBlG,CAAC;UAzB+F9C,EAAE,CAAA0C,UAAA,IAAA+B,0CAAA,aA0BxE,CAAC,IAAAiB,0CAAA,iBAuCL,CAAC;UAjEyE1F,EAAE,CAAA+C,YAAA,CA2G7F,CAAC;QAAA;QAAA,IAAArB,EAAA;UA3G0F1B,EAAE,CAAAsC,SAAA,EAkBpD,CAAC;UAlBiDtC,EAAE,CAAA4F,WAAA,sBAAAjE,GAAA,CAAA2G,MAAA,WAkBpD,CAAC,uBAAA3G,GAAA,CAAA4G,MAAA,YACC,CAAC,yBAAA5G,GAAA,CAAA4G,MAAA,cACG,CAAC,2BAAA5G,GAAA,CAAAyB,UACT,CAAC,wBAAAzB,GAAA,CAAA6G,aACD,CAAC,uBAAA7G,GAAA,CAAA+C,OACR,CAAC,qBAAA/C,GAAA,CAAAuH,GAAA,UACG,CAAC;UAxBsDlJ,EAAE,CAAA+B,UAAA,kDAAAJ,GAAA,CAAAsB,MAiBzC,CAAC;UAjBsCjD,EAAE,CAAAsC,SAAA,CA4DhG,CAAC;UA5D6FtC,EAAE,CAAAgD,aAAA,IAAArB,GAAA,CAAA2G,MAAA,oBA4DhG,CAAC;UA5D6FtI,EAAE,CAAAsC,SAAA,CA0GhG,CAAC;UA1G6FtC,EAAE,CAAAgD,aAAA,IAAArB,GAAA,CAAA6G,aAAA,SA0GhG,CAAC;QAAA;MAAA;MAAAiF,YAAA,GAEuDrM,YAAY,EAA+BD,EAAE,CAACuM,eAAe,EAAgK1M,cAAc,EAA+BD,EAAE,CAAC4M,+BAA+B,EAAgLnN,OAAO,EAAoFC,gBAAgB,EAAoJC,OAAO;MAAAkN,aAAA;MAAAC,eAAA;IAAA,EAAgJ;EAAE;AACl7B;AACAtN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,CACf,EAAEoH,mBAAmB,CAAC4F,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,CACf,EAAEoH,mBAAmB,CAAC4F,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AAC1DvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,CACf,EAAEoH,mBAAmB,CAAC4F,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACnDvN,UAAU,CAAC,CACPW,WAAW,CAAC,CAAC,CAChB,EAAEgH,mBAAmB,CAAC4F,SAAS,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC7DvN,UAAU,CAAC,CACPW,WAAW,CAAC,CAAC,CAChB,EAAEgH,mBAAmB,CAAC4F,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACtDvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,EACZI,WAAW,CAAC,CAAC,CAChB,EAAEgH,mBAAmB,CAAC4F,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AAC1DvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,EACZI,WAAW,CAAC,CAAC,CAChB,EAAEgH,mBAAmB,CAAC4F,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACxDvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,CACf,EAAEoH,mBAAmB,CAAC4F,SAAS,EAAE,eAAe,EAAE,KAAK,CAAC,CAAC;AAC1DvN,UAAU,CAAC,CACPO,UAAU,CAAC,CAAC,CACf,EAAEoH,mBAAmB,CAAC4F,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAC5DvN,UAAU,CAAC,CACPW,WAAW,CAAC,CAAC,CAChB,EAAEgH,mBAAmB,CAAC4F,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACpD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9IoG/N,EAAE,CAAAgO,iBAAA,CA8IX9F,mBAAmB,EAAc,CAAC;IACjHyE,IAAI,EAAE1M,SAAS;IACfgO,IAAI,EAAE,CAAC;MACCJ,eAAe,EAAE3N,uBAAuB,CAACgO,MAAM;MAC/CN,aAAa,EAAEzN,iBAAiB,CAACgO,IAAI;MACrCC,QAAQ,EAAE,aAAa;MACvBtB,QAAQ,EAAE,YAAY;MACtBuB,mBAAmB,EAAE,KAAK;MAC1BtB,UAAU,EAAE,IAAI;MAChBuB,OAAO,EAAE,CAAClN,YAAY,EAAEJ,cAAc,EAAER,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,CAAC;MAC3E4M,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACgB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEX,IAAI,EAAE3M,EAAE,CAACsM;EAAkB,CAAC,EAAE;IAAEK,IAAI,EAAE9L,EAAE,CAAC0L;EAAgB,CAAC,EAAE;IAAEI,IAAI,EAAEtL,EAAE,CAACmL,cAAc;IAAE+B,UAAU,EAAE,CAAC;MACrH5B,IAAI,EAAEvM;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgD,UAAU,EAAE,CAAC;MACtCuJ,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEsF,OAAO,EAAE,CAAC;MACVgH,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEkE,aAAa,EAAE,CAAC;MAChBoI,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEkI,MAAM,EAAE,CAAC;MACToE,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE6C,QAAQ,EAAE,CAAC;MACXyJ,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE4D,gBAAgB,EAAE,CAAC;MACnB0I,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEmC,SAAS,EAAE,CAAC;MACZmK,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEgI,aAAa,EAAE,CAAC;MAChBsE,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE0I,WAAW,EAAE,CAAC;MACd4D,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE8H,QAAQ,EAAE,CAAC;MACXwE,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAEiI,MAAM,EAAE,CAAC;MACTqE,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE2I,aAAa,EAAE,CAAC;MAChB2D,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE6D,eAAe,EAAE,CAAC;MAClByI,IAAI,EAAEtM;IACV,CAAC,CAAC;IAAE4I,OAAO,EAAE,CAAC;MACV0D,IAAI,EAAEtM;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMmO,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACtC,IAAI,YAAAuC,yBAAArC,CAAA;MAAA,YAAAA,CAAA,IAAwFoC,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBA3S8E1O,EAAE,CAAA2O,gBAAA;MAAAhC,IAAA,EA2SS6B,gBAAgB;MAAAF,OAAA,GAAYpG,mBAAmB;MAAA0G,OAAA,GAAa1G,mBAAmB;IAAA,EAAI;EAAE;EAChM;IAAS,IAAI,CAAC2G,IAAI,kBA5S8E7O,EAAE,CAAA8O,gBAAA;MAAAR,OAAA,GA4SqCpG,mBAAmB;IAAA,EAAI;EAAE;AACpK;AACA;EAAA,QAAA6F,SAAA,oBAAAA,SAAA,KA9SoG/N,EAAE,CAAAgO,iBAAA,CA8SXQ,gBAAgB,EAAc,CAAC;IAC9G7B,IAAI,EAAErM,QAAQ;IACd2N,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACpG,mBAAmB,CAAC;MAC9B0G,OAAO,EAAE,CAAC1G,mBAAmB;IACjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,mBAAmB,EAAEsG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}