{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// Not exported from index\n/** @private */\nexport class TextMessageFormat {\n  static write(output) {\n    return `${output}${TextMessageFormat.RecordSeparator}`;\n  }\n  static parse(input) {\n    if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\n      throw new Error(\"Message is incomplete.\");\n    }\n    const messages = input.split(TextMessageFormat.RecordSeparator);\n    messages.pop();\n    return messages;\n  }\n}\nTextMessageFormat.RecordSeparatorCode = 0x1e;\nTextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);", "map": {"version": 3, "names": ["TextMessageFormat", "write", "output", "RecordSeparator", "parse", "input", "length", "Error", "messages", "split", "pop", "RecordSeparatorCode", "String", "fromCharCode"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/TextMessageFormat.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    static write(output) {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n    static parse(input) {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\nTextMessageFormat.RecordSeparatorCode = 0x1e;\r\nTextMessageFormat.RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,MAAMA,iBAAiB,CAAC;EAC3B,OAAOC,KAAKA,CAACC,MAAM,EAAE;IACjB,OAAQ,GAAEA,MAAO,GAAEF,iBAAiB,CAACG,eAAgB,EAAC;EAC1D;EACA,OAAOC,KAAKA,CAACC,KAAK,EAAE;IAChB,IAAIA,KAAK,CAACA,KAAK,CAACC,MAAM,GAAG,CAAC,CAAC,KAAKN,iBAAiB,CAACG,eAAe,EAAE;MAC/D,MAAM,IAAII,KAAK,CAAC,wBAAwB,CAAC;IAC7C;IACA,MAAMC,QAAQ,GAAGH,KAAK,CAACI,KAAK,CAACT,iBAAiB,CAACG,eAAe,CAAC;IAC/DK,QAAQ,CAACE,GAAG,CAAC,CAAC;IACd,OAAOF,QAAQ;EACnB;AACJ;AACAR,iBAAiB,CAACW,mBAAmB,GAAG,IAAI;AAC5CX,iBAAiB,CAACG,eAAe,GAAGS,MAAM,CAACC,YAAY,CAACb,iBAAiB,CAACW,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}