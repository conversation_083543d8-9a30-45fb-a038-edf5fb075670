{"ast": null, "code": "import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n    includes = arrayIncludes,\n    isCommon = true,\n    length = array.length,\n    result = [],\n    valuesLength = values.length;\n  if (!length) {\n    return result;\n  }\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  } else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n  outer: while (++index < length) {\n    var value = array[index],\n      computed = iteratee == null ? value : iteratee(value);\n    value = comparator || value !== 0 ? value : 0;\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n      result.push(value);\n    } else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\nexport default baseDifference;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "arrayIncludes", "arrayIncludesWith", "arrayMap", "baseUnary", "cacheHas", "LARGE_ARRAY_SIZE", "baseDifference", "array", "values", "iteratee", "comparator", "index", "includes", "isCommon", "length", "result", "valuesLength", "outer", "value", "computed", "valuesIndex", "push"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseDifference.js"], "sourcesContent": ["import SetCache from './_SetCache.js';\nimport arrayIncludes from './_arrayIncludes.js';\nimport arrayIncludesWith from './_arrayIncludesWith.js';\nimport arrayMap from './_arrayMap.js';\nimport baseUnary from './_baseUnary.js';\nimport cacheHas from './_cacheHas.js';\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      isCommon = true,\n      length = array.length,\n      result = [],\n      valuesLength = values.length;\n\n  if (!length) {\n    return result;\n  }\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  }\n  else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee == null ? value : iteratee(value);\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n      result.push(value);\n    }\n    else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nexport default baseDifference;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,gBAAgB,GAAG,GAAG;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,UAAU,EAAE;EAC3D,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,QAAQ,GAAGZ,aAAa;IACxBa,QAAQ,GAAG,IAAI;IACfC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,MAAM,GAAG,EAAE;IACXC,YAAY,GAAGR,MAAM,CAACM,MAAM;EAEhC,IAAI,CAACA,MAAM,EAAE;IACX,OAAOC,MAAM;EACf;EACA,IAAIN,QAAQ,EAAE;IACZD,MAAM,GAAGN,QAAQ,CAACM,MAAM,EAAEL,SAAS,CAACM,QAAQ,CAAC,CAAC;EAChD;EACA,IAAIC,UAAU,EAAE;IACdE,QAAQ,GAAGX,iBAAiB;IAC5BY,QAAQ,GAAG,KAAK;EAClB,CAAC,MACI,IAAIL,MAAM,CAACM,MAAM,IAAIT,gBAAgB,EAAE;IAC1CO,QAAQ,GAAGR,QAAQ;IACnBS,QAAQ,GAAG,KAAK;IAChBL,MAAM,GAAG,IAAIT,QAAQ,CAACS,MAAM,CAAC;EAC/B;EACAS,KAAK,EACL,OAAO,EAAEN,KAAK,GAAGG,MAAM,EAAE;IACvB,IAAII,KAAK,GAAGX,KAAK,CAACI,KAAK,CAAC;MACpBQ,QAAQ,GAAGV,QAAQ,IAAI,IAAI,GAAGS,KAAK,GAAGT,QAAQ,CAACS,KAAK,CAAC;IAEzDA,KAAK,GAAIR,UAAU,IAAIQ,KAAK,KAAK,CAAC,GAAIA,KAAK,GAAG,CAAC;IAC/C,IAAIL,QAAQ,IAAIM,QAAQ,KAAKA,QAAQ,EAAE;MACrC,IAAIC,WAAW,GAAGJ,YAAY;MAC9B,OAAOI,WAAW,EAAE,EAAE;QACpB,IAAIZ,MAAM,CAACY,WAAW,CAAC,KAAKD,QAAQ,EAAE;UACpC,SAASF,KAAK;QAChB;MACF;MACAF,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC;IACpB,CAAC,MACI,IAAI,CAACN,QAAQ,CAACJ,MAAM,EAAEW,QAAQ,EAAET,UAAU,CAAC,EAAE;MAChDK,MAAM,CAACM,IAAI,CAACH,KAAK,CAAC;IACpB;EACF;EACA,OAAOH,MAAM;AACf;AAEA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}