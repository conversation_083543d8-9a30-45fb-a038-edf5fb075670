<div class="flex h-screen bg-[var(--background-light-gray)] text-[var(--text-dark)]">
  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col overflow-hidden bg-[var(--background-light-gray)] h-full"
       [ngClass]="{'mr-0': !showDocumentChat, 'mr-[33.333333%]': showDocumentChat}">
    <!-- Documents List View -->
    <div *ngIf="showingDocumentsList && !selectedDocument && !isAddingOrEditing"
      class="flex-1 flex flex-col overflow-hidden p-4">
      <div class="flex items-center justify-between mb-4">
        <div class="flex items-center justify-center">
          <i class="ri-folder-line text-[var(--primary-purple)] mr-2 text-lg"></i>
          <h2 class="text-lg font-[var(--font-weight-medium)] m-0 text-[var(--text-dark)]">{{ isPublicRoute ? 'Notes List' : 'Documents List' }}</h2>
        </div>
        <button (click)="addDocument($event)"
          class="bg-[var(--primary-purple)] text-[var(--background-white)] p-2 px-3 rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-black transition-[var(--transition-default)] outline-none border-none cursor-pointer">
          <i class="ri-add-line text-xl"></i>
        </button>
      </div>

      <div class="flex-1 overflow-y-auto">
        <div *ngFor="let document of documents"
          class="flex items-center justify-between p-3 mb-2 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)] hover:bg-[var(--hover-blue-gray)] cursor-pointer transition-[var(--transition-default)]"
          (click)="selectDocument(document)">
          <div class="flex items-center">
            <i class="ri-file-text-line text-[var(--primary-purple)] mr-3 text-lg"></i>
            <div class="text-[var(--text-dark)]">{{ document.title }}</div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div *ngIf="documents.length === 0"
        class="flex flex-col items-center justify-center p-6 sm:p-10 bg-[var(--background-white)] rounded-[var(--border-radius-large)] border border-dashed border-[var(--hover-blue-gray)] shadow-[var(--box-shadow)]">
        <i class="ri-file-list-3-line text-4xl sm:text-6xl text-[var(--text-medium-gray)] mb-4"></i>
        <p class="text-[var(--text-medium-gray)] text-center mb-4">{{ isPublicRoute ? 'No notes have been added yet.' : 'No documents have been added yet.' }}</p>
        <button (click)="addDocument($event)"
          class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-black rounded-[var(--border-radius-small)] flex items-center gap-2 transition-[var(--transition-default)] outline-none border-none cursor-pointer text-[var(--background-white)]">
          {{ isPublicRoute ? 'Add Your First Note' : 'Add Your First Document' }}
        </button>
      </div>
    </div>

    <!-- Document Details View -->
    <div *ngIf="selectedDocument && !isAddingOrEditing" class="flex-1 flex flex-col overflow-hidden p-6">
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center">
          <button (click)="backToList()"
            class="mr-4 p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
            <i
              class="ri-arrow-left-line text-[var(--primary-purple)] hover:text-[var(--text-dark)] transition-colors"></i>
          </button>
          <h2 class="text-xl font-[var(--font-weight-medium)] text-[var(--text-dark)]">{{ selectedDocument.title }}</h2>
        </div>
        <div class="flex items-center space-x-2">
          <!-- Favorite Toggle Button -->
          <button (click)="toggleFavorite(selectedDocument)"
            class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
            <i class="ri-star-line text-lg"
               [class.ri-star-fill]="isDocumentFavorite(selectedDocument)"
               [class.text-yellow-400]="isDocumentFavorite(selectedDocument)"
               [class.text-[var(--primary-purple)]]="!isDocumentFavorite(selectedDocument)"></i>
          </button>
          <!-- Edit Button -->
          <button (click)="editDocument(selectedDocument)"
            class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
            <i
              class="ri-edit-2-line text-[var(--primary-purple)] hover:text-[var(--text-dark)] transition-colors text-lg"></i>
          </button>
          <!-- Delete Button -->
          <button (click)="deleteDocument(selectedDocument)"
            class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-red-500 transition-[var(--transition-default)] cursor-pointer outline-none border-none">
            <i
              class="ri-delete-bin-line text-red-500 hover:text-white transition-colors text-lg"></i>
          </button>
        </div>
      </div>

      <div
        class="flex-1 overflow-y-auto p-4 bg-[var(--background-white)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)]">
        <!-- Document Content -->
        <div [innerHTML]="selectedDocument.formattedContent || selectedDocument.content"
          class="prose max-w-none text-[var(--text-dark)]"></div>

        <!-- Document Attachments (if any) -->
        <div *ngIf="selectedDocument.attachments && selectedDocument.attachments.length > 0"
          class="mt-6 p-4 bg-[var(--background-light-gray)] rounded-[var(--border-radius-large)]">
          <h3 class="text-lg font-[var(--font-weight-medium)] mb-2 text-[var(--text-dark)]">Attachments</h3>
          <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
            <div
              class="rounded-[var(--border-radius-small)] overflow-hidden border border-[var(--border-light)] bg-[var(--background-white)] p-2 relative group shadow-[var(--box-shadow)]"
              *ngFor="let file of selectedDocument.attachments; let i = index">
              <div class="file-preview h-24 flex items-center justify-center overflow-hidden">
                <img *ngIf="isImageFile(file.fileName)" [src]="file.filePath" alt="File preview" class="max-h-full">
                <div *ngIf="!isImageFile(file.fileName)" class="file-icon flex items-center justify-center h-full">
                  <i class="ri-file-line text-4xl text-[var(--text-medium-gray)]"></i>
                </div>
                <button class="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200
                  bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center"
                  (click)="markFileForDeletion(file, i)">
                  <i class="ri-close-line"></i>
                </button>
              </div>
              <div class="file-name text-sm text-center mt-2 truncate text-[var(--text-dark)]">{{file.fileName}}</div>
            </div>
          </div>
        </div>

        <!-- Display the files with a modern look and feel -->
        <div *ngIf="selectedDocument.files"
          class="mt-6 p-4 bg-[var(--background-light-gray)] rounded-[var(--border-radius-large)] shadow-[var(--box-shadow)]">
          <h3 class="text-xl font-[var(--font-weight-semibold)] mb-4 text-[var(--text-dark)]">Files</h3>
          <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-0">
            <div *ngFor="let file of selectedDocument.files.split(',')"
              class="p-4 border border-[var(--border-light)] rounded-[var(--border-radius-medium)] bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] transition-all duration-200 ease-in-out">
              <img *ngIf="isImageFile(file.trim())" [src]="'https://localhost:44314/uploads/' + file.trim()"
                class="w-full h-full rounded-[var(--border-radius-medium)] object-cover mx-auto mb-2"
                alt="File preview" />
              <div *ngIf="!isImageFile(file.trim())" class="h-32 flex items-center justify-center">
                <i class="ri-file-line text-5xl text-[var(--text-medium-gray)]"></i>
              </div>
            </div>
          </div>
        </div>

      </div>
    </div>

    <!-- Add/Edit Document -->
    <div *ngIf="isAddingOrEditing" class="flex-1 flex flex-col overflow-hidden p-6">
      <!-- Header Section -->
      <div class="flex items-center justify-between px-6 py-4 border-b border-[var(--border-light)] bg-[var(--background-dark)]">
        <!-- Left Side -->
        <div class="flex items-center gap-4">
          <button (click)="onCancel()"
            class="p-2 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)] hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] cursor-pointer outline-none border-none">
            <i class="ri-arrow-left-line text-[var(--primary-purple)] hover:text-[var(--text-dark)] transition-colors"></i>
          </button>
          <h2 class="text-xl font-[var(--font-weight-medium)] text-[var(--text-dark)]">
            {{ isPublicRoute ? (isNewDocument ? 'New Note' : 'Edit Note') : (isNewDocument ? 'New Document' : 'Edit Document') }}
          </h2>
        </div>

        <!-- Right Side - Removed unnecessary margins and borders -->
        <div class="flex items-center gap-3">
          <button (click)="onCancel()"
            class="px-4 py-2 bg-[var(--hover-blue-gray)] text-[var(--text-dark)] rounded-[var(--border-radius-small)] hover:bg-gray-300 transition-[var(--transition-default)] outline-none border-none cursor-pointer">
            Cancel
          </button>
          <button (click)="saveDocument()"
            class="px-4 py-2 bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[var(--border-radius-small)] hover:bg-[var(--secondary-purple)] hover:text-black transition-[var(--transition-default)] outline-none border-none cursor-pointer">
            Save
          </button>
        </div>
      </div>

      <!-- Form Section -->
      <div class="flex flex-col gap-6 flex-1 overflow-auto">
        <!-- Title -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-[var(--font-weight-medium)] text-[var(--text-dark)]">Title</label>
          <input type="text" [placeholder]="'Enter document title...'" [ngModel]="documentToEdit?.title"
            (ngModelChange)="documentToEdit.title = $event"
            class="w-full py-2 px-3 bg-[var(--background-white)] border border-[var(--border-light)] rounded-[var(--border-radius-small)] text-[var(--text-dark)] shadow-[var(--box-shadow)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-purple)] focus:border-transparent">
        </div>

        <!-- File Upload -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-[var(--font-weight-medium)] text-[var(--text-dark)]">{{ isPublicRoute ? 'Upload Files' : 'Upload Your Docs' }}</label>
          <div class="flex items-center gap-4 overflow-x-auto">
            <!-- Upload Area -->
            <div class="flex-shrink-0 w-48">
              <div
                class="border border-dashed border-[var(--hover-blue-gray)] rounded-[var(--border-radius-large)] p-4 text-center bg-[var(--background-white)] shadow-[var(--box-shadow)]">
                <div class="flex flex-col items-center gap-2">
                  <i class="ri-upload-cloud-line text-2xl text-[var(--text-medium-gray)]"></i>
                  <p class="text-sm text-[var(--text-medium-gray)]">Click to Add</p>
                  <input type="file" class="hidden" #fileInput (change)="onFileSelect($event)" multiple>
                  <button (click)="fileInput.click()"
                    class="px-4 py-2 bg-[var(--primary-purple)] hover:bg-[var(--secondary-purple)] hover:text-black rounded-[var(--border-radius-small)] transition-[var(--transition-default)] outline-none border-none cursor-pointer text-[var(--background-white)]">
                    Select Files
                  </button>
                </div>
              </div>
            </div>

            <!-- Existing Files -->
            <div *ngFor="let file of documentToEdit?.files?.split(', ') || []; let i = index"
              class="flex-shrink-0 w-40 h-40 border border-[var(--border-light)] rounded-[var(--border-radius-medium)] bg-[var(--background-white)] relative group shadow-[var(--box-shadow)]">
              <div class="aspect-square flex items-center justify-center bg-[var(--background-light-gray)] p-2">
                <img [src]="'https://localhost:44314/uploads/' + file" [alt]="file"
                  class="max-h-full max-w-full object-contain">
              </div>
              <div
                class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                <button (click)="markFileForDeletion(file, i)"
                  class="bg-red-500 hover:bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center">
                  <i class="ri-delete-bin-line"></i>
                </button>
              </div>

            </div>

            <!-- New Files -->
            <div *ngFor="let file of selectedFiles; let i = index"
              class="flex-shrink-0 w-40 h-40  bg-[var(--background-white)] rounded-xl overflow-hidden relative group transition-all duration-200 hover:shadow-lg">
              <!-- Image Preview Container -->
              <div class="aspect-square relative">
                <!-- Background Pattern -->
                <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100"></div>

                <!-- Image -->
                <div class="absolute inset-0 flex items-center justify-center p-3">
                  <img [src]="getFilePreviewUrl(file)" [alt]="file.name"
                    class="max-h-full max-w-full object-contain rounded-lg transition-transform duration-200 group-hover:scale-105">
                </div>

                <!-- Hover Overlay with Delete Button -->
                <div
                  class="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-all duration-200 flex items-center justify-center">
                  <button (click)="removeSelectedFile(i)"
                    class="transform scale-90 group-hover:scale-100 transition-all duration-200 bg-white/10 hover:bg-red-500 backdrop-blur-sm text-white rounded-full w-10 h-10 flex items-center justify-center border border-white/20 hover:border-transparent">
                    <i class="ri-delete-bin-line text-lg"></i>
                  </button>
                </div>
              </div>

              <!-- File Info -->
              <div class="p-3 border-t border-gray-100">
                <div class="flex items-center gap-1 text-[11px] text-[var(--text-muted)]">
                  <i class="ri-hard-drive-line text-xs"></i>
                  <span>{{(file.size / 1024).toFixed(1)}} KB</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Description -->
        <div class="flex flex-col gap-2 w-full">
          <label class="text-sm font-[var(--font-weight-medium)] text-[var(--text-dark)]">Description</label>
          <div id="editor"
            class="w-full p-2 rounded-[var(--border-radius-small)] border border-[var(--hover-blue-gray)] text-[var(--text-dark)] min-h-[200px] bg-[var(--background-white)] shadow-[var(--box-shadow)]"
            [style.display]="isAddingOrEditing ? 'block' : 'none'">
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Debug indicator -->
<div *ngIf="showDocumentChat" class="fixed top-4 left-4 bg-green-500 text-white p-2 rounded z-[100]">
  Chat is OPEN: {{ showDocumentChat }}
</div>

<!-- Right Sidebar for Document AI Chat -->
<div *ngIf="showDocumentChat"
  class="border-l border-[var(--hover-blue-gray)] bg-[var(--background-white)] shadow-md transition-all duration-300 flex flex-col overflow-hidden fixed top-0 right-0 z-50 w-1/3 translate-x-0 opacity-100"
  style="height: calc(100vh - 0px);">

    <!-- Chat Header -->
    <div class="p-4 border-b border-[var(--hover-blue-gray)] flex justify-between items-center sticky top-0 bg-[var(--primary-purple)] z-10">
      <div class="flex items-center">
        <div class="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3">
          <i class="ri-robot-line text-[var(--primary-purple)]"></i>
        </div>
        <h3 class="text-lg font-medium text-white">Document AI Assistant</h3>
      </div>
      <div class="flex items-center gap-2">
        <!-- Chat Options Menu -->
        <button (click)="toggleChatOptions()"
                class="text-white hover:text-white p-2 rounded-full hover:bg-[rgba(255,255,255,0.2)] transition-all duration-200">
          <i class="ri-more-2-line"></i>
        </button>
        <!-- Close Button -->
        <button (click)="showDocumentChat = false"
          class="text-white hover:text-white p-2 rounded-full hover:bg-[rgba(255,255,255,0.2)] transition-all duration-200">
          <i class="ri-close-line"></i>
        </button>
      </div>
    </div>

    <!-- Chat Options Dropdown -->
    <div *ngIf="showChatOptions"
         class="absolute top-16 right-4 bg-[var(--background-white)] rounded-lg shadow-lg border z-60 min-w-48 border-[var(--hover-blue-gray)]">
      <div class="p-2">
        <button (click)="clearChatHistory()"
                class="w-full text-left px-3 py-2 rounded-md hover:bg-[var(--hover-blue-gray)] transition-colors flex items-center gap-2">
          <i class="ri-delete-bin-line text-red-500"></i>
          <span>Clear Chat History</span>
        </button>
        <button (click)="exportChatHistory()"
                class="w-full text-left px-3 py-2 rounded-md hover:bg-[var(--hover-blue-gray)] transition-colors flex items-center gap-2">
          <i class="ri-download-line text-blue-500"></i>
          <span>Export Chat</span>
        </button>
        <button (click)="analyzeCurrentDocument()"
                class="w-full text-left px-3 py-2 rounded-md hover:bg-[var(--hover-blue-gray)] transition-colors flex items-center gap-2">
          <i class="ri-file-search-line text-green-500"></i>
          <span>Analyze Current Document</span>
        </button>
      </div>
    </div>

    <!-- Chat Messages Container -->
    <div class="flex-grow overflow-y-auto p-4" style="height: calc(100vh - 180px);" #chatContainer>
      <!-- Chat Messages -->
      <div class="space-y-4">

        <!-- Welcome Message -->
        <div *ngIf="documentChatMessages.length === 0" class="text-center py-8">
          <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-[var(--primary-purple)]/20 flex items-center justify-center">
            <i class="ri-chat-smile-3-line text-2xl text-[var(--primary-purple)]"></i>
          </div>
          <h4 class="font-semibold mb-2 text-[var(--text-dark)]">Document AI Assistant</h4>
          <p class="text-sm text-[var(--text-medium-gray)]">I can help you analyze, edit, and understand your documents. Ask me anything!</p>

          <!-- Quick Action Buttons -->
          <div class="mt-4 flex flex-wrap gap-2 justify-center">
            <button (click)="sendQuickMessage('Summarize this document')"
                    class="px-3 py-1 text-xs rounded-full border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] hover:bg-[var(--hover-blue-gray)] transition-colors">
              Summarize Document
            </button>
            <button (click)="sendQuickMessage('What are the key points?')"
                    class="px-3 py-1 text-xs rounded-full border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] hover:bg-[var(--hover-blue-gray)] transition-colors">
              Key Points
            </button>
            <button (click)="sendQuickMessage('Check grammar and style')"
                    class="px-3 py-1 text-xs rounded-full border border-[var(--hover-blue-gray)] text-[var(--text-medium-gray)] hover:bg-[var(--hover-blue-gray)] transition-colors">
              Grammar Check
            </button>
          </div>
        </div>

        <div *ngFor="let message of documentChatMessages; trackBy: trackByMessageId" class="mb-4">
          <!-- User Message -->
          <div *ngIf="message.sender === 'user'" class="flex items-start mb-6">
            <div class="w-10 h-10 rounded-full bg-[var(--hover-blue-gray)] flex items-center justify-center mr-3 shadow-sm">
              <i class="ri-user-line text-[var(--primary-purple)]"></i>
            </div>
            <div class="relative">
              <div class="absolute top-0 -left-2 w-0 h-0 border-8 border-transparent border-r-[var(--background-light-gray)]"></div>
              <div class="bg-[var(--background-light-gray)] p-4 rounded-2xl rounded-tl-none max-w-[280px] shadow-sm">
                <p class="text-sm text-[var(--text-dark)] break-words">{{ message.content }}</p>
              </div>
              <div class="text-xs text-[var(--text-medium-gray)] mt-1 ml-2">
                {{ message.timestamp | date:'shortTime' }}
              </div>
            </div>
          </div>

          <!-- Agent Message -->
          <div *ngIf="message.sender === 'agent'" class="flex items-start mb-6 justify-end">
            <div class="relative">
              <div class="absolute top-0 -right-2 w-0 h-0 border-8 border-transparent border-l-[var(--primary-purple)]"></div>
              <div class="bg-[var(--primary-purple)] p-4 rounded-2xl rounded-tr-none max-w-[280px] text-white shadow-sm">
                <p class="text-sm break-words whitespace-pre-wrap">{{ message.content }}</p>

                <!-- Message Actions -->
                <div class="flex items-center gap-2 mt-2 pt-2 border-t border-white/10">
                  <button (click)="copyMessage(message.content)"
                          class="p-1 rounded hover:bg-white/10 transition-colors"
                          title="Copy message">
                    <i class="ri-file-copy-line text-xs opacity-70"></i>
                  </button>
                  <button (click)="likeMessage(message)"
                          class="p-1 rounded hover:bg-white/10 transition-colors"
                          title="Like message">
                    <i [class]="message.liked ? 'ri-thumb-up-fill text-green-400' : 'ri-thumb-up-line'"
                       class="text-xs opacity-70"></i>
                  </button>
                  <button (click)="regenerateResponse(message)"
                          class="p-1 rounded hover:bg-white/10 transition-colors"
                          title="Regenerate response">
                    <i class="ri-refresh-line text-xs opacity-70"></i>
                  </button>
                </div>
              </div>
              <div class="text-xs text-[var(--text-medium-gray)] mt-1 mr-2 text-right">
                {{ message.timestamp | date:'shortTime' }}
              </div>
            </div>
            <div class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center ml-3 shadow-sm">
              <i class="ri-robot-line text-white"></i>
            </div>
          </div>

          <!-- Loading Message -->
          <div *ngIf="message.sender === 'loading'" class="flex items-start mb-6 justify-end">
            <div class="relative">
              <div class="absolute top-0 -right-2 w-0 h-0 border-8 border-transparent border-l-[var(--primary-purple)]"></div>
              <div class="bg-[var(--primary-purple)] p-4 rounded-2xl rounded-tr-none max-w-[280px] text-white flex items-center shadow-sm">
                <div class="flex space-x-2">
                  <div class="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                  <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                  <div class="w-2 h-2 bg-white rounded-full animate-bounce" style="animation-delay: 0.4s"></div>
                </div>
              </div>
            </div>
            <div class="w-10 h-10 rounded-full bg-[var(--primary-purple)] flex items-center justify-center ml-3 shadow-sm">
              <i class="ri-robot-line text-white"></i>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Chat Input -->
    <div class="p-4 border-t border-[var(--hover-blue-gray)] bg-[var(--background-white)] shadow-lg sticky bottom-0">
      <div class="flex items-center bg-[var(--background-light-gray)] rounded-full overflow-hidden border border-[var(--hover-blue-gray)] shadow-inner">
        <input type="text"
               [(ngModel)]="documentChatInput"
               (keyup.enter)="sendDocumentChatMessage()"
               placeholder="Ask about your document..."
               class="flex-1 p-3 border-none focus:outline-none text-sm text-[var(--text-dark)] bg-transparent"
               [disabled]="isProcessingDocumentChat">
        <button (click)="sendDocumentChatMessage()"
          class="p-2 mx-1 my-1 bg-[var(--primary-purple)] text-white rounded-full hover:bg-opacity-90 transition-all duration-200 focus:outline-none flex items-center justify-center w-9 h-9"
          [disabled]="isProcessingDocumentChat || !documentChatInput.trim()">
          <i class="ri-send-plane-fill" *ngIf="!isProcessingDocumentChat"></i>
          <i class="ri-loader-4-line animate-spin" *ngIf="isProcessingDocumentChat"></i>
        </button>
      </div>
      <div class="text-xs text-center mt-2 text-[var(--text-medium-gray)]">
        Press Enter to send your message
      </div>
    </div>
  </div>

<!-- Chat Toggle Button -->
<div class="fixed bottom-6 right-6 z-40" *ngIf="!showDocumentChat">
  <div class="relative">
    <!-- Notification Badge -->
    <div *ngIf="unreadDocumentChatCount > 0"
         class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold">
      {{ unreadDocumentChatCount > 99 ? '99+' : unreadDocumentChatCount }}
    </div>

    <!-- Main Chat Button -->
    <button (click)="toggleDocumentChat()"
            class="w-14 h-14 rounded-full shadow-lg transition-all duration-300 hover:scale-110 flex items-center justify-center bg-purple-600 hover:bg-purple-700 border-2 border-white">
      <i class="ri-chat-3-line text-white text-xl"></i>
    </button>

    <!-- Pulse Animation -->
    <div class="absolute inset-0 rounded-full animate-ping opacity-20 bg-[var(--primary-purple)]"></div>
  </div>
</div>

<!-- Add this hidden editor container that's always present in the DOM -->
<div style="display: none">
  <div id="editor-container"></div>
</div>
