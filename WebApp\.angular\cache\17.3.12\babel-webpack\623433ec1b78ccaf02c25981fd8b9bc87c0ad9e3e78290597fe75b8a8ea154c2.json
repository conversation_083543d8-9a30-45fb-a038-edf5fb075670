{"ast": null, "code": "/**\n * This function is like `baseIndexOf` except that it accepts a comparator.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOfWith(array, value, fromIndex, comparator) {\n  var index = fromIndex - 1,\n    length = array.length;\n  while (++index < length) {\n    if (comparator(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\nexport default baseIndexOfWith;", "map": {"version": 3, "names": ["baseIndexOfWith", "array", "value", "fromIndex", "comparator", "index", "length"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseIndexOfWith.js"], "sourcesContent": ["/**\n * This function is like `baseIndexOf` except that it accepts a comparator.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOfWith(array, value, fromIndex, comparator) {\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (comparator(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nexport default baseIndexOfWith;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,eAAeA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAC5D,IAAIC,KAAK,GAAGF,SAAS,GAAG,CAAC;IACrBG,MAAM,GAAGL,KAAK,CAACK,MAAM;EAEzB,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIF,UAAU,CAACH,KAAK,CAACI,KAAK,CAAC,EAAEH,KAAK,CAAC,EAAE;MACnC,OAAOG,KAAK;IACd;EACF;EACA,OAAO,CAAC,CAAC;AACX;AAEA,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}