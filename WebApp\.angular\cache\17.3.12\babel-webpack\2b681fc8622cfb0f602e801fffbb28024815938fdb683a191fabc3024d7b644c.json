{"ast": null, "code": "import arrayEachRight from './_arrayEachRight.js';\nimport baseEachRight from './_baseEachRight.js';\nimport castFunction from './_castFunction.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.forEach` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @alias eachRight\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n * @see _.forEach\n * @example\n *\n * _.forEachRight([1, 2], function(value) {\n *   console.log(value);\n * });\n * // => Logs `2` then `1`.\n */\nfunction forEachRight(collection, iteratee) {\n  var func = isArray(collection) ? arrayEachRight : baseEachRight;\n  return func(collection, castFunction(iteratee));\n}\nexport default forEachRight;", "map": {"version": 3, "names": ["arrayEachRight", "baseEachRight", "castFunction", "isArray", "forEachRight", "collection", "iteratee", "func"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/forEachRight.js"], "sourcesContent": ["import arrayEachRight from './_arrayEachRight.js';\nimport baseEachRight from './_baseEachRight.js';\nimport castFunction from './_castFunction.js';\nimport isArray from './isArray.js';\n\n/**\n * This method is like `_.forEach` except that it iterates over elements of\n * `collection` from right to left.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @alias eachRight\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array|Object} Returns `collection`.\n * @see _.forEach\n * @example\n *\n * _.forEachRight([1, 2], function(value) {\n *   console.log(value);\n * });\n * // => Logs `2` then `1`.\n */\nfunction forEachRight(collection, iteratee) {\n  var func = isArray(collection) ? arrayEachRight : baseEachRight;\n  return func(collection, castFunction(iteratee));\n}\n\nexport default forEachRight;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EAC1C,IAAIC,IAAI,GAAGJ,OAAO,CAACE,UAAU,CAAC,GAAGL,cAAc,GAAGC,aAAa;EAC/D,OAAOM,IAAI,CAACF,UAAU,EAAEH,YAAY,CAACI,QAAQ,CAAC,CAAC;AACjD;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}