{"ast": null, "code": "/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\nexport default getValue;", "map": {"version": 3, "names": ["getValue", "object", "key", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_getValue.js"], "sourcesContent": ["/**\n * Gets the value at `key` of `object`.\n *\n * @private\n * @param {Object} [object] The object to query.\n * @param {string} key The key of the property to get.\n * @returns {*} Returns the property value.\n */\nfunction getValue(object, key) {\n  return object == null ? undefined : object[key];\n}\n\nexport default getValue;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,MAAM,EAAEC,GAAG,EAAE;EAC7B,OAAOD,MAAM,IAAI,IAAI,GAAGE,SAAS,GAAGF,MAAM,CAACC,GAAG,CAAC;AACjD;AAEA,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}