{"ast": null, "code": "import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n\n/**\n * Checks if `object` conforms to `source` by invoking the predicate\n * properties of `source` with the corresponding property values of `object`.\n *\n * **Note:** This method is equivalent to `_.conforms` when `source` is\n * partially applied.\n *\n * @static\n * @memberOf _\n * @since 4.14.0\n * @category Lang\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 1; } });\n * // => true\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 2; } });\n * // => false\n */\nfunction conformsTo(object, source) {\n  return source == null || baseConformsTo(object, source, keys(source));\n}\nexport default conformsTo;", "map": {"version": 3, "names": ["baseConformsTo", "keys", "conformsTo", "object", "source"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/conformsTo.js"], "sourcesContent": ["import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n\n/**\n * Checks if `object` conforms to `source` by invoking the predicate\n * properties of `source` with the corresponding property values of `object`.\n *\n * **Note:** This method is equivalent to `_.conforms` when `source` is\n * partially applied.\n *\n * @static\n * @memberOf _\n * @since 4.14.0\n * @category Lang\n * @param {Object} object The object to inspect.\n * @param {Object} source The object of property predicates to conform to.\n * @returns {boolean} Returns `true` if `object` conforms, else `false`.\n * @example\n *\n * var object = { 'a': 1, 'b': 2 };\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 1; } });\n * // => true\n *\n * _.conformsTo(object, { 'b': function(n) { return n > 2; } });\n * // => false\n */\nfunction conformsTo(object, source) {\n  return source == null || baseConformsTo(object, source, keys(source));\n}\n\nexport default conformsTo;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,OAAOA,MAAM,IAAI,IAAI,IAAIJ,cAAc,CAACG,MAAM,EAAEC,MAAM,EAAEH,IAAI,CAACG,MAAM,CAAC,CAAC;AACvE;AAEA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}