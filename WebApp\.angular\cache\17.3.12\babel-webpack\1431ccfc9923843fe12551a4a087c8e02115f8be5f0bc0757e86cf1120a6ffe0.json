{"ast": null, "code": "import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, forwardRef, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i5 from '@angular/forms';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i4 from 'ng-zorro-antd/core/form';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"*\"];\nconst _c1 = [\"inputElement\"];\nconst _c2 = [\"nz-checkbox\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzCheckboxGroupComponent_For_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 1);\n    i0.ɵɵlistener(\"nzCheckedChange\", function NzCheckboxGroupComponent_For_1_Template_label_nzCheckedChange_0_listener($event) {\n      const option_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onCheckedChange(option_r2, $event));\n    });\n    i0.ɵɵelementStart(1, \"span\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const option_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", option_r2.disabled || ctx_r2.nzDisabled)(\"nzChecked\", option_r2.checked);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r2.label);\n  }\n}\nclass NzCheckboxWrapperComponent {\n  constructor() {\n    this.nzOnChange = new EventEmitter();\n    this.checkboxList = [];\n  }\n  addCheckbox(value) {\n    this.checkboxList.push(value);\n  }\n  removeCheckbox(value) {\n    this.checkboxList.splice(this.checkboxList.indexOf(value), 1);\n  }\n  onChange() {\n    const listOfCheckedValue = this.checkboxList.filter(item => item.nzChecked).map(item => item.nzValue);\n    this.nzOnChange.emit(listOfCheckedValue);\n  }\n  static {\n    this.ɵfac = function NzCheckboxWrapperComponent_Factory(t) {\n      return new (t || NzCheckboxWrapperComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxWrapperComponent,\n      selectors: [[\"nz-checkbox-wrapper\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      outputs: {\n        nzOnChange: \"nzOnChange\"\n      },\n      exportAs: [\"nzCheckboxWrapper\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function NzCheckboxWrapperComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxWrapperComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-wrapper',\n      exportAs: 'nzCheckboxWrapper',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: ` <ng-content></ng-content> `,\n      host: {\n        class: 'ant-checkbox-group'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzOnChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzCheckboxComponent {\n  innerCheckedChange(checked) {\n    if (!this.nzDisabled) {\n      this.nzChecked = checked;\n      this.onChange(this.nzChecked);\n      this.nzCheckedChange.emit(this.nzChecked);\n      if (this.nzCheckboxWrapperComponent) {\n        this.nzCheckboxWrapperComponent.onChange();\n      }\n    }\n  }\n  writeValue(value) {\n    this.nzChecked = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  focus() {\n    this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n  }\n  blur() {\n    this.inputElement.nativeElement.blur();\n  }\n  constructor(ngZone, elementRef, nzCheckboxWrapperComponent, cdr, focusMonitor, directionality, nzFormStatusService) {\n    this.ngZone = ngZone;\n    this.elementRef = elementRef;\n    this.nzCheckboxWrapperComponent = nzCheckboxWrapperComponent;\n    this.cdr = cdr;\n    this.focusMonitor = focusMonitor;\n    this.directionality = directionality;\n    this.nzFormStatusService = nzFormStatusService;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.nzCheckedChange = new EventEmitter();\n    this.nzValue = null;\n    this.nzAutoFocus = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzChecked = false;\n    this.nzId = null;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.addCheckbox(this);\n    }\n    this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.preventDefault();\n        this.focus();\n        if (this.nzDisabled) {\n          return;\n        }\n        this.ngZone.run(() => {\n          this.innerCheckedChange(!this.nzChecked);\n          this.cdr.markForCheck();\n        });\n      });\n      fromEvent(this.inputElement.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => event.stopPropagation());\n    });\n  }\n  ngAfterViewInit() {\n    if (this.nzAutoFocus) {\n      this.focus();\n    }\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    if (this.nzCheckboxWrapperComponent) {\n      this.nzCheckboxWrapperComponent.removeCheckbox(this);\n    }\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCheckboxComponent_Factory(t) {\n      return new (t || NzCheckboxComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(NzCheckboxWrapperComponent, 8), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i3.Directionality, 8), i0.ɵɵdirectiveInject(i4.NzFormStatusService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxComponent,\n      selectors: [[\"\", \"nz-checkbox\", \"\"]],\n      viewQuery: function NzCheckboxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c1, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.inputElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-checkbox-wrapper\"],\n      hostVars: 6,\n      hostBindings: function NzCheckboxComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-wrapper-in-form-item\", !!ctx.nzFormStatusService)(\"ant-checkbox-wrapper-checked\", ctx.nzChecked)(\"ant-checkbox-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzValue: \"nzValue\",\n        nzAutoFocus: \"nzAutoFocus\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzChecked: \"nzChecked\",\n        nzId: \"nzId\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      exportAs: [\"nzCheckbox\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 11,\n      consts: [[\"inputElement\", \"\"], [1, \"ant-checkbox\"], [\"type\", \"checkbox\", 1, \"ant-checkbox-input\", 3, \"ngModelChange\", \"checked\", \"ngModel\", \"disabled\"], [1, \"ant-checkbox-inner\"]],\n      template: function NzCheckboxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 1)(1, \"input\", 2, 0);\n          i0.ɵɵlistener(\"ngModelChange\", function NzCheckboxComponent_Template_input_ngModelChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.innerCheckedChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(3, \"span\", 3);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"span\");\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-checked\", ctx.nzChecked && !ctx.nzIndeterminate)(\"ant-checkbox-disabled\", ctx.nzDisabled)(\"ant-checkbox-indeterminate\", ctx.nzIndeterminate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"ngModel\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled);\n          i0.ɵɵattribute(\"autofocus\", ctx.nzAutoFocus ? \"autofocus\" : null)(\"id\", ctx.nzId);\n        }\n      },\n      dependencies: [FormsModule, i5.CheckboxControlValueAccessor, i5.NgControlStatus, i5.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzAutoFocus\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzIndeterminate\", void 0);\n__decorate([InputBoolean()], NzCheckboxComponent.prototype, \"nzChecked\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-checkbox]',\n      exportAs: 'nzCheckbox',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-wrapper',\n        '[class.ant-checkbox-wrapper-in-form-item]': '!!nzFormStatusService',\n        '[class.ant-checkbox-wrapper-checked]': 'nzChecked',\n        '[class.ant-checkbox-rtl]': `dir === 'rtl'`\n      },\n      imports: [FormsModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: NzCheckboxWrapperComponent,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i4.NzFormStatusService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    inputElement: [{\n      type: ViewChild,\n      args: ['inputElement', {\n        static: true\n      }]\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzValue: [{\n      type: Input\n    }],\n    nzAutoFocus: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzId: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCheckboxGroupComponent {\n  onCheckedChange(option, checked) {\n    option.checked = checked;\n    this.onChange(this.options);\n  }\n  constructor(elementRef, focusMonitor, cdr, directionality) {\n    this.elementRef = elementRef;\n    this.focusMonitor = focusMonitor;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this.onChange = () => {};\n    this.onTouched = () => {};\n    this.options = [];\n    this.nzDisabled = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.isNzDisableFirstChange = true;\n  }\n  ngOnInit() {\n    this.focusMonitor.monitor(this.elementRef, true).pipe(takeUntil(this.destroy$)).subscribe(focusOrigin => {\n      if (!focusOrigin) {\n        Promise.resolve().then(() => this.onTouched());\n      }\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.focusMonitor.stopMonitoring(this.elementRef);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  writeValue(value) {\n    this.options = value;\n    this.cdr.markForCheck();\n  }\n  registerOnChange(fn) {\n    this.onChange = fn;\n  }\n  registerOnTouched(fn) {\n    this.onTouched = fn;\n  }\n  setDisabledState(disabled) {\n    this.nzDisabled = this.isNzDisableFirstChange && this.nzDisabled || disabled;\n    this.isNzDisableFirstChange = false;\n    this.cdr.markForCheck();\n  }\n  static {\n    this.ɵfac = function NzCheckboxGroupComponent_Factory(t) {\n      return new (t || NzCheckboxGroupComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i2.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzCheckboxGroupComponent,\n      selectors: [[\"nz-checkbox-group\"]],\n      hostAttrs: [1, \"ant-checkbox-group\"],\n      hostVars: 2,\n      hostBindings: function NzCheckboxGroupComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-checkbox-group-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzDisabled: \"nzDisabled\"\n      },\n      exportAs: [\"nzCheckboxGroup\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }]), i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzDisabled\", \"nzChecked\"], [\"nz-checkbox\", \"\", 1, \"ant-checkbox-group-item\", 3, \"nzCheckedChange\", \"nzDisabled\", \"nzChecked\"]],\n      template: function NzCheckboxGroupComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵrepeaterCreate(0, NzCheckboxGroupComponent_For_1_Template, 3, 3, \"label\", 0, _forTrack0);\n        }\n        if (rf & 2) {\n          i0.ɵɵrepeater(ctx.options);\n        }\n      },\n      dependencies: [NzCheckboxComponent],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([InputBoolean()], NzCheckboxGroupComponent.prototype, \"nzDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxGroupComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-checkbox-group',\n      exportAs: 'nzCheckboxGroup',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    @for (option of options; track option.value) {\n      <label\n        nz-checkbox\n        class=\"ant-checkbox-group-item\"\n        [nzDisabled]=\"option.disabled || nzDisabled\"\n        [nzChecked]=\"option.checked!\"\n        (nzCheckedChange)=\"onCheckedChange(option, $event)\"\n      >\n        <span>{{ option.label }}</span>\n      </label>\n    }\n  `,\n      providers: [{\n        provide: NG_VALUE_ACCESSOR,\n        useExisting: forwardRef(() => NzCheckboxGroupComponent),\n        multi: true\n      }],\n      host: {\n        class: 'ant-checkbox-group',\n        '[class.ant-checkbox-group-rtl]': `dir === 'rtl'`\n      },\n      imports: [NzCheckboxComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i2.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i3.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxModule {\n  static {\n    this.ɵfac = function NzCheckboxModule_Factory(t) {\n      return new (t || NzCheckboxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzCheckboxModule,\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCheckboxModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n      exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxModule, NzCheckboxWrapperComponent };", "map": {"version": 3, "names": ["__decorate", "i0", "EventEmitter", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Output", "forwardRef", "Optional", "ViewChild", "Input", "NgModule", "i5", "NG_VALUE_ACCESSOR", "FormsModule", "Subject", "fromEvent", "takeUntil", "InputBoolean", "i2", "i3", "i4", "_c0", "_c1", "_c2", "_forTrack0", "$index", "$item", "value", "NzCheckboxGroupComponent_For_1_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "NzCheckboxGroupComponent_For_1_Template_label_nzCheckedChange_0_listener", "$event", "option_r2", "ɵɵrestoreView", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "onCheckedChange", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "disabled", "nzDisabled", "checked", "ɵɵadvance", "ɵɵtextInterpolate", "label", "NzCheckboxWrapperComponent", "constructor", "nzOnChange", "checkboxList", "addCheckbox", "push", "removeCheckbox", "splice", "indexOf", "onChange", "listOfCheckedValue", "filter", "item", "nzChecked", "map", "nzValue", "emit", "ɵfac", "NzCheckboxWrapperComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "outputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "NzCheckboxWrapperComponent_Template", "ɵɵprojectionDef", "ɵɵprojection", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "OnPush", "None", "host", "class", "NzCheckboxComponent", "innerCheckedChange", "nzCheckedChange", "nzCheckboxWrapperComponent", "writeValue", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "registerOnChange", "fn", "registerOnTouched", "onTouched", "setDisabledState", "isNzDisableFirstChange", "focus", "focusMonitor", "focusVia", "inputElement", "blur", "nativeElement", "ngZone", "elementRef", "directionality", "nzFormStatusService", "dir", "destroy$", "nzAutoFocus", "nzIndeterminate", "nzId", "ngOnInit", "monitor", "pipe", "subscribe", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "resolve", "then", "change", "direction", "detectChanges", "runOutsideAngular", "event", "preventDefault", "run", "stopPropagation", "ngAfterViewInit", "ngOnDestroy", "stopMonitoring", "next", "complete", "NzCheckboxComponent_Factory", "ɵɵdirectiveInject", "NgZone", "ElementRef", "ChangeDetectorRef", "FocusMonitor", "Directionality", "NzFormStatusService", "viewQuery", "NzCheckboxComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "NzCheckboxComponent_HostBindings", "ɵɵclassProp", "inputs", "ɵɵProvidersFeature", "provide", "useExisting", "multi", "attrs", "consts", "NzCheckboxComponent_Template", "NzCheckboxComponent_Template_input_ngModelChange_1_listener", "ɵɵelement", "ɵɵattribute", "dependencies", "CheckboxControlValueAccessor", "NgControlStatus", "NgModel", "prototype", "providers", "imports", "decorators", "static", "NzCheckboxGroupComponent", "option", "options", "NzCheckboxGroupComponent_Factory", "NzCheckboxGroupComponent_HostBindings", "NzCheckboxGroupComponent_Template", "ɵɵrepeaterCreate", "ɵɵrepeater", "NzCheckboxModule", "NzCheckboxModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-checkbox.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ChangeDetectionStrategy, ViewEncapsulation, Output, forwardRef, Optional, ViewChild, Input, NgModule } from '@angular/core';\nimport * as i5 from '@angular/forms';\nimport { NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';\nimport { Subject, fromEvent } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport * as i4 from 'ng-zorro-antd/core/form';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxWrapperComponent {\n    constructor() {\n        this.nzOnChange = new EventEmitter();\n        this.checkboxList = [];\n    }\n    addCheckbox(value) {\n        this.checkboxList.push(value);\n    }\n    removeCheckbox(value) {\n        this.checkboxList.splice(this.checkboxList.indexOf(value), 1);\n    }\n    onChange() {\n        const listOfCheckedValue = this.checkboxList.filter(item => item.nzChecked).map(item => item.nzValue);\n        this.nzOnChange.emit(listOfCheckedValue);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxWrapperComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCheckboxWrapperComponent, isStandalone: true, selector: \"nz-checkbox-wrapper\", outputs: { nzOnChange: \"nzOnChange\" }, host: { classAttribute: \"ant-checkbox-group\" }, exportAs: [\"nzCheckboxWrapper\"], ngImport: i0, template: ` <ng-content></ng-content> `, isInline: true, changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxWrapperComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-checkbox-wrapper',\n                    exportAs: 'nzCheckboxWrapper',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: ` <ng-content></ng-content> `,\n                    host: {\n                        class: 'ant-checkbox-group'\n                    },\n                    standalone: true\n                }]\n        }], propDecorators: { nzOnChange: [{\n                type: Output\n            }] } });\n\nclass NzCheckboxComponent {\n    innerCheckedChange(checked) {\n        if (!this.nzDisabled) {\n            this.nzChecked = checked;\n            this.onChange(this.nzChecked);\n            this.nzCheckedChange.emit(this.nzChecked);\n            if (this.nzCheckboxWrapperComponent) {\n                this.nzCheckboxWrapperComponent.onChange();\n            }\n        }\n    }\n    writeValue(value) {\n        this.nzChecked = value;\n        this.cdr.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(disabled) {\n        this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n    }\n    focus() {\n        this.focusMonitor.focusVia(this.inputElement, 'keyboard');\n    }\n    blur() {\n        this.inputElement.nativeElement.blur();\n    }\n    constructor(ngZone, elementRef, nzCheckboxWrapperComponent, cdr, focusMonitor, directionality, nzFormStatusService) {\n        this.ngZone = ngZone;\n        this.elementRef = elementRef;\n        this.nzCheckboxWrapperComponent = nzCheckboxWrapperComponent;\n        this.cdr = cdr;\n        this.focusMonitor = focusMonitor;\n        this.directionality = directionality;\n        this.nzFormStatusService = nzFormStatusService;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        this.isNzDisableFirstChange = true;\n        this.onChange = () => { };\n        this.onTouched = () => { };\n        this.nzCheckedChange = new EventEmitter();\n        this.nzValue = null;\n        this.nzAutoFocus = false;\n        this.nzDisabled = false;\n        this.nzIndeterminate = false;\n        this.nzChecked = false;\n        this.nzId = null;\n    }\n    ngOnInit() {\n        this.focusMonitor\n            .monitor(this.elementRef, true)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(focusOrigin => {\n            if (!focusOrigin) {\n                Promise.resolve().then(() => this.onTouched());\n            }\n        });\n        if (this.nzCheckboxWrapperComponent) {\n            this.nzCheckboxWrapperComponent.addCheckbox(this);\n        }\n        this.directionality.change.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n        this.ngZone.runOutsideAngular(() => {\n            fromEvent(this.elementRef.nativeElement, 'click')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => {\n                event.preventDefault();\n                this.focus();\n                if (this.nzDisabled) {\n                    return;\n                }\n                this.ngZone.run(() => {\n                    this.innerCheckedChange(!this.nzChecked);\n                    this.cdr.markForCheck();\n                });\n            });\n            fromEvent(this.inputElement.nativeElement, 'click')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => event.stopPropagation());\n        });\n    }\n    ngAfterViewInit() {\n        if (this.nzAutoFocus) {\n            this.focus();\n        }\n    }\n    ngOnDestroy() {\n        this.focusMonitor.stopMonitoring(this.elementRef);\n        if (this.nzCheckboxWrapperComponent) {\n            this.nzCheckboxWrapperComponent.removeCheckbox(this);\n        }\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxComponent, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: NzCheckboxWrapperComponent, optional: true }, { token: i0.ChangeDetectorRef }, { token: i2.FocusMonitor }, { token: i3.Directionality, optional: true }, { token: i4.NzFormStatusService, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCheckboxComponent, isStandalone: true, selector: \"[nz-checkbox]\", inputs: { nzValue: \"nzValue\", nzAutoFocus: \"nzAutoFocus\", nzDisabled: \"nzDisabled\", nzIndeterminate: \"nzIndeterminate\", nzChecked: \"nzChecked\", nzId: \"nzId\" }, outputs: { nzCheckedChange: \"nzCheckedChange\" }, host: { properties: { \"class.ant-checkbox-wrapper-in-form-item\": \"!!nzFormStatusService\", \"class.ant-checkbox-wrapper-checked\": \"nzChecked\", \"class.ant-checkbox-rtl\": \"dir === 'rtl'\" }, classAttribute: \"ant-checkbox-wrapper\" }, providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => NzCheckboxComponent),\n                multi: true\n            }\n        ], viewQueries: [{ propertyName: \"inputElement\", first: true, predicate: [\"inputElement\"], descendants: true, static: true }], exportAs: [\"nzCheckbox\"], ngImport: i0, template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i5.CheckboxControlValueAccessor, selector: \"input[type=checkbox][formControlName],input[type=checkbox][formControl],input[type=checkbox][ngModel]\" }, { kind: \"directive\", type: i5.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i5.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzCheckboxComponent.prototype, \"nzAutoFocus\", void 0);\n__decorate([\n    InputBoolean()\n], NzCheckboxComponent.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzCheckboxComponent.prototype, \"nzIndeterminate\", void 0);\n__decorate([\n    InputBoolean()\n], NzCheckboxComponent.prototype, \"nzChecked\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: '[nz-checkbox]',\n                    exportAs: 'nzCheckbox',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <span\n      class=\"ant-checkbox\"\n      [class.ant-checkbox-checked]=\"nzChecked && !nzIndeterminate\"\n      [class.ant-checkbox-disabled]=\"nzDisabled\"\n      [class.ant-checkbox-indeterminate]=\"nzIndeterminate\"\n    >\n      <input\n        #inputElement\n        type=\"checkbox\"\n        class=\"ant-checkbox-input\"\n        [attr.autofocus]=\"nzAutoFocus ? 'autofocus' : null\"\n        [attr.id]=\"nzId\"\n        [checked]=\"nzChecked\"\n        [ngModel]=\"nzChecked\"\n        [disabled]=\"nzDisabled\"\n        (ngModelChange)=\"innerCheckedChange($event)\"\n      />\n      <span class=\"ant-checkbox-inner\"></span>\n    </span>\n    <span><ng-content></ng-content></span>\n  `,\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NzCheckboxComponent),\n                            multi: true\n                        }\n                    ],\n                    host: {\n                        class: 'ant-checkbox-wrapper',\n                        '[class.ant-checkbox-wrapper-in-form-item]': '!!nzFormStatusService',\n                        '[class.ant-checkbox-wrapper-checked]': 'nzChecked',\n                        '[class.ant-checkbox-rtl]': `dir === 'rtl'`\n                    },\n                    imports: [FormsModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: NzCheckboxWrapperComponent, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ChangeDetectorRef }, { type: i2.FocusMonitor }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NzFormStatusService, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { inputElement: [{\n                type: ViewChild,\n                args: ['inputElement', { static: true }]\n            }], nzCheckedChange: [{\n                type: Output\n            }], nzValue: [{\n                type: Input\n            }], nzAutoFocus: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzIndeterminate: [{\n                type: Input\n            }], nzChecked: [{\n                type: Input\n            }], nzId: [{\n                type: Input\n            }] } });\n\nclass NzCheckboxGroupComponent {\n    onCheckedChange(option, checked) {\n        option.checked = checked;\n        this.onChange(this.options);\n    }\n    constructor(elementRef, focusMonitor, cdr, directionality) {\n        this.elementRef = elementRef;\n        this.focusMonitor = focusMonitor;\n        this.cdr = cdr;\n        this.directionality = directionality;\n        this.onChange = () => { };\n        this.onTouched = () => { };\n        this.options = [];\n        this.nzDisabled = false;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        this.isNzDisableFirstChange = true;\n    }\n    ngOnInit() {\n        this.focusMonitor\n            .monitor(this.elementRef, true)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(focusOrigin => {\n            if (!focusOrigin) {\n                Promise.resolve().then(() => this.onTouched());\n            }\n        });\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n    }\n    ngOnDestroy() {\n        this.focusMonitor.stopMonitoring(this.elementRef);\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    writeValue(value) {\n        this.options = value;\n        this.cdr.markForCheck();\n    }\n    registerOnChange(fn) {\n        this.onChange = fn;\n    }\n    registerOnTouched(fn) {\n        this.onTouched = fn;\n    }\n    setDisabledState(disabled) {\n        this.nzDisabled = (this.isNzDisableFirstChange && this.nzDisabled) || disabled;\n        this.isNzDisableFirstChange = false;\n        this.cdr.markForCheck();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxGroupComponent, deps: [{ token: i0.ElementRef }, { token: i2.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: i3.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzCheckboxGroupComponent, isStandalone: true, selector: \"nz-checkbox-group\", inputs: { nzDisabled: \"nzDisabled\" }, host: { properties: { \"class.ant-checkbox-group-rtl\": \"dir === 'rtl'\" }, classAttribute: \"ant-checkbox-group\" }, providers: [\n            {\n                provide: NG_VALUE_ACCESSOR,\n                useExisting: forwardRef(() => NzCheckboxGroupComponent),\n                multi: true\n            }\n        ], exportAs: [\"nzCheckboxGroup\"], ngImport: i0, template: `\n    @for (option of options; track option.value) {\n      <label\n        nz-checkbox\n        class=\"ant-checkbox-group-item\"\n        [nzDisabled]=\"option.disabled || nzDisabled\"\n        [nzChecked]=\"option.checked!\"\n        (nzCheckedChange)=\"onCheckedChange(option, $event)\"\n      >\n        <span>{{ option.label }}</span>\n      </label>\n    }\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzCheckboxComponent, selector: \"[nz-checkbox]\", inputs: [\"nzValue\", \"nzAutoFocus\", \"nzDisabled\", \"nzIndeterminate\", \"nzChecked\", \"nzId\"], outputs: [\"nzCheckedChange\"], exportAs: [\"nzCheckbox\"] }], encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzCheckboxGroupComponent.prototype, \"nzDisabled\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxGroupComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-checkbox-group',\n                    exportAs: 'nzCheckboxGroup',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    @for (option of options; track option.value) {\n      <label\n        nz-checkbox\n        class=\"ant-checkbox-group-item\"\n        [nzDisabled]=\"option.disabled || nzDisabled\"\n        [nzChecked]=\"option.checked!\"\n        (nzCheckedChange)=\"onCheckedChange(option, $event)\"\n      >\n        <span>{{ option.label }}</span>\n      </label>\n    }\n  `,\n                    providers: [\n                        {\n                            provide: NG_VALUE_ACCESSOR,\n                            useExisting: forwardRef(() => NzCheckboxGroupComponent),\n                            multi: true\n                        }\n                    ],\n                    host: {\n                        class: 'ant-checkbox-group',\n                        '[class.ant-checkbox-group-rtl]': `dir === 'rtl'`\n                    },\n                    imports: [NzCheckboxComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i2.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzDisabled: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxModule, imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent], exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxModule, imports: [NzCheckboxComponent, NzCheckboxGroupComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent],\n                    exports: [NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxWrapperComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCheckboxComponent, NzCheckboxGroupComponent, NzCheckboxModule, NzCheckboxWrapperComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC7J,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,iBAAiB,EAAEC,WAAW,QAAQ,gBAAgB;AAC/D,SAASC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACzC,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,YAAY,QAAQ,yBAAyB;AACtD,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,yBAAyB;;AAE7C;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,UAAA,GAAAA,CAAAC,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,KAAA;AAAA,SAAAC,wCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GAmBoG/B,EAAE,CAAAgC,gBAAA;IAAFhC,EAAE,CAAAiC,cAAA,cAgThG,CAAC;IAhT6FjC,EAAE,CAAAkC,UAAA,6BAAAC,yEAAAC,MAAA;MAAA,MAAAC,SAAA,GAAFrC,EAAE,CAAAsC,aAAA,CAAAP,GAAA,EAAAQ,SAAA;MAAA,MAAAC,MAAA,GAAFxC,EAAE,CAAAyC,aAAA;MAAA,OAAFzC,EAAE,CAAA0C,WAAA,CA+S3EF,MAAA,CAAAG,eAAA,CAAAN,SAAA,EAAAD,MAA8B,CAAC;IAAA,EAAC;IA/SyCpC,EAAE,CAAAiC,cAAA,UAiTzF,CAAC;IAjTsFjC,EAAE,CAAA4C,MAAA,EAiTvE,CAAC;IAjToE5C,EAAE,CAAA6C,YAAA,CAiThE,CAAC,CAC1B,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAQ,SAAA,GAAAP,GAAA,CAAAS,SAAA;IAAA,MAAAC,MAAA,GAlTsFxC,EAAE,CAAAyC,aAAA;IAAFzC,EAAE,CAAA8C,UAAA,eAAAT,SAAA,CAAAU,QAAA,IAAAP,MAAA,CAAAQ,UA6SnD,CAAC,cAAAX,SAAA,CAAAY,OAChB,CAAC;IA9S+DjD,EAAE,CAAAkD,SAAA,EAiTvE,CAAC;IAjToElD,EAAE,CAAAmD,iBAAA,CAAAd,SAAA,CAAAe,KAiTvE,CAAC;EAAA;AAAA;AAhUhC,MAAMC,0BAA0B,CAAC;EAC7BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,IAAItD,YAAY,CAAC,CAAC;IACpC,IAAI,CAACuD,YAAY,GAAG,EAAE;EAC1B;EACAC,WAAWA,CAAC9B,KAAK,EAAE;IACf,IAAI,CAAC6B,YAAY,CAACE,IAAI,CAAC/B,KAAK,CAAC;EACjC;EACAgC,cAAcA,CAAChC,KAAK,EAAE;IAClB,IAAI,CAAC6B,YAAY,CAACI,MAAM,CAAC,IAAI,CAACJ,YAAY,CAACK,OAAO,CAAClC,KAAK,CAAC,EAAE,CAAC,CAAC;EACjE;EACAmC,QAAQA,CAAA,EAAG;IACP,MAAMC,kBAAkB,GAAG,IAAI,CAACP,YAAY,CAACQ,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,SAAS,CAAC,CAACC,GAAG,CAACF,IAAI,IAAIA,IAAI,CAACG,OAAO,CAAC;IACrG,IAAI,CAACb,UAAU,CAACc,IAAI,CAACN,kBAAkB,CAAC;EAC5C;EACA;IAAS,IAAI,CAACO,IAAI,YAAAC,mCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFnB,0BAA0B;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACoB,IAAI,kBAD8EzE,EAAE,CAAA0E,iBAAA;MAAAC,IAAA,EACJtB,0BAA0B;MAAAuB,SAAA;MAAAC,SAAA;MAAAC,OAAA;QAAAvB,UAAA;MAAA;MAAAwB,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADxBjF,EAAE,CAAAkF,mBAAA;MAAAC,kBAAA,EAAA9D,GAAA;MAAA+D,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oCAAA1D,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF7B,EAAE,CAAAwF,eAAA;UAAFxF,EAAE,CAAAyF,YAAA,EACuP,CAAC;QAAA;MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAmH;EAAE;AACnd;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG5F,EAAE,CAAA6F,iBAAA,CAGXxC,0BAA0B,EAAc,CAAC;IACxHsB,IAAI,EAAEzE,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BhB,QAAQ,EAAE,mBAAmB;MAC7BiB,mBAAmB,EAAE,KAAK;MAC1BL,eAAe,EAAExF,uBAAuB,CAAC8F,MAAM;MAC/CP,aAAa,EAAEtF,iBAAiB,CAAC8F,IAAI;MACrCZ,QAAQ,EAAG,6BAA4B;MACvCa,IAAI,EAAE;QACFC,KAAK,EAAE;MACX,CAAC;MACDpB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzB,UAAU,EAAE,CAAC;MAC3BoB,IAAI,EAAEtE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMgG,mBAAmB,CAAC;EACtBC,kBAAkBA,CAACrD,OAAO,EAAE;IACxB,IAAI,CAAC,IAAI,CAACD,UAAU,EAAE;MAClB,IAAI,CAACkB,SAAS,GAAGjB,OAAO;MACxB,IAAI,CAACa,QAAQ,CAAC,IAAI,CAACI,SAAS,CAAC;MAC7B,IAAI,CAACqC,eAAe,CAAClC,IAAI,CAAC,IAAI,CAACH,SAAS,CAAC;MACzC,IAAI,IAAI,CAACsC,0BAA0B,EAAE;QACjC,IAAI,CAACA,0BAA0B,CAAC1C,QAAQ,CAAC,CAAC;MAC9C;IACJ;EACJ;EACA2C,UAAUA,CAAC9E,KAAK,EAAE;IACd,IAAI,CAACuC,SAAS,GAAGvC,KAAK;IACtB,IAAI,CAAC+E,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC/C,QAAQ,GAAG+C,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACE,SAAS,GAAGF,EAAE;EACvB;EACAG,gBAAgBA,CAACjE,QAAQ,EAAE;IACvB,IAAI,CAACC,UAAU,GAAI,IAAI,CAACiE,sBAAsB,IAAI,IAAI,CAACjE,UAAU,IAAKD,QAAQ;IAC9E,IAAI,CAACkE,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACP,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAO,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,YAAY,CAACC,QAAQ,CAAC,IAAI,CAACC,YAAY,EAAE,UAAU,CAAC;EAC7D;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACD,YAAY,CAACE,aAAa,CAACD,IAAI,CAAC,CAAC;EAC1C;EACAhE,WAAWA,CAACkE,MAAM,EAAEC,UAAU,EAAEjB,0BAA0B,EAAEE,GAAG,EAAES,YAAY,EAAEO,cAAc,EAAEC,mBAAmB,EAAE;IAChH,IAAI,CAACH,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACjB,0BAA0B,GAAGA,0BAA0B;IAC5D,IAAI,CAACE,GAAG,GAAGA,GAAG;IACd,IAAI,CAACS,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACO,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACC,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI/G,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACmG,sBAAsB,GAAG,IAAI;IAClC,IAAI,CAACnD,QAAQ,GAAG,MAAM,CAAE,CAAC;IACzB,IAAI,CAACiD,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B,IAAI,CAACR,eAAe,GAAG,IAAItG,YAAY,CAAC,CAAC;IACzC,IAAI,CAACmE,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC0D,WAAW,GAAG,KAAK;IACxB,IAAI,CAAC9E,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC+E,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC7D,SAAS,GAAG,KAAK;IACtB,IAAI,CAAC8D,IAAI,GAAG,IAAI;EACpB;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACd,YAAY,CACZe,OAAO,CAAC,IAAI,CAACT,UAAU,EAAE,IAAI,CAAC,CAC9BU,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACC,WAAW,IAAI;MAC1B,IAAI,CAACA,WAAW,EAAE;QACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACzB,SAAS,CAAC,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACP,0BAA0B,EAAE;MACjC,IAAI,CAACA,0BAA0B,CAAC/C,WAAW,CAAC,IAAI,CAAC;IACrD;IACA,IAAI,CAACiE,cAAc,CAACe,MAAM,CAACN,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAEM,SAAS,IAAK;MAC/E,IAAI,CAACd,GAAG,GAAGc,SAAS;MACpB,IAAI,CAAChC,GAAG,CAACiC,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACf,GAAG,GAAG,IAAI,CAACF,cAAc,CAAC/F,KAAK;IACpC,IAAI,CAAC6F,MAAM,CAACoB,iBAAiB,CAAC,MAAM;MAChC7H,SAAS,CAAC,IAAI,CAAC0G,UAAU,CAACF,aAAa,EAAE,OAAO,CAAC,CAC5CY,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACS,KAAK,IAAI;QACpBA,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC5B,KAAK,CAAC,CAAC;QACZ,IAAI,IAAI,CAAClE,UAAU,EAAE;UACjB;QACJ;QACA,IAAI,CAACwE,MAAM,CAACuB,GAAG,CAAC,MAAM;UAClB,IAAI,CAACzC,kBAAkB,CAAC,CAAC,IAAI,CAACpC,SAAS,CAAC;UACxC,IAAI,CAACwC,GAAG,CAACC,YAAY,CAAC,CAAC;QAC3B,CAAC,CAAC;MACN,CAAC,CAAC;MACF5F,SAAS,CAAC,IAAI,CAACsG,YAAY,CAACE,aAAa,EAAE,OAAO,CAAC,CAC9CY,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACS,KAAK,IAAIA,KAAK,CAACG,eAAe,CAAC,CAAC,CAAC;IACpD,CAAC,CAAC;EACN;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACnB,WAAW,EAAE;MAClB,IAAI,CAACZ,KAAK,CAAC,CAAC;IAChB;EACJ;EACAgC,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/B,YAAY,CAACgC,cAAc,CAAC,IAAI,CAAC1B,UAAU,CAAC;IACjD,IAAI,IAAI,CAACjB,0BAA0B,EAAE;MACjC,IAAI,CAACA,0BAA0B,CAAC7C,cAAc,CAAC,IAAI,CAAC;IACxD;IACA,IAAI,CAACkE,QAAQ,CAACuB,IAAI,CAAC,CAAC;IACpB,IAAI,CAACvB,QAAQ,CAACwB,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAAC/E,IAAI,YAAAgF,4BAAA9E,CAAA;MAAA,YAAAA,CAAA,IAAwF6B,mBAAmB,EA3H7BrG,EAAE,CAAAuJ,iBAAA,CA2H6CvJ,EAAE,CAACwJ,MAAM,GA3HxDxJ,EAAE,CAAAuJ,iBAAA,CA2HmEvJ,EAAE,CAACyJ,UAAU,GA3HlFzJ,EAAE,CAAAuJ,iBAAA,CA2H6FlG,0BAA0B,MA3HzHrD,EAAE,CAAAuJ,iBAAA,CA2HoJvJ,EAAE,CAAC0J,iBAAiB,GA3H1K1J,EAAE,CAAAuJ,iBAAA,CA2HqLrI,EAAE,CAACyI,YAAY,GA3HtM3J,EAAE,CAAAuJ,iBAAA,CA2HiNpI,EAAE,CAACyI,cAAc,MA3HpO5J,EAAE,CAAAuJ,iBAAA,CA2H+PnI,EAAE,CAACyI,mBAAmB;IAAA,CAA4D;EAAE;EACrb;IAAS,IAAI,CAACpF,IAAI,kBA5H8EzE,EAAE,CAAA0E,iBAAA;MAAAC,IAAA,EA4HJ0B,mBAAmB;MAAAzB,SAAA;MAAAkF,SAAA,WAAAC,0BAAAlI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5HjB7B,EAAE,CAAAgK,WAAA,CAAA1I,GAAA;QAAA;QAAA,IAAAO,EAAA;UAAA,IAAAoI,EAAA;UAAFjK,EAAE,CAAAkK,cAAA,CAAAD,EAAA,GAAFjK,EAAE,CAAAmK,WAAA,QAAArI,GAAA,CAAAuF,YAAA,GAAA4C,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAvF,SAAA;MAAAwF,QAAA;MAAAC,YAAA,WAAAC,iCAAA1I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF7B,EAAE,CAAAwK,WAAA,wCAAA1I,GAAA,CAAA6F,mBA4Hc,CAAC,iCAAA7F,GAAA,CAAAoC,SAAD,CAAC,qBAAApC,GAAA,CAAA8F,GAAA,KAAX,KAAU,CAAC;QAAA;MAAA;MAAA6C,MAAA;QAAArG,OAAA;QAAA0D,WAAA;QAAA9E,UAAA;QAAA+E,eAAA;QAAA7D,SAAA;QAAA8D,IAAA;MAAA;MAAAlD,OAAA;QAAAyB,eAAA;MAAA;MAAAxB,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA5HjBjF,EAAE,CAAA0K,kBAAA,CA4HggB,CAC1lB;QACIC,OAAO,EAAE/J,iBAAiB;QAC1BgK,WAAW,EAAEtK,UAAU,CAAC,MAAM+F,mBAAmB,CAAC;QAClDwE,KAAK,EAAE;MACX,CAAC,CACJ,GAlI2F7K,EAAE,CAAAkF,mBAAA;MAAA4F,KAAA,EAAAvJ,GAAA;MAAA4D,kBAAA,EAAA9D,GAAA;MAAA+D,KAAA;MAAAC,IAAA;MAAA0F,MAAA;MAAAzF,QAAA,WAAA0F,6BAAAnJ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAE,GAAA,GAAF/B,EAAE,CAAAgC,gBAAA;UAAFhC,EAAE,CAAAwF,eAAA;UAAFxF,EAAE,CAAAiC,cAAA,aAwIlG,CAAC,iBAWE,CAAC;UAnJ4FjC,EAAE,CAAAkC,UAAA,2BAAA+I,4DAAA7I,MAAA;YAAFpC,EAAE,CAAAsC,aAAA,CAAAP,GAAA;YAAA,OAAF/B,EAAE,CAAA0C,WAAA,CAkJ7EZ,GAAA,CAAAwE,kBAAA,CAAAlE,MAAyB,CAAC;UAAA,EAAC;UAlJgDpC,EAAE,CAAA6C,YAAA,CAmJ/F,CAAC;UAnJ4F7C,EAAE,CAAAkL,SAAA,aAoJzD,CAAC;UApJsDlL,EAAE,CAAA6C,YAAA,CAqJ5F,CAAC;UArJyF7C,EAAE,CAAAiC,cAAA,UAsJ7F,CAAC;UAtJ0FjC,EAAE,CAAAyF,YAAA,EAsJpE,CAAC;UAtJiEzF,EAAE,CAAA6C,YAAA,CAsJ7D,CAAC;QAAA;QAAA,IAAAhB,EAAA;UAtJ0D7B,EAAE,CAAAwK,WAAA,yBAAA1I,GAAA,CAAAoC,SAAA,KAAApC,GAAA,CAAAiG,eAqIrC,CAAC,0BAAAjG,GAAA,CAAAkB,UACnB,CAAC,+BAAAlB,GAAA,CAAAiG,eACS,CAAC;UAvI0C/H,EAAE,CAAAkD,SAAA,CA+I1E,CAAC;UA/IuElD,EAAE,CAAA8C,UAAA,YAAAhB,GAAA,CAAAoC,SA+I1E,CAAC,YAAApC,GAAA,CAAAoC,SACD,CAAC,aAAApC,GAAA,CAAAkB,UACC,CAAC;UAjJqEhD,EAAE,CAAAmL,WAAA,cAAArJ,GAAA,CAAAgG,WAAA,6BAAAhG,GAAA,CAAAkG,IAAA;QAAA;MAAA;MAAAoD,YAAA,GAuJxCvK,WAAW,EAA+BF,EAAE,CAAC0K,4BAA4B,EAAkJ1K,EAAE,CAAC2K,eAAe,EAAsF3K,EAAE,CAAC4K,OAAO;MAAA7F,aAAA;MAAAC,eAAA;IAAA,EAAmR;EAAE;AAChqB;AACA5F,UAAU,CAAC,CACPkB,YAAY,CAAC,CAAC,CACjB,EAAEoF,mBAAmB,CAACmF,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACxDzL,UAAU,CAAC,CACPkB,YAAY,CAAC,CAAC,CACjB,EAAEoF,mBAAmB,CAACmF,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACvDzL,UAAU,CAAC,CACPkB,YAAY,CAAC,CAAC,CACjB,EAAEoF,mBAAmB,CAACmF,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AAC5DzL,UAAU,CAAC,CACPkB,YAAY,CAAC,CAAC,CACjB,EAAEoF,mBAAmB,CAACmF,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACtD;EAAA,QAAA5F,SAAA,oBAAAA,SAAA,KArKoG5F,EAAE,CAAA6F,iBAAA,CAqKXQ,mBAAmB,EAAc,CAAC;IACjH1B,IAAI,EAAEzE,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBhB,QAAQ,EAAE,YAAY;MACtBiB,mBAAmB,EAAE,KAAK;MAC1BL,eAAe,EAAExF,uBAAuB,CAAC8F,MAAM;MAC/CP,aAAa,EAAEtF,iBAAiB,CAAC8F,IAAI;MACrCZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBmG,SAAS,EAAE,CACP;QACId,OAAO,EAAE/J,iBAAiB;QAC1BgK,WAAW,EAAEtK,UAAU,CAAC,MAAM+F,mBAAmB,CAAC;QAClDwE,KAAK,EAAE;MACX,CAAC,CACJ;MACD1E,IAAI,EAAE;QACFC,KAAK,EAAE,sBAAsB;QAC7B,2CAA2C,EAAE,uBAAuB;QACpE,sCAAsC,EAAE,WAAW;QACnD,0BAA0B,EAAG;MACjC,CAAC;MACDsF,OAAO,EAAE,CAAC7K,WAAW,CAAC;MACtBmE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEL,IAAI,EAAE3E,EAAE,CAACwJ;EAAO,CAAC,EAAE;IAAE7E,IAAI,EAAE3E,EAAE,CAACyJ;EAAW,CAAC,EAAE;IAAE9E,IAAI,EAAEtB,0BAA0B;IAAEsI,UAAU,EAAE,CAAC;MAC9GhH,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC,EAAE;IAAEoE,IAAI,EAAE3E,EAAE,CAAC0J;EAAkB,CAAC,EAAE;IAAE/E,IAAI,EAAEzD,EAAE,CAACyI;EAAa,CAAC,EAAE;IAAEhF,IAAI,EAAExD,EAAE,CAACyI,cAAc;IAAE+B,UAAU,EAAE,CAAC;MACrGhH,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC,EAAE;IAAEoE,IAAI,EAAEvD,EAAE,CAACyI,mBAAmB;IAAE8B,UAAU,EAAE,CAAC;MAC/ChH,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8G,YAAY,EAAE,CAAC;MACxC1C,IAAI,EAAEnE,SAAS;MACfsF,IAAI,EAAE,CAAC,cAAc,EAAE;QAAE8F,MAAM,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAErF,eAAe,EAAE,CAAC;MAClB5B,IAAI,EAAEtE;IACV,CAAC,CAAC;IAAE+D,OAAO,EAAE,CAAC;MACVO,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEqH,WAAW,EAAE,CAAC;MACdnD,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEuC,UAAU,EAAE,CAAC;MACb2B,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEsH,eAAe,EAAE,CAAC;MAClBpD,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEyD,SAAS,EAAE,CAAC;MACZS,IAAI,EAAElE;IACV,CAAC,CAAC;IAAEuH,IAAI,EAAE,CAAC;MACPrD,IAAI,EAAElE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoL,wBAAwB,CAAC;EAC3BlJ,eAAeA,CAACmJ,MAAM,EAAE7I,OAAO,EAAE;IAC7B6I,MAAM,CAAC7I,OAAO,GAAGA,OAAO;IACxB,IAAI,CAACa,QAAQ,CAAC,IAAI,CAACiI,OAAO,CAAC;EAC/B;EACAzI,WAAWA,CAACmE,UAAU,EAAEN,YAAY,EAAET,GAAG,EAAEgB,cAAc,EAAE;IACvD,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACN,YAAY,GAAGA,YAAY;IAChC,IAAI,CAACT,GAAG,GAAGA,GAAG;IACd,IAAI,CAACgB,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC5D,QAAQ,GAAG,MAAM,CAAE,CAAC;IACzB,IAAI,CAACiD,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B,IAAI,CAACgF,OAAO,GAAG,EAAE;IACjB,IAAI,CAAC/I,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC4E,GAAG,GAAG,KAAK;IAChB,IAAI,CAACC,QAAQ,GAAG,IAAI/G,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACmG,sBAAsB,GAAG,IAAI;EACtC;EACAgB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACd,YAAY,CACZe,OAAO,CAAC,IAAI,CAACT,UAAU,EAAE,IAAI,CAAC,CAC9BU,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACC,WAAW,IAAI;MAC1B,IAAI,CAACA,WAAW,EAAE;QACdC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACzB,SAAS,CAAC,CAAC,CAAC;MAClD;IACJ,CAAC,CAAC;IACF,IAAI,CAACW,cAAc,CAACe,MAAM,EAAEN,IAAI,CAACnH,SAAS,CAAC,IAAI,CAAC6G,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAEM,SAAS,IAAK;MAChF,IAAI,CAACd,GAAG,GAAGc,SAAS;MACpB,IAAI,CAAChC,GAAG,CAACiC,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACf,GAAG,GAAG,IAAI,CAACF,cAAc,CAAC/F,KAAK;EACxC;EACAuH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/B,YAAY,CAACgC,cAAc,CAAC,IAAI,CAAC1B,UAAU,CAAC;IACjD,IAAI,CAACI,QAAQ,CAACuB,IAAI,CAAC,CAAC;IACpB,IAAI,CAACvB,QAAQ,CAACwB,QAAQ,CAAC,CAAC;EAC5B;EACA5C,UAAUA,CAAC9E,KAAK,EAAE;IACd,IAAI,CAACoK,OAAO,GAAGpK,KAAK;IACpB,IAAI,CAAC+E,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC/C,QAAQ,GAAG+C,EAAE;EACtB;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACE,SAAS,GAAGF,EAAE;EACvB;EACAG,gBAAgBA,CAACjE,QAAQ,EAAE;IACvB,IAAI,CAACC,UAAU,GAAI,IAAI,CAACiE,sBAAsB,IAAI,IAAI,CAACjE,UAAU,IAAKD,QAAQ;IAC9E,IAAI,CAACkE,sBAAsB,GAAG,KAAK;IACnC,IAAI,CAACP,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACA;IAAS,IAAI,CAACrC,IAAI,YAAA0H,iCAAAxH,CAAA;MAAA,YAAAA,CAAA,IAAwFqH,wBAAwB,EAjSlC7L,EAAE,CAAAuJ,iBAAA,CAiSkDvJ,EAAE,CAACyJ,UAAU,GAjSjEzJ,EAAE,CAAAuJ,iBAAA,CAiS4ErI,EAAE,CAACyI,YAAY,GAjS7F3J,EAAE,CAAAuJ,iBAAA,CAiSwGvJ,EAAE,CAAC0J,iBAAiB,GAjS9H1J,EAAE,CAAAuJ,iBAAA,CAiSyIpI,EAAE,CAACyI,cAAc;IAAA,CAA4D;EAAE;EAC1T;IAAS,IAAI,CAACnF,IAAI,kBAlS8EzE,EAAE,CAAA0E,iBAAA;MAAAC,IAAA,EAkSJkH,wBAAwB;MAAAjH,SAAA;MAAAC,SAAA;MAAAwF,QAAA;MAAAC,YAAA,WAAA2B,sCAAApK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlStB7B,EAAE,CAAAwK,WAAA,2BAAA1I,GAAA,CAAA8F,GAAA,KAkSI,KAAe,CAAC;QAAA;MAAA;MAAA6C,MAAA;QAAAzH,UAAA;MAAA;MAAA+B,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAlStBjF,EAAE,CAAA0K,kBAAA,CAkS2O,CACrU;QACIC,OAAO,EAAE/J,iBAAiB;QAC1BgK,WAAW,EAAEtK,UAAU,CAAC,MAAMuL,wBAAwB,CAAC;QACvDhB,KAAK,EAAE;MACX,CAAC,CACJ,GAxS2F7K,EAAE,CAAAkF,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAA0F,MAAA;MAAAzF,QAAA,WAAA4G,kCAAArK,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF7B,EAAE,CAAAmM,gBAAA,IAAAvK,uCAAA,oBAAAJ,UAmTlG,CAAC;QAAA;QAAA,IAAAK,EAAA;UAnT+F7B,EAAE,CAAAoM,UAAA,CAAAtK,GAAA,CAAAiK,OAmTlG,CAAC;QAAA;MAAA;MAAAX,YAAA,GAC0D/E,mBAAmB;MAAAX,aAAA;IAAA,EAA6N;EAAE;AACjT;AACA3F,UAAU,CAAC,CACPkB,YAAY,CAAC,CAAC,CACjB,EAAE4K,wBAAwB,CAACL,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AAC5D;EAAA,QAAA5F,SAAA,oBAAAA,SAAA,KAzToG5F,EAAE,CAAA6F,iBAAA,CAyTXgG,wBAAwB,EAAc,CAAC;IACtHlH,IAAI,EAAEzE,SAAS;IACf4F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BhB,QAAQ,EAAE,iBAAiB;MAC3BiB,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEtF,iBAAiB,CAAC8F,IAAI;MACrCZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBmG,SAAS,EAAE,CACP;QACId,OAAO,EAAE/J,iBAAiB;QAC1BgK,WAAW,EAAEtK,UAAU,CAAC,MAAMuL,wBAAwB,CAAC;QACvDhB,KAAK,EAAE;MACX,CAAC,CACJ;MACD1E,IAAI,EAAE;QACFC,KAAK,EAAE,oBAAoB;QAC3B,gCAAgC,EAAG;MACvC,CAAC;MACDsF,OAAO,EAAE,CAACrF,mBAAmB,CAAC;MAC9BrB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEL,IAAI,EAAE3E,EAAE,CAACyJ;EAAW,CAAC,EAAE;IAAE9E,IAAI,EAAEzD,EAAE,CAACyI;EAAa,CAAC,EAAE;IAAEhF,IAAI,EAAE3E,EAAE,CAAC0J;EAAkB,CAAC,EAAE;IAAE/E,IAAI,EAAExD,EAAE,CAACyI,cAAc;IAAE+B,UAAU,EAAE,CAAC;MAC3IhH,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyC,UAAU,EAAE,CAAC;MACtC2B,IAAI,EAAElE;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM4L,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAAC/H,IAAI,YAAAgI,yBAAA9H,CAAA;MAAA,YAAAA,CAAA,IAAwF6H,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACE,IAAI,kBAvW8EvM,EAAE,CAAAwM,gBAAA;MAAA7H,IAAA,EAuWS0H,gBAAgB;MAAAX,OAAA,GAAYrF,mBAAmB,EAAEwF,wBAAwB,EAAExI,0BAA0B;MAAAoJ,OAAA,GAAapG,mBAAmB,EAAEwF,wBAAwB,EAAExI,0BAA0B;IAAA,EAAI;EAAE;EAC5S;IAAS,IAAI,CAACqJ,IAAI,kBAxW8E1M,EAAE,CAAA2M,gBAAA;MAAAjB,OAAA,GAwWqCrF,mBAAmB,EAAEwF,wBAAwB;IAAA,EAAI;EAAE;AAC9L;AACA;EAAA,QAAAjG,SAAA,oBAAAA,SAAA,KA1WoG5F,EAAE,CAAA6F,iBAAA,CA0WXwG,gBAAgB,EAAc,CAAC;IAC9G1H,IAAI,EAAEjE,QAAQ;IACdoF,IAAI,EAAE,CAAC;MACC4F,OAAO,EAAE,CAACrF,mBAAmB,EAAEwF,wBAAwB,EAAExI,0BAA0B,CAAC;MACpFoJ,OAAO,EAAE,CAACpG,mBAAmB,EAAEwF,wBAAwB,EAAExI,0BAA0B;IACvF,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASgD,mBAAmB,EAAEwF,wBAAwB,EAAEQ,gBAAgB,EAAEhJ,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}