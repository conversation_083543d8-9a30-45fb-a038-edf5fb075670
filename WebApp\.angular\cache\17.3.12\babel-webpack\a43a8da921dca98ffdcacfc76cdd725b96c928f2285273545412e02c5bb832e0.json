{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-user.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./add-user.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, Inject } from '@angular/core';\nimport { NzTableModule } from 'ng-zorro-antd/table';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NZ_MODAL_DATA, NzModalModule, NzModalRef, NzModalService } from 'ng-zorro-antd/modal';\nimport { AssignWorkspaceServiceProxy, UserAccountServiceProxy } from '../../../shared/service-proxies/service-proxies';\nlet AddUserComponent = class AddUserComponent {\n  constructor(modalRef, userAccountService, modalService, assignWorkspaceService, data // Inject the workspace ID and user list\n  ) {\n    this.modalRef = modalRef;\n    this.userAccountService = userAccountService;\n    this.modalService = modalService;\n    this.assignWorkspaceService = assignWorkspaceService;\n    this.data = data;\n    this.users = []; // Array to hold user data\n    this.addedUsers = []; // Array to hold added users\n  }\n  ngOnInit() {\n    this.loadUsers();\n    console.log(this.data.id);\n    console.log(this.data.users);\n  }\n  loadUsers() {\n    this.userAccountService.getAll().subscribe(users => {\n      this.users = users.filter(user => !this.data.users.some(u => u.email === user.email));\n    });\n  }\n  addUser(user) {\n    this.assignWorkspaceService.assignUser(this.data.id, user.email).subscribe(response => {\n      console.log('User added:', response);\n      this.addedUsers.push(user);\n      // remove added user from the list\n      this.users = this.users.filter(u => u.email !== user.email);\n    });\n  }\n  closeDialog() {\n    this.modalRef.close({\n      addedUsers: this.addedUsers // Return the added users to the parent component\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NzModalRef\n    }, {\n      type: UserAccountServiceProxy\n    }, {\n      type: NzModalService\n    }, {\n      type: AssignWorkspaceServiceProxy\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NZ_MODAL_DATA]\n      }]\n    }];\n  }\n};\nAddUserComponent = __decorate([Component({\n  selector: 'app-add-user',\n  standalone: true,\n  imports: [CommonModule, NzTableModule, NzButtonModule, NzModalModule],\n  template: __NG_CLI_RESOURCE__0,\n  providers: [AssignWorkspaceServiceProxy],\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddUserComponent);\nexport { AddUserComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "Inject", "NzTableModule", "NzButtonModule", "NZ_MODAL_DATA", "NzModalModule", "NzModalRef", "NzModalService", "AssignWorkspaceServiceProxy", "UserAccountServiceProxy", "AddUserComponent", "constructor", "modalRef", "userAccountService", "modalService", "assignWorkspaceService", "data", "users", "addedUsers", "ngOnInit", "loadUsers", "console", "log", "id", "getAll", "subscribe", "filter", "user", "some", "u", "email", "addUser", "assignUser", "response", "push", "closeDialog", "close", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0", "providers"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\dialogs\\add-user\\add-user.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, Inject, Input } from '@angular/core';\r\nimport { NzTableModule } from 'ng-zorro-antd/table';\r\nimport { NzButtonModule } from 'ng-zorro-antd/button';\r\nimport {\r\n  NZ_MODAL_DATA,\r\n  NzModalModule,\r\n  NzModalRef,\r\n  NzModalService,\r\n} from 'ng-zorro-antd/modal';\r\nimport {\r\n  AssignWorkspaceServiceProxy,\r\n  UserAccountServiceProxy,\r\n} from '../../../shared/service-proxies/service-proxies';\r\n@Component({\r\n  selector: 'app-add-user',\r\n  standalone: true,\r\n  imports: [CommonModule, NzTableModule, NzButtonModule, NzModalModule],\r\n  templateUrl: './add-user.component.html',\r\n  styleUrl: './add-user.component.css',\r\n  providers: [AssignWorkspaceServiceProxy],\r\n})\r\nexport class AddUserComponent {\r\n  users: any = []; // Array to hold user data\r\n  addedUsers: any = []; // Array to hold added users\r\n  constructor(\r\n    private modalRef: NzModalRef,\r\n    private userAccountService: UserAccountServiceProxy,\r\n    private modalService: NzModalService,\r\n    private assignWorkspaceService: AssignWorkspaceServiceProxy,\r\n    @Inject(NZ_MODAL_DATA) public data: { id: number; users: any } // Inject the workspace ID and user list\r\n  ) {}\r\n  ngOnInit(): void {\r\n    this.loadUsers();\r\n    console.log(this.data.id);\r\n    console.log(this.data.users);\r\n  }\r\n  loadUsers() {\r\n    this.userAccountService.getAll().subscribe((users: any) => {\r\n      this.users = users.filter((user: any) => !this.data.users.some((u: any) => u.email === user.email));\r\n    });\r\n  }\r\n  addUser(user: any) {\r\n    this.assignWorkspaceService\r\n      .assignUser(this.data.id, user.email)\r\n      .subscribe((response: any) => {\r\n        console.log('User added:', response);\r\n        this.addedUsers.push(user);\r\n        // remove added user from the list\r\n        this.users = this.users.filter((u: any) => u.email !== user.email);\r\n      });\r\n  }\r\n  closeDialog(): void {\r\n    this.modalRef.close({\r\n      addedUsers: this.addedUsers, // Return the added users to the parent component\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,MAAM,QAAe,eAAe;AACxD,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,aAAa,EACbC,aAAa,EACbC,UAAU,EACVC,cAAc,QACT,qBAAqB;AAC5B,SACEC,2BAA2B,EAC3BC,uBAAuB,QAClB,iDAAiD;AASjD,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAG3BC,YACUC,QAAoB,EACpBC,kBAA2C,EAC3CC,YAA4B,EAC5BC,sBAAmD,EAC7BC,IAAgC,CAAC;EAAA,E;IAJvD,KAAAJ,QAAQ,GAARA,QAAQ;IACR,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACA,KAAAC,IAAI,GAAJA,IAAI;IAPpC,KAAAC,KAAK,GAAQ,EAAE,CAAC,CAAC;IACjB,KAAAC,UAAU,GAAQ,EAAE,CAAC,CAAC;EAOnB;EACHC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;IAChBC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,CAACO,EAAE,CAAC;IACzBF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,CAACC,KAAK,CAAC;EAC9B;EACAG,SAASA,CAAA;IACP,IAAI,CAACP,kBAAkB,CAACW,MAAM,EAAE,CAACC,SAAS,CAAER,KAAU,IAAI;MACxD,IAAI,CAACA,KAAK,GAAGA,KAAK,CAACS,MAAM,CAAEC,IAAS,IAAK,CAAC,IAAI,CAACX,IAAI,CAACC,KAAK,CAACW,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,KAAK,KAAKH,IAAI,CAACG,KAAK,CAAC,CAAC;IACrG,CAAC,CAAC;EACJ;EACAC,OAAOA,CAACJ,IAAS;IACf,IAAI,CAACZ,sBAAsB,CACxBiB,UAAU,CAAC,IAAI,CAAChB,IAAI,CAACO,EAAE,EAAEI,IAAI,CAACG,KAAK,CAAC,CACpCL,SAAS,CAAEQ,QAAa,IAAI;MAC3BZ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEW,QAAQ,CAAC;MACpC,IAAI,CAACf,UAAU,CAACgB,IAAI,CAACP,IAAI,CAAC;MAC1B;MACA,IAAI,CAACV,KAAK,GAAG,IAAI,CAACA,KAAK,CAACS,MAAM,CAAEG,CAAM,IAAKA,CAAC,CAACC,KAAK,KAAKH,IAAI,CAACG,KAAK,CAAC;IACpE,CAAC,CAAC;EACN;EACAK,WAAWA,CAAA;IACT,IAAI,CAACvB,QAAQ,CAACwB,KAAK,CAAC;MAClBlB,UAAU,EAAE,IAAI,CAACA,UAAU,CAAE;KAC9B,CAAC;EACJ;;;;;;;;;;;;;cA1BGjB,MAAM;QAAAoC,IAAA,GAACjC,aAAa;MAAA;IAAA,E;;;AARZM,gBAAgB,GAAA4B,UAAA,EAR5BtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,cAAc;EACxBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAC1C,YAAY,EAAEG,aAAa,EAAEC,cAAc,EAAEE,aAAa,CAAC;EACrEqC,QAAA,EAAAC,oBAAwC;EAExCC,SAAS,EAAE,CAACpC,2BAA2B,CAAC;;CACzC,CAAC,C,EACWE,gBAAgB,CAmC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}