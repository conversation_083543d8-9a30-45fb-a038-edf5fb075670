/* Agent List Container Styles */
.agent-list-container {
  overflow-y: auto;
  scrollbar-width: thin;
}

/* Custom scrollbar for webkit browsers */
.agent-list-container::-webkit-scrollbar {
  width: 6px;
}

.agent-list-container::-webkit-scrollbar-track {
  background: transparent;
}

.agent-list-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.agent-list-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Agent Card Hover Effects */
.agent-card {
  transform: translateY(0);
  transition: all 0.2s ease-in-out;
}

.agent-card:hover {
  transform: translateY(-2px);
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Smooth transitions for theme changes */
.agent-list-container * {
  transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
}