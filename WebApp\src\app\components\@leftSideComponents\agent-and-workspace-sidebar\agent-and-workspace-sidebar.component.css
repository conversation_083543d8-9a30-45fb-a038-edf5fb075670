/* Agent and Workspace Sidebar Styling - Based on Notes Sidebar */
:host {
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Tooltip styles */
.tooltip-container {
  position: relative;
  display: inline-block;
}

.custom-tooltip {
  visibility: hidden;
  position: absolute;
  background-color: var(--background-light-gray);
  color: var(--text-dark);
  text-align: center;
  padding: 4px 8px;
  border-radius: 4px;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--hover-blue-gray);
}

.bottom-tooltip {
  top: 125%;
  left: 50%;
  transform: translateX(-50%);
}

.tooltip-container:hover .custom-tooltip {
  visibility: visible;
  opacity: 1;
}

/* Item Styling - Exact Settings Sidebar Pattern */

/* Active indicator bar animation - Exact match */
.absolute.left-0 {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced active indicator in light theme - Exact match */
:host-context(:not(.dark)) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(107, 70, 193, 0.5);
  width: 4px !important;
}

/* Enhanced active indicator in dark theme - Exact match */
:host-context(.dark) .absolute.left-0.opacity-100 {
  box-shadow: 0 0 8px rgba(16, 163, 127, 0.5);
  width: 4px !important;
}

/* Hover effects - Exact match from settings sidebar */
.hover\:bg-\[var\(--primary-purple\)\]:hover {
  background-color: var(--primary-purple) !important;
  transform: translateX(2px);
}

.group:hover {
  transform: translateX(2px);
}

/* Improve hover text visibility - Theme-specific colors */
:host-context(:not(.dark)) .group:hover i,
:host-context(:not(.dark)) .group:hover span {
  color: black !important;
}

:host-context(.dark) .group:hover i,
:host-context(.dark) .group:hover span {
  color: white !important;
}

/* Transition effects - Exact match */
.transition-all {
  transition: all 0.3s ease;
}

/* Smooth transitions for all elements - Exact match */
i, span {
  transition: all 0.2s ease;
}

/* Accordion Styles */
.accordion-section {
  margin-bottom: 8px;
}

.accordion-header {
  user-select: none;
  border-radius: 6px;
  transition: background-color 0.15s ease;
}

.accordion-header:hover {
  background-color: rgba(255, 255, 255, 0.06) !important;
}

.accordion-content {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

/* Custom scrollbar for accordion content */
.accordion-content::-webkit-scrollbar {
  width: 4px;
}

.accordion-content::-webkit-scrollbar-track {
  background: transparent;
}

.accordion-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
}

.accordion-content::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Smooth transitions for accordion */
.accordion-content {
  transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out, margin-top 0.3s ease-in-out;
}

/* Chevron rotation animation */
.accordion-header i {
  transition: transform 0.2s ease-in-out;
}

/* Loading Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Enhanced Professional Scrollbar Styling - Aligned with Global Styles */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
  border-radius: 9999px;
  transition: all 0.2s ease;
  opacity: 0.8;
}

::-webkit-scrollbar-thumb:hover,
:hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
  transform: scaleX(1.1);
}

/* Theme-aware scrollbar styling */
.dark-theme ::-webkit-scrollbar-thumb,
:host(.dark-theme) ::-webkit-scrollbar-thumb {
  background-color: var(--hover-blue-gray);
}

.dark-theme ::-webkit-scrollbar-thumb:hover,
.dark-theme :hover ::-webkit-scrollbar-thumb,
:host(.dark-theme) ::-webkit-scrollbar-thumb:hover,
:host(.dark-theme) :hover ::-webkit-scrollbar-thumb {
  background-color: var(--secondary-purple);
  cursor: pointer;
}

/* Firefox scrollbar styling */
.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: var(--hover-blue-gray) transparent;
  transition: scrollbar-color 0.3s ease;
}

.overflow-y-auto:hover {
  scrollbar-color: var(--secondary-purple) transparent;
}

/* Focus states for accessibility */
button:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

.cursor-pointer:focus-visible {
  outline: 2px solid var(--primary-purple);
  outline-offset: 2px;
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(0);
}

/* Item hover effects */
.cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Theme-specific enhancements */
.dark-theme .cursor-pointer:hover,
:host(.dark-theme) .cursor-pointer:hover {
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}

/* Enhanced Item Styling - Consistent with Settings Sidebar */
.space-y-1 > div.group {
  position: relative;
  background: transparent;
  border: 1px solid transparent;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Active state for items */
.space-y-1 > div.group:active {
  transform: translateX(1px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* Star icon enhancements */
.space-y-1 > div.group .ri-star-fill {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 50%;
  padding: 2px;
}

.space-y-1 > div.group .ri-star-fill:hover {
  background-color: rgba(251, 191, 36, 0.1);
  transform: scale(1.2);
}

/* Responsive design for smaller screens */
@media (max-width: 768px) {
  .p-4 {
    padding: 0.75rem;
  }

  .p-3 {
    padding: 0.5rem;
  }

  .text-sm {
    font-size: 0.75rem;
  }
}

/* Animation for items */
.space-y-1 > * {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ensure proper spacing */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

/* Text truncation */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Flex utilities */
.flex-shrink-0 {
  flex-shrink: 0;
}

.min-w-0 {
  min-width: 0;
}
