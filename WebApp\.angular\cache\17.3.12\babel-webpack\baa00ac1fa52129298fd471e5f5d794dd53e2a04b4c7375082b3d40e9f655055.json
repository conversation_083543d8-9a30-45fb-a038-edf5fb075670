{"ast": null, "code": "/**\n * @name endOfTomorrow\n * @category Day Helpers\n * @summary Return the end of tomorrow.\n * @pure false\n *\n * @description\n * Return the end of tomorrow.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of tomorrow\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfTomorrow()\n * //=> Tue Oct 7 2014 23:59:59.999\n */\nexport default function endOfTomorrow() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}", "map": {"version": 3, "names": ["endOfTomorrow", "now", "Date", "year", "getFullYear", "month", "getMonth", "day", "getDate", "date", "setFullYear", "setHours"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/endOfTomorrow/index.js"], "sourcesContent": ["/**\n * @name endOfTomorrow\n * @category Day Helpers\n * @summary Return the end of tomorrow.\n * @pure false\n *\n * @description\n * Return the end of tomorrow.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `new Date()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the end of tomorrow\n *\n * @example\n * // If today is 6 October 2014:\n * const result = endOfTomorrow()\n * //=> Tue Oct 7 2014 23:59:59.999\n */\nexport default function endOfTomorrow() {\n  var now = new Date();\n  var year = now.getFullYear();\n  var month = now.getMonth();\n  var day = now.getDate();\n  var date = new Date(0);\n  date.setFullYear(year, month, day + 1);\n  date.setHours(23, 59, 59, 999);\n  return date;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,aAAaA,CAAA,EAAG;EACtC,IAAIC,GAAG,GAAG,IAAIC,IAAI,CAAC,CAAC;EACpB,IAAIC,IAAI,GAAGF,GAAG,CAACG,WAAW,CAAC,CAAC;EAC5B,IAAIC,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAAC,CAAC;EAC1B,IAAIC,GAAG,GAAGN,GAAG,CAACO,OAAO,CAAC,CAAC;EACvB,IAAIC,IAAI,GAAG,IAAIP,IAAI,CAAC,CAAC,CAAC;EACtBO,IAAI,CAACC,WAAW,CAACP,IAAI,EAAEE,KAAK,EAAEE,GAAG,GAAG,CAAC,CAAC;EACtCE,IAAI,CAACE,QAAQ,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EAC9B,OAAOF,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}