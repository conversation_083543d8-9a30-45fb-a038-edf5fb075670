{"ast": null, "code": "/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n  nativeMin = Math.min;\n\n/**\n * The base implementation of `_.inRange` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to check.\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\nfunction baseInRange(number, start, end) {\n  return number >= nativeMin(start, end) && number < nativeMax(start, end);\n}\nexport default baseInRange;", "map": {"version": 3, "names": ["nativeMax", "Math", "max", "nativeMin", "min", "baseInRange", "number", "start", "end"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseInRange.js"], "sourcesContent": ["/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * The base implementation of `_.inRange` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to check.\n * @param {number} start The start of the range.\n * @param {number} end The end of the range.\n * @returns {boolean} Returns `true` if `number` is in the range, else `false`.\n */\nfunction baseInRange(number, start, end) {\n  return number >= nativeMin(start, end) && number < nativeMax(start, end);\n}\n\nexport default baseInRange;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EACvC,OAAOF,MAAM,IAAIH,SAAS,CAACI,KAAK,EAAEC,GAAG,CAAC,IAAIF,MAAM,GAAGN,SAAS,CAACO,KAAK,EAAEC,GAAG,CAAC;AAC1E;AAEA,eAAeH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}