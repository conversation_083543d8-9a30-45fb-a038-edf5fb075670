import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  AgentChatServiceProxy,
  ResponseMessageList,
  WorkspaceDto,
  WorkspaceServiceProxy,
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-agent-and-workspace-sidebar',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule],
  templateUrl: './agent-and-workspace-sidebar.component.html',
  styleUrl: './agent-and-workspace-sidebar.component.css',
})
export class AgentAndWorkspaceSidebarComponent implements OnInit {
  // Inject services
  themeService = inject(ThemeService);
  router = inject(Router);

  // Component properties
  agents: string[] = [];
  workspaces: WorkspaceDto[] = [];

  // Loading states
  isLoadingAgents = false;
  isLoadingWorkspaces = false;

  // Error states
  agentsError: string | null = null;
  workspacesError: string | null = null;

  // Accordion state management
  accordionSections = {
    agents: {
      isExpanded: true,
      title: 'Agents',
      icon: 'ri-robot-line'
    },
    workspaces: {
      isExpanded: false,
      title: 'Workspaces',
      icon: 'ri-building-line'
    }
  };

  // UI state
  selectedWorkspace: WorkspaceDto | null = null;

  constructor(
    private _agentChatService: AgentChatServiceProxy,
    private workspaceService: WorkspaceServiceProxy
  ) {}

  ngOnInit() {
    this.loadAgents();
    this.LoadAllWorkspaces();
    this.loadAccordionState();
  }

  loadAgents() {
    this.isLoadingAgents = true;
    this.agentsError = null;

    this._agentChatService.agentContainingChat().subscribe({
      next: (response: ResponseMessageList) => {
        // Handle the ResponseMessageList format
        if (response) {
          if (response.isError === false) {
            // Success case: extract agents from message array
            if (response.message && Array.isArray(response.message)) {
              this.agents = response.message;
              this.agentsError = null;
              console.log('Successfully loaded agents:', this.agents);
            } else {
              // No agents in response
              this.agents = [];
              this.agentsError = 'No agents found';
              console.warn(
                'Response message is not an array:',
                response.message
              );
            }
          } else {
            // Error case: API returned an error
            this.agents = [];
            this.agentsError = response.message
              ? response.message.join(', ')
              : 'Failed to load agents';
            console.error('API returned error:', response.message);
          }
        } else {
          // Invalid response
          this.agents = [];
          this.agentsError = 'Invalid response from server';
          console.error('Invalid response:', response);
        }

        this.isLoadingAgents = false;
      },
      error: (error) => {
        console.error('HTTP Error loading agents:', error);
        this.agentsError = 'Failed to connect to server';
        this.isLoadingAgents = false;
        this.agents = [];
      },
    });
  }
  LoadAllWorkspaces() {
    this.isLoadingWorkspaces = true;
    this.workspacesError = null;

    this.workspaceService.getAll().subscribe({
      next: (response: WorkspaceDto[]) => {
        this.workspaces = response || [];
        this.workspacesError = null;
        this.isLoadingWorkspaces = false;
        console.log('Workspaces loaded:', this.workspaces);
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
        this.workspacesError = 'Failed to load workspaces';
        this.isLoadingWorkspaces = false;
        this.workspaces = [];
      },
    });
  }

  navigateToAgentChat(agentName: string) {
    if (!agentName) return;

    console.log('Navigating to agent chat:', agentName);
    this.router.navigate(['/agent-chat', agentName]);
  }

  /**
   * Retry loading agents
   */
  retryLoadAgents() {
    this.loadAgents();
  }

  /**
   * Retry loading workspaces
   */
  retryLoadWorkspaces() {
    this.LoadAllWorkspaces();
  }

  /**
   * Navigate to workspace
   */
  navigateToWorkspace(workspace: WorkspaceDto) {
    if (!workspace) return;

    this.selectedWorkspace = workspace;
    console.log('Selected workspace:', workspace);

    // Navigate to workspace page or update current workspace context
    // You can customize this based on your routing needs
    this.router.navigate(['/workspace-chats', workspace.title]);
  }

  /**
   * Format agent name for display (convert CamelCase to readable format)
   */
  formatAgentName(agentName: string): string {
    if (!agentName) return '';

    // Convert CamelCase to readable format
    // EmailGeneratorAgent -> Email Generator Agent
    return agentName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^Agent\s*/, '') // Remove "Agent" prefix if exists
      .replace(/\s*Agent$/, '') // Remove "Agent" suffix if exists
      .trim(); // Remove extra spaces
  }



  /**
   * Get workspace member count
   */
  getWorkspaceMemberCount(workspace: WorkspaceDto): number {
    return workspace.members ? workspace.members.length : 0;
  }

  /**
   * Get workspace model display name
   */
  getWorkspaceModel(workspace: WorkspaceDto): string {
    if (!workspace.modelName) return 'No model assigned';

    // Format model name for display
    return workspace.modelName
      .replace(/OpenAI_/, '')
      .replace(/_/g, ' ')
      .replace(/gpt-/i, 'GPT-')
      .replace(/gemini-/i, 'Gemini ');
  }

  /**
   * Check if workspace is default
   */
  isDefaultWorkspace(workspace: WorkspaceDto): boolean {
    return workspace.isDefault === true;
  }



  /**
   * Get workspace initials for avatar display
   */
  getWorkspaceInitials(workspaceName: string | undefined): string {
    if (!workspaceName) return 'W';

    const words = workspaceName.split(' ').filter(word => word.length > 0);

    if (words.length === 1) {
      return words[0].substring(0, 2).toUpperCase();
    } else if (words.length >= 2) {
      return (words[0][0] + words[1][0]).toUpperCase();
    }

    return workspaceName.substring(0, 2).toUpperCase();
  }

  /**
   * Get agent avatar color based on agent name
   */
  getAgentAvatarColor(agentName: string): string {
    if (!agentName) return 'blue';

    const colors = ['blue', 'green', 'orange', 'purple', 'indigo'];
    const hash = this.hashString(agentName);
    return colors[hash % colors.length];
  }

  /**
   * Get workspace avatar color based on workspace name
   */
  getWorkspaceAvatarColor(workspaceName: string | undefined): string {
    if (!workspaceName) return 'cyan';

    const colors = ['cyan', 'emerald', 'amber', 'rose', 'violet'];
    const hash = this.hashString(workspaceName);
    return colors[hash % colors.length];
  }

  /**
   * Simple hash function for consistent color assignment
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Toggle accordion section with mutual exclusivity
   */
  toggleAccordionSection(sectionKey: 'agents' | 'workspaces') {
    const currentState = this.accordionSections[sectionKey].isExpanded;

    // If the section is currently collapsed, expand it and close the other
    if (!currentState) {
      // Close all sections first
      this.accordionSections.agents.isExpanded = false;
      this.accordionSections.workspaces.isExpanded = false;

      // Then open the requested section
      this.accordionSections[sectionKey].isExpanded = true;
    } else {
      // If the section is currently expanded, just collapse it
      this.accordionSections[sectionKey].isExpanded = false;
    }

    this.saveAccordionState();
  }

  /**
   * Load accordion state from session storage with mutual exclusivity enforcement
   */
  loadAccordionState() {
    try {
      const savedState = sessionStorage.getItem('accordionState');
      if (savedState) {
        const parsedState = JSON.parse(savedState);

        // Enforce mutual exclusivity: only one section can be open
        if (parsedState.agents && parsedState.workspaces) {
          // If both were saved as open, default to agents open
          this.accordionSections.agents.isExpanded = true;
          this.accordionSections.workspaces.isExpanded = false;
        } else if (parsedState.agents !== undefined && parsedState.workspaces !== undefined) {
          this.accordionSections.agents.isExpanded = parsedState.agents;
          this.accordionSections.workspaces.isExpanded = parsedState.workspaces;
        } else {
          // Partial state, apply defaults
          this.setDefaultAccordionState();
        }
      } else {
        // No saved state, apply defaults
        this.setDefaultAccordionState();
      }
    } catch (error) {
      console.warn('Failed to load accordion state:', error);
      this.setDefaultAccordionState();
    }
  }

  /**
   * Set default accordion state (Agents open, Workspaces closed)
   */
  private setDefaultAccordionState() {
    this.accordionSections.agents.isExpanded = true;
    this.accordionSections.workspaces.isExpanded = false;
  }

  /**
   * Save accordion state to session storage
   */
  saveAccordionState() {
    try {
      const stateToSave = {
        agents: this.accordionSections.agents.isExpanded,
        workspaces: this.accordionSections.workspaces.isExpanded
      };
      sessionStorage.setItem('accordionState', JSON.stringify(stateToSave));
    } catch (error) {
      console.warn('Failed to save accordion state:', error);
    }
  }

  /**
   * Check if accordion section is expanded
   */
  isAccordionExpanded(sectionKey: 'agents' | 'workspaces'): boolean {
    return this.accordionSections[sectionKey].isExpanded;
  }
}
