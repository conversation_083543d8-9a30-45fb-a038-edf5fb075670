import { Component } from '@angular/core';
import { AgentChatServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';

@Component({
  selector: 'app-agent-and-workspace-sidebar',
  standalone: true,
  imports: [ServiceProxyModule],
  templateUrl: './agent-and-workspace-sidebar.component.html',
  styleUrl: './agent-and-workspace-sidebar.component.css'
})
export class AgentAndWorkspaceSidebarComponent {

    constructor( private _agentChatService: AgentChatServiceProxy) { }
  agents: any;

  oninit() {
    this.loadAgents();
  }

  loadAgents() {
    this._agentChatService.agentContainingChat().subscribe((res)=>{
      this.agents = res;
      console.log(this.agents);
    });
  }


}
