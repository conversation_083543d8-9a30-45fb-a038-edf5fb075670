import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import {
  AgentChatServiceProxy,
  ResponseMessageList,
  WorkspaceDto,
  WorkspaceServiceProxy,
} from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-agent-and-workspace-sidebar',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule],
  templateUrl: './agent-and-workspace-sidebar.component.html',
  styleUrl: './agent-and-workspace-sidebar.component.css',
})
export class AgentAndWorkspaceSidebarComponent implements OnInit {
  // Inject services
  themeService = inject(ThemeService);
  router = inject(Router);

  // Component properties
  agents: any;
  isLoading = false;
  error: string | null = null;
  workspaces: WorkspaceDto[] = [];

  constructor(
    private _agentChatService: AgentChatServiceProxy,
    private workspaceService: WorkspaceServiceProxy
  ) {}

  ngOnInit() {
    this.loadAgents();
    this.LoadAllWorkspaces();
  }

  loadAgents() {
    this.isLoading = true;
    this.error = null;

    this._agentChatService.agentContainingChat().subscribe({
      next: (response: ResponseMessageList) => {
        // Handle the ResponseMessageList format
        if (response) {
          if (response.isError === false) {
            // Success case: extract agents from message array
            if (response.message && Array.isArray(response.message)) {
              this.agents = response.message;
              this.error = null;
              console.log('Successfully loaded agents:', this.agents);
            } else {
              // No agents in response
              this.agents = [];
              this.error = 'No agents found';
              console.warn(
                'Response message is not an array:',
                response.message
              );
            }
          } else {
            // Error case: API returned an error
            this.agents = [];
            this.error = response.message
              ? response.message.join(', ')
              : 'Failed to load agents';
            console.error('API returned error:', response.message);
          }
        } else {
          // Invalid response
          this.agents = [];
          this.error = 'Invalid response from server';
          console.error('Invalid response:', response);
        }

        this.isLoading = false;
      },
      error: (error) => {
        console.error('HTTP Error loading agents:', error);
        this.error = 'Failed to connect to server';
        this.isLoading = false;
        this.agents = [];
      },
    });
  }
  LoadAllWorkspaces() {
    this.workspaceService.getAll().subscribe({
      next: (response: any) => {
        this.workspaces = response;
        console.log('Workspaces loaded:', this.workspaces);
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
      },
    });
  }

  navigateToAgentChat(agentName: string) {
    if (!agentName) return;

    console.log('Navigating to agent chat:', agentName);
    this.router.navigate(['/agent-chat', agentName]);
  }

  retryLoadAgents() {
    this.loadAgents();
  }
  formatAgentName(agentName: string): string {
    if (!agentName) return '';

    // Convert CamelCase to readable format
    // EmailGeneratorAgent -> Email Generator Agent
    return agentName
      .replace(/([A-Z])/g, ' $1') // Add space before capital letters
      .replace(/^Agent\s*/, '') // Remove "Agent" prefix if exists
      .replace(/\s*Agent$/, '') // Remove "Agent" suffix if exists
      .trim(); // Remove extra spaces
  }
}
