import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { AgentChatServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../../shared/service-proxies/service-proxy.module';
import { ThemeService } from '../../../../shared/services/theam.service';

@Component({
  selector: 'app-agent-and-workspace-sidebar',
  standalone: true,
  imports: [ServiceProxyModule, CommonModule],
  templateUrl: './agent-and-workspace-sidebar.component.html',
  styleUrl: './agent-and-workspace-sidebar.component.css'
})
export class AgentAndWorkspaceSidebarComponent implements OnInit {

  // Inject services
  themeService = inject(ThemeService);
  router = inject(Router);

  // Component properties
  agents: any;
  isLoading = false;
  error: string | null = null;

  constructor(private _agentChatService: AgentChatServiceProxy) { }

  ngOnInit() {
    this.loadAgents();
  }

  /**
   * Load agents from the API
   */
  loadAgents() {
    this.isLoading = true;
    this.error = null;

    this._agentChatService.agentContainingChat().subscribe({
      next: (res) => {
        console.log('Agents response:', res);

        // Handle the response - it should be an array of agent names
        if (Array.isArray(res)) {
          this.agents = res.message;
        } else if (res && typeof res === 'object') {
          // If it's an object, try to extract agent names
          this.agents = Object.values(res).filter(item => typeof item === 'string');
        } else {
          this.agents = [];
        }

        this.isLoading = false;
        console.log('Processed agents:', this.agents);
      },
      error: (error) => {
        console.error('Error loading agents:', error);
        this.error = 'Failed to load agents';
        this.isLoading = false;
        this.agents = [];
      }
    });
  }

  /**
   * Navigate to agent chat page
   * @param agentName The name of the agent to chat with
   */
  navigateToAgentChat(agentName: string) {
    if (!agentName) return;

    console.log('Navigating to agent chat:', agentName);
    this.router.navigate(['/agent-chat', agentName]);
  }

  /**
   * Retry loading agents
   */
  retryLoadAgents() {
    this.loadAgents();
  }
}
