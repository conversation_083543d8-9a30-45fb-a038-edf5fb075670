{"ast": null, "code": "/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeReverse = arrayProto.reverse;\n\n/**\n * Reverses `array` so that the first element becomes the last, the second\n * element becomes the second to last, and so on.\n *\n * **Note:** This method mutates `array` and is based on\n * [`Array#reverse`](https://mdn.io/Array/reverse).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.reverse(array);\n * // => [3, 2, 1]\n *\n * console.log(array);\n * // => [3, 2, 1]\n */\nfunction reverse(array) {\n  return array == null ? array : nativeReverse.call(array);\n}\nexport default reverse;", "map": {"version": 3, "names": ["arrayProto", "Array", "prototype", "nativeReverse", "reverse", "array", "call"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/reverse.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeReverse = arrayProto.reverse;\n\n/**\n * Reverses `array` so that the first element becomes the last, the second\n * element becomes the second to last, and so on.\n *\n * **Note:** This method mutates `array` and is based on\n * [`Array#reverse`](https://mdn.io/Array/reverse).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [1, 2, 3];\n *\n * _.reverse(array);\n * // => [3, 2, 1]\n *\n * console.log(array);\n * // => [3, 2, 1]\n */\nfunction reverse(array) {\n  return array == null ? array : nativeReverse.call(array);\n}\n\nexport default reverse;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAGC,KAAK,CAACC,SAAS;;AAEhC;AACA,IAAIC,aAAa,GAAGH,UAAU,CAACI,OAAO;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,KAAK,EAAE;EACtB,OAAOA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGF,aAAa,CAACG,IAAI,CAACD,KAAK,CAAC;AAC1D;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}