{"ast": null, "code": "import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.conforms` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property predicates to conform to.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseConforms(source) {\n  var props = keys(source);\n  return function (object) {\n    return baseConformsTo(object, source, props);\n  };\n}\nexport default baseConforms;", "map": {"version": 3, "names": ["baseConformsTo", "keys", "baseConforms", "source", "props", "object"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseConforms.js"], "sourcesContent": ["import baseConformsTo from './_baseConformsTo.js';\nimport keys from './keys.js';\n\n/**\n * The base implementation of `_.conforms` which doesn't clone `source`.\n *\n * @private\n * @param {Object} source The object of property predicates to conform to.\n * @returns {Function} Returns the new spec function.\n */\nfunction baseConforms(source) {\n  var props = keys(source);\n  return function(object) {\n    return baseConformsTo(object, source, props);\n  };\n}\n\nexport default baseConforms;\n"], "mappings": "AAAA,OAAOA,cAAc,MAAM,sBAAsB;AACjD,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,MAAM,EAAE;EAC5B,IAAIC,KAAK,GAAGH,IAAI,CAACE,MAAM,CAAC;EACxB,OAAO,UAASE,MAAM,EAAE;IACtB,OAAOL,cAAc,CAACK,MAAM,EAAEF,MAAM,EAAEC,KAAK,CAAC;EAC9C,CAAC;AACH;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}