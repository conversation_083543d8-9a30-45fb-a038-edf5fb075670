{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { <PERSON><PERSON> } from '@angular/core';\nimport { TimeFormatService } from './time-format.service';\nlet RelativeTimePipe = class RelativeTimePipe {\n  constructor(timeFormatService) {\n    this.timeFormatService = timeFormatService;\n  }\n  transform(timestamp) {\n    return this.timeFormatService.getFormattedTimeObservable(timestamp);\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: TimeFormatService\n    }];\n  }\n};\nRelativeTimePipe = __decorate([Pipe({\n  name: 'relativeTime',\n  pure: false,\n  standalone: true\n})], RelativeTimePipe);\nexport { RelativeTimePipe };", "map": {"version": 3, "names": ["<PERSON><PERSON>", "TimeFormatService", "RelativeTimePipe", "constructor", "timeFormatService", "transform", "timestamp", "getFormattedTimeObservable", "__decorate", "name", "pure", "standalone"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\services\\relative-time.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\r\nimport { TimeFormatService } from './time-format.service';\r\nimport { Observable } from 'rxjs';\r\n\r\n@Pipe({\r\n  name: 'relativeTime',\r\n  pure: false,\r\n  standalone: true\r\n})\r\nexport class RelativeTimePipe implements PipeTransform {\r\n  constructor(private timeFormatService: TimeFormatService) {}\r\n\r\n  transform(timestamp: number): Observable<string> {\r\n    return this.timeFormatService.getFormattedTimeObservable(timestamp);\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,IAAI,QAAuB,eAAe;AACnD,SAASC,iBAAiB,QAAQ,uBAAuB;AAQlD,IAAMC,gBAAgB,GAAtB,MAAMA,gBAAgB;EAC3BC,YAAoBC,iBAAoC;IAApC,KAAAA,iBAAiB,GAAjBA,iBAAiB;EAAsB;EAE3DC,SAASA,CAACC,SAAiB;IACzB,OAAO,IAAI,CAACF,iBAAiB,CAACG,0BAA0B,CAACD,SAAS,CAAC;EACrE;;;;;;;AALWJ,gBAAgB,GAAAM,UAAA,EAL5BR,IAAI,CAAC;EACJS,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;CACb,CAAC,C,EACWT,gBAAgB,CAM5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}