
<!-- Agent List Container -->
<div class="agent-list-container h-full w-full p-4"
     [ngClass]="{
       'bg-[#2b2b33] text-white': themeService.isDarkMode(),
       'bg-[var(--background-white)] text-[var(--text-dark)]': !themeService.isDarkMode()
     }">

  <!-- Header -->
  <div class="mb-6">
    <h2 class="text-xl font-bold mb-2"
        [ngClass]="{
          'text-white': themeService.isDarkMode(),
          'text-[var(--text-dark)]': !themeService.isDarkMode()
        }">
      Available Agents
    </h2>
    <p class="text-sm"
       [ngClass]="{
         'text-gray-300': themeService.isDarkMode(),
         'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
       }">
      Select an agent to start chatting
    </p>
  </div>

  <!-- Loading State -->
  <div *ngIf="isLoading" class="flex items-center justify-center py-8">
    <div class="flex items-center gap-2">
      <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin"></div>
      <span class="text-sm"
            [ngClass]="{
              'text-gray-300': themeService.isDarkMode(),
              'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
            }">
        Loading agents...
      </span>
    </div>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !isLoading" class="text-center py-8">
    <div class="mb-4">
      <i class="ri-error-warning-line text-3xl text-red-500"></i>
    </div>
    <p class="text-sm text-red-500 mb-4">{{ error }}</p>
    <button
      (click)="retryLoadAgents()"
      class="px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors">
      Retry
    </button>
  </div>

  <!-- Agent List -->
  <div *ngIf="!isLoading && !error && agents.length > 0" class="space-y-3">
    @for (agentName of agents; track $index) {
      <div
        (click)="navigateToAgentChat(agentName)"
        class="agent-card p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md"
        [ngClass]="{
          'bg-[#3a3a45] border-[#4a4a55] hover:border-[#00c39a] hover:bg-[#404050]': themeService.isDarkMode(),
          'bg-[var(--background-white)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)] hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">

        <!-- Agent Icon and Name -->
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 rounded-full flex items-center justify-center"
               [ngClass]="{
                 'bg-[#00c39a]': themeService.isDarkMode(),
                 'bg-[var(--primary-purple)]': !themeService.isDarkMode()
               }">
            <i class="ri-robot-line text-white text-lg"></i>
          </div>

          <div class="flex-1">
            <h3 class="font-semibold text-base"
                [ngClass]="{
                  'text-white': themeService.isDarkMode(),
                  'text-[var(--text-dark)]': !themeService.isDarkMode()
                }">
              {{ formatAgentName(agentName) }}
            </h3>
            
          </div>

          <!-- Arrow Icon -->
          <div class="text-lg"
               [ngClass]="{
                 'text-gray-400': themeService.isDarkMode(),
                 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
               }">
            <i class="ri-arrow-right-s-line"></i>
          </div>
        </div>
      </div>
    }
  </div>

  <!-- Empty State -->
  <div *ngIf="!isLoading && !error && agents.length === 0" class="text-center py-8">
    <div class="mb-4">
      <i class="ri-robot-line text-4xl"
         [ngClass]="{
           'text-gray-500': themeService.isDarkMode(),
           'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
         }"></i>
    </div>
    <p class="text-sm"
       [ngClass]="{
         'text-gray-400': themeService.isDarkMode(),
         'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
       }">
      No agents available
    </p>
    <button
      (click)="retryLoadAgents()"
      class="mt-4 px-4 py-2 bg-[var(--primary-purple)] text-white rounded-md hover:bg-[var(--secondary-purple)] transition-colors">
      Refresh
    </button>
  </div>

 
</div>