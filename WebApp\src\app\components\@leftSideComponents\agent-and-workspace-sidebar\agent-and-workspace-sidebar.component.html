<!-- Agent and Workspace Sidebar Container -->
<div class="h-full flex flex-col bg-[var(--background-white)] text-[var(--text-dark)]" [ngClass]="{
    'bg-[#2b2b33] text-white': themeService.isDarkMode(),
    'bg-white text-[var(--text-dark)]': !themeService.isDarkMode()
  }">

  <!-- Header Section -->
  <div
    class="px-[var(--padding-small)] py-[var(--padding-small)] border-b border-[var(--hover-blue-gray)] flex justify-between flex-col gap-2"
    [ngClass]="{
      'border-[#3a3a45]': themeService.isDarkMode(),
      'border-[var(--border-light)]': !themeService.isDarkMode()
    }">
    <div class="flex items-center justify-between w-full">
      <div class="flex flex-col">
        <div class="flex items-center gap-1.5">
          <span class="font-bold text-[var(--text-dark)] text-lg" [ngClass]="{
              'text-white': themeService.isDarkMode(),
              'text-[var(--text-dark)]': !themeService.isDarkMode()
            }">
            Chats
          </span>
        </div>
      </div>
    </div>
  </div>
  <!-- Accordion Content Section -->
  <div class="flex-1 flex flex-col overflow-hidden">

    <!-- AGENTS SECTION (TOP) -->
    <div class="flex flex-col flex-shrink-0 order-1">
      <!-- Agents Accordion Header -->
      <div
        (click)="toggleAccordionSection('agents')"
        class="flex-shrink-0 px-[var(--padding-small)] py-3 border-b border-[var(--hover-blue-gray)] cursor-pointer hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
        [ngClass]="{
          'border-[#3a3a45] hover:bg-[#3a3a45]': themeService.isDarkMode(),
          'border-[var(--border-light)] hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <i class="ri-robot-line text-sm transition-colors duration-200" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }"></i>
            <h3 class="text-sm font-medium transition-colors duration-200" [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }">
              Agents
            </h3>
            <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
                'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
                'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              {{ agents.length }}
            </span>
          </div>
          <!-- Accordion Toggle Icon -->
          <i class="text-sm transition-transform duration-200"
             [ngClass]="{
               'ri-arrow-down-s-line': !isAccordionExpanded('agents'),
               'ri-arrow-up-s-line': isAccordionExpanded('agents'),
               'text-[#ACACBE]': themeService.isDarkMode(),
               'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
             }"></i>
        </div>
      </div>

      <!-- Agents Collapsible Content -->
      <div
        *ngIf="isAccordionExpanded('agents')"
        class="overflow-hidden transition-all duration-300 ease-in-out">
        <div class="overflow-y-auto px-[var(--padding-small)] py-2" style="max-height: calc(100vh - 300px);">

          <!-- Loading State for Agents -->
          <div *ngIf="isLoadingAgents" class="flex items-center justify-center py-4">
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
              </div>
              <span class="text-sm" [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                }">
                Loading agents...
              </span>
            </div>
          </div>

          <!-- Agents List -->
          <div *ngIf="!isLoadingAgents && !agentsError && agents.length > 0" class="space-y-1">
            @for (agentName of agents; track $index) {
            <div (click)="navigateToAgentChat(agentName)"
              class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
                'hover:bg-[var(--primary-purple)]': true,
                'hover:text-white': true
              }">

              <!-- Active indicator bar -->
              <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all opacity-0"></div>

              <i class="ri-robot-line text-lg transition-colors duration-200" [ngClass]="{
                  'text-[#ACACBE]': themeService.isDarkMode(),
                  'text-[var(--primary-purple)]': !themeService.isDarkMode(),
                  'group-hover:text-white': themeService.isDarkMode(),
                  'group-hover:text-black': !themeService.isDarkMode()
                }"></i>

              <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
                  'text-[#ACACBE]': themeService.isDarkMode(),
                  'text-[var(--text-dark)]': !themeService.isDarkMode(),
                  'group-hover:text-white': themeService.isDarkMode(),
                  'group-hover:text-black': !themeService.isDarkMode()
                }">
                {{ formatAgentName(agentName) }}
              </span>
            </div>
            }
          </div>

          <!-- Error State for Agents -->
          <div *ngIf="agentsError" class="px-3 py-2">
            <div class="text-red-500 text-sm">{{ agentsError }}</div>
          </div>

          <!-- Empty State for Agents -->
          <div *ngIf="!isLoadingAgents && !agentsError && agents.length === 0"
            class="flex flex-col items-center justify-center py-8 text-center">
            <span class="text-[var(--text-medium-gray)] text-sm">No agents available.</span>
            <i class="ri-robot-line text-[var(--text-medium-gray)] text-2xl mt-2 opacity-50"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- WORKSPACES SECTION (DYNAMIC POSITIONING) -->
    <div class="flex flex-col flex-shrink-0"
         [ngClass]="{
           'order-2': !isAccordionExpanded('agents') && !isAccordionExpanded('workspaces'),
           'order-2': isAccordionExpanded('workspaces'),
           'order-3': isAccordionExpanded('agents') && !isAccordionExpanded('workspaces')
         }">
      <!-- Workspaces Accordion Header -->
      <div
        (click)="toggleAccordionSection('workspaces')"
        class="flex-shrink-0 px-[var(--padding-small)] py-3 border-b border-[var(--hover-blue-gray)] cursor-pointer hover:bg-[var(--hover-blue-gray)] transition-all duration-200"
        [ngClass]="{
          'border-[#3a3a45] hover:bg-[#3a3a45]': themeService.isDarkMode(),
          'border-[var(--border-light)] hover:bg-[var(--background-light-gray)]': !themeService.isDarkMode()
        }">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <i class="ri-building-line text-sm transition-colors duration-200" [ngClass]="{
                'text-[#ACACBE]': themeService.isDarkMode(),
                'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }"></i>
            <h3 class="text-sm font-medium transition-colors duration-200" [ngClass]="{
                'text-white': themeService.isDarkMode(),
                'text-[var(--text-dark)]': !themeService.isDarkMode()
              }">
              Workspaces
            </h3>
            <span class="text-xs px-1.5 py-0.5 rounded-full" [ngClass]="{
                'bg-[#3a3a45] text-gray-300': themeService.isDarkMode(),
                'bg-[var(--background-light-gray)] text-[var(--text-medium-gray)]': !themeService.isDarkMode()
              }">
              {{ workspaces.length }}
            </span>
          </div>
          <!-- Accordion Toggle Icon -->
          <i class="text-sm transition-transform duration-200"
             [ngClass]="{
               'ri-arrow-down-s-line': !isAccordionExpanded('workspaces'),
               'ri-arrow-up-s-line': isAccordionExpanded('workspaces'),
               'text-[#ACACBE]': themeService.isDarkMode(),
               'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
             }"></i>
        </div>
      </div>

      <!-- Workspaces Collapsible Content -->
      <div
        *ngIf="isAccordionExpanded('workspaces')"
        class="overflow-hidden transition-all duration-300 ease-in-out">
        <div class="overflow-y-auto px-[var(--padding-small)] py-2" style="max-height: calc(100vh - 300px);">

          <!-- Loading State for Workspaces -->
          <div *ngIf="isLoadingWorkspaces" class="flex items-center justify-center py-4">
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 border-2 border-[var(--primary-purple)] border-t-transparent rounded-full animate-spin">
              </div>
              <span class="text-sm" [ngClass]="{
                  'text-gray-300': themeService.isDarkMode(),
                  'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
                }">
                Loading workspaces...
              </span>
            </div>
          </div>

          <!-- Workspaces List -->
          <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length > 0" class="space-y-1">
            @for (workspace of workspaces; track workspace.id) {
            <div (click)="navigateToWorkspace(workspace)"
              class="flex items-center gap-3 py-2 px-3 rounded-md cursor-pointer transition-all relative group" [ngClass]="{
                'hover:bg-[var(--primary-purple)]': true,
                'hover:text-white': true
              }">

              <!-- Active indicator bar -->
              <div class="absolute left-0 top-0 bottom-0 w-1.5 rounded-r-full transition-all opacity-0"></div>

              <i class="ri-building-line text-lg transition-colors duration-200" [ngClass]="{
                  'text-[#ACACBE]': themeService.isDarkMode(),
                  'text-[var(--primary-purple)]': !themeService.isDarkMode(),
                  'group-hover:text-white': themeService.isDarkMode(),
                  'group-hover:text-black': !themeService.isDarkMode()
                }"></i>

              <span class="font-medium text-sm transition-colors duration-200 truncate flex-1 min-w-0" [ngClass]="{
                  'text-[#ACACBE]': themeService.isDarkMode(),
                  'text-[var(--text-dark)]': !themeService.isDarkMode(),
                  'group-hover:text-white': themeService.isDarkMode(),
                  'group-hover:text-black': !themeService.isDarkMode()
                }">
                {{ workspace.title }}
              </span>

            </div>
            }
          </div>

          <!-- Error State for Workspaces -->
          <div *ngIf="workspacesError" class="px-3 py-2">
            <div class="text-red-500 text-sm">{{ workspacesError }}</div>
          </div>

          <!-- Empty State for Workspaces -->
          <div *ngIf="!isLoadingWorkspaces && !workspacesError && workspaces.length === 0"
            class="flex flex-col items-center justify-center py-8 text-center">
            <span class="text-[var(--text-medium-gray)] text-sm">No workspaces available.</span>
            <i class="ri-building-line text-[var(--text-medium-gray)] text-2xl mt-2 opacity-50"></i>
          </div>
        </div>
      </div>
    </div>

    <!-- Flexible Spacer to fill remaining space -->
    <div class="flex-1"
         [ngClass]="{
           'order-3': !isAccordionExpanded('agents') && !isAccordionExpanded('workspaces'),
           'order-3': isAccordionExpanded('workspaces'),
           'order-2': isAccordionExpanded('agents') && !isAccordionExpanded('workspaces')
         }"></div>

  </div>
</div>
