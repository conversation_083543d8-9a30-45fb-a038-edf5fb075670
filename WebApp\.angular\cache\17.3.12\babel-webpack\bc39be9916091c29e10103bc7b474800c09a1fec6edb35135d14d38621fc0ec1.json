{"ast": null, "code": "import baseSetData from './_baseSetData.js';\nimport createBind from './_createBind.js';\nimport createCurry from './_createCurry.js';\nimport createHybrid from './_createHybrid.js';\nimport createPartial from './_createPartial.js';\nimport getData from './_getData.js';\nimport mergeData from './_mergeData.js';\nimport setData from './_setData.js';\nimport setWrapToString from './_setWrapToString.js';\nimport toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n  WRAP_BIND_KEY_FLAG = 2,\n  WRAP_CURRY_FLAG = 8,\n  WRAP_CURRY_RIGHT_FLAG = 16,\n  WRAP_PARTIAL_FLAG = 32,\n  WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates a function that either curries or invokes `func` with optional\n * `this` binding and partially applied arguments.\n *\n * @private\n * @param {Function|string} func The function or method name to wrap.\n * @param {number} bitmask The bitmask flags.\n *    1 - `_.bind`\n *    2 - `_.bindKey`\n *    4 - `_.curry` or `_.curryRight` of a bound function\n *    8 - `_.curry`\n *   16 - `_.curryRight`\n *   32 - `_.partial`\n *   64 - `_.partialRight`\n *  128 - `_.rearg`\n *  256 - `_.ary`\n *  512 - `_.flip`\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to be partially applied.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createWrap(func, bitmask, thisArg, partials, holders, argPos, ary, arity) {\n  var isBindKey = bitmask & WRAP_BIND_KEY_FLAG;\n  if (!isBindKey && typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var length = partials ? partials.length : 0;\n  if (!length) {\n    bitmask &= ~(WRAP_PARTIAL_FLAG | WRAP_PARTIAL_RIGHT_FLAG);\n    partials = holders = undefined;\n  }\n  ary = ary === undefined ? ary : nativeMax(toInteger(ary), 0);\n  arity = arity === undefined ? arity : toInteger(arity);\n  length -= holders ? holders.length : 0;\n  if (bitmask & WRAP_PARTIAL_RIGHT_FLAG) {\n    var partialsRight = partials,\n      holdersRight = holders;\n    partials = holders = undefined;\n  }\n  var data = isBindKey ? undefined : getData(func);\n  var newData = [func, bitmask, thisArg, partials, holders, partialsRight, holdersRight, argPos, ary, arity];\n  if (data) {\n    mergeData(newData, data);\n  }\n  func = newData[0];\n  bitmask = newData[1];\n  thisArg = newData[2];\n  partials = newData[3];\n  holders = newData[4];\n  arity = newData[9] = newData[9] === undefined ? isBindKey ? 0 : func.length : nativeMax(newData[9] - length, 0);\n  if (!arity && bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG)) {\n    bitmask &= ~(WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG);\n  }\n  if (!bitmask || bitmask == WRAP_BIND_FLAG) {\n    var result = createBind(func, bitmask, thisArg);\n  } else if (bitmask == WRAP_CURRY_FLAG || bitmask == WRAP_CURRY_RIGHT_FLAG) {\n    result = createCurry(func, bitmask, arity);\n  } else if ((bitmask == WRAP_PARTIAL_FLAG || bitmask == (WRAP_BIND_FLAG | WRAP_PARTIAL_FLAG)) && !holders.length) {\n    result = createPartial(func, bitmask, thisArg, partials);\n  } else {\n    result = createHybrid.apply(undefined, newData);\n  }\n  var setter = data ? baseSetData : setData;\n  return setWrapToString(setter(result, newData), func, bitmask);\n}\nexport default createWrap;", "map": {"version": 3, "names": ["baseSetData", "createBind", "createCurry", "createHybrid", "createPartial", "getData", "mergeData", "setData", "setWrapToString", "toInteger", "FUNC_ERROR_TEXT", "WRAP_BIND_FLAG", "WRAP_BIND_KEY_FLAG", "WRAP_CURRY_FLAG", "WRAP_CURRY_RIGHT_FLAG", "WRAP_PARTIAL_FLAG", "WRAP_PARTIAL_RIGHT_FLAG", "nativeMax", "Math", "max", "createWrap", "func", "bitmask", "thisArg", "partials", "holders", "argPos", "ary", "arity", "isBindKey", "TypeError", "length", "undefined", "partialsRight", "holdersRight", "data", "newData", "result", "apply", "setter"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_createWrap.js"], "sourcesContent": ["import baseSetData from './_baseSetData.js';\nimport createBind from './_createBind.js';\nimport createCurry from './_createCurry.js';\nimport createHybrid from './_createHybrid.js';\nimport createPartial from './_createPartial.js';\nimport getData from './_getData.js';\nimport mergeData from './_mergeData.js';\nimport setData from './_setData.js';\nimport setWrapToString from './_setWrapToString.js';\nimport toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_BIND_FLAG = 1,\n    WRAP_BIND_KEY_FLAG = 2,\n    WRAP_CURRY_FLAG = 8,\n    WRAP_CURRY_RIGHT_FLAG = 16,\n    WRAP_PARTIAL_FLAG = 32,\n    WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates a function that either curries or invokes `func` with optional\n * `this` binding and partially applied arguments.\n *\n * @private\n * @param {Function|string} func The function or method name to wrap.\n * @param {number} bitmask The bitmask flags.\n *    1 - `_.bind`\n *    2 - `_.bindKey`\n *    4 - `_.curry` or `_.curryRight` of a bound function\n *    8 - `_.curry`\n *   16 - `_.curryRight`\n *   32 - `_.partial`\n *   64 - `_.partialRight`\n *  128 - `_.rearg`\n *  256 - `_.ary`\n *  512 - `_.flip`\n * @param {*} [thisArg] The `this` binding of `func`.\n * @param {Array} [partials] The arguments to be partially applied.\n * @param {Array} [holders] The `partials` placeholder indexes.\n * @param {Array} [argPos] The argument positions of the new function.\n * @param {number} [ary] The arity cap of `func`.\n * @param {number} [arity] The arity of `func`.\n * @returns {Function} Returns the new wrapped function.\n */\nfunction createWrap(func, bitmask, thisArg, partials, holders, argPos, ary, arity) {\n  var isBindKey = bitmask & WRAP_BIND_KEY_FLAG;\n  if (!isBindKey && typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  var length = partials ? partials.length : 0;\n  if (!length) {\n    bitmask &= ~(WRAP_PARTIAL_FLAG | WRAP_PARTIAL_RIGHT_FLAG);\n    partials = holders = undefined;\n  }\n  ary = ary === undefined ? ary : nativeMax(toInteger(ary), 0);\n  arity = arity === undefined ? arity : toInteger(arity);\n  length -= holders ? holders.length : 0;\n\n  if (bitmask & WRAP_PARTIAL_RIGHT_FLAG) {\n    var partialsRight = partials,\n        holdersRight = holders;\n\n    partials = holders = undefined;\n  }\n  var data = isBindKey ? undefined : getData(func);\n\n  var newData = [\n    func, bitmask, thisArg, partials, holders, partialsRight, holdersRight,\n    argPos, ary, arity\n  ];\n\n  if (data) {\n    mergeData(newData, data);\n  }\n  func = newData[0];\n  bitmask = newData[1];\n  thisArg = newData[2];\n  partials = newData[3];\n  holders = newData[4];\n  arity = newData[9] = newData[9] === undefined\n    ? (isBindKey ? 0 : func.length)\n    : nativeMax(newData[9] - length, 0);\n\n  if (!arity && bitmask & (WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG)) {\n    bitmask &= ~(WRAP_CURRY_FLAG | WRAP_CURRY_RIGHT_FLAG);\n  }\n  if (!bitmask || bitmask == WRAP_BIND_FLAG) {\n    var result = createBind(func, bitmask, thisArg);\n  } else if (bitmask == WRAP_CURRY_FLAG || bitmask == WRAP_CURRY_RIGHT_FLAG) {\n    result = createCurry(func, bitmask, arity);\n  } else if ((bitmask == WRAP_PARTIAL_FLAG || bitmask == (WRAP_BIND_FLAG | WRAP_PARTIAL_FLAG)) && !holders.length) {\n    result = createPartial(func, bitmask, thisArg, partials);\n  } else {\n    result = createHybrid.apply(undefined, newData);\n  }\n  var setter = data ? baseSetData : setData;\n  return setWrapToString(setter(result, newData), func, bitmask);\n}\n\nexport default createWrap;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,eAAe,MAAM,uBAAuB;AACnD,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA,IAAIC,cAAc,GAAG,CAAC;EAClBC,kBAAkB,GAAG,CAAC;EACtBC,eAAe,GAAG,CAAC;EACnBC,qBAAqB,GAAG,EAAE;EAC1BC,iBAAiB,GAAG,EAAE;EACtBC,uBAAuB,GAAG,EAAE;;AAEhC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EACjF,IAAIC,SAAS,GAAGP,OAAO,GAAGV,kBAAkB;EAC5C,IAAI,CAACiB,SAAS,IAAI,OAAOR,IAAI,IAAI,UAAU,EAAE;IAC3C,MAAM,IAAIS,SAAS,CAACpB,eAAe,CAAC;EACtC;EACA,IAAIqB,MAAM,GAAGP,QAAQ,GAAGA,QAAQ,CAACO,MAAM,GAAG,CAAC;EAC3C,IAAI,CAACA,MAAM,EAAE;IACXT,OAAO,IAAI,EAAEP,iBAAiB,GAAGC,uBAAuB,CAAC;IACzDQ,QAAQ,GAAGC,OAAO,GAAGO,SAAS;EAChC;EACAL,GAAG,GAAGA,GAAG,KAAKK,SAAS,GAAGL,GAAG,GAAGV,SAAS,CAACR,SAAS,CAACkB,GAAG,CAAC,EAAE,CAAC,CAAC;EAC5DC,KAAK,GAAGA,KAAK,KAAKI,SAAS,GAAGJ,KAAK,GAAGnB,SAAS,CAACmB,KAAK,CAAC;EACtDG,MAAM,IAAIN,OAAO,GAAGA,OAAO,CAACM,MAAM,GAAG,CAAC;EAEtC,IAAIT,OAAO,GAAGN,uBAAuB,EAAE;IACrC,IAAIiB,aAAa,GAAGT,QAAQ;MACxBU,YAAY,GAAGT,OAAO;IAE1BD,QAAQ,GAAGC,OAAO,GAAGO,SAAS;EAChC;EACA,IAAIG,IAAI,GAAGN,SAAS,GAAGG,SAAS,GAAG3B,OAAO,CAACgB,IAAI,CAAC;EAEhD,IAAIe,OAAO,GAAG,CACZf,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,OAAO,EAAEQ,aAAa,EAAEC,YAAY,EACtER,MAAM,EAAEC,GAAG,EAAEC,KAAK,CACnB;EAED,IAAIO,IAAI,EAAE;IACR7B,SAAS,CAAC8B,OAAO,EAAED,IAAI,CAAC;EAC1B;EACAd,IAAI,GAAGe,OAAO,CAAC,CAAC,CAAC;EACjBd,OAAO,GAAGc,OAAO,CAAC,CAAC,CAAC;EACpBb,OAAO,GAAGa,OAAO,CAAC,CAAC,CAAC;EACpBZ,QAAQ,GAAGY,OAAO,CAAC,CAAC,CAAC;EACrBX,OAAO,GAAGW,OAAO,CAAC,CAAC,CAAC;EACpBR,KAAK,GAAGQ,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAAC,CAAC,CAAC,KAAKJ,SAAS,GACxCH,SAAS,GAAG,CAAC,GAAGR,IAAI,CAACU,MAAM,GAC5Bd,SAAS,CAACmB,OAAO,CAAC,CAAC,CAAC,GAAGL,MAAM,EAAE,CAAC,CAAC;EAErC,IAAI,CAACH,KAAK,IAAIN,OAAO,IAAIT,eAAe,GAAGC,qBAAqB,CAAC,EAAE;IACjEQ,OAAO,IAAI,EAAET,eAAe,GAAGC,qBAAqB,CAAC;EACvD;EACA,IAAI,CAACQ,OAAO,IAAIA,OAAO,IAAIX,cAAc,EAAE;IACzC,IAAI0B,MAAM,GAAGpC,UAAU,CAACoB,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;EACjD,CAAC,MAAM,IAAID,OAAO,IAAIT,eAAe,IAAIS,OAAO,IAAIR,qBAAqB,EAAE;IACzEuB,MAAM,GAAGnC,WAAW,CAACmB,IAAI,EAAEC,OAAO,EAAEM,KAAK,CAAC;EAC5C,CAAC,MAAM,IAAI,CAACN,OAAO,IAAIP,iBAAiB,IAAIO,OAAO,KAAKX,cAAc,GAAGI,iBAAiB,CAAC,KAAK,CAACU,OAAO,CAACM,MAAM,EAAE;IAC/GM,MAAM,GAAGjC,aAAa,CAACiB,IAAI,EAAEC,OAAO,EAAEC,OAAO,EAAEC,QAAQ,CAAC;EAC1D,CAAC,MAAM;IACLa,MAAM,GAAGlC,YAAY,CAACmC,KAAK,CAACN,SAAS,EAAEI,OAAO,CAAC;EACjD;EACA,IAAIG,MAAM,GAAGJ,IAAI,GAAGnC,WAAW,GAAGO,OAAO;EACzC,OAAOC,eAAe,CAAC+B,MAAM,CAACF,MAAM,EAAED,OAAO,CAAC,EAAEf,IAAI,EAAEC,OAAO,CAAC;AAChE;AAEA,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}