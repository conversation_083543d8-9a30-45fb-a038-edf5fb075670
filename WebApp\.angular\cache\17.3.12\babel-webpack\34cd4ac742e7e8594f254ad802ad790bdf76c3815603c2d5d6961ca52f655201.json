{"ast": null, "code": "import baseGetTag from './_baseGetTag.js';\nimport isObject from './isObject.js';\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n  funcTag = '[object Function]',\n  genTag = '[object GeneratorFunction]',\n  proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\nexport default isFunction;", "map": {"version": 3, "names": ["baseGetTag", "isObject", "asyncTag", "funcTag", "genTag", "proxyTag", "isFunction", "value", "tag"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/isFunction.js"], "sourcesContent": ["import baseGetTag from './_baseGetTag.js';\nimport isObject from './isObject.js';\n\n/** `Object#toString` result references. */\nvar asyncTag = '[object AsyncFunction]',\n    funcTag = '[object Function]',\n    genTag = '[object GeneratorFunction]',\n    proxyTag = '[object Proxy]';\n\n/**\n * Checks if `value` is classified as a `Function` object.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a function, else `false`.\n * @example\n *\n * _.isFunction(_);\n * // => true\n *\n * _.isFunction(/abc/);\n * // => false\n */\nfunction isFunction(value) {\n  if (!isObject(value)) {\n    return false;\n  }\n  // The use of `Object#toString` avoids issues with the `typeof` operator\n  // in Safari 9 which returns 'object' for typed arrays and other constructors.\n  var tag = baseGetTag(value);\n  return tag == funcTag || tag == genTag || tag == asyncTag || tag == proxyTag;\n}\n\nexport default isFunction;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,QAAQ,GAAG,wBAAwB;EACnCC,OAAO,GAAG,mBAAmB;EAC7BC,MAAM,GAAG,4BAA4B;EACrCC,QAAQ,GAAG,gBAAgB;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,IAAI,CAACN,QAAQ,CAACM,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EACd;EACA;EACA;EACA,IAAIC,GAAG,GAAGR,UAAU,CAACO,KAAK,CAAC;EAC3B,OAAOC,GAAG,IAAIL,OAAO,IAAIK,GAAG,IAAIJ,MAAM,IAAII,GAAG,IAAIN,QAAQ,IAAIM,GAAG,IAAIH,QAAQ;AAC9E;AAEA,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}