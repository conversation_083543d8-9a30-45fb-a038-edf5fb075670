{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, ReplaySubject, timer } from 'rxjs';\nimport { startWith, distinctUntilChanged, switchMap, debounce, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzSpinComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵelement(1, \"i\", 4)(2, \"i\", 4)(3, \"i\", 4)(4, \"i\", 4);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction NzSpinComponent_div_2_ng_template_2_Template(rf, ctx) {}\nfunction NzSpinComponent_div_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.nzTip);\n  }\n}\nfunction NzSpinComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 5);\n    i0.ɵɵtemplate(2, NzSpinComponent_div_2_ng_template_2_Template, 0, 0, \"ng-template\", 6)(3, NzSpinComponent_div_2_div_3_Template, 2, 1, \"div\", 7);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const defaultTemplate_r2 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"ant-spin-rtl\", ctx_r0.dir === \"rtl\")(\"ant-spin-spinning\", ctx_r0.isLoading)(\"ant-spin-lg\", ctx_r0.nzSize === \"large\")(\"ant-spin-sm\", ctx_r0.nzSize === \"small\")(\"ant-spin-show-text\", ctx_r0.nzTip);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzIndicator || defaultTemplate_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzTip);\n  }\n}\nfunction NzSpinComponent_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-spin-blur\", ctx_r0.isLoading);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME = 'spin';\nclass NzSpinComponent {\n  constructor(nzConfigService, cdr, directionality) {\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzIndicator = null;\n    this.nzSize = 'default';\n    this.nzTip = null;\n    this.nzDelay = 0;\n    this.nzSimple = false;\n    this.nzSpinning = true;\n    this.destroy$ = new Subject();\n    this.spinning$ = new BehaviorSubject(this.nzSpinning);\n    this.delay$ = new ReplaySubject(1);\n    this.isLoading = false;\n    this.dir = 'ltr';\n  }\n  ngOnInit() {\n    const loading$ = this.delay$.pipe(startWith(this.nzDelay), distinctUntilChanged(), switchMap(delay => {\n      if (delay === 0) {\n        return this.spinning$;\n      }\n      return this.spinning$.pipe(debounce(spinning => timer(spinning ? delay : 0)));\n    }), takeUntil(this.destroy$));\n    loading$.subscribe(loading => {\n      this.isLoading = loading;\n      this.cdr.markForCheck();\n    });\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => this.cdr.markForCheck());\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSpinning,\n      nzDelay\n    } = changes;\n    if (nzSpinning) {\n      this.spinning$.next(this.nzSpinning);\n    }\n    if (nzDelay) {\n      this.delay$.next(this.nzDelay);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzSpinComponent_Factory(t) {\n      return new (t || NzSpinComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzSpinComponent,\n      selectors: [[\"nz-spin\"]],\n      hostVars: 2,\n      hostBindings: function NzSpinComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-spin-nested-loading\", !ctx.nzSimple);\n        }\n      },\n      inputs: {\n        nzIndicator: \"nzIndicator\",\n        nzSize: \"nzSize\",\n        nzTip: \"nzTip\",\n        nzDelay: \"nzDelay\",\n        nzSimple: \"nzSimple\",\n        nzSpinning: \"nzSpinning\"\n      },\n      exportAs: [\"nzSpin\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 2,\n      consts: [[\"defaultTemplate\", \"\"], [4, \"ngIf\"], [\"class\", \"ant-spin-container\", 3, \"ant-spin-blur\", 4, \"ngIf\"], [1, \"ant-spin-dot\", \"ant-spin-dot-spin\"], [1, \"ant-spin-dot-item\"], [1, \"ant-spin\"], [3, \"ngTemplateOutlet\"], [\"class\", \"ant-spin-text\", 4, \"ngIf\"], [1, \"ant-spin-text\"], [1, \"ant-spin-container\"]],\n      template: function NzSpinComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzSpinComponent_ng_template_0_Template, 5, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzSpinComponent_div_2_Template, 4, 12, \"div\", 1)(3, NzSpinComponent_div_3_Template, 2, 2, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.nzSimple);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2\n    });\n  }\n}\n__decorate([WithConfig()], NzSpinComponent.prototype, \"nzIndicator\", void 0);\n__decorate([InputNumber()], NzSpinComponent.prototype, \"nzDelay\", void 0);\n__decorate([InputBoolean()], NzSpinComponent.prototype, \"nzSimple\", void 0);\n__decorate([InputBoolean()], NzSpinComponent.prototype, \"nzSpinning\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpinComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-spin',\n      exportAs: 'nzSpin',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #defaultTemplate>\n      <span class=\"ant-spin-dot ant-spin-dot-spin\">\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n      </span>\n    </ng-template>\n    <div *ngIf=\"isLoading\">\n      <div\n        class=\"ant-spin\"\n        [class.ant-spin-rtl]=\"dir === 'rtl'\"\n        [class.ant-spin-spinning]=\"isLoading\"\n        [class.ant-spin-lg]=\"nzSize === 'large'\"\n        [class.ant-spin-sm]=\"nzSize === 'small'\"\n        [class.ant-spin-show-text]=\"nzTip\"\n      >\n        <ng-template [ngTemplateOutlet]=\"nzIndicator || defaultTemplate\"></ng-template>\n        <div class=\"ant-spin-text\" *ngIf=\"nzTip\">{{ nzTip }}</div>\n      </div>\n    </div>\n    <div *ngIf=\"!nzSimple\" class=\"ant-spin-container\" [class.ant-spin-blur]=\"isLoading\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        '[class.ant-spin-nested-loading]': '!nzSimple'\n      },\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzIndicator: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzTip: [{\n      type: Input\n    }],\n    nzDelay: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzSpinning: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpinModule {\n  static {\n    this.ɵfac = function NzSpinModule_Factory(t) {\n      return new (t || NzSpinModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzSpinModule,\n      imports: [NzSpinComponent],\n      exports: [NzSpinComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzSpinModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzSpinComponent],\n      exports: [NzSpinComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSpinComponent, NzSpinModule };", "map": {"version": 3, "names": ["__decorate", "NgIf", "NgTemplateOutlet", "i0", "Component", "ViewEncapsulation", "Optional", "Input", "NgModule", "Subject", "BehaviorSubject", "ReplaySubject", "timer", "startWith", "distinctUntilChanged", "switchMap", "debounce", "takeUntil", "i1", "WithConfig", "InputNumber", "InputBoolean", "i2", "_c0", "NzSpinComponent_ng_template_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "NzSpinComponent_div_2_ng_template_2_Template", "NzSpinComponent_div_2_div_3_Template", "ɵɵtext", "ctx_r0", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "nzTip", "NzSpinComponent_div_2_Template", "ɵɵtemplate", "defaultTemplate_r2", "ɵɵreference", "ɵɵclassProp", "dir", "isLoading", "nzSize", "ɵɵproperty", "nzIndicator", "NzSpinComponent_div_3_Template", "ɵɵprojection", "NZ_CONFIG_MODULE_NAME", "NzSpinComponent", "constructor", "nzConfigService", "cdr", "directionality", "_nzModuleName", "nzDelay", "nzSimple", "nzSpinning", "destroy$", "spinning$", "delay$", "ngOnInit", "loading$", "pipe", "delay", "spinning", "subscribe", "loading", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getConfigChangeEventForComponent", "change", "direction", "detectChanges", "value", "ngOnChanges", "changes", "next", "ngOnDestroy", "complete", "ɵfac", "NzSpinComponent_Factory", "t", "ɵɵdirectiveInject", "NzConfigService", "ChangeDetectorRef", "Directionality", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostVars", "hostBindings", "NzSpinComponent_HostBindings", "inputs", "exportAs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "NzSpinComponent_Template", "ɵɵprojectionDef", "ɵɵtemplateRefExtractor", "dependencies", "encapsulation", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "None", "host", "imports", "decorators", "NzSpinModule", "NzSpinModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-spin.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgIf, NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, Input, NgModule } from '@angular/core';\nimport { Subject, BehaviorSubject, ReplaySubject, timer } from 'rxjs';\nimport { startWith, distinctUntilChanged, switchMap, debounce, takeUntil } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport { InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i2 from '@angular/cdk/bidi';\n\nconst NZ_CONFIG_MODULE_NAME = 'spin';\nclass NzSpinComponent {\n    constructor(nzConfigService, cdr, directionality) {\n        this.nzConfigService = nzConfigService;\n        this.cdr = cdr;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.nzIndicator = null;\n        this.nzSize = 'default';\n        this.nzTip = null;\n        this.nzDelay = 0;\n        this.nzSimple = false;\n        this.nzSpinning = true;\n        this.destroy$ = new Subject();\n        this.spinning$ = new BehaviorSubject(this.nzSpinning);\n        this.delay$ = new ReplaySubject(1);\n        this.isLoading = false;\n        this.dir = 'ltr';\n    }\n    ngOnInit() {\n        const loading$ = this.delay$.pipe(startWith(this.nzDelay), distinctUntilChanged(), switchMap(delay => {\n            if (delay === 0) {\n                return this.spinning$;\n            }\n            return this.spinning$.pipe(debounce(spinning => timer(spinning ? delay : 0)));\n        }), takeUntil(this.destroy$));\n        loading$.subscribe(loading => {\n            this.isLoading = loading;\n            this.cdr.markForCheck();\n        });\n        this.nzConfigService\n            .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(() => this.cdr.markForCheck());\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n    }\n    ngOnChanges(changes) {\n        const { nzSpinning, nzDelay } = changes;\n        if (nzSpinning) {\n            this.spinning$.next(this.nzSpinning);\n        }\n        if (nzDelay) {\n            this.delay$.next(this.nzDelay);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinComponent, deps: [{ token: i1.NzConfigService }, { token: i0.ChangeDetectorRef }, { token: i2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzSpinComponent, isStandalone: true, selector: \"nz-spin\", inputs: { nzIndicator: \"nzIndicator\", nzSize: \"nzSize\", nzTip: \"nzTip\", nzDelay: \"nzDelay\", nzSimple: \"nzSimple\", nzSpinning: \"nzSpinning\" }, host: { properties: { \"class.ant-spin-nested-loading\": \"!nzSimple\" } }, exportAs: [\"nzSpin\"], usesOnChanges: true, ngImport: i0, template: `\n    <ng-template #defaultTemplate>\n      <span class=\"ant-spin-dot ant-spin-dot-spin\">\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n      </span>\n    </ng-template>\n    <div *ngIf=\"isLoading\">\n      <div\n        class=\"ant-spin\"\n        [class.ant-spin-rtl]=\"dir === 'rtl'\"\n        [class.ant-spin-spinning]=\"isLoading\"\n        [class.ant-spin-lg]=\"nzSize === 'large'\"\n        [class.ant-spin-sm]=\"nzSize === 'small'\"\n        [class.ant-spin-show-text]=\"nzTip\"\n      >\n        <ng-template [ngTemplateOutlet]=\"nzIndicator || defaultTemplate\"></ng-template>\n        <div class=\"ant-spin-text\" *ngIf=\"nzTip\">{{ nzTip }}</div>\n      </div>\n    </div>\n    <div *ngIf=\"!nzSimple\" class=\"ant-spin-container\" [class.ant-spin-blur]=\"isLoading\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    WithConfig()\n], NzSpinComponent.prototype, \"nzIndicator\", void 0);\n__decorate([\n    InputNumber()\n], NzSpinComponent.prototype, \"nzDelay\", void 0);\n__decorate([\n    InputBoolean()\n], NzSpinComponent.prototype, \"nzSimple\", void 0);\n__decorate([\n    InputBoolean()\n], NzSpinComponent.prototype, \"nzSpinning\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-spin',\n                    exportAs: 'nzSpin',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-template #defaultTemplate>\n      <span class=\"ant-spin-dot ant-spin-dot-spin\">\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n        <i class=\"ant-spin-dot-item\"></i>\n      </span>\n    </ng-template>\n    <div *ngIf=\"isLoading\">\n      <div\n        class=\"ant-spin\"\n        [class.ant-spin-rtl]=\"dir === 'rtl'\"\n        [class.ant-spin-spinning]=\"isLoading\"\n        [class.ant-spin-lg]=\"nzSize === 'large'\"\n        [class.ant-spin-sm]=\"nzSize === 'small'\"\n        [class.ant-spin-show-text]=\"nzTip\"\n      >\n        <ng-template [ngTemplateOutlet]=\"nzIndicator || defaultTemplate\"></ng-template>\n        <div class=\"ant-spin-text\" *ngIf=\"nzTip\">{{ nzTip }}</div>\n      </div>\n    </div>\n    <div *ngIf=\"!nzSimple\" class=\"ant-spin-container\" [class.ant-spin-blur]=\"isLoading\">\n      <ng-content></ng-content>\n    </div>\n  `,\n                    host: {\n                        '[class.ant-spin-nested-loading]': '!nzSimple'\n                    },\n                    imports: [NgIf, NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.NzConfigService }, { type: i0.ChangeDetectorRef }, { type: i2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzIndicator: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzTip: [{\n                type: Input\n            }], nzDelay: [{\n                type: Input\n            }], nzSimple: [{\n                type: Input\n            }], nzSpinning: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzSpinModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinModule, imports: [NzSpinComponent], exports: [NzSpinComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzSpinModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzSpinComponent],\n                    exports: [NzSpinComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzSpinComponent, NzSpinModule };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,IAAI,EAAEC,gBAAgB,QAAQ,iBAAiB;AACxD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AACvF,SAASC,OAAO,EAAEC,eAAe,EAAEC,aAAa,EAAEC,KAAK,QAAQ,MAAM;AACrE,SAASC,SAAS,EAAEC,oBAAoB,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,gBAAgB;AAChG,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,WAAW,EAAEC,YAAY,QAAQ,yBAAyB;AACnE,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AAAC,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAuD4DtB,EAAE,CAAAwB,cAAA,aAGpD,CAAC;IAHiDxB,EAAE,CAAAyB,SAAA,UAI9D,CAAC,UACD,CAAC,UACD,CAAC,UACD,CAAC;IAP2DzB,EAAE,CAAA0B,YAAA,CAQ1F,CAAC;EAAA;AAAA;AAAA,SAAAC,6CAAAL,EAAA,EAAAC,GAAA;AAAA,SAAAK,qCAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IARuFtB,EAAE,CAAAwB,cAAA,YAoBtD,CAAC;IApBmDxB,EAAE,CAAA6B,MAAA,EAoB3C,CAAC;IApBwC7B,EAAE,CAAA0B,YAAA,CAoBrC,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAQ,MAAA,GApBkC9B,EAAE,CAAA+B,aAAA;IAAF/B,EAAE,CAAAgC,SAAA,CAoB3C,CAAC;IApBwChC,EAAE,CAAAiC,iBAAA,CAAAH,MAAA,CAAAI,KAoB3C,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBwCtB,EAAE,CAAAwB,cAAA,SAU5E,CAAC,YAQrB,CAAC;IAlB6FxB,EAAE,CAAAoC,UAAA,IAAAT,4CAAA,wBAmB9B,CAAC,IAAAC,oCAAA,gBACzB,CAAC;IApBmD5B,EAAE,CAAA0B,YAAA,CAqB3F,CAAC,CACH,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAQ,MAAA,GAtB0F9B,EAAE,CAAA+B,aAAA;IAAA,MAAAM,kBAAA,GAAFrC,EAAE,CAAAsC,WAAA;IAAFtC,EAAE,CAAAgC,SAAA,CAa3D,CAAC;IAbwDhC,EAAE,CAAAuC,WAAA,iBAAAT,MAAA,CAAAU,GAAA,UAa3D,CAAC,sBAAAV,MAAA,CAAAW,SACA,CAAC,gBAAAX,MAAA,CAAAY,MAAA,YACE,CAAC,gBAAAZ,MAAA,CAAAY,MAAA,YACD,CAAC,uBAAAZ,MAAA,CAAAI,KACP,CAAC;IAjB0DlC,EAAE,CAAAgC,SAAA,CAmB/B,CAAC;IAnB4BhC,EAAE,CAAA2C,UAAA,qBAAAb,MAAA,CAAAc,WAAA,IAAAP,kBAmB/B,CAAC;IAnB4BrC,EAAE,CAAAgC,SAAA,CAoBxD,CAAC;IApBqDhC,EAAE,CAAA2C,UAAA,SAAAb,MAAA,CAAAI,KAoBxD,CAAC;EAAA;AAAA;AAAA,SAAAW,+BAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApBqDtB,EAAE,CAAAwB,cAAA,YAuBf,CAAC;IAvBYxB,EAAE,CAAA8C,YAAA,EAwBxE,CAAC;IAxBqE9C,EAAE,CAAA0B,YAAA,CAyB7F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAQ,MAAA,GAzB0F9B,EAAE,CAAA+B,aAAA;IAAF/B,EAAE,CAAAuC,WAAA,kBAAAT,MAAA,CAAAW,SAuBhB,CAAC;EAAA;AAAA;AA5EvF,MAAMM,qBAAqB,GAAG,MAAM;AACpC,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAACC,eAAe,EAAEC,GAAG,EAAEC,cAAc,EAAE;IAC9C,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,aAAa,GAAGN,qBAAqB;IAC1C,IAAI,CAACH,WAAW,GAAG,IAAI;IACvB,IAAI,CAACF,MAAM,GAAG,SAAS;IACvB,IAAI,CAACR,KAAK,GAAG,IAAI;IACjB,IAAI,CAACoB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,QAAQ,GAAG,IAAInD,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACoD,SAAS,GAAG,IAAInD,eAAe,CAAC,IAAI,CAACiD,UAAU,CAAC;IACrD,IAAI,CAACG,MAAM,GAAG,IAAInD,aAAa,CAAC,CAAC,CAAC;IAClC,IAAI,CAACiC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,GAAG,GAAG,KAAK;EACpB;EACAoB,QAAQA,CAAA,EAAG;IACP,MAAMC,QAAQ,GAAG,IAAI,CAACF,MAAM,CAACG,IAAI,CAACpD,SAAS,CAAC,IAAI,CAAC4C,OAAO,CAAC,EAAE3C,oBAAoB,CAAC,CAAC,EAAEC,SAAS,CAACmD,KAAK,IAAI;MAClG,IAAIA,KAAK,KAAK,CAAC,EAAE;QACb,OAAO,IAAI,CAACL,SAAS;MACzB;MACA,OAAO,IAAI,CAACA,SAAS,CAACI,IAAI,CAACjD,QAAQ,CAACmD,QAAQ,IAAIvD,KAAK,CAACuD,QAAQ,GAAGD,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;IACjF,CAAC,CAAC,EAAEjD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC;IAC7BI,QAAQ,CAACI,SAAS,CAACC,OAAO,IAAI;MAC1B,IAAI,CAACzB,SAAS,GAAGyB,OAAO;MACxB,IAAI,CAACf,GAAG,CAACgB,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACjB,eAAe,CACfkB,gCAAgC,CAACrB,qBAAqB,CAAC,CACvDe,IAAI,CAAChD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAC9BQ,SAAS,CAAC,MAAM,IAAI,CAACd,GAAG,CAACgB,YAAY,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACf,cAAc,CAACiB,MAAM,EAAEP,IAAI,CAAChD,SAAS,CAAC,IAAI,CAAC2C,QAAQ,CAAC,CAAC,CAACQ,SAAS,CAAEK,SAAS,IAAK;MAChF,IAAI,CAAC9B,GAAG,GAAG8B,SAAS;MACpB,IAAI,CAACnB,GAAG,CAACoB,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAC/B,GAAG,GAAG,IAAI,CAACY,cAAc,CAACoB,KAAK;EACxC;EACAC,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAElB,UAAU;MAAEF;IAAQ,CAAC,GAAGoB,OAAO;IACvC,IAAIlB,UAAU,EAAE;MACZ,IAAI,CAACE,SAAS,CAACiB,IAAI,CAAC,IAAI,CAACnB,UAAU,CAAC;IACxC;IACA,IAAIF,OAAO,EAAE;MACT,IAAI,CAACK,MAAM,CAACgB,IAAI,CAAC,IAAI,CAACrB,OAAO,CAAC;IAClC;EACJ;EACAsB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnB,QAAQ,CAACkB,IAAI,CAAC,CAAC;IACpB,IAAI,CAAClB,QAAQ,CAACoB,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhC,eAAe,EAAzBhD,EAAE,CAAAiF,iBAAA,CAAyClE,EAAE,CAACmE,eAAe,GAA7DlF,EAAE,CAAAiF,iBAAA,CAAwEjF,EAAE,CAACmF,iBAAiB,GAA9FnF,EAAE,CAAAiF,iBAAA,CAAyG9D,EAAE,CAACiE,cAAc;IAAA,CAA4D;EAAE;EAC1R;IAAS,IAAI,CAACC,IAAI,kBAD8ErF,EAAE,CAAAsF,iBAAA;MAAAC,IAAA,EACJvC,eAAe;MAAAwC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,6BAAArE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADbtB,EAAE,CAAAuC,WAAA,6BAAAhB,GAAA,CAAAgC,QACU,CAAC;QAAA;MAAA;MAAAqC,MAAA;QAAAhD,WAAA;QAAAF,MAAA;QAAAR,KAAA;QAAAoB,OAAA;QAAAC,QAAA;QAAAC,UAAA;MAAA;MAAAqC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADb/F,EAAE,CAAAgG,oBAAA,EAAFhG,EAAE,CAAAiG,mBAAA;MAAAC,kBAAA,EAAA9E,GAAA;MAAA+E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAjF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtB,EAAE,CAAAwG,eAAA;UAAFxG,EAAE,CAAAoC,UAAA,IAAAf,sCAAA,gCAAFrB,EAAE,CAAAyG,sBAErE,CAAC,IAAAtE,8BAAA,iBAQR,CAAC,IAAAU,8BAAA,gBAa4D,CAAC;QAAA;QAAA,IAAAvB,EAAA;UAvBYtB,EAAE,CAAAgC,SAAA,EAU9E,CAAC;UAV2EhC,EAAE,CAAA2C,UAAA,SAAApB,GAAA,CAAAkB,SAU9E,CAAC;UAV2EzC,EAAE,CAAAgC,SAAA,CAuB9E,CAAC;UAvB2EhC,EAAE,CAAA2C,UAAA,UAAApB,GAAA,CAAAgC,QAuB9E,CAAC;QAAA;MAAA;MAAAmD,YAAA,GAGsC5G,IAAI,EAA6FC,gBAAgB;MAAA4G,aAAA;IAAA,EAAqK;EAAE;AACvV;AACA9G,UAAU,CAAC,CACPmB,UAAU,CAAC,CAAC,CACf,EAAEgC,eAAe,CAAC4D,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACpD/G,UAAU,CAAC,CACPoB,WAAW,CAAC,CAAC,CAChB,EAAE+B,eAAe,CAAC4D,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAChD/G,UAAU,CAAC,CACPqB,YAAY,CAAC,CAAC,CACjB,EAAE8B,eAAe,CAAC4D,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AACjD/G,UAAU,CAAC,CACPqB,YAAY,CAAC,CAAC,CACjB,EAAE8B,eAAe,CAAC4D,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACnD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxCoG7G,EAAE,CAAA8G,iBAAA,CAwCX9D,eAAe,EAAc,CAAC;IAC7GuC,IAAI,EAAEtF,SAAS;IACf8G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,SAAS;MACnBnB,QAAQ,EAAE,QAAQ;MAClBoB,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEzG,iBAAiB,CAACgH,IAAI;MACrCZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBa,IAAI,EAAE;QACF,iCAAiC,EAAE;MACvC,CAAC;MACDC,OAAO,EAAE,CAACtH,IAAI,EAAEC,gBAAgB,CAAC;MACjC+F,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEP,IAAI,EAAExE,EAAE,CAACmE;EAAgB,CAAC,EAAE;IAAEK,IAAI,EAAEvF,EAAE,CAACmF;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEpE,EAAE,CAACiE,cAAc;IAAEiC,UAAU,EAAE,CAAC;MACrH9B,IAAI,EAAEpF;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEyC,WAAW,EAAE,CAAC;MACvC2C,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEsC,MAAM,EAAE,CAAC;MACT6C,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAE8B,KAAK,EAAE,CAAC;MACRqD,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEkD,OAAO,EAAE,CAAC;MACViC,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEmD,QAAQ,EAAE,CAAC;MACXgC,IAAI,EAAEnF;IACV,CAAC,CAAC;IAAEoD,UAAU,EAAE,CAAC;MACb+B,IAAI,EAAEnF;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMkH,YAAY,CAAC;EACf;IAAS,IAAI,CAACxC,IAAI,YAAAyC,qBAAAvC,CAAA;MAAA,YAAAA,CAAA,IAAwFsC,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBArG8ExH,EAAE,CAAAyH,gBAAA;MAAAlC,IAAA,EAqGS+B,YAAY;MAAAF,OAAA,GAAYpE,eAAe;MAAA0E,OAAA,GAAa1E,eAAe;IAAA,EAAI;EAAE;EACpL;IAAS,IAAI,CAAC2E,IAAI,kBAtG8E3H,EAAE,CAAA4H,gBAAA,IAsGwB;EAAE;AAChI;AACA;EAAA,QAAAf,SAAA,oBAAAA,SAAA,KAxGoG7G,EAAE,CAAA8G,iBAAA,CAwGXQ,YAAY,EAAc,CAAC;IAC1G/B,IAAI,EAAElF,QAAQ;IACd0G,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACpE,eAAe,CAAC;MAC1B0E,OAAO,EAAE,CAAC1E,eAAe;IAC7B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,eAAe,EAAEsE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}