<div class="h-[calc(100vh - 74px)] w-full bg-[var(--background-light-gray)]" style="height: calc(100vh - 74px);">

  <!-- Main content with splitter -->
  <as-split direction="horizontal" [gutterSize]="2" [useTransition]="true" (dragEnd)="onSplitDragEnd($event)"
    (dragProgress)="onSplitDragProgress($event)" (gutterDblClick)="onGutterDoubleClick($event)">

    <!-- Main Chat Container -->
    <as-split-area [size]="mainContentSplitSize" [minSize]="50" class="main-content-area">
      <div class="flex flex-col h-full transition-all duration-300 ease-in-out"> <!-- Chat Messages -->
        <div #chatContainer (scroll)="onChatScroll()" class="flex-1 overflow-y-auto px-2 py-4 sm:px-4 sm:py-6">
          <div class="w-[90%] mx-auto">            <!-- Welcome Message when no chat history -->
            <div *ngIf="hasNoHistories" class="mb-6 sm:mb-8 md:mb-10">
              <div class="flex flex-col items-center justify-center p-4 sm:p-6">
                <!-- Message when no chat histories -->
                <div *ngIf="selectedAgent" class="text-center mb-6">
                  <p class="text-[var(--text-medium-gray)] text-base">
                    No chat history found for <span class="font-semibold text-[var(--primary-purple)]">{{selectedAgent}}</span>
                  </p>
                  <p class="text-[var(--text-medium-gray)] text-sm mt-2">
                    Start a conversation by sending a message below.
                  </p>
                </div>
              </div>
            </div>            <!-- Existing chat messages -->
            <div *ngIf="!hasNoHistories && currentConversation?.histories" class="flex flex-col gap-4 mb-4">
              <p class="text-[var(--text-dark)] font-semibold mb-2">
                Chat History with {{currentConversation.agentName || 'Assistant'}}
              </p>
            </div>
            <div *ngFor="let message of currentConversation?.histories" class="flex flex-col gap-4">
              <!-- User Message -->
              <div
                class="flex group gap-2 sm:gap-3 items-start justify-start bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                <div
                  class="h-8 w-8 sm:h-10 sm:w-10 bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] flex items-center justify-center">
                  <i class="ri-user-2-fill text-lg sm:text-xl text-[var(--primary-purple)]"></i>
                </div>
                <div class="flex flex-col w-full">
                  <span class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1">You • just now</span>
                  <div class="flex gap-1 justify-between" *ngIf="!message.editingMode">
                    <div
                      class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)] p-2 sm:p-3 rounded-[8px] shadow-[var(--box-shadow)]">
                      <span #messageText>{{ message.question }}</span>
                    </div>
                    <button
                      class="p-2 outline-none border-none h-8 w-8 sm:h-10 sm:w-10 rounded-[var(--border-radius-small)] bg-[var(--hover-blue-gray)]
                opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out hover:bg-[var(--secondary-purple)] mt-2 flex items-center justify-center cursor-pointer text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]"
                      (click)="message.editingMode = !message.editingMode; previousResponse = message.question || ''">
                      <i class="ri-pencil-line text-lg sm:text-xl"></i>
                    </button>
                  </div>
                  <div
                    class="editmode flex items-center bg-[var(--hover-blue-gray)] p-2 sm:p-3 rounded-2xl w-full relative"
                    *ngIf="message.editingMode"> <textarea type="text" [(ngModel)]="message.question"
                      placeholder="What can I help you with, {{auth.getUserName() || 'there'}}? Type @ to mention an agent"
                      class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                      (input)="adjustInputHeight()" (keydown)="handleKeyDown($event)"></textarea>
                    <div class="flex flex-col sm:flex-row gap-2 absolute right-2 bottom-2 sm:right-3 sm:bottom-3">
                      <button (click)="message.editingMode = false; message.question = previousResponse"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--text-dark)] hover:bg-[#2F2F2F] transition-[var(--transition-default)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer text-[var(--background-white)] min-h-[40px]">
                        Cancel
                      </button>
                      <button (click)="editMessage(message)"
                        class="px-3 py-1 sm:px-4 sm:py-2 bg-[var(--background-white)] hover:bg-[var(--hover-blue-gray)] text-xs sm:text-sm rounded-xl outline-none border-none cursor-pointer min-h-[40px] text-[var(--text-dark)]">
                        Send
                      </button>
                    </div>
                  </div>
                </div>
              </div>              <!-- AI Response -->
              <div class="flex w-full mb-4">
                <!-- Show loading state when message is loading -->
                <ng-container *ngIf="message.isLoading">
                  <div class="flex-1 group w-full">
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px);">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{currentConversation.agentName || 'Assistant'}} </span>
                                </div>
                                <div>{{selectedWorkspace || 'Workspace'}} </div>
                              </div>
                            </div>
                            <!-- Loading indicator -->
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <div class="flex items-center gap-2 text-[var(--text-medium-gray)]">
                                <div class="flex gap-1">
                                  <span
                                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.3s]"></span>
                                  <span
                                    class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce [animation-delay:-0.15s]"></span>
                                  <span class="w-2 h-2 bg-[var(--text-medium-gray)] rounded-full animate-bounce"></span>
                                </div>
                                <span class="text-xs sm:text-sm">Generating response...</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <!-- Show streaming state when message is streaming -->
                <ng-container *ngIf="isMessageStreaming(message)">
                  <div class="flex-1 group w-full">
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px);">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{currentConversation.agentName || 'Assistant'}} </span>
                                </div>
                                <div>{{selectedWorkspace || 'Workspace'}} </div>
                              </div>
                            </div>
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <!-- Streaming response content - display the latest response text -->
                              <div class="response-container">
                                <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>

                <!-- Show actual response when available -->
                <ng-container *ngIf="!message.isLoading && !isMessageStreaming(message) && message.responses && message.responses.length > 0">
                  <div class="flex-1 group w-full">
                    <!-- Current response container -->
                    <div
                      class="bg-[var(--background-white)] rounded-[8px] shadow-[var(--box-shadow)] px-2 py-2 sm:px-4 sm:py-3">
                      <!-- Header: Icon and Timestamp -->
                      <div class="flex gap-2 w-full">
                        <div
                          class="flex items-center justify-center w-[40px] h-[40px] bg-[var(--primary-purple)] rounded-[var(--border-radius-small)]">
                          <i class="ri-sparkling-2-fill text-[var(--background-white)] text-lg sm:text-xl"></i>
                        </div>
                        <div class="flex justify-between mb-1" style="width: calc(100% - 40px);">
                          <div class="flex justify-center px-2 gap-2 flex-col w-full">
                            <div class="text-[var(--text-medium-gray)] text-xs sm:text-sm px-1 flex items-start gap-1 justify-between">
                              <div class="flex gap-[1px] flex-col justify-center">
                                <div>
                                  <span class="text-[var(--text-dark)]">{{currentConversation.agentName || 'Assistant'}} </span>
                                </div>
                                <div>{{selectedWorkspace || 'Workspace'}} </div>
                              </div>
                            </div>
                            <div class="text-sm sm:text-[15px] md:text-[16px] text-[var(--text-dark)]">
                              <!-- Response content - Using the current response -->
                              <div class="response-container">
                                <markdown [data]="getCurrentResponse(message)?.responseText || ''"></markdown>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Action Buttons Section with Navigation -->
                      <div class="flex justify-start items-center mt-3">
                        <div
                          class="flex flex-wrap items-center gap-1 p-1 rounded-md bg-[var(--background-white)] shadow-sm border border-[var(--hover-blue-gray)] *:border-none *:outline-none *:bg-transparent *:text-[16px] sm:*:text-[18px] *:cursor-pointer">                          <!-- Navigation buttons -->
                          <button *ngIf="message.responses.length > 1"
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="goToPreviousResponse(message)" [disabled]="getCurrentResponseIndex(message.id || '') === 0"
                            [ngClass]="{'opacity-50': getCurrentResponseIndex(message.id || '') === 0}">
                            <i class="ri-arrow-left-s-line text-[var(--text-medium-gray)]"></i>
                          </button>
                          <!-- Response counter -->
                          <span *ngIf="message.responses.length > 1" class="  text-[var(--text-medium-gray)]">
                            {{getCurrentResponseIndex(message.id || '') + 1}}/{{message.responses.length}}
                          </span>
                          <button *ngIf="message.responses.length > 1"
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="nextResponse(message)"
                            [disabled]="getCurrentResponseIndex(message.id || '') === message.responses.length - 1"
                            [ngClass]="{'opacity-50': getCurrentResponseIndex(message.id || '') === message.responses.length - 1}">
                            <i class="ri-arrow-right-s-line text-[var(--text-medium-gray)]"></i>
                          </button>

                          <!-- Standard action buttons -->
                          <button
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="copyContent(getCurrentResponse(message)?.responseText)" title="Copy to clipboard">
                            <i class="ri-file-copy-line text-[var(--text-medium-gray)]"></i>
                          </button>
                          <button
                            class="p-1 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)]"
                            (click)="regenerateResponse(message)" title="Regenerate response">
                            <i class="ri-restart-line text-[var(--text-medium-gray)]"></i>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>

          </div>
        </div> <!-- Scroll Down Arrow Button -->
        <div *ngIf="showScrollButton" (click)="scrollToBottom()"
          class="fixed cursor-pointer bottom-[208px] right-[70px] p-2 flex justify-center items-center bg-[var(--primary-purple)] rounded-full text-[var(--background-white)]"
          style="z-index: 9999;">
          <i class="ri-arrow-down-s-line text-xl "></i>
        </div> <!-- Input Section -->
        <div class="p-2 sm:p-4 border-t border-[var(--hover-blue-gray)] relative">
          <div class="w-[90%] mx-auto">
            <div
              class="flex relative items-end justify-start bg-[var(--background-white)] rounded-[8px] px-2 py-2 sm:px-4 sm:py-3 shadow-[var(--box-shadow)]">

              <!-- Left-Side Icons -->
              <div class="w-full flex items-center gap-2  left-0 ">
                <div class="flex items-center gap-2 absolute left-2 sm:left-4 z-10 bottom-2">
                  <!-- Tools icon with counter -->
                  <div class="relative ml-2">
                    <div (click)="toggleAgentSidebar()"
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)]"
                      title="View available agents for this workspace">
                      <i class="ri-tools-fill text-[var(--text-dark)]"></i>
                      <span
                        class="text-xs font-semibold bg-[var(--primary-purple)] text-white rounded-full px-1.5 py-0.5 min-w-[20px] text-center">
                        {{workspaceAgents.length}}
                      </span>
                    </div>
                  </div>

                  <!-- Workspace selection dropdown -->
                  <div class="relative">
                    <div
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)] shadow-[0_0_1px_var(--shadow-color)]"
                      (click)="toggleWorkspaceList.classList.toggle('opacity-0'); toggleWorkspaceList.classList.toggle('hidden')">
                      <span class="font-bold text-[var(--text-dark)]">{{selectedWorkspace}}</span>
                      <span class="text-[var(--font-size-body)]">
                        <i class="ri-arrow-down-s-line text-[var(--text-dark)]"></i>
                      </span>
                    </div>
                    <div #toggleWorkspaceList
                      class="bg-[var(--dialog-bg)] hidden opacity-0 absolute bottom-[calc(100%+0.5rem)] left-0 rounded-[var(--border-radius-large)] p-4 w-56 text-[var(--text-dark)] shadow-[var(--box-shadow)] max-h-[400px] overflow-hidden"
                      style="z-index: 1000;">
                      <div
                        class="space-y-2 overflow-y-scroll h-40 [&::-webkit-scrollbar]:w-1.5 [&::-webkit-scrollbar-thumb]:bg-[var(--hover-blue-gray)] [&::-webkit-scrollbar-thumb]:rounded-full [&::-webkit-scrollbar-track]:bg-transparent hover:[&::-webkit-scrollbar-thumb]:bg-[var(--secondary-purple)] hover:[&::-webkit-scrollbar-thumb]:cursor-pointer">
                        <!-- Workspace options from API -->
                        <div
                          class="flex items-center max-h-40 overflow-hidden cursor-pointer p-2 hover:bg-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] transition-[var(--transition-default)] duration-300"
                          *ngFor="let workspace of workspaces"
                          (click)="selectWorkspace(workspace.title || ''); toggleWorkspaceList.classList.toggle('opacity-0'); toggleWorkspaceList.classList.toggle('hidden');">
                          <span class="font-[var(--font-family)]">{{workspace.title}}</span>
                          <span class="ml-auto text-[var(--primary-purple)]"
                            *ngIf="workspace.title === selectedWorkspace">
                            <i class="ri-check-line"></i>
                          </span>
                        </div>
                        <!-- Show message when no workspaces are available -->
                        <div *ngIf="workspaces.length === 0" class="p-2 text-[var(--text-medium-gray)]">
                          No workspaces available
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Selected Agent Indicator -->
                  <div *ngIf="selectedAgent" class="relative">
                    <div
                      class="flex bg-[var(--dialog-bg)] items-center gap-2 cursor-pointer border border-[var(--hover-blue-gray)] rounded-[var(--border-radius-small)] px-2 py-1 transition-[var(--transition-default)] hover:bg-[var(--hover-blue-gray)] shadow-[0_0_1px_var(--shadow-color)]"
                      title="Currently selected agent">
                      <i class="ri-user-2-line text-[var(--primary-purple)]"></i>
                      <span class="font-bold text-[var(--text-dark)]">
                        {{selectedAgent}}
                      </span>
                      <button (click)="clearSelectedAgent()"
                        class="text-[var(--text-medium-gray)] hover:text-[var(--primary-purple)]">
                        <i class="ri-close-line"></i>
                      </button>
                    </div>
                  </div>
                </div>                <!-- Textarea -->
                <div class="flex items-center gap-2 w-full pb-[40px] sm:pb-[50px] relative">
                  <textarea #chatInput [(ngModel)]="userInput.question" (input)="adjustInputHeight()"
                    placeholder="What can I help you with, {{auth.getUserName() || 'there'}}? Type @ to mention an agent"
                    class="outline-none resize-none bg-transparent px-3 sm:px-4 border-none text-sm sm:text-[16px] md:text-[17px] flex-1 min-h-[80px] sm:min-h-[100px] max-h-[150px] sm:max-h-[200px] line-height-[var(--line-height)] text-[var(--text-dark)] placeholder-[var(--text-medium-gray)]"
                    autofocus (keydown)="handleKeyDown($event)"></textarea>
                </div>
                <!-- Send Button -->
                <div class="flex items-end h-full self-end">
                  <div class="flex items-end gap-2">
                    <button (click)="sendMessage()" [disabled]="isMessageLoading"
                      class="bg-[var(--primary-purple)] text-[var(--background-white)] rounded-[6px] px-3 py-1.5 sm:px-4 sm:py-2 cursor-pointer hover:bg-[var(--secondary-purple)] transition-[var(--transition-default)] disabled:opacity-50 border-none outline-none flex items-center gap-1 min-h-[40px] sm:min-h-[48px]">
                      <span class="text-sm sm:text-[16px] md:text-[17px]">Send</span>
                      <i [class]="isMessageLoading ? 'ri-stop-circle-line' : 'ri-send-plane-fill'"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    </as-split-area>

    <!-- Right Sidebar Area - Fixed initial width of 350px -->
    <as-split-area *ngIf="showSearchResultsSidebar || isAgentSidebarOpen" [size]="rightSidebarSplitSize" [minSize]="15"
      class="right-sidebar-area">
      <div class="h-full w-full relative sidebar-container">
        <!-- Width indicator (visible during drag) -->
        <div class="width-indicator" *ngIf="isDragging">{{ rightSidebarWidth | number:'1.0-0' }}px</div>

        <!-- Source References Sidebar Component -->
        <app-source-references *ngIf="showSearchResultsSidebar" [showSidebar]="showSearchResultsSidebar"
          [searchResults]="searchResults" [currentSourceName]="currentSourceName"
          (closeSidebar)="showSearchResultsSidebar = false">
        </app-source-references>

        <!-- Agent Tools Sidebar Component -->
        <app-agent-sidebar *ngIf="isAgentSidebarOpen" [isAgentSidebarOpen]="isAgentSidebarOpen"
          [agentSidebarTitle]="agentSidebarTitle" [workspaceAgents]="workspaceAgents" (onClose)="toggleAgentSidebar()"
          (onSelectAgent)="selectAgent($event)">
        </app-agent-sidebar>
      </div>
    </as-split-area>
  </as-split>
</div>
