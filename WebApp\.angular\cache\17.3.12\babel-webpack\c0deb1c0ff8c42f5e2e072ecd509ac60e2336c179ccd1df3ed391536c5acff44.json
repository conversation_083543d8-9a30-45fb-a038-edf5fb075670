{"ast": null, "code": "import { Registry } from 'parchment';\nconst MAX_REGISTER_ITERATIONS = 100;\nconst CORE_FORMATS = ['block', 'break', 'cursor', 'inline', 'scroll', 'text'];\nconst createRegistryWithFormats = (formats, sourceRegistry, debug) => {\n  const registry = new Registry();\n  CORE_FORMATS.forEach(name => {\n    const coreBlot = sourceRegistry.query(name);\n    if (coreBlot) registry.register(coreBlot);\n  });\n  formats.forEach(name => {\n    let format = sourceRegistry.query(name);\n    if (!format) {\n      debug.error(`Cannot register \"${name}\" specified in \"formats\" config. Are you sure it was registered?`);\n    }\n    let iterations = 0;\n    while (format) {\n      registry.register(format);\n      format = 'blotName' in format ? format.requiredContainer ?? null : null;\n      iterations += 1;\n      if (iterations > MAX_REGISTER_ITERATIONS) {\n        debug.error(`Cycle detected in registering blot requiredContainer: \"${name}\"`);\n        break;\n      }\n    }\n  });\n  return registry;\n};\nexport default createRegistryWithFormats;", "map": {"version": 3, "names": ["Registry", "MAX_REGISTER_ITERATIONS", "CORE_FORMATS", "createRegistryWithFormats", "formats", "sourceRegistry", "debug", "registry", "for<PERSON>ach", "name", "coreBlot", "query", "register", "format", "error", "iterations", "requiredC<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/core/utils/createRegistryWithFormats.js"], "sourcesContent": ["import { Registry } from 'parchment';\nconst MAX_REGISTER_ITERATIONS = 100;\nconst CORE_FORMATS = ['block', 'break', 'cursor', 'inline', 'scroll', 'text'];\nconst createRegistryWithFormats = (formats, sourceRegistry, debug) => {\n  const registry = new Registry();\n  CORE_FORMATS.forEach(name => {\n    const coreBlot = sourceRegistry.query(name);\n    if (coreBlot) registry.register(coreBlot);\n  });\n  formats.forEach(name => {\n    let format = sourceRegistry.query(name);\n    if (!format) {\n      debug.error(`Cannot register \"${name}\" specified in \"formats\" config. Are you sure it was registered?`);\n    }\n    let iterations = 0;\n    while (format) {\n      registry.register(format);\n      format = 'blotName' in format ? format.requiredContainer ?? null : null;\n      iterations += 1;\n      if (iterations > MAX_REGISTER_ITERATIONS) {\n        debug.error(`Cycle detected in registering blot requiredContainer: \"${name}\"`);\n        break;\n      }\n    }\n  });\n  return registry;\n};\nexport default createRegistryWithFormats;\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,WAAW;AACpC,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAMC,YAAY,GAAG,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC;AAC7E,MAAMC,yBAAyB,GAAGA,CAACC,OAAO,EAAEC,cAAc,EAAEC,KAAK,KAAK;EACpE,MAAMC,QAAQ,GAAG,IAAIP,QAAQ,CAAC,CAAC;EAC/BE,YAAY,CAACM,OAAO,CAACC,IAAI,IAAI;IAC3B,MAAMC,QAAQ,GAAGL,cAAc,CAACM,KAAK,CAACF,IAAI,CAAC;IAC3C,IAAIC,QAAQ,EAAEH,QAAQ,CAACK,QAAQ,CAACF,QAAQ,CAAC;EAC3C,CAAC,CAAC;EACFN,OAAO,CAACI,OAAO,CAACC,IAAI,IAAI;IACtB,IAAII,MAAM,GAAGR,cAAc,CAACM,KAAK,CAACF,IAAI,CAAC;IACvC,IAAI,CAACI,MAAM,EAAE;MACXP,KAAK,CAACQ,KAAK,CAAE,oBAAmBL,IAAK,kEAAiE,CAAC;IACzG;IACA,IAAIM,UAAU,GAAG,CAAC;IAClB,OAAOF,MAAM,EAAE;MACbN,QAAQ,CAACK,QAAQ,CAACC,MAAM,CAAC;MACzBA,MAAM,GAAG,UAAU,IAAIA,MAAM,GAAGA,MAAM,CAACG,iBAAiB,IAAI,IAAI,GAAG,IAAI;MACvED,UAAU,IAAI,CAAC;MACf,IAAIA,UAAU,GAAGd,uBAAuB,EAAE;QACxCK,KAAK,CAACQ,KAAK,CAAE,0DAAyDL,IAAK,GAAE,CAAC;QAC9E;MACF;IACF;EACF,CAAC,CAAC;EACF,OAAOF,QAAQ;AACjB,CAAC;AACD,eAAeJ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}