{"ast": null, "code": "import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_RIGHT_FLAG = 16;\n\n/**\n * This method is like `_.curry` except that arguments are applied to `func`\n * in the manner of `_.partialRight` instead of `_.partial`.\n *\n * The `_.curryRight.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for provided arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of curried functions.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to curry.\n * @param {number} [arity=func.length] The arity of `func`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new curried function.\n * @example\n *\n * var abc = function(a, b, c) {\n *   return [a, b, c];\n * };\n *\n * var curried = _.curryRight(abc);\n *\n * curried(3)(2)(1);\n * // => [1, 2, 3]\n *\n * curried(2, 3)(1);\n * // => [1, 2, 3]\n *\n * curried(1, 2, 3);\n * // => [1, 2, 3]\n *\n * // Curried with placeholders.\n * curried(3)(1, _)(2);\n * // => [1, 2, 3]\n */\nfunction curryRight(func, arity, guard) {\n  arity = guard ? undefined : arity;\n  var result = createWrap(func, WRAP_CURRY_RIGHT_FLAG, undefined, undefined, undefined, undefined, undefined, arity);\n  result.placeholder = curryRight.placeholder;\n  return result;\n}\n\n// Assign default placeholders.\ncurryRight.placeholder = {};\nexport default curryRight;", "map": {"version": 3, "names": ["createWrap", "WRAP_CURRY_RIGHT_FLAG", "curryRight", "func", "arity", "guard", "undefined", "result", "placeholder"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/curryRight.js"], "sourcesContent": ["import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_CURRY_RIGHT_FLAG = 16;\n\n/**\n * This method is like `_.curry` except that arguments are applied to `func`\n * in the manner of `_.partialRight` instead of `_.partial`.\n *\n * The `_.curryRight.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for provided arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of curried functions.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to curry.\n * @param {number} [arity=func.length] The arity of `func`.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new curried function.\n * @example\n *\n * var abc = function(a, b, c) {\n *   return [a, b, c];\n * };\n *\n * var curried = _.curryRight(abc);\n *\n * curried(3)(2)(1);\n * // => [1, 2, 3]\n *\n * curried(2, 3)(1);\n * // => [1, 2, 3]\n *\n * curried(1, 2, 3);\n * // => [1, 2, 3]\n *\n * // Curried with placeholders.\n * curried(3)(1, _)(2);\n * // => [1, 2, 3]\n */\nfunction curryRight(func, arity, guard) {\n  arity = guard ? undefined : arity;\n  var result = createWrap(func, WRAP_CURRY_RIGHT_FLAG, undefined, undefined, undefined, undefined, undefined, arity);\n  result.placeholder = curryRight.placeholder;\n  return result;\n}\n\n// Assign default placeholders.\ncurryRight.placeholder = {};\n\nexport default curryRight;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,qBAAqB,GAAG,EAAE;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACtCD,KAAK,GAAGC,KAAK,GAAGC,SAAS,GAAGF,KAAK;EACjC,IAAIG,MAAM,GAAGP,UAAU,CAACG,IAAI,EAAEF,qBAAqB,EAAEK,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEF,KAAK,CAAC;EAClHG,MAAM,CAACC,WAAW,GAAGN,UAAU,CAACM,WAAW;EAC3C,OAAOD,MAAM;AACf;;AAEA;AACAL,UAAU,CAACM,WAAW,GAAG,CAAC,CAAC;AAE3B,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}