{"ast": null, "code": "import baseCreate from './_baseCreate.js';\nimport baseLodash from './_baseLodash.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Creates a lazy wrapper object which wraps `value` to enable lazy evaluation.\n *\n * @private\n * @constructor\n * @param {*} value The value to wrap.\n */\nfunction LazyWrapper(value) {\n  this.__wrapped__ = value;\n  this.__actions__ = [];\n  this.__dir__ = 1;\n  this.__filtered__ = false;\n  this.__iteratees__ = [];\n  this.__takeCount__ = MAX_ARRAY_LENGTH;\n  this.__views__ = [];\n}\n\n// Ensure `LazyWrapper` is an instance of `baseLodash`.\nLazyWrapper.prototype = baseCreate(baseLodash.prototype);\nLazyWrapper.prototype.constructor = LazyWrapper;\nexport default LazyWrapper;", "map": {"version": 3, "names": ["baseCreate", "<PERSON><PERSON><PERSON><PERSON>", "MAX_ARRAY_LENGTH", "LazyWrapper", "value", "__wrapped__", "__actions__", "__dir__", "__filtered__", "__iteratees__", "__takeCount__", "__views__", "prototype", "constructor"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_LazyWrapper.js"], "sourcesContent": ["import baseCreate from './_baseCreate.js';\nimport baseLodash from './_baseLodash.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Creates a lazy wrapper object which wraps `value` to enable lazy evaluation.\n *\n * @private\n * @constructor\n * @param {*} value The value to wrap.\n */\nfunction LazyWrapper(value) {\n  this.__wrapped__ = value;\n  this.__actions__ = [];\n  this.__dir__ = 1;\n  this.__filtered__ = false;\n  this.__iteratees__ = [];\n  this.__takeCount__ = MAX_ARRAY_LENGTH;\n  this.__views__ = [];\n}\n\n// Ensure `LazyWrapper` is an instance of `baseLodash`.\nLazyWrapper.prototype = baseCreate(baseLodash.prototype);\nLazyWrapper.prototype.constructor = LazyWrapper;\n\nexport default LazyWrapper;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAI,CAACC,WAAW,GAAGD,KAAK;EACxB,IAAI,CAACE,WAAW,GAAG,EAAE;EACrB,IAAI,CAACC,OAAO,GAAG,CAAC;EAChB,IAAI,CAACC,YAAY,GAAG,KAAK;EACzB,IAAI,CAACC,aAAa,GAAG,EAAE;EACvB,IAAI,CAACC,aAAa,GAAGR,gBAAgB;EACrC,IAAI,CAACS,SAAS,GAAG,EAAE;AACrB;;AAEA;AACAR,WAAW,CAACS,SAAS,GAAGZ,UAAU,CAACC,UAAU,CAACW,SAAS,CAAC;AACxDT,WAAW,CAACS,SAAS,CAACC,WAAW,GAAGV,WAAW;AAE/C,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}