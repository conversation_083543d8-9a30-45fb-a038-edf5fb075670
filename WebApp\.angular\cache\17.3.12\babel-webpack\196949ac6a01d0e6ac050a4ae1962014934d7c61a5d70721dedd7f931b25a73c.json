{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @param {Date[]|Number[]} datesArray - the dates to compare\n * @returns {Date} - the earliest of the dates\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport default function min(dirtyDatesArray) {\n  requiredArgs(1, arguments);\n  var datesArray;\n  // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n    // If `dirtyDatesArray` is Array-like Object, convert to Array.\n  } else if (_typeof(dirtyDatesArray) === 'object' && dirtyDatesArray !== null) {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  } else {\n    // `dirtyDatesArray` is non-iterable, return Invalid Date\n    return new Date(NaN);\n  }\n  var result;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (result === undefined || result > currentDate || isNaN(currentDate.getDate())) {\n      result = currentDate;\n    }\n  });\n  return result || new Date(NaN);\n}", "map": {"version": 3, "names": ["_typeof", "toDate", "requiredArgs", "min", "dirtyDatesArray", "arguments", "datesArray", "for<PERSON>ach", "Array", "prototype", "slice", "call", "Date", "NaN", "result", "dirtyDate", "currentDate", "undefined", "isNaN", "getDate"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/min/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @param {Date[]|Number[]} datesArray - the dates to compare\n * @returns {Date} - the earliest of the dates\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport default function min(dirtyDatesArray) {\n  requiredArgs(1, arguments);\n  var datesArray;\n  // `dirtyDatesArray` is Array, Set or Map, or object with custom `forEach` method\n  if (dirtyDatesArray && typeof dirtyDatesArray.forEach === 'function') {\n    datesArray = dirtyDatesArray;\n    // If `dirtyDatesArray` is Array-like Object, convert to Array.\n  } else if (_typeof(dirtyDatesArray) === 'object' && dirtyDatesArray !== null) {\n    datesArray = Array.prototype.slice.call(dirtyDatesArray);\n  } else {\n    // `dirtyDatesArray` is non-iterable, return Invalid Date\n    return new Date(NaN);\n  }\n  var result;\n  datesArray.forEach(function (dirtyDate) {\n    var currentDate = toDate(dirtyDate);\n    if (result === undefined || result > currentDate || isNaN(currentDate.getDate())) {\n      result = currentDate;\n    }\n  });\n  return result || new Date(NaN);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,GAAGA,CAACC,eAAe,EAAE;EAC3CF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,UAAU;EACd;EACA,IAAIF,eAAe,IAAI,OAAOA,eAAe,CAACG,OAAO,KAAK,UAAU,EAAE;IACpED,UAAU,GAAGF,eAAe;IAC5B;EACF,CAAC,MAAM,IAAIJ,OAAO,CAACI,eAAe,CAAC,KAAK,QAAQ,IAAIA,eAAe,KAAK,IAAI,EAAE;IAC5EE,UAAU,GAAGE,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACP,eAAe,CAAC;EAC1D,CAAC,MAAM;IACL;IACA,OAAO,IAAIQ,IAAI,CAACC,GAAG,CAAC;EACtB;EACA,IAAIC,MAAM;EACVR,UAAU,CAACC,OAAO,CAAC,UAAUQ,SAAS,EAAE;IACtC,IAAIC,WAAW,GAAGf,MAAM,CAACc,SAAS,CAAC;IACnC,IAAID,MAAM,KAAKG,SAAS,IAAIH,MAAM,GAAGE,WAAW,IAAIE,KAAK,CAACF,WAAW,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE;MAChFL,MAAM,GAAGE,WAAW;IACtB;EACF,CAAC,CAAC;EACF,OAAOF,MAAM,IAAI,IAAIF,IAAI,CAACC,GAAG,CAAC;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}