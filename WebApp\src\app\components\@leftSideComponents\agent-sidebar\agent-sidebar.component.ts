import { Component } from '@angular/core';
import { AgentChatServiceProxy, ResponseMessageList } from '../../../../shared/service-proxies/service-proxies';

@Component({
  selector: 'app-agent-sidebar',
  standalone: true,
  imports: [],
  templateUrl: './agent-sidebar.component.html',
  styleUrl: './agent-sidebar.component.css'
})
export class AgentSidebarComponent {

  constructor( private _agentChatService: AgentChatServiceProxy) { }
  agents: any;

  oninit() {
    this.loadAgents();
  }

  loadAgents() {
    this._agentChatService.agentContainingChat().subscribe((res)=>{
      this.agents = res;
    });
  }


}
