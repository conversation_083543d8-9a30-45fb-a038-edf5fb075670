{"ast": null, "code": "import ListCache from './_ListCache.js';\nimport stackClear from './_stackClear.js';\nimport stackDelete from './_stackDelete.js';\nimport stackGet from './_stackGet.js';\nimport stackHas from './_stackHas.js';\nimport stackSet from './_stackSet.js';\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\nexport default Stack;", "map": {"version": 3, "names": ["ListCache", "stackClear", "stackDelete", "stackGet", "stackHas", "stackSet", "<PERSON><PERSON>", "entries", "data", "__data__", "size", "prototype", "clear", "get", "has", "set"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_Stack.js"], "sourcesContent": ["import ListCache from './_ListCache.js';\nimport stackClear from './_stackClear.js';\nimport stackDelete from './_stackDelete.js';\nimport stackGet from './_stackGet.js';\nimport stackHas from './_stackHas.js';\nimport stackSet from './_stackSet.js';\n\n/**\n * Creates a stack cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction Stack(entries) {\n  var data = this.__data__ = new ListCache(entries);\n  this.size = data.size;\n}\n\n// Add methods to `Stack`.\nStack.prototype.clear = stackClear;\nStack.prototype['delete'] = stackDelete;\nStack.prototype.get = stackGet;\nStack.prototype.has = stackHas;\nStack.prototype.set = stackSet;\n\nexport default Stack;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,OAAO,EAAE;EACtB,IAAIC,IAAI,GAAG,IAAI,CAACC,QAAQ,GAAG,IAAIT,SAAS,CAACO,OAAO,CAAC;EACjD,IAAI,CAACG,IAAI,GAAGF,IAAI,CAACE,IAAI;AACvB;;AAEA;AACAJ,KAAK,CAACK,SAAS,CAACC,KAAK,GAAGX,UAAU;AAClCK,KAAK,CAACK,SAAS,CAAC,QAAQ,CAAC,GAAGT,WAAW;AACvCI,KAAK,CAACK,SAAS,CAACE,GAAG,GAAGV,QAAQ;AAC9BG,KAAK,CAACK,SAAS,CAACG,GAAG,GAAGV,QAAQ;AAC9BE,KAAK,CAACK,SAAS,CAACI,GAAG,GAAGV,QAAQ;AAE9B,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}