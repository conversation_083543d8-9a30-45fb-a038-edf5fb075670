{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport baseMean from './_baseMean.js';\n\n/**\n * This method is like `_.mean` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be averaged.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the mean.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.meanBy(objects, function(o) { return o.n; });\n * // => 5\n *\n * // The `_.property` iteratee shorthand.\n * _.meanBy(objects, 'n');\n * // => 5\n */\nfunction meanBy(array, iteratee) {\n  return baseMean(array, baseIteratee(iteratee, 2));\n}\nexport default meanBy;", "map": {"version": 3, "names": ["baseIteratee", "baseMean", "meanBy", "array", "iteratee"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/meanBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport baseMean from './_baseMean.js';\n\n/**\n * This method is like `_.mean` except that it accepts `iteratee` which is\n * invoked for each element in `array` to generate the value to be averaged.\n * The iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Math\n * @param {Array} array The array to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {number} Returns the mean.\n * @example\n *\n * var objects = [{ 'n': 4 }, { 'n': 2 }, { 'n': 8 }, { 'n': 6 }];\n *\n * _.meanBy(objects, function(o) { return o.n; });\n * // => 5\n *\n * // The `_.property` iteratee shorthand.\n * _.meanBy(objects, 'n');\n * // => 5\n */\nfunction meanBy(array, iteratee) {\n  return baseMean(array, baseIteratee(iteratee, 2));\n}\n\nexport default meanBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC/B,OAAOH,QAAQ,CAACE,KAAK,EAAEH,YAAY,CAACI,QAAQ,EAAE,CAAC,CAAC,CAAC;AACnD;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}