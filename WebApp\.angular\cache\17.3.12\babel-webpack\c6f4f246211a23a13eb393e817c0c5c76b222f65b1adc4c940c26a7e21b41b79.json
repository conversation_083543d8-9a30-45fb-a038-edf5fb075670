{"ast": null, "code": "import baseMatches from './_baseMatches.js';\nimport baseMatchesProperty from './_baseMatchesProperty.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\nimport property from './property.js';\n\n/**\n * The base implementation of `_.iteratee`.\n *\n * @private\n * @param {*} [value=_.identity] The value to convert to an iteratee.\n * @returns {Function} Returns the iteratee.\n */\nfunction baseIteratee(value) {\n  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.\n  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.\n  if (typeof value == 'function') {\n    return value;\n  }\n  if (value == null) {\n    return identity;\n  }\n  if (typeof value == 'object') {\n    return isArray(value) ? baseMatchesProperty(value[0], value[1]) : baseMatches(value);\n  }\n  return property(value);\n}\nexport default baseIteratee;", "map": {"version": 3, "names": ["baseMatches", "baseMatchesProperty", "identity", "isArray", "property", "baseIteratee", "value"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseIteratee.js"], "sourcesContent": ["import baseMatches from './_baseMatches.js';\nimport baseMatchesProperty from './_baseMatchesProperty.js';\nimport identity from './identity.js';\nimport isArray from './isArray.js';\nimport property from './property.js';\n\n/**\n * The base implementation of `_.iteratee`.\n *\n * @private\n * @param {*} [value=_.identity] The value to convert to an iteratee.\n * @returns {Function} Returns the iteratee.\n */\nfunction baseIteratee(value) {\n  // Don't store the `typeof` result in a variable to avoid a JIT bug in Safari 9.\n  // See https://bugs.webkit.org/show_bug.cgi?id=156034 for more details.\n  if (typeof value == 'function') {\n    return value;\n  }\n  if (value == null) {\n    return identity;\n  }\n  if (typeof value == 'object') {\n    return isArray(value)\n      ? baseMatchesProperty(value[0], value[1])\n      : baseMatches(value);\n  }\n  return property(value);\n}\n\nexport default baseIteratee;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,mBAAmB,MAAM,2BAA2B;AAC3D,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B;EACA;EACA,IAAI,OAAOA,KAAK,IAAI,UAAU,EAAE;IAC9B,OAAOA,KAAK;EACd;EACA,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB,OAAOJ,QAAQ;EACjB;EACA,IAAI,OAAOI,KAAK,IAAI,QAAQ,EAAE;IAC5B,OAAOH,OAAO,CAACG,KAAK,CAAC,GACjBL,mBAAmB,CAACK,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC,GACvCN,WAAW,CAACM,KAAK,CAAC;EACxB;EACA,OAAOF,QAAQ,CAACE,KAAK,CAAC;AACxB;AAEA,eAAeD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}