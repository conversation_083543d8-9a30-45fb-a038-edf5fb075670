import {
  Component,
  <PERSON>Child,
  <PERSON>ement<PERSON>ef,
  On<PERSON>nit,
  inject,
  AfterViewInit,
  On<PERSON><PERSON>roy,
  HostListener,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { marked } from 'marked';
import { Subscription } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AgentDefinitionServiceProxy,
  WorkspaceServiceProxy,
  AgentChatServiceProxy,
  AgentChatRequestDto,
  AgentChatHistoryDto,
  AgentChatConversationDto,
  AgentChatResponseDto,
  PaginatedAgentChatConversationDto,
  PluginResponseDto,
  ChatSource,
  ChatSourceDescription,
} from '../../../shared/service-proxies/service-proxies';
import { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';
import { AuthService } from '../../../shared/services/auth.service';
import { ChatService } from '../../services/chat.service';
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { MarkdownModule } from 'ngx-markdown';
import { SourceReferencesComponent } from '../../components/@rightSideComponents/source-references/source-references.component';
import { AgentSidebarComponent } from '../../components/@rightSideComponents/agent-sidebar/agent-sidebar.component';
import { PluginsSidebarComponent } from '../../components/@rightSideComponents/plugins-sidebar/plugins-sidebar.component';
import { AngularSplitModule } from 'angular-split';
import { ChatListService } from '../../services/chat-list.service';
import { DateTime } from 'luxon';

@Component({
  selector: 'app-agent-chat',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ServiceProxyModule,
    MarkdownModule,
    NzModalModule,
    SourceReferencesComponent,
    AgentSidebarComponent,
    PluginsSidebarComponent,
    AngularSplitModule,
  ],
  providers: [NzModalService],
  templateUrl: './agent-chat.component.html',
  styleUrl: './agent-chat.component.css',
})
export class AgentChatComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chatInput') chatInput!: ElementRef;
  @ViewChild('chatContainer') chatContainer!: ElementRef;

  // User message input
  userInput: AgentChatRequestDto = new AgentChatRequestDto();

  // Chat data
  conversations: any[] = [];
  currentConversation: AgentChatConversationDto = new AgentChatConversationDto({
    agentName: '',
    histories: [],
  });
  isMessageLoading = false;
  previousResponse = '';

  // Search results and source references
  searchResults: any[] = [];
  currentSourceName = '';

  // Response navigation tracking
  currentResponseIndexes: { [key: string]: number } = {};
  // Streaming properties (similar to hero component)
  isStreamingActive = false;
  currentStreamingMessageId?: string;

  // Workspaces and agents
  workspaces: any = [];
  selectedWorkspace = '';
  workspaceAgents: any = [];
  // Pagination state
  currentPage: number = 1;
  pageSize: number = 20;
  hasMoreData: boolean = true;
  isLoadingMore: boolean = false;
  allHistoriesLoaded: boolean = false;
  totalHistoryCount: number = 0;

  // UI state
  showScrollButton: boolean = false;
  selectedAgent = '';
  isAgentSidebarOpen = false;
  agentSidebarTitle = 'Agent Tools';
  hasNoHistories = true;

  // Plugin sidebar state
  isPluginSidebarOpen = false;

  // Splitter properties
  mainContentSplitSize = 100;
  rightSidebarSplitSize = 0;
  rightSidebarWidth = 350;
  isDragging = false;
  showSearchResultsSidebar = false;

  // Message subscription
  private messageSubscription?: Subscription;

  constructor(
    private router: Router,
    private agentDefinition: AgentDefinitionServiceProxy,
    private chatService: ChatService,
    public auth: AuthService,
    private message: NzMessageService,
    private workspaceService: WorkspaceServiceProxy,
    private agentChatService: AgentChatServiceProxy
  ) {
    // Configure marked options
    marked.setOptions({
      breaks: true,
      gfm: true,
    });
  }

  route = inject(ActivatedRoute);
  chatListService = inject(ChatListService);
  ngOnInit(): void {
    // Initialize component
    this.loadSavedRightSidebarWidth();
    this.loadWorkspaces();

    // Set up streaming message subscription
    this.setupStreamingSubscription();

    // Get agent name from route parameter
    this.route.paramMap.subscribe((params) => {
      const agentName = params.get('name');
      if (agentName) {
        this.selectedAgent = agentName;
        this.userInput.agentName = agentName;
      }

      // Load agent chat histories using the route parameter if available
      this.loadAgentChatHistories(agentName || undefined);
    });
  }

  ngAfterViewInit(): void {
    this.scrollToBottom();
    this.adjustInputHeight();
  }

  ngOnDestroy(): void {
    if (this.messageSubscription) {
      this.messageSubscription.unsubscribe();
    }
  }

  /**
   * Handle window resize events to update split sizes
   */
  @HostListener('window:resize', ['$event'])
  onWindowResize(_event: any): void {
    this.updateSplitSizes();
  }

  /**
   * Sets up streaming message subscription similar to hero component
   */
  private setupStreamingSubscription(): void {
    this.messageSubscription = this.chatService.messageReceived$.subscribe(
      ({ message, isError, isComplete }) => {
        console.log('Agent chat - Message received:', message);

        if (isError) {
          this.isMessageLoading = false;
          this.isStreamingActive = false;
          this.currentStreamingMessageId = undefined;

          // Handle error message
          if (this.currentConversation?.histories) {
            const lastMessage =
              this.currentConversation.histories[
              this.currentConversation.histories.length - 1
              ];
            if (
              lastMessage &&
              lastMessage.id === this.currentStreamingMessageId
            ) {
              const messageText = this.extractMessageText(message);
              if (!lastMessage.responses) {
                lastMessage.responses = [];
              }
              lastMessage.responses.push(
                new AgentChatResponseDto({
                  id: '',
                  responseText: messageText,
                  chatSource: 'Error',
                  timestamp: DateTime.now(),
                })
              );
            }
          }
        } else {
          const messageText = this.extractMessageText(message);
          console.log('Agent chat - Extracted text:', messageText);

          if (
            messageText &&
            this.isStreamingActive &&
            this.currentStreamingMessageId
          ) {
            // Handle streaming completion
            if (isComplete) {
              this.handleStreamingComplete();
              return;
            }

            // Find the streaming message and append text
            if (this.currentConversation?.histories) {
              const streamingMessage = this.currentConversation.histories.find(
                (h) => h.id === this.currentStreamingMessageId
              );
              if (streamingMessage) {
                // Initialize responses if needed
                if (!streamingMessage.responses) {
                  streamingMessage.responses = [];
                }

                // Get or create the current response
                if (streamingMessage.responses.length === 0) {
                  streamingMessage.responses.push(
                    new AgentChatResponseDto({
                      id: '',
                      responseText: messageText,
                      chatSource: 'Streaming',
                      timestamp: DateTime.now(),
                    })
                  );
                } else {
                  // Append to existing response
                  const lastResponse =
                    streamingMessage.responses[
                    streamingMessage.responses.length - 1
                    ];
                  lastResponse.responseText =
                    (lastResponse.responseText || '') + messageText;
                }

                // Transition from loading to streaming state
                if (streamingMessage.isLoading) {
                  streamingMessage.isLoading = false;
                  // Add isStreaming property dynamically (similar to hero component)
                  (streamingMessage as any).isStreaming = true;
                }

                // Auto-scroll as content streams in
                setTimeout(() => this.scrollToBottom(), 50);
              }
            }
          }
        }
      }
    );
  }

  /**
   * Handles streaming completion
   */
  private handleStreamingComplete(): void {
    if (this.currentStreamingMessageId && this.currentConversation?.histories) {
      const streamingMessage = this.currentConversation.histories.find(
        (h) => h.id === this.currentStreamingMessageId
      );
      if (streamingMessage) {
        // Clear streaming state
        (streamingMessage as any).isStreaming = false;
        streamingMessage.isLoading = false;
        this.isStreamingActive = false;
        this.currentStreamingMessageId = undefined;
        this.isMessageLoading = false;

        this.scrollToBottom();
      }
    }
  }

  /**
   * Extracts message text from ResponseMessage (same as hero component)
   */
  private extractMessageText(message: any): string {
    return message.isError ? ' ' : message.message;
  }

  /**
   * Check if a message is currently streaming
   */
  isMessageStreaming(message: any): boolean {
    return (message as any).isStreaming === true;
  }


  loadAgentChatHistories(agentName?: string, resetPagination: boolean = true) {
    if (resetPagination) {
      this.currentPage = 1;
      this.hasMoreData = true;
      this.allHistoriesLoaded = false;
      this.totalHistoryCount = 0;
    }

    this.isMessageLoading = true;

    // Log the agent name for debugging
    console.log('Loading chat histories for agent:', agentName || 'all agents', 'Page:', this.currentPage);

    this.agentChatService.getHistoriesPaginated(agentName, this.currentPage, this.pageSize).subscribe({
      next: (paginatedResult) => {
        console.log('Paginated result:', paginatedResult);

        // Update pagination state
        this.hasMoreData = paginatedResult.hasMore || false;
        this.totalHistoryCount = paginatedResult.totalCount || 0;
        this.allHistoriesLoaded = !this.hasMoreData;

        // Process the histories from the paginated result
        const histories = paginatedResult.histories || [];

        // Set the current response index to the latest response for each history
        histories.forEach((history) => {
          if (history.responses && history.responses.length > 0) {
            this.currentResponseIndexes[history.id || ''] =
              history.responses.length - 1;
          }
        });

        if (resetPagination) {
          // First load - create new conversation
          this.currentConversation = new AgentChatConversationDto({
            agentName: agentName || paginatedResult.agentName,
            histories: histories,
          });
          this.conversations = [this.currentConversation];
        } else {
          // Loading more data - prepend to existing histories (since we want newest at bottom)
          if (this.currentConversation.histories) {
            this.currentConversation.histories = [...histories, ...this.currentConversation.histories];
          } else {
            this.currentConversation.histories = histories;
          }
        }

        // Set hasNoHistories flag
        this.hasNoHistories = this.totalHistoryCount === 0;

        // Set selected agent if not already set
        if (this.currentConversation.agentName && !this.selectedAgent) {
          this.selectedAgent = this.currentConversation.agentName;
          this.userInput.agentName = this.currentConversation.agentName;
        }

        this.isMessageLoading = false;
        this.isLoadingMore = false;

        if (resetPagination) {
          setTimeout(() => this.scrollToBottom(), 100);
        }
      },
      error: (error) => {
        console.error('Error loading agent chat histories:', error);
        this.message.error('Failed to load chat histories');
        this.isMessageLoading = false;
        this.isLoadingMore = false;
      },
    });
  }

  loadWorkspaces() {
    this.workspaceService.getAll().subscribe({
      next: (workspaces) => {
        this.workspaces = workspaces;
        if (this.workspaces.length > 0) {
          this.selectedWorkspace = this.workspaces[0].title;
          this.loadAgentsForWorkspace(this.selectedWorkspace);
        }
      },
      error: (error) => {
        console.error('Error loading workspaces:', error);
      },
    });
  }

  /**
   * Loads agents for a specific workspace
   */
  loadAgentsForWorkspace(workspaceName: string) {
    if (!workspaceName) return;

    this.agentDefinition.getAllByWorkspace(workspaceName).subscribe({
      next: (agents) => {
        this.workspaceAgents = agents;
      },
      error: (error) => {
        console.error('Error loading agents for workspace:', error);
      },
    });
  }

  /**
   * Simplified method - plugin loading is now handled by the plugin sidebar component
   * This method is kept for backward compatibility but no longer performs data loading
   */
  loadPluginsForAgent() {
    // Plugin loading is now handled by the plugins-sidebar component itself
    // This method is kept for any future agent-chat specific plugin logic
    console.log('Plugin loading delegated to plugins-sidebar component for agent:', this.selectedAgent);
  }








  /**
   * Loads and saves right sidebar width from localStorage
   */
  loadSavedRightSidebarWidth() {
    const savedWidth = localStorage.getItem('rightSidebarWidth');
    if (savedWidth) {
      this.rightSidebarWidth = parseInt(savedWidth, 10);
    }
  }

  /**
   * Saves right sidebar width to localStorage
   */
  private saveRightSidebarWidth(width: number): void {
    try {
      localStorage.setItem('rightSidebarWidth', String(width));
    } catch (error) {
      console.warn('Failed to save sidebar width to localStorage:', error);
    }
  }

  /**
   * Updates split sizes based on sidebar state and window width
   */
  private updateSplitSizes(): void {
    if (!this.showSearchResultsSidebar) {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
      return;
    }

    const windowWidth = window.innerWidth;
    const sidebarPercentage = Math.min(
      Math.max((this.rightSidebarWidth / windowWidth) * 100, 20),
      50
    );

    this.rightSidebarSplitSize = sidebarPercentage;
    this.mainContentSplitSize = 100 - sidebarPercentage;
  }

  /**
   * Scrolls the chat container to the bottom
   */
  scrollToBottom() {
    try {
      if (this.chatContainer) {
        setTimeout(() => {
          this.chatContainer.nativeElement.scrollTop =
            this.chatContainer.nativeElement.scrollHeight;
        }, 100);
      }
    } catch (err) {
      console.error('Error scrolling to bottom:', err);
    }
  }



  /**
   * Loads more chat histories when scrolling up
   */
  loadMoreHistories() {
    if (!this.hasMoreData || this.isLoadingMore || this.allHistoriesLoaded) {
      return;
    }

    this.isLoadingMore = true;
    this.currentPage++;

    // Store current scroll position to maintain it after loading
    const element = this.chatContainer.nativeElement;
    const previousScrollHeight = element.scrollHeight;

    this.loadAgentChatHistories(this.selectedAgent, false);

    // After loading, adjust scroll position to maintain user's view
    setTimeout(() => {
      if (this.chatContainer) {
        const newScrollHeight = this.chatContainer.nativeElement.scrollHeight;
        const scrollDifference = newScrollHeight - previousScrollHeight;
        this.chatContainer.nativeElement.scrollTop = scrollDifference + 50; // Add small offset
      }
    }, 100);
  }

  /**
   * Adjusts the height of the input textarea
   */
  adjustInputHeight() {
    if (this.chatInput && this.chatInput.nativeElement) {
      const element = this.chatInput.nativeElement;
      element.style.height = 'auto';
      element.style.height = Math.min(element.scrollHeight, 200) + 'px';
    }
  }

  /**
   * Sends a message to agent chat API
   */  async sendMessage() {
    if (!this.userInput.question?.trim()) return;
    if (!this.userInput.agentName && this.selectedAgent) {
      this.userInput.agentName = this.selectedAgent;
    }

    // Store the original message for immediate display
    const originalMessage = this.userInput.question;
    const agentName = this.userInput.agentName || this.selectedAgent;

    // Find current conversation or create a new one
    if (this.currentConversation.agentName !== agentName) {
      const foundConversation = this.conversations.find(
        (c) => c.agentName === agentName
      );

      if (foundConversation) {
        this.currentConversation = foundConversation;
      } else {
        this.currentConversation = new AgentChatConversationDto({
          agentName: agentName,
          histories: [],
        });

        this.conversations.push(this.currentConversation);
      }
    }

    // Ensure histories array exists
    if (!this.currentConversation.histories) {
      this.currentConversation.histories = [];
    }

    // Create a temporary message object to show immediately in the UI
    const tempMessage = new AgentChatHistoryDto({
      id: 'temp-' + Date.now(), // Temporary ID
      question: originalMessage,
      responses: [], // Empty responses initially
      timestamp: DateTime.now(),
    });
    tempMessage.isLoading = true;

    // Add the temporary message to the conversation immediately
    this.currentConversation.histories.push(tempMessage);

    // Update hasNoHistories flag since we now have a conversation
    this.hasNoHistories = false;

    // Update pagination state since we added a new message
    this.totalHistoryCount++;

    // Clear input immediately for better UX
    this.userInput.question = '';
    this.adjustInputHeight();

    // Set loading state
    this.isMessageLoading = true;

    // Scroll to show the new message
    setTimeout(() => this.scrollToBottom(), 100);

    console.log('Sending message to agent:', agentName);

    // Set up streaming for this message
    this.currentStreamingMessageId = tempMessage.id;
    this.isStreamingActive = true;

    // Send the actual request
    const requestDto = new AgentChatRequestDto({
      question: originalMessage,
      agentName: agentName,
    });

    this.agentChatService.sendAgentMessage(requestDto).subscribe({
      next: (response) => {
        if (!response) {
          console.error('Received null response from agent chat service');
          this.message.error('Received invalid response from server');
          this.isMessageLoading = false;
          return;
        }

        // Find and replace the temporary message with the actual response
        if (this.currentConversation.histories) {
          const tempIndex = this.currentConversation.histories.findIndex(
            (h) => h.id === tempMessage.id
          );
          if (tempIndex !== -1) {
            // Replace the temporary message with the actual response
            this.currentConversation.histories[tempIndex] = response;

            // Set the current response index to show the latest response
            if (response.id) {
              this.currentResponseIndexes[response.id] =
                (response.responses || []).length - 1;
            }
          } else {
            // Fallback: just add the response if temp message not found
            this.currentConversation.histories.push(response);
            if (response.id) {
              this.currentResponseIndexes[response.id] =
                (response.responses || []).length - 1;
            }
          }
        }

        // Clear loading state
        this.isMessageLoading = false;
        this.scrollToBottom();
      },
      error: (error) => {
        console.error('Error sending agent message:', error);
        this.message.error('Failed to send message');

        // Remove the temporary message on error
        if (this.currentConversation.histories) {
          const tempIndex = this.currentConversation.histories.findIndex(
            (h) => h.id === tempMessage.id
          );
          if (tempIndex !== -1) {
            this.currentConversation.histories.splice(tempIndex, 1);
          }
        }

        // Restore the original message to the input
        this.userInput.question = originalMessage;
        this.adjustInputHeight();

        this.isMessageLoading = false;
      },
    });
  }

  /**
   * Regenerate response for a specific chat message
   */
  regenerateResponse(history: AgentChatHistoryDto) {
    if (!history || !history.id) return;

    // Set loading state for this specific message
    history.isLoading = true;
    const agentName = this.currentConversation?.agentName;

    this.agentChatService.agentChatRegenerate(history.id, agentName).subscribe({
      next: (response) => {
        // Clear loading state for this message
        history.isLoading = false;

        // Update the responses in the history
        if (!history.responses) {
          history.responses = [];
        }

        // Create a new response from regenerated content
        if (response.responseText) {
          const newResponse = new AgentChatResponseDto({
            id: response.id || '',
            responseText: response.responseText,
            chatSource: 'Regenerated',
            timestamp: DateTime.now(),
          });
          history.responses.push(newResponse);

          // Update the current response index to show the latest response
          const historyId = history.id || '';
          this.currentResponseIndexes[historyId] = history.responses.length - 1;
        }

        this.scrollToBottom();
      },
      error: (error) => {
        console.error('Error regenerating response:', error);
        this.message.error('Failed to regenerate response');

        // Clear loading state for this message on error
        history.isLoading = false;
      },
    });
  }

  /**
   * Edit an existing message
   */
  editMessage(history: AgentChatHistoryDto) {
    if (!history) return;

    // Store the edited question
    const editedQuestion = history.question;

    // Exit editing mode
    history.editingMode = false;

    // Set the input to the edited question and send it
    this.userInput.question = editedQuestion;
    this.sendMessage();
  }

  // Input handling methods
  handleKeyDown(event: any) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendMessage();
    }
  }

  onInput() {
    this.adjustInputHeight();
  }

  onChatScroll() {
    if (!this.chatContainer) return;

    const element = this.chatContainer.nativeElement;
    const scrollTop = element.scrollTop;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;

    // Show/hide scroll to bottom button
    this.showScrollButton = scrollHeight - scrollTop - clientHeight > 100;

    // Check if user scrolled to top and we have more data to load
    if (scrollTop <= 50 && this.hasMoreData && !this.isLoadingMore && !this.isMessageLoading) {
      this.loadMoreHistories();
    }
  }

  // Splitter methods
  onSplitDragEnd(_event: any) {
    this.saveRightSidebarWidth(this.rightSidebarWidth);
  }

  onSplitDragProgress(_event: any) {
    this.isDragging = true;
    setTimeout(() => {
      this.isDragging = false;
    }, 1000);
  }

  onGutterDoubleClick(_event: any) {
    if (this.rightSidebarSplitSize > 0) {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    } else {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    }
  }

  // UI control methods
  toggleAgentSidebar() {
    if (this.router.url.includes('/agent-chat')) {
      this.isPluginSidebarOpen = !this.isPluginSidebarOpen;
      this.isAgentSidebarOpen = false;
      this.showSearchResultsSidebar = false;

    } else {
      this.isAgentSidebarOpen = !this.isAgentSidebarOpen;
    }

    if (this.isAgentSidebarOpen || this.isPluginSidebarOpen) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }



  toggleSearchResultsSidebar(chatSourceDescriptions?: any[], sourceName?: string) {
    this.showSearchResultsSidebar = !this.showSearchResultsSidebar;

    // If we have chat source descriptions, populate the search results
    if (chatSourceDescriptions && chatSourceDescriptions.length > 0) {
      this.currentSourceName = sourceName || 'Web Search';
      this.searchResults = chatSourceDescriptions.map(desc => ({
        title: desc.title || desc.Title || 'No Title',
        url: desc.url || desc.URL || '',
        description: desc.description || desc.Description || 'No description available'
      }));
    }

    if (this.showSearchResultsSidebar) {
      this.rightSidebarSplitSize = 30;
      this.mainContentSplitSize = 70;
    } else {
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }


  /**
   * Handles plugin selection from the sidebar
   * @param plugin The selected plugin
   */
  onSelectPlugin(plugin: PluginResponseDto): void {
    console.log('Selected plugin:', plugin);
    // You can implement plugin-specific actions here
    // For example, show plugin details, configure plugin, etc.
    this.message.info(`Selected plugin: ${plugin.pluginName}`);
  }

  /**
   * Handles closing the plugin sidebar
   */
  onClosePluginSidebar(): void {
    this.isPluginSidebarOpen = false;
    this.rightSidebarSplitSize = 0;
    this.mainContentSplitSize = 100;
  }


  hasChatSource(response: AgentChatResponseDto | null): boolean {
    return !!(response && response.chatSource && response.chatSource.trim());
  }

  getParsedChatSources(response: AgentChatResponseDto | null): any[] {
    if (!response || !response.chatSource || !response.chatSource.trim()) {
      return [];
    }

    try {
      const parsed = JSON.parse(response.chatSource);

      // Ensure we return an array
      if (Array.isArray(parsed)) {
        return parsed.map(source => ({
          source: source.Source || source.source || 'Unknown',
          chatSourceDescriptions: source.ChatSourceDescriptions || source.chatSourceDescriptions || []
        }));
      }
      return [];
    } catch (error) {
      console.error('Error parsing chat sources:', error);
      return [];
    }
  }

  hasSourceDescriptions(response: any): boolean {
    if (!response || !response.chatSourceDescriptions) {
      return false;
    }

    if (!Array.isArray(response.chatSourceDescriptions)) {
      return false;
    }

    // Check if array has items and at least one item has valid data
    return response.chatSourceDescriptions.length > 0 &&
      response.chatSourceDescriptions.some((source: any) =>
        source && (source.title || source.url || source.description)
      );
  }

  /**
   * Gets the count of valid source descriptions for a response
   * @param response The response object
   * @returns The number of valid source descriptions
   */
  getSourceCount(response: any): number {
    if (!this.hasSourceDescriptions(response)) {
      return 0;
    }

    // Count only sources that have valid data
    return response.chatSourceDescriptions.filter((source: any) =>
      source && (source.title || source.url || source.description)
    ).length;
  }

  /**
   * Handles clicking on source indicators to show source references
   * @param response The response containing source descriptions
   */
  onSourceClick(response: any): void {
    if (!response || !this.hasSourceDescriptions(response)) {
      return;
    }

    this.toggleSearchResultsSidebar(
      response.chatSourceDescriptions,
      response.chatSource || 'Web Search'
    );
  }


  selectAgent(agent: any) {
    this.selectedAgent = agent.agentName || agent;
    this.userInput.agentName = this.selectedAgent;

    // Navigate to the agent's chat URL
    this.router.navigate(['/agent-chat', this.selectedAgent]);

    // Close sidebar if open
    if (this.isAgentSidebarOpen) {
      this.isAgentSidebarOpen = false;
      this.rightSidebarSplitSize = 0;
      this.mainContentSplitSize = 100;
    }
  }

  // Helper methods
  copyContent(content: string | undefined) {
    if (content) {
      navigator.clipboard.writeText(content);
      this.message.success('Content copied to clipboard');
    }
  }

  // Response navigation methods
  getCurrentResponseIndex(historyId: string): number {
    return this.currentResponseIndexes[historyId] || 0;
  }

  getCurrentResponse(
    history: AgentChatHistoryDto
  ): AgentChatResponseDto | null {
    if (!history?.responses?.length) return null;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    return history.responses[currentIndex] || null;
  }

  goToPreviousResponse(history: AgentChatHistoryDto) {
    if (!history || !history.responses || history.responses.length <= 1) return;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    if (currentIndex > 0) {
      this.currentResponseIndexes[historyId] = currentIndex - 1;
    }
  }

  nextResponse(history: AgentChatHistoryDto) {
    if (!history || !history.responses || history.responses.length <= 1) return;

    const historyId = history.id || '';
    const currentIndex = this.getCurrentResponseIndex(historyId);

    if (currentIndex < history.responses.length - 1) {
      this.currentResponseIndexes[historyId] = currentIndex + 1;
    }
  }



}

// Add this to the AgentChatHistoryDto class to support editing and loading states
declare module '../../../shared/service-proxies/service-proxies' {
  interface AgentChatHistoryDto {
    editingMode?: boolean;
    isLoading?: boolean;
  }

  interface AgentChatResponseDto {
    copied?: boolean;
  }
}
