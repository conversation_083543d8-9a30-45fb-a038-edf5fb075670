{"ast": null, "code": "import { __decorate } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, Optional, ViewChild, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2$1 from 'ng-zorro-antd/core/services';\nimport { gridResponsiveMap, NzBreakpointEnum } from 'ng-zorro-antd/core/services';\nimport { toNumber, InputBoolean, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i1$1 from 'ng-zorro-antd/select';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport * as i1$2 from '@angular/cdk/bidi';\nimport * as i1$3 from 'ng-zorro-antd/i18n';\n\n/* eslint-disable */\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"nz-pagination-item\", \"\"];\nconst _c1 = (a0, a1) => ({\n  $implicit: a0,\n  page: a1\n});\nfunction NzPaginationItemComponent_ng_template_0_Case_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r1 = i0.ɵɵnextContext().page;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(page_r1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template, 1, 0, \"span\", 3)(2, NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 4);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 2);\n    i0.ɵɵtemplate(1, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template, 1, 0, \"span\", 4)(2, NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template, 1, 0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.next_page);\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r1.direction === \"rtl\" ? 1 : 2);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template, 1, 0, \"span\", 8)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template, 1, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(0, ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 9);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 8);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template, 1, 0, \"span\", 9)(1, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template, 1, 0);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵconditional(0, ctx_r1.direction === \"rtl\" ? 0 : 1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Case_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 5)(1, \"div\", 6);\n    i0.ɵɵtemplate(2, NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template, 2, 1)(3, NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template, 2, 1);\n    i0.ɵɵelementStart(4, \"span\", 7);\n    i0.ɵɵtext(5, \"\\u2022\\u2022\\u2022\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const type_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(2, (tmp_5_0 = type_r3) === \"prev_5\" ? 2 : tmp_5_0 === \"next_5\" ? 3 : -1);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Case_0_Template, 2, 1)(1, NzPaginationItemComponent_ng_template_0_Case_1_Template, 3, 3)(2, NzPaginationItemComponent_ng_template_0_Case_2_Template, 3, 3)(3, NzPaginationItemComponent_ng_template_0_Case_3_Template, 6, 1);\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    const type_r3 = ctx.$implicit;\n    i0.ɵɵconditional(0, (tmp_4_0 = type_r3) === \"page\" ? 0 : tmp_4_0 === \"prev\" ? 1 : tmp_4_0 === \"next\" ? 2 : 3);\n  }\n}\nfunction NzPaginationItemComponent_ng_template_2_Template(rf, ctx) {}\nconst _c2 = [\"nz-pagination-options\", \"\"];\nconst _forTrack0 = ($index, $item) => $item.value;\nfunction NzPaginationOptionsComponent_Conditional_0_For_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-option\", 3);\n  }\n  if (rf & 2) {\n    const option_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"nzLabel\", option_r3.label)(\"nzValue\", option_r3.value);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-select\", 2);\n    i0.ɵɵlistener(\"ngModelChange\", function NzPaginationOptionsComponent_Conditional_0_Template_nz_select_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    });\n    i0.ɵɵrepeaterCreate(1, NzPaginationOptionsComponent_Conditional_0_For_2_Template, 1, 2, \"nz-option\", 3, _forTrack0);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r1.disabled)(\"nzSize\", ctx_r1.nzSize)(\"ngModel\", ctx_r1.pageSize);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r1.listOfPageSizeOption);\n  }\n}\nfunction NzPaginationOptionsComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementStart(2, \"input\", 4);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationOptionsComponent_Conditional_1_Template_input_keydown_enter_2_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.jump_to, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.page, \" \");\n  }\n}\nconst _c3 = [\"containerTemplate\"];\nfunction _forTrack1($index, $item) {\n  return this.trackByPageItem;\n}\nconst _c4 = (a0, a1) => ({\n  $implicit: a0,\n  range: a1\n});\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template(rf, ctx) {}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\", 1);\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template, 0, 0, \"ng-template\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.showTotal)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c4, ctx_r0.total, ctx_r0.ranges));\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_For_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 5);\n    i0.ɵɵlistener(\"gotoIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_gotoIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpPage($event));\n    })(\"diffIndex\", function NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_diffIndex_0_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.jumpDiff($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const page_r3 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"type\", page_r3.type)(\"index\", page_r3.index)(\"disabled\", !!page_r3.disabled)(\"itemRender\", ctx_r0.itemRender)(\"active\", ctx_r0.pageIndex === page_r3.index)(\"direction\", ctx_r0.dir);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 6);\n    i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageIndexChange($event));\n    })(\"pageSizeChange\", function NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.onPageSizeChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"total\", ctx_r0.total)(\"locale\", ctx_r0.locale)(\"disabled\", ctx_r0.disabled)(\"nzSize\", ctx_r0.nzSize)(\"showSizeChanger\", ctx_r0.showSizeChanger)(\"showQuickJumper\", ctx_r0.showQuickJumper)(\"pageIndex\", ctx_r0.pageIndex)(\"pageSize\", ctx_r0.pageSize)(\"pageSizeOptions\", ctx_r0.pageSizeOptions);\n  }\n}\nfunction NzPaginationDefaultComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"ul\");\n    i0.ɵɵtemplate(1, NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template, 2, 5, \"li\", 1);\n    i0.ɵɵrepeaterCreate(2, NzPaginationDefaultComponent_ng_template_0_For_3_Template, 1, 7, \"li\", 2, _forTrack1, true);\n    i0.ɵɵtemplate(4, NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template, 1, 9, \"li\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵconditional(1, ctx_r0.showTotal ? 1 : -1);\n    i0.ɵɵadvance();\n    i0.ɵɵrepeater(ctx_r0.listOfPageItem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵconditional(4, ctx_r0.showQuickJumper || ctx_r0.showSizeChanger ? 4 : -1);\n  }\n}\nfunction NzPaginationSimpleComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"ul\")(1, \"li\", 1);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.prePage());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"li\", 2)(3, \"input\", 3);\n    i0.ɵɵlistener(\"keydown.enter\", function NzPaginationSimpleComponent_ng_template_0_Template_input_keydown_enter_3_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.jumpToPageViaInput($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 4);\n    i0.ɵɵtext(5, \"/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"li\", 5);\n    i0.ɵɵlistener(\"click\", function NzPaginationSimpleComponent_ng_template_0_Template_li_click_7_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.nextPage());\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isFirstIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale.prev_page);\n    i0.ɵɵadvance();\n    i0.ɵɵattribute(\"title\", ctx_r1.pageIndex + \"/\" + ctx_r1.lastIndex);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.disabled)(\"value\", ctx_r1.pageIndex);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.lastIndex, \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"locale\", ctx_r1.locale)(\"disabled\", ctx_r1.isLastIndex)(\"direction\", ctx_r1.dir)(\"itemRender\", ctx_r1.itemRender);\n    i0.ɵɵattribute(\"title\", ctx_r1.locale == null ? null : ctx_r1.locale.next_page);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const simplePagination_r2 = i0.ɵɵreference(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", simplePagination_r2.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template(rf, ctx) {}\nfunction NzPaginationComponent_Conditional_0_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template, 0, 0, \"ng-template\", 4);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const defaultPagination_r3 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", defaultPagination_r3.template);\n  }\n}\nfunction NzPaginationComponent_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Conditional_0_Template, 1, 1, null, 4)(1, NzPaginationComponent_Conditional_0_Conditional_1_Template, 1, 1);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(0, ctx_r3.nzSimple ? 0 : 1);\n  }\n}\nclass NzPaginationItemComponent {\n  constructor() {\n    this.active = false;\n    this.index = null;\n    this.disabled = false;\n    this.direction = 'ltr';\n    this.type = null;\n    this.itemRender = null;\n    this.diffIndex = new EventEmitter();\n    this.gotoIndex = new EventEmitter();\n    this.title = null;\n  }\n  clickItem() {\n    if (!this.disabled) {\n      if (this.type === 'page') {\n        this.gotoIndex.emit(this.index);\n      } else {\n        this.diffIndex.emit({\n          next: 1,\n          prev: -1,\n          prev_5: -5,\n          next_5: 5\n        }[this.type]);\n      }\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      locale,\n      index,\n      type\n    } = changes;\n    if (locale || index || type) {\n      this.title = {\n        page: `${this.index}`,\n        next: this.locale?.next_page,\n        prev: this.locale?.prev_page,\n        prev_5: this.locale?.prev_5,\n        next_5: this.locale?.next_5\n      }[this.type];\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationItemComponent_Factory(t) {\n      return new (t || NzPaginationItemComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationItemComponent,\n      selectors: [[\"li\", \"nz-pagination-item\", \"\"]],\n      hostVars: 19,\n      hostBindings: function NzPaginationItemComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzPaginationItemComponent_click_HostBindingHandler() {\n            return ctx.clickItem();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"title\", ctx.title);\n          i0.ɵɵclassProp(\"ant-pagination-prev\", ctx.type === \"prev\")(\"ant-pagination-next\", ctx.type === \"next\")(\"ant-pagination-item\", ctx.type === \"page\")(\"ant-pagination-jump-prev\", ctx.type === \"prev_5\")(\"ant-pagination-jump-prev-custom-icon\", ctx.type === \"prev_5\")(\"ant-pagination-jump-next\", ctx.type === \"next_5\")(\"ant-pagination-jump-next-custom-icon\", ctx.type === \"next_5\")(\"ant-pagination-disabled\", ctx.disabled)(\"ant-pagination-item-active\", ctx.active);\n        }\n      },\n      inputs: {\n        active: \"active\",\n        locale: \"locale\",\n        index: \"index\",\n        disabled: \"disabled\",\n        direction: \"direction\",\n        type: \"type\",\n        itemRender: \"itemRender\"\n      },\n      outputs: {\n        diffIndex: \"diffIndex\",\n        gotoIndex: \"gotoIndex\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c0,\n      decls: 3,\n      vars: 5,\n      consts: [[\"renderItemTemplate\", \"\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"type\", \"button\", 1, \"ant-pagination-item-link\", 3, \"disabled\"], [\"nz-icon\", \"\", \"nzType\", \"right\"], [\"nz-icon\", \"\", \"nzType\", \"left\"], [1, \"ant-pagination-item-link\"], [1, \"ant-pagination-item-container\"], [1, \"ant-pagination-item-ellipsis\"], [\"nz-icon\", \"\", \"nzType\", \"double-right\", 1, \"ant-pagination-item-link-icon\"], [\"nz-icon\", \"\", \"nzType\", \"double-left\", 1, \"ant-pagination-item-link-icon\"]],\n      template: function NzPaginationItemComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationItemComponent_ng_template_0_Template, 4, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzPaginationItemComponent_ng_template_2_Template, 0, 0, \"ng-template\", 1);\n        }\n        if (rf & 2) {\n          const renderItemTemplate_r4 = i0.ɵɵreference(1);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.itemRender || renderItemTemplate_r4)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c1, ctx.type, ctx.index));\n        }\n      },\n      dependencies: [NzIconModule, i1.NzIconDirective, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationItemComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-item]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #renderItemTemplate let-type let-page=\"page\">\n      @switch (type) {\n        @case ('page') {\n          <a>{{ page }}</a>\n        }\n        @case ('prev') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.prev_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"right\"></span>\n            } @else {\n              <span nz-icon nzType=\"left\"></span>\n            }\n          </button>\n        }\n        @case ('next') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.next_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"left\"></span>\n            } @else {\n              <span nz-icon nzType=\"right\"></span>\n            }\n          </button>\n        }\n        @default {\n          <a class=\"ant-pagination-item-link\">\n            <div class=\"ant-pagination-item-container\">\n              @switch (type) {\n                @case ('prev_5') {\n                  @if (direction === 'rtl') {\n                    <span\n                      nz-icon\n                      nzType=\"double-right\"\n                      class=\"ant-pagination-item-link-icon\"\n                    ></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n                @case ('next_5') {\n                  @if (direction === 'rtl') {\n                    <span nz-icon nzType=\"double-left\"\n                          class=\"ant-pagination-item-link-icon\"></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n              }\n              <span class=\"ant-pagination-item-ellipsis\">•••</span>\n            </div>\n          </a>\n        }\n      }\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"itemRender || renderItemTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: type, page: index }\"\n    />\n  `,\n      host: {\n        '[class.ant-pagination-prev]': `type === 'prev'`,\n        '[class.ant-pagination-next]': `type === 'next'`,\n        '[class.ant-pagination-item]': `type === 'page'`,\n        '[class.ant-pagination-jump-prev]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-prev-custom-icon]': `type === 'prev_5'`,\n        '[class.ant-pagination-jump-next]': `type === 'next_5'`,\n        '[class.ant-pagination-jump-next-custom-icon]': `type === 'next_5'`,\n        '[class.ant-pagination-disabled]': 'disabled',\n        '[class.ant-pagination-item-active]': 'active',\n        '[attr.title]': 'title',\n        '(click)': 'clickItem()'\n      },\n      imports: [NzIconModule, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], null, {\n    active: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    index: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    direction: [{\n      type: Input\n    }],\n    type: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    diffIndex: [{\n      type: Output\n    }],\n    gotoIndex: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationOptionsComponent {\n  constructor() {\n    this.nzSize = 'default';\n    this.disabled = false;\n    this.showSizeChanger = false;\n    this.showQuickJumper = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageSizeOptions = [];\n    this.pageIndexChange = new EventEmitter();\n    this.pageSizeChange = new EventEmitter();\n    this.listOfPageSizeOption = [];\n  }\n  onPageSizeChange(size) {\n    if (this.pageSize !== size) {\n      this.pageSizeChange.next(size);\n    }\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = Math.floor(toNumber(target.value, this.pageIndex));\n    this.pageIndexChange.next(index);\n    target.value = '';\n  }\n  ngOnChanges(changes) {\n    const {\n      pageSize,\n      pageSizeOptions,\n      locale\n    } = changes;\n    if (pageSize || pageSizeOptions || locale) {\n      this.listOfPageSizeOption = [...new Set([...this.pageSizeOptions, this.pageSize])].map(item => ({\n        value: item,\n        label: `${item} ${this.locale.items_per_page}`\n      }));\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationOptionsComponent_Factory(t) {\n      return new (t || NzPaginationOptionsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationOptionsComponent,\n      selectors: [[\"li\", \"nz-pagination-options\", \"\"]],\n      hostAttrs: [1, \"ant-pagination-options\"],\n      inputs: {\n        nzSize: \"nzSize\",\n        disabled: \"disabled\",\n        showSizeChanger: \"showSizeChanger\",\n        showQuickJumper: \"showQuickJumper\",\n        locale: \"locale\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\",\n        pageSizeOptions: \"pageSizeOptions\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\",\n        pageSizeChange: \"pageSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      decls: 2,\n      vars: 2,\n      consts: [[1, \"ant-pagination-options-size-changer\", 3, \"nzDisabled\", \"nzSize\", \"ngModel\"], [1, \"ant-pagination-options-quick-jumper\"], [1, \"ant-pagination-options-size-changer\", 3, \"ngModelChange\", \"nzDisabled\", \"nzSize\", \"ngModel\"], [3, \"nzLabel\", \"nzValue\"], [3, \"keydown.enter\", \"disabled\"]],\n      template: function NzPaginationOptionsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationOptionsComponent_Conditional_0_Template, 3, 3, \"nz-select\", 0)(1, NzPaginationOptionsComponent_Conditional_1_Template, 4, 3, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.showSizeChanger ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(1, ctx.showQuickJumper ? 1 : -1);\n        }\n      },\n      dependencies: [NzSelectModule, i1$1.NzOptionComponent, i1$1.NzSelectComponent, FormsModule, i2.NgControlStatus, i2.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationOptionsComponent, [{\n    type: Component,\n    args: [{\n      selector: 'li[nz-pagination-options]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showSizeChanger) {\n      <nz-select\n        class=\"ant-pagination-options-size-changer\"\n        [nzDisabled]=\"disabled\"\n        [nzSize]=\"nzSize\"\n        [ngModel]=\"pageSize\"\n        (ngModelChange)=\"onPageSizeChange($event)\"\n      >\n        @for (option of listOfPageSizeOption; track option.value) {\n          <nz-option [nzLabel]=\"option.label\" [nzValue]=\"option.value\" />\n        }\n      </nz-select>\n    }\n\n    @if (showQuickJumper) {\n      <div class=\"ant-pagination-options-quick-jumper\">\n        {{ locale.jump_to }}\n        <input [disabled]=\"disabled\" (keydown.enter)=\"jumpToPageViaInput($event)\" />\n        {{ locale.page }}\n      </div>\n    }\n  `,\n      host: {\n        class: 'ant-pagination-options'\n      },\n      imports: [NzSelectModule, FormsModule],\n      standalone: true\n    }]\n  }], () => [], {\n    nzSize: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationDefaultComponent {\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.nzSize = 'default';\n    this.itemRender = null;\n    this.showTotal = null;\n    this.disabled = false;\n    this.showSizeChanger = false;\n    this.showQuickJumper = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageSizeOptions = [10, 20, 30, 40];\n    this.pageIndexChange = new EventEmitter();\n    this.pageSizeChange = new EventEmitter();\n    this.ranges = [0, 0];\n    this.listOfPageItem = [];\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpPage(index) {\n    this.onPageIndexChange(index);\n  }\n  jumpDiff(diff) {\n    this.jumpPage(this.pageIndex + diff);\n  }\n  trackByPageItem(_, value) {\n    return `${value.type}-${value.index}`;\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  onPageSizeChange(size) {\n    this.pageSizeChange.next(size);\n  }\n  getLastIndex(total, pageSize) {\n    return Math.ceil(total / pageSize);\n  }\n  buildIndexes() {\n    const lastIndex = this.getLastIndex(this.total, this.pageSize);\n    this.listOfPageItem = this.getListOfPageItem(this.pageIndex, lastIndex);\n  }\n  getListOfPageItem(pageIndex, lastIndex) {\n    // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n    const concatWithPrevNext = listOfPage => {\n      const prevItem = {\n        type: 'prev',\n        disabled: pageIndex === 1\n      };\n      const nextItem = {\n        type: 'next',\n        disabled: pageIndex === lastIndex\n      };\n      return [prevItem, ...listOfPage, nextItem];\n    };\n    const generatePage = (start, end) => {\n      const list = [];\n      for (let i = start; i <= end; i++) {\n        list.push({\n          index: i,\n          type: 'page'\n        });\n      }\n      return list;\n    };\n    if (lastIndex <= 9) {\n      return concatWithPrevNext(generatePage(1, lastIndex));\n    } else {\n      // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n      const generateRangeItem = (selected, last) => {\n        let listOfRange = [];\n        const prevFiveItem = {\n          type: 'prev_5'\n        };\n        const nextFiveItem = {\n          type: 'next_5'\n        };\n        const firstPageItem = generatePage(1, 1);\n        const lastPageItem = generatePage(lastIndex, lastIndex);\n        if (selected < 5) {\n          // If the 4th is selected, one more page will be displayed.\n          const maxLeft = selected === 4 ? 6 : 5;\n          listOfRange = [...generatePage(2, maxLeft), nextFiveItem];\n        } else if (selected < last - 3) {\n          listOfRange = [prevFiveItem, ...generatePage(selected - 2, selected + 2), nextFiveItem];\n        } else {\n          // If the 4th from last is selected, one more page will be displayed.\n          const minRight = selected === last - 3 ? last - 5 : last - 4;\n          listOfRange = [prevFiveItem, ...generatePage(minRight, last - 1)];\n        }\n        return [...firstPageItem, ...listOfRange, ...lastPageItem];\n      };\n      return concatWithPrevNext(generateRangeItem(pageIndex, lastIndex));\n    }\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      pageSize,\n      total\n    } = changes;\n    if (pageIndex || pageSize || total) {\n      this.ranges = [(this.pageIndex - 1) * this.pageSize + 1, Math.min(this.pageIndex * this.pageSize, this.total)];\n      this.buildIndexes();\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationDefaultComponent_Factory(t) {\n      return new (t || NzPaginationDefaultComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationDefaultComponent,\n      selectors: [[\"nz-pagination-default\"]],\n      viewQuery: function NzPaginationDefaultComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      inputs: {\n        nzSize: \"nzSize\",\n        itemRender: \"itemRender\",\n        showTotal: \"showTotal\",\n        disabled: \"disabled\",\n        locale: \"locale\",\n        showSizeChanger: \"showSizeChanger\",\n        showQuickJumper: \"showQuickJumper\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\",\n        pageSizeOptions: \"pageSizeOptions\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\",\n        pageSizeChange: \"pageSizeChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"containerTemplate\", \"\"], [1, \"ant-pagination-total-text\"], [\"nz-pagination-item\", \"\", 3, \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"nz-pagination-item\", \"\", 3, \"gotoIndex\", \"diffIndex\", \"locale\", \"type\", \"index\", \"disabled\", \"itemRender\", \"active\", \"direction\"], [\"nz-pagination-options\", \"\", 3, \"pageIndexChange\", \"pageSizeChange\", \"total\", \"locale\", \"disabled\", \"nzSize\", \"showSizeChanger\", \"showQuickJumper\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"]],\n      template: function NzPaginationDefaultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationDefaultComponent_ng_template_0_Template, 5, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-default',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        @if (showTotal) {\n          <li class=\"ant-pagination-total-text\">\n            <ng-template\n              [ngTemplateOutlet]=\"showTotal\"\n              [ngTemplateOutletContext]=\"{ $implicit: total, range: ranges }\"\n            />\n          </li>\n        }\n\n        @for (page of listOfPageItem; track trackByPageItem) {\n          <li\n            nz-pagination-item\n            [locale]=\"locale\"\n            [type]=\"page.type\"\n            [index]=\"page.index\"\n            [disabled]=\"!!page.disabled\"\n            [itemRender]=\"itemRender\"\n            [active]=\"pageIndex === page.index\"\n            (gotoIndex)=\"jumpPage($event)\"\n            (diffIndex)=\"jumpDiff($event)\"\n            [direction]=\"dir\"\n          ></li>\n        }\n\n        @if (showQuickJumper || showSizeChanger) {\n          <li\n            nz-pagination-options\n            [total]=\"total\"\n            [locale]=\"locale\"\n            [disabled]=\"disabled\"\n            [nzSize]=\"nzSize\"\n            [showSizeChanger]=\"showSizeChanger\"\n            [showQuickJumper]=\"showQuickJumper\"\n            [pageIndex]=\"pageIndex\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"pageSizeOptions\"\n            (pageIndexChange)=\"onPageIndexChange($event)\"\n            (pageSizeChange)=\"onPageSizeChange($event)\"\n          ></li>\n        }\n      </ul>\n    </ng-template>\n  `,\n      imports: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    showTotal: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    showSizeChanger: [{\n      type: Input\n    }],\n    showQuickJumper: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageSizeOptions: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }],\n    pageSizeChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzPaginationSimpleComponent {\n  constructor(cdr, renderer, elementRef, directionality) {\n    this.cdr = cdr;\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.directionality = directionality;\n    this.itemRender = null;\n    this.disabled = false;\n    this.total = 0;\n    this.pageIndex = 1;\n    this.pageSize = 10;\n    this.pageIndexChange = new EventEmitter();\n    this.lastIndex = 0;\n    this.isFirstIndex = false;\n    this.isLastIndex = false;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n  }\n  ngOnInit() {\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.updateRtlStyle();\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n    this.updateRtlStyle();\n  }\n  updateRtlStyle() {\n    if (this.dir === 'rtl') {\n      this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    } else {\n      this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  jumpToPageViaInput($event) {\n    const target = $event.target;\n    const index = toNumber(target.value, this.pageIndex);\n    this.onPageIndexChange(index);\n    target.value = `${this.pageIndex}`;\n  }\n  prePage() {\n    this.onPageIndexChange(this.pageIndex - 1);\n  }\n  nextPage() {\n    this.onPageIndexChange(this.pageIndex + 1);\n  }\n  onPageIndexChange(index) {\n    this.pageIndexChange.next(index);\n  }\n  updateBindingValue() {\n    this.lastIndex = Math.ceil(this.total / this.pageSize);\n    this.isFirstIndex = this.pageIndex === 1;\n    this.isLastIndex = this.pageIndex === this.lastIndex;\n  }\n  ngOnChanges(changes) {\n    const {\n      pageIndex,\n      total,\n      pageSize\n    } = changes;\n    if (pageIndex || total || pageSize) {\n      this.updateBindingValue();\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationSimpleComponent_Factory(t) {\n      return new (t || NzPaginationSimpleComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationSimpleComponent,\n      selectors: [[\"nz-pagination-simple\"]],\n      viewQuery: function NzPaginationSimpleComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c3, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.template = _t.first);\n        }\n      },\n      inputs: {\n        itemRender: \"itemRender\",\n        disabled: \"disabled\",\n        locale: \"locale\",\n        total: \"total\",\n        pageIndex: \"pageIndex\",\n        pageSize: \"pageSize\"\n      },\n      outputs: {\n        pageIndexChange: \"pageIndexChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 0,\n      consts: [[\"containerTemplate\", \"\"], [\"nz-pagination-item\", \"\", \"type\", \"prev\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"], [1, \"ant-pagination-simple-pager\"], [\"size\", \"3\", 3, \"keydown.enter\", \"disabled\", \"value\"], [1, \"ant-pagination-slash\"], [\"nz-pagination-item\", \"\", \"type\", \"next\", 3, \"click\", \"locale\", \"disabled\", \"direction\", \"itemRender\"]],\n      template: function NzPaginationSimpleComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzPaginationSimpleComponent_ng_template_0_Template, 8, 14, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n        }\n      },\n      dependencies: [NzPaginationItemComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationSimpleComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination-simple',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <ng-template #containerTemplate>\n      <ul>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale.prev_page\"\n          [disabled]=\"isFirstIndex\"\n          [direction]=\"dir\"\n          (click)=\"prePage()\"\n          type=\"prev\"\n          [itemRender]=\"itemRender\"\n        ></li>\n        <li [attr.title]=\"pageIndex + '/' + lastIndex\" class=\"ant-pagination-simple-pager\">\n          <input [disabled]=\"disabled\" [value]=\"pageIndex\" (keydown.enter)=\"jumpToPageViaInput($event)\" size=\"3\" />\n          <span class=\"ant-pagination-slash\">/</span>\n          {{ lastIndex }}\n        </li>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale?.next_page\"\n          [disabled]=\"isLastIndex\"\n          [direction]=\"dir\"\n          (click)=\"nextPage()\"\n          type=\"next\"\n          [itemRender]=\"itemRender\"\n        ></li>\n      </ul>\n    </ng-template>\n  `,\n      imports: [NzPaginationItemComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    template: [{\n      type: ViewChild,\n      args: ['containerTemplate', {\n        static: true\n      }]\n    }],\n    itemRender: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    locale: [{\n      type: Input\n    }],\n    total: [{\n      type: Input\n    }],\n    pageIndex: [{\n      type: Input\n    }],\n    pageSize: [{\n      type: Input\n    }],\n    pageIndexChange: [{\n      type: Output\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'pagination';\nclass NzPaginationComponent {\n  validatePageIndex(value, lastIndex) {\n    if (value > lastIndex) {\n      return lastIndex;\n    } else if (value < 1) {\n      return 1;\n    } else {\n      return value;\n    }\n  }\n  onPageIndexChange(index) {\n    const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n    const validIndex = this.validatePageIndex(index, lastIndex);\n    if (validIndex !== this.nzPageIndex && !this.nzDisabled) {\n      this.nzPageIndex = validIndex;\n      this.nzPageIndexChange.emit(this.nzPageIndex);\n    }\n  }\n  onPageSizeChange(size) {\n    this.nzPageSize = size;\n    this.nzPageSizeChange.emit(size);\n    const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n    if (this.nzPageIndex > lastIndex) {\n      this.onPageIndexChange(lastIndex);\n    }\n  }\n  onTotalChange(total) {\n    const lastIndex = this.getLastIndex(total, this.nzPageSize);\n    if (this.nzPageIndex > lastIndex) {\n      Promise.resolve().then(() => {\n        this.onPageIndexChange(lastIndex);\n        this.cdr.markForCheck();\n      });\n    }\n  }\n  getLastIndex(total, pageSize) {\n    return Math.ceil(total / pageSize);\n  }\n  constructor(i18n, cdr, breakpointService, nzConfigService, directionality) {\n    this.i18n = i18n;\n    this.cdr = cdr;\n    this.breakpointService = breakpointService;\n    this.nzConfigService = nzConfigService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzPageSizeChange = new EventEmitter();\n    this.nzPageIndexChange = new EventEmitter();\n    this.nzShowTotal = null;\n    this.nzItemRender = null;\n    this.nzSize = 'default';\n    this.nzPageSizeOptions = [10, 20, 30, 40];\n    this.nzShowSizeChanger = false;\n    this.nzShowQuickJumper = false;\n    this.nzSimple = false;\n    this.nzDisabled = false;\n    this.nzResponsive = false;\n    this.nzHideOnSinglePage = false;\n    this.nzTotal = 0;\n    this.nzPageIndex = 1;\n    this.nzPageSize = 10;\n    this.showPagination = true;\n    this.size = 'default';\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n    this.total$ = new ReplaySubject(1);\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Pagination');\n      this.cdr.markForCheck();\n    });\n    this.total$.pipe(takeUntil(this.destroy$)).subscribe(total => {\n      this.onTotalChange(total);\n    });\n    this.breakpointService.subscribe(gridResponsiveMap).pipe(takeUntil(this.destroy$)).subscribe(bp => {\n      if (this.nzResponsive) {\n        this.size = bp === NzBreakpointEnum.xs ? 'small' : 'default';\n        this.cdr.markForCheck();\n      }\n    });\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    this.dir = this.directionality.value;\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzHideOnSinglePage,\n      nzTotal,\n      nzPageSize,\n      nzSize\n    } = changes;\n    if (nzTotal) {\n      this.total$.next(this.nzTotal);\n    }\n    if (nzHideOnSinglePage || nzTotal || nzPageSize) {\n      this.showPagination = this.nzHideOnSinglePage && this.nzTotal > this.nzPageSize || this.nzTotal > 0 && !this.nzHideOnSinglePage;\n    }\n    if (nzSize) {\n      this.size = nzSize.currentValue;\n    }\n  }\n  static {\n    this.ɵfac = function NzPaginationComponent_Factory(t) {\n      return new (t || NzPaginationComponent)(i0.ɵɵdirectiveInject(i1$3.NzI18nService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2$1.NzBreakpointService), i0.ɵɵdirectiveInject(i3.NzConfigService), i0.ɵɵdirectiveInject(i1$2.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzPaginationComponent,\n      selectors: [[\"nz-pagination\"]],\n      hostAttrs: [1, \"ant-pagination\"],\n      hostVars: 8,\n      hostBindings: function NzPaginationComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-pagination-simple\", ctx.nzSimple)(\"ant-pagination-disabled\", ctx.nzDisabled)(\"ant-pagination-mini\", !ctx.nzSimple && ctx.size === \"small\")(\"ant-pagination-rtl\", ctx.dir === \"rtl\");\n        }\n      },\n      inputs: {\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzSize: \"nzSize\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzShowSizeChanger: \"nzShowSizeChanger\",\n        nzShowQuickJumper: \"nzShowQuickJumper\",\n        nzSimple: \"nzSimple\",\n        nzDisabled: \"nzDisabled\",\n        nzResponsive: \"nzResponsive\",\n        nzHideOnSinglePage: \"nzHideOnSinglePage\",\n        nzTotal: \"nzTotal\",\n        nzPageIndex: \"nzPageIndex\",\n        nzPageSize: \"nzPageSize\"\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\"\n      },\n      exportAs: [\"nzPagination\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 18,\n      consts: [[\"simplePagination\", \"\"], [\"defaultPagination\", \"\"], [3, \"pageIndexChange\", \"disabled\", \"itemRender\", \"locale\", \"pageSize\", \"total\", \"pageIndex\"], [3, \"pageIndexChange\", \"pageSizeChange\", \"nzSize\", \"itemRender\", \"showTotal\", \"disabled\", \"locale\", \"showSizeChanger\", \"showQuickJumper\", \"total\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzPaginationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, NzPaginationComponent_Conditional_0_Template, 2, 1);\n          i0.ɵɵelementStart(1, \"nz-pagination-simple\", 2, 0);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_simple_pageIndexChange_1_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"nz-pagination-default\", 3, 1);\n          i0.ɵɵlistener(\"pageIndexChange\", function NzPaginationComponent_Template_nz_pagination_default_pageIndexChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageIndexChange($event));\n          })(\"pageSizeChange\", function NzPaginationComponent_Template_nz_pagination_default_pageSizeChange_3_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n          });\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵconditional(0, ctx.showPagination ? 0 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.nzDisabled)(\"itemRender\", ctx.nzItemRender)(\"locale\", ctx.locale)(\"pageSize\", ctx.nzPageSize)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"nzSize\", ctx.size)(\"itemRender\", ctx.nzItemRender)(\"showTotal\", ctx.nzShowTotal)(\"disabled\", ctx.nzDisabled)(\"locale\", ctx.locale)(\"showSizeChanger\", ctx.nzShowSizeChanger)(\"showQuickJumper\", ctx.nzShowQuickJumper)(\"total\", ctx.nzTotal)(\"pageIndex\", ctx.nzPageIndex)(\"pageSize\", ctx.nzPageSize)(\"pageSizeOptions\", ctx.nzPageSizeOptions);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig()], NzPaginationComponent.prototype, \"nzSize\", void 0);\n__decorate([WithConfig()], NzPaginationComponent.prototype, \"nzPageSizeOptions\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzPaginationComponent.prototype, \"nzSimple\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzResponsive\", void 0);\n__decorate([InputBoolean()], NzPaginationComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzTotal\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzPageIndex\", void 0);\n__decorate([InputNumber()], NzPaginationComponent.prototype, \"nzPageSize\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-pagination',\n      exportAs: 'nzPagination',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    @if (showPagination) {\n      @if (nzSimple) {\n        <ng-template [ngTemplateOutlet]=\"simplePagination.template\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"defaultPagination.template\" />\n      }\n    }\n\n    <nz-pagination-simple\n      #simplePagination\n      [disabled]=\"nzDisabled\"\n      [itemRender]=\"nzItemRender\"\n      [locale]=\"locale\"\n      [pageSize]=\"nzPageSize\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n    ></nz-pagination-simple>\n    <nz-pagination-default\n      #defaultPagination\n      [nzSize]=\"size\"\n      [itemRender]=\"nzItemRender\"\n      [showTotal]=\"nzShowTotal\"\n      [disabled]=\"nzDisabled\"\n      [locale]=\"locale\"\n      [showSizeChanger]=\"nzShowSizeChanger\"\n      [showQuickJumper]=\"nzShowQuickJumper\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      [pageSize]=\"nzPageSize\"\n      [pageSizeOptions]=\"nzPageSizeOptions\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n      (pageSizeChange)=\"onPageSizeChange($event)\"\n    ></nz-pagination-default>\n  `,\n      host: {\n        class: 'ant-pagination',\n        '[class.ant-pagination-simple]': 'nzSimple',\n        '[class.ant-pagination-disabled]': 'nzDisabled',\n        '[class.ant-pagination-mini]': `!nzSimple && size === 'small'`,\n        '[class.ant-pagination-rtl]': `dir === 'rtl'`\n      },\n      imports: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$3.NzI18nService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2$1.NzBreakpointService\n  }, {\n    type: i3.NzConfigService\n  }, {\n    type: i1$2.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input\n    }],\n    nzShowQuickJumper: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzResponsive: [{\n      type: Input\n    }],\n    nzHideOnSinglePage: [{\n      type: Input\n    }],\n    nzTotal: [{\n      type: Input\n    }],\n    nzPageIndex: [{\n      type: Input\n    }],\n    nzPageSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationModule {\n  static {\n    this.ɵfac = function NzPaginationModule_Factory(t) {\n      return new (t || NzPaginationModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzPaginationModule,\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n      exports: [NzPaginationComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzPaginationModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzPaginationComponent, NzPaginationSimpleComponent, NzPaginationOptionsComponent, NzPaginationItemComponent, NzPaginationDefaultComponent],\n      exports: [NzPaginationComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzPaginationComponent, NzPaginationDefaultComponent, NzPaginationItemComponent, NzPaginationModule, NzPaginationOptionsComponent, NzPaginationSimpleComponent };", "map": {"version": 3, "names": ["__decorate", "NgTemplateOutlet", "i0", "EventEmitter", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Input", "Output", "Optional", "ViewChild", "NgModule", "Subject", "ReplaySubject", "takeUntil", "i3", "WithConfig", "i2$1", "gridResponsiveMap", "NzBreakpointEnum", "toNumber", "InputBoolean", "InputNumber", "i1", "NzIconModule", "i2", "FormsModule", "i1$1", "NzSelectModule", "i1$2", "i1$3", "_c0", "_c1", "a0", "a1", "$implicit", "page", "NzPaginationItemComponent_ng_template_0_Case_0_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "page_r1", "ɵɵnextContext", "ɵɵadvance", "ɵɵtextInterpolate", "NzPaginationItemComponent_ng_template_0_Case_1_Conditional_1_Template", "ɵɵelement", "NzPaginationItemComponent_ng_template_0_Case_1_Conditional_2_Template", "NzPaginationItemComponent_ng_template_0_Case_1_Template", "ɵɵtemplate", "ctx_r1", "ɵɵproperty", "disabled", "ɵɵattribute", "locale", "prev_page", "ɵɵconditional", "direction", "NzPaginationItemComponent_ng_template_0_Case_2_Conditional_1_Template", "NzPaginationItemComponent_ng_template_0_Case_2_Conditional_2_Template", "NzPaginationItemComponent_ng_template_0_Case_2_Template", "next_page", "NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_0_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Conditional_1_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Case_2_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_0_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Conditional_1_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Case_3_Template", "NzPaginationItemComponent_ng_template_0_Case_3_Template", "tmp_5_0", "type_r3", "NzPaginationItemComponent_ng_template_0_Template", "tmp_4_0", "NzPaginationItemComponent_ng_template_2_Template", "_c2", "_forTrack0", "$index", "$item", "value", "NzPaginationOptionsComponent_Conditional_0_For_2_Template", "option_r3", "label", "NzPaginationOptionsComponent_Conditional_0_Template", "_r1", "ɵɵgetCurrentView", "ɵɵlistener", "NzPaginationOptionsComponent_Conditional_0_Template_nz_select_ngModelChange_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onPageSizeChange", "ɵɵrepeaterCreate", "nzSize", "pageSize", "ɵɵrepeater", "listOfPageSizeOption", "NzPaginationOptionsComponent_Conditional_1_Template", "_r4", "NzPaginationOptionsComponent_Conditional_1_Template_input_keydown_enter_2_listener", "jumpToPageViaInput", "ɵɵtextInterpolate1", "jump_to", "_c3", "_forTrack1", "this", "trackByPageItem", "_c4", "range", "NzPaginationDefaultComponent_ng_template_0_Conditional_1_ng_template_1_Template", "NzPaginationDefaultComponent_ng_template_0_Conditional_1_Template", "ctx_r0", "showTotal", "ɵɵpureFunction2", "total", "ranges", "NzPaginationDefaultComponent_ng_template_0_For_3_Template", "_r2", "NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_gotoIndex_0_listener", "jumpPage", "NzPaginationDefaultComponent_ng_template_0_For_3_Template_li_diffIndex_0_listener", "jumpDiff", "page_r3", "type", "index", "itemRender", "pageIndex", "dir", "NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template", "NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageIndexChange_0_listener", "onPageIndexChange", "NzPaginationDefaultComponent_ng_template_0_Conditional_4_Template_li_pageSizeChange_0_listener", "showSizeChanger", "showQuickJumper", "pageSizeOptions", "NzPaginationDefaultComponent_ng_template_0_Template", "listOfPageItem", "NzPaginationSimpleComponent_ng_template_0_Template", "NzPaginationSimpleComponent_ng_template_0_Template_li_click_1_listener", "prePage", "NzPaginationSimpleComponent_ng_template_0_Template_input_keydown_enter_3_listener", "NzPaginationSimpleComponent_ng_template_0_Template_li_click_7_listener", "nextPage", "isFirstIndex", "lastIndex", "isLastIndex", "NzPaginationComponent_Conditional_0_Conditional_0_ng_template_0_Template", "NzPaginationComponent_Conditional_0_Conditional_0_Template", "simplePagination_r2", "ɵɵreference", "template", "NzPaginationComponent_Conditional_0_Conditional_1_ng_template_0_Template", "NzPaginationComponent_Conditional_0_Conditional_1_Template", "defaultPagination_r3", "NzPaginationComponent_Conditional_0_Template", "ctx_r3", "nzSimple", "NzPaginationItemComponent", "constructor", "active", "diffIndex", "gotoIndex", "title", "clickItem", "emit", "next", "prev", "prev_5", "next_5", "ngOnChanges", "changes", "ɵfac", "NzPaginationItemComponent_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostVars", "hostBindings", "NzPaginationItemComponent_HostBindings", "NzPaginationItemComponent_click_HostBindingHandler", "ɵɵclassProp", "inputs", "outputs", "standalone", "features", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "attrs", "decls", "vars", "consts", "NzPaginationItemComponent_Template", "ɵɵtemplateRefExtractor", "renderItemTemplate_r4", "dependencies", "NzIconDirective", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "preserveWhitespaces", "None", "OnPush", "host", "imports", "NzPaginationOptionsComponent", "pageIndexChange", "pageSizeChange", "size", "target", "Math", "floor", "Set", "map", "item", "items_per_page", "NzPaginationOptionsComponent_Factory", "hostAttrs", "NzPaginationOptionsComponent_Template", "NzOptionComponent", "NzSelectComponent", "NgControlStatus", "NgModel", "class", "NzPaginationDefaultComponent", "cdr", "renderer", "elementRef", "directionality", "destroy$", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "nativeElement", "ngOnInit", "change", "pipe", "subscribe", "updateRtlStyle", "detectChanges", "addClass", "removeClass", "ngOnDestroy", "complete", "diff", "_", "getLastIndex", "ceil", "buildIndexes", "getListOfPageItem", "concatWithPrevNext", "listOfPage", "prevItem", "nextItem", "generatePage", "start", "end", "list", "i", "push", "generateRangeItem", "selected", "last", "listOfRange", "prevFiveItem", "nextFiveItem", "firstPageItem", "lastPageItem", "maxLeft", "minRight", "min", "NzPaginationDefaultComponent_Factory", "ɵɵdirectiveInject", "ChangeDetectorRef", "Renderer2", "ElementRef", "Directionality", "viewQuery", "NzPaginationDefaultComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "NzPaginationDefaultComponent_Template", "decorators", "static", "NzPaginationSimpleComponent", "updateBindingValue", "NzPaginationSimpleComponent_Factory", "NzPaginationSimpleComponent_Query", "NzPaginationSimpleComponent_Template", "NZ_CONFIG_MODULE_NAME", "NzPaginationComponent", "validatePageIndex", "nzTotal", "nzPageSize", "validIndex", "nzPageIndex", "nzDisabled", "nzPageIndexChange", "nzPageSizeChange", "onTotalChange", "Promise", "resolve", "then", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "i18n", "breakpointService", "nzConfigService", "_nzModuleName", "nzShowTotal", "nzItemRender", "nzPageSizeOptions", "nzShowSizeChanger", "nzShowQuickJumper", "nzResponsive", "nzHideOnSinglePage", "showPagination", "total$", "localeChange", "getLocaleData", "bp", "xs", "currentValue", "NzPaginationComponent_Factory", "NzI18nService", "NzBreakpointService", "NzConfigService", "NzPaginationComponent_HostBindings", "exportAs", "NzPaginationComponent_Template", "NzPaginationComponent_Template_nz_pagination_simple_pageIndexChange_1_listener", "NzPaginationComponent_Template_nz_pagination_default_pageIndexChange_3_listener", "NzPaginationComponent_Template_nz_pagination_default_pageSizeChange_3_listener", "prototype", "NzPaginationModule", "NzPaginationModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-pagination.mjs"], "sourcesContent": ["import { __decorate } from 'tslib';\nimport { NgTemplateOutlet } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { EventEmitter, Component, ViewEncapsulation, ChangeDetectionStrategy, Input, Output, Optional, ViewChild, NgModule } from '@angular/core';\nimport { Subject, ReplaySubject } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport * as i3 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2$1 from 'ng-zorro-antd/core/services';\nimport { gridResponsiveMap, NzBreakpointEnum } from 'ng-zorro-antd/core/services';\nimport { toNumber, InputBoolean, InputNumber } from 'ng-zorro-antd/core/util';\nimport * as i1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i2 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i1$1 from 'ng-zorro-antd/select';\nimport { NzSelectModule } from 'ng-zorro-antd/select';\nimport * as i1$2 from '@angular/cdk/bidi';\nimport * as i1$3 from 'ng-zorro-antd/i18n';\n\n/* eslint-disable */\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationItemComponent {\n    constructor() {\n        this.active = false;\n        this.index = null;\n        this.disabled = false;\n        this.direction = 'ltr';\n        this.type = null;\n        this.itemRender = null;\n        this.diffIndex = new EventEmitter();\n        this.gotoIndex = new EventEmitter();\n        this.title = null;\n    }\n    clickItem() {\n        if (!this.disabled) {\n            if (this.type === 'page') {\n                this.gotoIndex.emit(this.index);\n            }\n            else {\n                this.diffIndex.emit({\n                    next: 1,\n                    prev: -1,\n                    prev_5: -5,\n                    next_5: 5\n                }[this.type]);\n            }\n        }\n    }\n    ngOnChanges(changes) {\n        const { locale, index, type } = changes;\n        if (locale || index || type) {\n            this.title = {\n                page: `${this.index}`,\n                next: this.locale?.next_page,\n                prev: this.locale?.prev_page,\n                prev_5: this.locale?.prev_5,\n                next_5: this.locale?.next_5\n            }[this.type];\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationItemComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzPaginationItemComponent, isStandalone: true, selector: \"li[nz-pagination-item]\", inputs: { active: \"active\", locale: \"locale\", index: \"index\", disabled: \"disabled\", direction: \"direction\", type: \"type\", itemRender: \"itemRender\" }, outputs: { diffIndex: \"diffIndex\", gotoIndex: \"gotoIndex\" }, host: { listeners: { \"click\": \"clickItem()\" }, properties: { \"class.ant-pagination-prev\": \"type === 'prev'\", \"class.ant-pagination-next\": \"type === 'next'\", \"class.ant-pagination-item\": \"type === 'page'\", \"class.ant-pagination-jump-prev\": \"type === 'prev_5'\", \"class.ant-pagination-jump-prev-custom-icon\": \"type === 'prev_5'\", \"class.ant-pagination-jump-next\": \"type === 'next_5'\", \"class.ant-pagination-jump-next-custom-icon\": \"type === 'next_5'\", \"class.ant-pagination-disabled\": \"disabled\", \"class.ant-pagination-item-active\": \"active\", \"attr.title\": \"title\" } }, usesOnChanges: true, ngImport: i0, template: `\n    <ng-template #renderItemTemplate let-type let-page=\"page\">\n      @switch (type) {\n        @case ('page') {\n          <a>{{ page }}</a>\n        }\n        @case ('prev') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.prev_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"right\"></span>\n            } @else {\n              <span nz-icon nzType=\"left\"></span>\n            }\n          </button>\n        }\n        @case ('next') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.next_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"left\"></span>\n            } @else {\n              <span nz-icon nzType=\"right\"></span>\n            }\n          </button>\n        }\n        @default {\n          <a class=\"ant-pagination-item-link\">\n            <div class=\"ant-pagination-item-container\">\n              @switch (type) {\n                @case ('prev_5') {\n                  @if (direction === 'rtl') {\n                    <span\n                      nz-icon\n                      nzType=\"double-right\"\n                      class=\"ant-pagination-item-link-icon\"\n                    ></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n                @case ('next_5') {\n                  @if (direction === 'rtl') {\n                    <span nz-icon nzType=\"double-left\"\n                          class=\"ant-pagination-item-link-icon\"></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n              }\n              <span class=\"ant-pagination-item-ellipsis\">•••</span>\n            </div>\n          </a>\n        }\n      }\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"itemRender || renderItemTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: type, page: index }\"\n    />\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationItemComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'li[nz-pagination-item]',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <ng-template #renderItemTemplate let-type let-page=\"page\">\n      @switch (type) {\n        @case ('page') {\n          <a>{{ page }}</a>\n        }\n        @case ('prev') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.prev_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"right\"></span>\n            } @else {\n              <span nz-icon nzType=\"left\"></span>\n            }\n          </button>\n        }\n        @case ('next') {\n          <button type=\"button\" [disabled]=\"disabled\" [attr.title]=\"locale.next_page\" class=\"ant-pagination-item-link\">\n            @if (direction === 'rtl') {\n              <span nz-icon nzType=\"left\"></span>\n            } @else {\n              <span nz-icon nzType=\"right\"></span>\n            }\n          </button>\n        }\n        @default {\n          <a class=\"ant-pagination-item-link\">\n            <div class=\"ant-pagination-item-container\">\n              @switch (type) {\n                @case ('prev_5') {\n                  @if (direction === 'rtl') {\n                    <span\n                      nz-icon\n                      nzType=\"double-right\"\n                      class=\"ant-pagination-item-link-icon\"\n                    ></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-left\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n                @case ('next_5') {\n                  @if (direction === 'rtl') {\n                    <span nz-icon nzType=\"double-left\"\n                          class=\"ant-pagination-item-link-icon\"></span>\n                  } @else {\n                    <span nz-icon nzType=\"double-right\" class=\"ant-pagination-item-link-icon\"></span>\n                  }\n                }\n              }\n              <span class=\"ant-pagination-item-ellipsis\">•••</span>\n            </div>\n          </a>\n        }\n      }\n    </ng-template>\n    <ng-template\n      [ngTemplateOutlet]=\"itemRender || renderItemTemplate\"\n      [ngTemplateOutletContext]=\"{ $implicit: type, page: index }\"\n    />\n  `,\n                    host: {\n                        '[class.ant-pagination-prev]': `type === 'prev'`,\n                        '[class.ant-pagination-next]': `type === 'next'`,\n                        '[class.ant-pagination-item]': `type === 'page'`,\n                        '[class.ant-pagination-jump-prev]': `type === 'prev_5'`,\n                        '[class.ant-pagination-jump-prev-custom-icon]': `type === 'prev_5'`,\n                        '[class.ant-pagination-jump-next]': `type === 'next_5'`,\n                        '[class.ant-pagination-jump-next-custom-icon]': `type === 'next_5'`,\n                        '[class.ant-pagination-disabled]': 'disabled',\n                        '[class.ant-pagination-item-active]': 'active',\n                        '[attr.title]': 'title',\n                        '(click)': 'clickItem()'\n                    },\n                    imports: [NzIconModule, NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], propDecorators: { active: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], index: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], direction: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], itemRender: [{\n                type: Input\n            }], diffIndex: [{\n                type: Output\n            }], gotoIndex: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationOptionsComponent {\n    constructor() {\n        this.nzSize = 'default';\n        this.disabled = false;\n        this.showSizeChanger = false;\n        this.showQuickJumper = false;\n        this.total = 0;\n        this.pageIndex = 1;\n        this.pageSize = 10;\n        this.pageSizeOptions = [];\n        this.pageIndexChange = new EventEmitter();\n        this.pageSizeChange = new EventEmitter();\n        this.listOfPageSizeOption = [];\n    }\n    onPageSizeChange(size) {\n        if (this.pageSize !== size) {\n            this.pageSizeChange.next(size);\n        }\n    }\n    jumpToPageViaInput($event) {\n        const target = $event.target;\n        const index = Math.floor(toNumber(target.value, this.pageIndex));\n        this.pageIndexChange.next(index);\n        target.value = '';\n    }\n    ngOnChanges(changes) {\n        const { pageSize, pageSizeOptions, locale } = changes;\n        if (pageSize || pageSizeOptions || locale) {\n            this.listOfPageSizeOption = [...new Set([...this.pageSizeOptions, this.pageSize])].map(item => ({\n                value: item,\n                label: `${item} ${this.locale.items_per_page}`\n            }));\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationOptionsComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzPaginationOptionsComponent, isStandalone: true, selector: \"li[nz-pagination-options]\", inputs: { nzSize: \"nzSize\", disabled: \"disabled\", showSizeChanger: \"showSizeChanger\", showQuickJumper: \"showQuickJumper\", locale: \"locale\", total: \"total\", pageIndex: \"pageIndex\", pageSize: \"pageSize\", pageSizeOptions: \"pageSizeOptions\" }, outputs: { pageIndexChange: \"pageIndexChange\", pageSizeChange: \"pageSizeChange\" }, host: { classAttribute: \"ant-pagination-options\" }, usesOnChanges: true, ngImport: i0, template: `\n    @if (showSizeChanger) {\n      <nz-select\n        class=\"ant-pagination-options-size-changer\"\n        [nzDisabled]=\"disabled\"\n        [nzSize]=\"nzSize\"\n        [ngModel]=\"pageSize\"\n        (ngModelChange)=\"onPageSizeChange($event)\"\n      >\n        @for (option of listOfPageSizeOption; track option.value) {\n          <nz-option [nzLabel]=\"option.label\" [nzValue]=\"option.value\" />\n        }\n      </nz-select>\n    }\n\n    @if (showQuickJumper) {\n      <div class=\"ant-pagination-options-quick-jumper\">\n        {{ locale.jump_to }}\n        <input [disabled]=\"disabled\" (keydown.enter)=\"jumpToPageViaInput($event)\" />\n        {{ locale.page }}\n      </div>\n    }\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzSelectModule }, { kind: \"component\", type: i1$1.NzOptionComponent, selector: \"nz-option\", inputs: [\"nzTitle\", \"nzLabel\", \"nzValue\", \"nzKey\", \"nzDisabled\", \"nzHide\", \"nzCustomContent\"], exportAs: [\"nzOption\"] }, { kind: \"component\", type: i1$1.NzSelectComponent, selector: \"nz-select\", inputs: [\"nzId\", \"nzSize\", \"nzStatus\", \"nzOptionHeightPx\", \"nzOptionOverflowSize\", \"nzDropdownClassName\", \"nzDropdownMatchSelectWidth\", \"nzDropdownStyle\", \"nzNotFoundContent\", \"nzPlaceHolder\", \"nzPlacement\", \"nzMaxTagCount\", \"nzDropdownRender\", \"nzCustomTemplate\", \"nzSuffixIcon\", \"nzClearIcon\", \"nzRemoveIcon\", \"nzMenuItemSelectedIcon\", \"nzTokenSeparators\", \"nzMaxTagPlaceholder\", \"nzMaxMultipleCount\", \"nzMode\", \"nzFilterOption\", \"compareWith\", \"nzAllowClear\", \"nzBorderless\", \"nzShowSearch\", \"nzLoading\", \"nzAutoFocus\", \"nzAutoClearSearchValue\", \"nzServerSearch\", \"nzDisabled\", \"nzOpen\", \"nzSelectOnTab\", \"nzBackdrop\", \"nzOptions\", \"nzShowArrow\"], outputs: [\"nzOnSearch\", \"nzScrollToBottom\", \"nzOpenChange\", \"nzBlur\", \"nzFocus\"], exportAs: [\"nzSelect\"] }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i2.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i2.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationOptionsComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'li[nz-pagination-options]',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    @if (showSizeChanger) {\n      <nz-select\n        class=\"ant-pagination-options-size-changer\"\n        [nzDisabled]=\"disabled\"\n        [nzSize]=\"nzSize\"\n        [ngModel]=\"pageSize\"\n        (ngModelChange)=\"onPageSizeChange($event)\"\n      >\n        @for (option of listOfPageSizeOption; track option.value) {\n          <nz-option [nzLabel]=\"option.label\" [nzValue]=\"option.value\" />\n        }\n      </nz-select>\n    }\n\n    @if (showQuickJumper) {\n      <div class=\"ant-pagination-options-quick-jumper\">\n        {{ locale.jump_to }}\n        <input [disabled]=\"disabled\" (keydown.enter)=\"jumpToPageViaInput($event)\" />\n        {{ locale.page }}\n      </div>\n    }\n  `,\n                    host: { class: 'ant-pagination-options' },\n                    imports: [NzSelectModule, FormsModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { nzSize: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], showSizeChanger: [{\n                type: Input\n            }], showQuickJumper: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], total: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input\n            }], pageSize: [{\n                type: Input\n            }], pageSizeOptions: [{\n                type: Input\n            }], pageIndexChange: [{\n                type: Output\n            }], pageSizeChange: [{\n                type: Output\n            }] } });\n\nclass NzPaginationDefaultComponent {\n    constructor(cdr, renderer, elementRef, directionality) {\n        this.cdr = cdr;\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.directionality = directionality;\n        this.nzSize = 'default';\n        this.itemRender = null;\n        this.showTotal = null;\n        this.disabled = false;\n        this.showSizeChanger = false;\n        this.showQuickJumper = false;\n        this.total = 0;\n        this.pageIndex = 1;\n        this.pageSize = 10;\n        this.pageSizeOptions = [10, 20, 30, 40];\n        this.pageIndexChange = new EventEmitter();\n        this.pageSizeChange = new EventEmitter();\n        this.ranges = [0, 0];\n        this.listOfPageItem = [];\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n    }\n    ngOnInit() {\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.updateRtlStyle();\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n        this.updateRtlStyle();\n    }\n    updateRtlStyle() {\n        if (this.dir === 'rtl') {\n            this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n        }\n        else {\n            this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    jumpPage(index) {\n        this.onPageIndexChange(index);\n    }\n    jumpDiff(diff) {\n        this.jumpPage(this.pageIndex + diff);\n    }\n    trackByPageItem(_, value) {\n        return `${value.type}-${value.index}`;\n    }\n    onPageIndexChange(index) {\n        this.pageIndexChange.next(index);\n    }\n    onPageSizeChange(size) {\n        this.pageSizeChange.next(size);\n    }\n    getLastIndex(total, pageSize) {\n        return Math.ceil(total / pageSize);\n    }\n    buildIndexes() {\n        const lastIndex = this.getLastIndex(this.total, this.pageSize);\n        this.listOfPageItem = this.getListOfPageItem(this.pageIndex, lastIndex);\n    }\n    getListOfPageItem(pageIndex, lastIndex) {\n        // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n        const concatWithPrevNext = (listOfPage) => {\n            const prevItem = {\n                type: 'prev',\n                disabled: pageIndex === 1\n            };\n            const nextItem = {\n                type: 'next',\n                disabled: pageIndex === lastIndex\n            };\n            return [prevItem, ...listOfPage, nextItem];\n        };\n        const generatePage = (start, end) => {\n            const list = [];\n            for (let i = start; i <= end; i++) {\n                list.push({\n                    index: i,\n                    type: 'page'\n                });\n            }\n            return list;\n        };\n        if (lastIndex <= 9) {\n            return concatWithPrevNext(generatePage(1, lastIndex));\n        }\n        else {\n            // eslint-disable-next-line @typescript-eslint/explicit-function-return-type\n            const generateRangeItem = (selected, last) => {\n                let listOfRange = [];\n                const prevFiveItem = {\n                    type: 'prev_5'\n                };\n                const nextFiveItem = {\n                    type: 'next_5'\n                };\n                const firstPageItem = generatePage(1, 1);\n                const lastPageItem = generatePage(lastIndex, lastIndex);\n                if (selected < 5) {\n                    // If the 4th is selected, one more page will be displayed.\n                    const maxLeft = selected === 4 ? 6 : 5;\n                    listOfRange = [...generatePage(2, maxLeft), nextFiveItem];\n                }\n                else if (selected < last - 3) {\n                    listOfRange = [prevFiveItem, ...generatePage(selected - 2, selected + 2), nextFiveItem];\n                }\n                else {\n                    // If the 4th from last is selected, one more page will be displayed.\n                    const minRight = selected === last - 3 ? last - 5 : last - 4;\n                    listOfRange = [prevFiveItem, ...generatePage(minRight, last - 1)];\n                }\n                return [...firstPageItem, ...listOfRange, ...lastPageItem];\n            };\n            return concatWithPrevNext(generateRangeItem(pageIndex, lastIndex));\n        }\n    }\n    ngOnChanges(changes) {\n        const { pageIndex, pageSize, total } = changes;\n        if (pageIndex || pageSize || total) {\n            this.ranges = [(this.pageIndex - 1) * this.pageSize + 1, Math.min(this.pageIndex * this.pageSize, this.total)];\n            this.buildIndexes();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationDefaultComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i1$2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzPaginationDefaultComponent, isStandalone: true, selector: \"nz-pagination-default\", inputs: { nzSize: \"nzSize\", itemRender: \"itemRender\", showTotal: \"showTotal\", disabled: \"disabled\", locale: \"locale\", showSizeChanger: \"showSizeChanger\", showQuickJumper: \"showQuickJumper\", total: \"total\", pageIndex: \"pageIndex\", pageSize: \"pageSize\", pageSizeOptions: \"pageSizeOptions\" }, outputs: { pageIndexChange: \"pageIndexChange\", pageSizeChange: \"pageSizeChange\" }, viewQueries: [{ propertyName: \"template\", first: true, predicate: [\"containerTemplate\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: `\n    <ng-template #containerTemplate>\n      <ul>\n        @if (showTotal) {\n          <li class=\"ant-pagination-total-text\">\n            <ng-template\n              [ngTemplateOutlet]=\"showTotal\"\n              [ngTemplateOutletContext]=\"{ $implicit: total, range: ranges }\"\n            />\n          </li>\n        }\n\n        @for (page of listOfPageItem; track trackByPageItem) {\n          <li\n            nz-pagination-item\n            [locale]=\"locale\"\n            [type]=\"page.type\"\n            [index]=\"page.index\"\n            [disabled]=\"!!page.disabled\"\n            [itemRender]=\"itemRender\"\n            [active]=\"pageIndex === page.index\"\n            (gotoIndex)=\"jumpPage($event)\"\n            (diffIndex)=\"jumpDiff($event)\"\n            [direction]=\"dir\"\n          ></li>\n        }\n\n        @if (showQuickJumper || showSizeChanger) {\n          <li\n            nz-pagination-options\n            [total]=\"total\"\n            [locale]=\"locale\"\n            [disabled]=\"disabled\"\n            [nzSize]=\"nzSize\"\n            [showSizeChanger]=\"showSizeChanger\"\n            [showQuickJumper]=\"showQuickJumper\"\n            [pageIndex]=\"pageIndex\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"pageSizeOptions\"\n            (pageIndexChange)=\"onPageIndexChange($event)\"\n            (pageSizeChange)=\"onPageSizeChange($event)\"\n          ></li>\n        }\n      </ul>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzPaginationItemComponent, selector: \"li[nz-pagination-item]\", inputs: [\"active\", \"locale\", \"index\", \"disabled\", \"direction\", \"type\", \"itemRender\"], outputs: [\"diffIndex\", \"gotoIndex\"] }, { kind: \"component\", type: NzPaginationOptionsComponent, selector: \"li[nz-pagination-options]\", inputs: [\"nzSize\", \"disabled\", \"showSizeChanger\", \"showQuickJumper\", \"locale\", \"total\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], outputs: [\"pageIndexChange\", \"pageSizeChange\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationDefaultComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-pagination-default',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <ng-template #containerTemplate>\n      <ul>\n        @if (showTotal) {\n          <li class=\"ant-pagination-total-text\">\n            <ng-template\n              [ngTemplateOutlet]=\"showTotal\"\n              [ngTemplateOutletContext]=\"{ $implicit: total, range: ranges }\"\n            />\n          </li>\n        }\n\n        @for (page of listOfPageItem; track trackByPageItem) {\n          <li\n            nz-pagination-item\n            [locale]=\"locale\"\n            [type]=\"page.type\"\n            [index]=\"page.index\"\n            [disabled]=\"!!page.disabled\"\n            [itemRender]=\"itemRender\"\n            [active]=\"pageIndex === page.index\"\n            (gotoIndex)=\"jumpPage($event)\"\n            (diffIndex)=\"jumpDiff($event)\"\n            [direction]=\"dir\"\n          ></li>\n        }\n\n        @if (showQuickJumper || showSizeChanger) {\n          <li\n            nz-pagination-options\n            [total]=\"total\"\n            [locale]=\"locale\"\n            [disabled]=\"disabled\"\n            [nzSize]=\"nzSize\"\n            [showSizeChanger]=\"showSizeChanger\"\n            [showQuickJumper]=\"showQuickJumper\"\n            [pageIndex]=\"pageIndex\"\n            [pageSize]=\"pageSize\"\n            [pageSizeOptions]=\"pageSizeOptions\"\n            (pageIndexChange)=\"onPageIndexChange($event)\"\n            (pageSizeChange)=\"onPageSizeChange($event)\"\n          ></li>\n        }\n      </ul>\n    </ng-template>\n  `,\n                    imports: [NgTemplateOutlet, NzPaginationItemComponent, NzPaginationOptionsComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i1$2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { template: [{\n                type: ViewChild,\n                args: ['containerTemplate', { static: true }]\n            }], nzSize: [{\n                type: Input\n            }], itemRender: [{\n                type: Input\n            }], showTotal: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], showSizeChanger: [{\n                type: Input\n            }], showQuickJumper: [{\n                type: Input\n            }], total: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input\n            }], pageSize: [{\n                type: Input\n            }], pageSizeOptions: [{\n                type: Input\n            }], pageIndexChange: [{\n                type: Output\n            }], pageSizeChange: [{\n                type: Output\n            }] } });\n\nclass NzPaginationSimpleComponent {\n    constructor(cdr, renderer, elementRef, directionality) {\n        this.cdr = cdr;\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.directionality = directionality;\n        this.itemRender = null;\n        this.disabled = false;\n        this.total = 0;\n        this.pageIndex = 1;\n        this.pageSize = 10;\n        this.pageIndexChange = new EventEmitter();\n        this.lastIndex = 0;\n        this.isFirstIndex = false;\n        this.isLastIndex = false;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        renderer.removeChild(renderer.parentNode(elementRef.nativeElement), elementRef.nativeElement);\n    }\n    ngOnInit() {\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.updateRtlStyle();\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n        this.updateRtlStyle();\n    }\n    updateRtlStyle() {\n        if (this.dir === 'rtl') {\n            this.renderer.addClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n        }\n        else {\n            this.renderer.removeClass(this.elementRef.nativeElement, 'ant-pagination-rtl');\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    jumpToPageViaInput($event) {\n        const target = $event.target;\n        const index = toNumber(target.value, this.pageIndex);\n        this.onPageIndexChange(index);\n        target.value = `${this.pageIndex}`;\n    }\n    prePage() {\n        this.onPageIndexChange(this.pageIndex - 1);\n    }\n    nextPage() {\n        this.onPageIndexChange(this.pageIndex + 1);\n    }\n    onPageIndexChange(index) {\n        this.pageIndexChange.next(index);\n    }\n    updateBindingValue() {\n        this.lastIndex = Math.ceil(this.total / this.pageSize);\n        this.isFirstIndex = this.pageIndex === 1;\n        this.isLastIndex = this.pageIndex === this.lastIndex;\n    }\n    ngOnChanges(changes) {\n        const { pageIndex, total, pageSize } = changes;\n        if (pageIndex || total || pageSize) {\n            this.updateBindingValue();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationSimpleComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.Renderer2 }, { token: i0.ElementRef }, { token: i1$2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzPaginationSimpleComponent, isStandalone: true, selector: \"nz-pagination-simple\", inputs: { itemRender: \"itemRender\", disabled: \"disabled\", locale: \"locale\", total: \"total\", pageIndex: \"pageIndex\", pageSize: \"pageSize\" }, outputs: { pageIndexChange: \"pageIndexChange\" }, viewQueries: [{ propertyName: \"template\", first: true, predicate: [\"containerTemplate\"], descendants: true, static: true }], usesOnChanges: true, ngImport: i0, template: `\n    <ng-template #containerTemplate>\n      <ul>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale.prev_page\"\n          [disabled]=\"isFirstIndex\"\n          [direction]=\"dir\"\n          (click)=\"prePage()\"\n          type=\"prev\"\n          [itemRender]=\"itemRender\"\n        ></li>\n        <li [attr.title]=\"pageIndex + '/' + lastIndex\" class=\"ant-pagination-simple-pager\">\n          <input [disabled]=\"disabled\" [value]=\"pageIndex\" (keydown.enter)=\"jumpToPageViaInput($event)\" size=\"3\" />\n          <span class=\"ant-pagination-slash\">/</span>\n          {{ lastIndex }}\n        </li>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale?.next_page\"\n          [disabled]=\"isLastIndex\"\n          [direction]=\"dir\"\n          (click)=\"nextPage()\"\n          type=\"next\"\n          [itemRender]=\"itemRender\"\n        ></li>\n      </ul>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzPaginationItemComponent, selector: \"li[nz-pagination-item]\", inputs: [\"active\", \"locale\", \"index\", \"disabled\", \"direction\", \"type\", \"itemRender\"], outputs: [\"diffIndex\", \"gotoIndex\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationSimpleComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-pagination-simple',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <ng-template #containerTemplate>\n      <ul>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale.prev_page\"\n          [disabled]=\"isFirstIndex\"\n          [direction]=\"dir\"\n          (click)=\"prePage()\"\n          type=\"prev\"\n          [itemRender]=\"itemRender\"\n        ></li>\n        <li [attr.title]=\"pageIndex + '/' + lastIndex\" class=\"ant-pagination-simple-pager\">\n          <input [disabled]=\"disabled\" [value]=\"pageIndex\" (keydown.enter)=\"jumpToPageViaInput($event)\" size=\"3\" />\n          <span class=\"ant-pagination-slash\">/</span>\n          {{ lastIndex }}\n        </li>\n        <li\n          nz-pagination-item\n          [locale]=\"locale\"\n          [attr.title]=\"locale?.next_page\"\n          [disabled]=\"isLastIndex\"\n          [direction]=\"dir\"\n          (click)=\"nextPage()\"\n          type=\"next\"\n          [itemRender]=\"itemRender\"\n        ></li>\n      </ul>\n    </ng-template>\n  `,\n                    imports: [NzPaginationItemComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.Renderer2 }, { type: i0.ElementRef }, { type: i1$2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { template: [{\n                type: ViewChild,\n                args: ['containerTemplate', { static: true }]\n            }], itemRender: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], locale: [{\n                type: Input\n            }], total: [{\n                type: Input\n            }], pageIndex: [{\n                type: Input\n            }], pageSize: [{\n                type: Input\n            }], pageIndexChange: [{\n                type: Output\n            }] } });\n\nconst NZ_CONFIG_MODULE_NAME = 'pagination';\nclass NzPaginationComponent {\n    validatePageIndex(value, lastIndex) {\n        if (value > lastIndex) {\n            return lastIndex;\n        }\n        else if (value < 1) {\n            return 1;\n        }\n        else {\n            return value;\n        }\n    }\n    onPageIndexChange(index) {\n        const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n        const validIndex = this.validatePageIndex(index, lastIndex);\n        if (validIndex !== this.nzPageIndex && !this.nzDisabled) {\n            this.nzPageIndex = validIndex;\n            this.nzPageIndexChange.emit(this.nzPageIndex);\n        }\n    }\n    onPageSizeChange(size) {\n        this.nzPageSize = size;\n        this.nzPageSizeChange.emit(size);\n        const lastIndex = this.getLastIndex(this.nzTotal, this.nzPageSize);\n        if (this.nzPageIndex > lastIndex) {\n            this.onPageIndexChange(lastIndex);\n        }\n    }\n    onTotalChange(total) {\n        const lastIndex = this.getLastIndex(total, this.nzPageSize);\n        if (this.nzPageIndex > lastIndex) {\n            Promise.resolve().then(() => {\n                this.onPageIndexChange(lastIndex);\n                this.cdr.markForCheck();\n            });\n        }\n    }\n    getLastIndex(total, pageSize) {\n        return Math.ceil(total / pageSize);\n    }\n    constructor(i18n, cdr, breakpointService, nzConfigService, directionality) {\n        this.i18n = i18n;\n        this.cdr = cdr;\n        this.breakpointService = breakpointService;\n        this.nzConfigService = nzConfigService;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.nzPageSizeChange = new EventEmitter();\n        this.nzPageIndexChange = new EventEmitter();\n        this.nzShowTotal = null;\n        this.nzItemRender = null;\n        this.nzSize = 'default';\n        this.nzPageSizeOptions = [10, 20, 30, 40];\n        this.nzShowSizeChanger = false;\n        this.nzShowQuickJumper = false;\n        this.nzSimple = false;\n        this.nzDisabled = false;\n        this.nzResponsive = false;\n        this.nzHideOnSinglePage = false;\n        this.nzTotal = 0;\n        this.nzPageIndex = 1;\n        this.nzPageSize = 10;\n        this.showPagination = true;\n        this.size = 'default';\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n        this.total$ = new ReplaySubject(1);\n    }\n    ngOnInit() {\n        this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n            this.locale = this.i18n.getLocaleData('Pagination');\n            this.cdr.markForCheck();\n        });\n        this.total$.pipe(takeUntil(this.destroy$)).subscribe(total => {\n            this.onTotalChange(total);\n        });\n        this.breakpointService\n            .subscribe(gridResponsiveMap)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(bp => {\n            if (this.nzResponsive) {\n                this.size = bp === NzBreakpointEnum.xs ? 'small' : 'default';\n                this.cdr.markForCheck();\n            }\n        });\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        this.dir = this.directionality.value;\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    ngOnChanges(changes) {\n        const { nzHideOnSinglePage, nzTotal, nzPageSize, nzSize } = changes;\n        if (nzTotal) {\n            this.total$.next(this.nzTotal);\n        }\n        if (nzHideOnSinglePage || nzTotal || nzPageSize) {\n            this.showPagination =\n                (this.nzHideOnSinglePage && this.nzTotal > this.nzPageSize) || (this.nzTotal > 0 && !this.nzHideOnSinglePage);\n        }\n        if (nzSize) {\n            this.size = nzSize.currentValue;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationComponent, deps: [{ token: i1$3.NzI18nService }, { token: i0.ChangeDetectorRef }, { token: i2$1.NzBreakpointService }, { token: i3.NzConfigService }, { token: i1$2.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.3.8\", type: NzPaginationComponent, isStandalone: true, selector: \"nz-pagination\", inputs: { nzShowTotal: \"nzShowTotal\", nzItemRender: \"nzItemRender\", nzSize: \"nzSize\", nzPageSizeOptions: \"nzPageSizeOptions\", nzShowSizeChanger: \"nzShowSizeChanger\", nzShowQuickJumper: \"nzShowQuickJumper\", nzSimple: \"nzSimple\", nzDisabled: \"nzDisabled\", nzResponsive: \"nzResponsive\", nzHideOnSinglePage: \"nzHideOnSinglePage\", nzTotal: \"nzTotal\", nzPageIndex: \"nzPageIndex\", nzPageSize: \"nzPageSize\" }, outputs: { nzPageSizeChange: \"nzPageSizeChange\", nzPageIndexChange: \"nzPageIndexChange\" }, host: { properties: { \"class.ant-pagination-simple\": \"nzSimple\", \"class.ant-pagination-disabled\": \"nzDisabled\", \"class.ant-pagination-mini\": \"!nzSimple && size === 'small'\", \"class.ant-pagination-rtl\": \"dir === 'rtl'\" }, classAttribute: \"ant-pagination\" }, exportAs: [\"nzPagination\"], usesOnChanges: true, ngImport: i0, template: `\n    @if (showPagination) {\n      @if (nzSimple) {\n        <ng-template [ngTemplateOutlet]=\"simplePagination.template\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"defaultPagination.template\" />\n      }\n    }\n\n    <nz-pagination-simple\n      #simplePagination\n      [disabled]=\"nzDisabled\"\n      [itemRender]=\"nzItemRender\"\n      [locale]=\"locale\"\n      [pageSize]=\"nzPageSize\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n    ></nz-pagination-simple>\n    <nz-pagination-default\n      #defaultPagination\n      [nzSize]=\"size\"\n      [itemRender]=\"nzItemRender\"\n      [showTotal]=\"nzShowTotal\"\n      [disabled]=\"nzDisabled\"\n      [locale]=\"locale\"\n      [showSizeChanger]=\"nzShowSizeChanger\"\n      [showQuickJumper]=\"nzShowQuickJumper\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      [pageSize]=\"nzPageSize\"\n      [pageSizeOptions]=\"nzPageSizeOptions\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n      (pageSizeChange)=\"onPageSizeChange($event)\"\n    ></nz-pagination-default>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzPaginationSimpleComponent, selector: \"nz-pagination-simple\", inputs: [\"itemRender\", \"disabled\", \"locale\", \"total\", \"pageIndex\", \"pageSize\"], outputs: [\"pageIndexChange\"] }, { kind: \"component\", type: NzPaginationDefaultComponent, selector: \"nz-pagination-default\", inputs: [\"nzSize\", \"itemRender\", \"showTotal\", \"disabled\", \"locale\", \"showSizeChanger\", \"showQuickJumper\", \"total\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], outputs: [\"pageIndexChange\", \"pageSizeChange\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    WithConfig()\n], NzPaginationComponent.prototype, \"nzSize\", void 0);\n__decorate([\n    WithConfig()\n], NzPaginationComponent.prototype, \"nzPageSizeOptions\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzSimple\", void 0);\n__decorate([\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzResponsive\", void 0);\n__decorate([\n    InputBoolean()\n], NzPaginationComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([\n    InputNumber()\n], NzPaginationComponent.prototype, \"nzTotal\", void 0);\n__decorate([\n    InputNumber()\n], NzPaginationComponent.prototype, \"nzPageIndex\", void 0);\n__decorate([\n    InputNumber()\n], NzPaginationComponent.prototype, \"nzPageSize\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-pagination',\n                    exportAs: 'nzPagination',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    @if (showPagination) {\n      @if (nzSimple) {\n        <ng-template [ngTemplateOutlet]=\"simplePagination.template\" />\n      } @else {\n        <ng-template [ngTemplateOutlet]=\"defaultPagination.template\" />\n      }\n    }\n\n    <nz-pagination-simple\n      #simplePagination\n      [disabled]=\"nzDisabled\"\n      [itemRender]=\"nzItemRender\"\n      [locale]=\"locale\"\n      [pageSize]=\"nzPageSize\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n    ></nz-pagination-simple>\n    <nz-pagination-default\n      #defaultPagination\n      [nzSize]=\"size\"\n      [itemRender]=\"nzItemRender\"\n      [showTotal]=\"nzShowTotal\"\n      [disabled]=\"nzDisabled\"\n      [locale]=\"locale\"\n      [showSizeChanger]=\"nzShowSizeChanger\"\n      [showQuickJumper]=\"nzShowQuickJumper\"\n      [total]=\"nzTotal\"\n      [pageIndex]=\"nzPageIndex\"\n      [pageSize]=\"nzPageSize\"\n      [pageSizeOptions]=\"nzPageSizeOptions\"\n      (pageIndexChange)=\"onPageIndexChange($event)\"\n      (pageSizeChange)=\"onPageSizeChange($event)\"\n    ></nz-pagination-default>\n  `,\n                    host: {\n                        class: 'ant-pagination',\n                        '[class.ant-pagination-simple]': 'nzSimple',\n                        '[class.ant-pagination-disabled]': 'nzDisabled',\n                        '[class.ant-pagination-mini]': `!nzSimple && size === 'small'`,\n                        '[class.ant-pagination-rtl]': `dir === 'rtl'`\n                    },\n                    imports: [NgTemplateOutlet, NzPaginationSimpleComponent, NzPaginationDefaultComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1$3.NzI18nService }, { type: i0.ChangeDetectorRef }, { type: i2$1.NzBreakpointService }, { type: i3.NzConfigService }, { type: i1$2.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzPageSizeChange: [{\n                type: Output\n            }], nzPageIndexChange: [{\n                type: Output\n            }], nzShowTotal: [{\n                type: Input\n            }], nzItemRender: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzPageSizeOptions: [{\n                type: Input\n            }], nzShowSizeChanger: [{\n                type: Input\n            }], nzShowQuickJumper: [{\n                type: Input\n            }], nzSimple: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzResponsive: [{\n                type: Input\n            }], nzHideOnSinglePage: [{\n                type: Input\n            }], nzTotal: [{\n                type: Input\n            }], nzPageIndex: [{\n                type: Input\n            }], nzPageSize: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzPaginationModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationModule, imports: [NzPaginationComponent,\n            NzPaginationSimpleComponent,\n            NzPaginationOptionsComponent,\n            NzPaginationItemComponent,\n            NzPaginationDefaultComponent], exports: [NzPaginationComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationModule, imports: [NzPaginationComponent,\n            NzPaginationSimpleComponent,\n            NzPaginationOptionsComponent,\n            NzPaginationItemComponent,\n            NzPaginationDefaultComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzPaginationModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzPaginationComponent,\n                        NzPaginationSimpleComponent,\n                        NzPaginationOptionsComponent,\n                        NzPaginationItemComponent,\n                        NzPaginationDefaultComponent\n                    ],\n                    exports: [NzPaginationComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzPaginationComponent, NzPaginationDefaultComponent, NzPaginationItemComponent, NzPaginationModule, NzPaginationOptionsComponent, NzPaginationSimpleComponent };\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACjJ,SAASC,OAAO,EAAEC,aAAa,QAAQ,MAAM;AAC7C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,KAAKC,IAAI,MAAM,6BAA6B;AACnD,SAASC,iBAAiB,EAAEC,gBAAgB,QAAQ,6BAA6B;AACjF,SAASC,QAAQ,EAAEC,YAAY,EAAEC,WAAW,QAAQ,yBAAyB;AAC7E,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AACzC,OAAO,KAAKC,IAAI,MAAM,oBAAoB;;AAE1C;AACA;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAAC,SAAA,EAAAF,EAAA;EAAAG,IAAA,EAAAF;AAAA;AAAA,SAAAG,wDAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA2CoGpC,EAAE,CAAAsC,cAAA,OAK1F,CAAC;IALuFtC,EAAE,CAAAuC,MAAA,EAKhF,CAAC;IAL6EvC,EAAE,CAAAwC,YAAA,CAK5E,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAK,OAAA,GALyEzC,EAAE,CAAA0C,aAAA,GAAAR,IAAA;IAAFlC,EAAE,CAAA2C,SAAA,CAKhF,CAAC;IAL6E3C,EAAE,CAAA4C,iBAAA,CAAAH,OAKhF,CAAC;EAAA;AAAA;AAAA,SAAAI,sEAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAL6EpC,EAAE,CAAA8C,SAAA,aAUrD,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAX,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAVkDpC,EAAE,CAAA8C,SAAA,aAYtD,CAAC;EAAA;AAAA;AAAA,SAAAE,wDAAAZ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAZmDpC,EAAE,CAAAsC,cAAA,eAQgB,CAAC;IARnBtC,EAAE,CAAAiD,UAAA,IAAAJ,qEAAA,iBAShE,CAAC,IAAAE,qEAAA,MAEnB,CAAC;IAX+E/C,EAAE,CAAAwC,YAAA,CAcpF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,MAAA,GAdiFlD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAmD,UAAA,aAAAD,MAAA,CAAAE,QAQlD,CAAC;IAR+CpD,EAAE,CAAAqD,WAAA,UAAAH,MAAA,CAAAI,MAAA,CAAAC,SAAA;IAAFvD,EAAE,CAAA2C,SAAA,CAa1F,CAAC;IAbuF3C,EAAE,CAAAwD,aAAA,IAAAN,MAAA,CAAAO,SAAA,kBAa1F,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAbuFpC,EAAE,CAAA8C,SAAA,aAmBtD,CAAC;EAAA;AAAA;AAAA,SAAAa,sEAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnBmDpC,EAAE,CAAA8C,SAAA,aAqBrD,CAAC;EAAA;AAAA;AAAA,SAAAc,wDAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArBkDpC,EAAE,CAAAsC,cAAA,eAiBgB,CAAC;IAjBnBtC,EAAE,CAAAiD,UAAA,IAAAS,qEAAA,iBAkBhE,CAAC,IAAAC,qEAAA,MAEnB,CAAC;IApB+E3D,EAAE,CAAAwC,YAAA,CAuBpF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,MAAA,GAvBiFlD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAmD,UAAA,aAAAD,MAAA,CAAAE,QAiBlD,CAAC;IAjB+CpD,EAAE,CAAAqD,WAAA,UAAAH,MAAA,CAAAI,MAAA,CAAAO,SAAA;IAAF7D,EAAE,CAAA2C,SAAA,CAsB1F,CAAC;IAtBuF3C,EAAE,CAAAwD,aAAA,IAAAN,MAAA,CAAAO,SAAA,kBAsB1F,CAAC;EAAA;AAAA;AAAA,SAAAK,6EAAA1B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtBuFpC,EAAE,CAAA8C,SAAA,aAmC3E,CAAC;EAAA;AAAA;AAAA,SAAAiB,6EAAA3B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnCwEpC,EAAE,CAAA8C,SAAA,aAqCH,CAAC;EAAA;AAAA;AAAA,SAAAkB,+DAAA5B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArCApC,EAAE,CAAAiD,UAAA,IAAAa,4EAAA,iBA8B1D,CAAC,IAAAC,4EAAA,MAMnB,CAAC;EAAA;EAAA,IAAA3B,EAAA;IAAA,MAAAc,MAAA,GApCyElD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAwD,aAAA,IAAAN,MAAA,CAAAO,SAAA,kBAsCpF,CAAC;EAAA;AAAA;AAAA,SAAAQ,6EAAA7B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtCiFpC,EAAE,CAAA8C,SAAA,aA2ChC,CAAC;EAAA;AAAA;AAAA,SAAAoB,6EAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3C6BpC,EAAE,CAAA8C,SAAA,aA6CF,CAAC;EAAA;AAAA;AAAA,SAAAqB,+DAAA/B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7CDpC,EAAE,CAAAiD,UAAA,IAAAgB,4EAAA,iBAyC1D,CAAC,IAAAC,4EAAA,MAGnB,CAAC;EAAA;EAAA,IAAA9B,EAAA;IAAA,MAAAc,MAAA,GA5CyElD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAwD,aAAA,IAAAN,MAAA,CAAAO,SAAA,kBA8CpF,CAAC;EAAA;AAAA;AAAA,SAAAW,wDAAAhC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9CiFpC,EAAE,CAAAsC,cAAA,UA0BzD,CAAC,YACQ,CAAC;IA3B6CtC,EAAE,CAAAiD,UAAA,IAAAe,8DAAA,MA6BrE,CAAC,IAAAG,8DAAA,MAWD,CAAC;IAxCkEnE,EAAE,CAAAsC,cAAA,aAiD9C,CAAC;IAjD2CtC,EAAE,CAAAuC,MAAA,wBAiD3C,CAAC;IAjDwCvC,EAAE,CAAAwC,YAAA,CAiDpC,CAAC,CAClD,CAAC,CACL,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,IAAAiC,OAAA;IAAA,MAAAC,OAAA,GAnDsFtE,EAAE,CAAA0C,aAAA,GAAAT,SAAA;IAAFjC,EAAE,CAAA2C,SAAA,EAgDxF,CAAC;IAhDqF3C,EAAE,CAAAwD,aAAA,KAAAa,OAAA,GAAAC,OAAA,MA4BxF,QAAQ,OAAAD,OAAA,KAAR,QAAQ,SAoBR,CAAC;EAAA;AAAA;AAAA,SAAAE,iDAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhDqFpC,EAAE,CAAAiD,UAAA,IAAAd,uDAAA,MAI/E,CAAC,IAAAa,uDAAA,MAGD,CAAC,IAAAY,uDAAA,MASD,CAAC,IAAAQ,uDAAA,MASP,CAAC;EAAA;EAAA,IAAAhC,EAAA;IAAA,IAAAoC,OAAA;IAAA,MAAAF,OAAA,GAAAjC,GAAA,CAAAJ,SAAA;IAzBkFjC,EAAE,CAAAwD,aAAA,KAAAgB,OAAA,GAAAF,OAAA,MAGhG,MAAM,OAAAE,OAAA,KAAN,MAAM,OAAAA,OAAA,KAAN,MAAM,QAkDN,CAAC;EAAA;AAAA;AAAA,SAAAC,iDAAArC,EAAA,EAAAC,GAAA;AAAA,MAAAqC,GAAA;AAAA,MAAAC,UAAA,GAAAA,CAAAC,MAAA,EAAAC,KAAA,KAAAA,KAAA,CAAAC,KAAA;AAAA,SAAAC,0DAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArD6FpC,EAAE,CAAA8C,SAAA,kBAoN9B,CAAC;EAAA;EAAA,IAAAV,EAAA;IAAA,MAAA4C,SAAA,GAAA3C,GAAA,CAAAJ,SAAA;IApN2BjC,EAAE,CAAAmD,UAAA,YAAA6B,SAAA,CAAAC,KAoN1D,CAAC,YAAAD,SAAA,CAAAF,KAAwB,CAAC;EAAA;AAAA;AAAA,SAAAI,oDAAA9C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+C,GAAA,GApN8BnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAsC,cAAA,kBAkNhG,CAAC;IAlN6FtC,EAAE,CAAAqF,UAAA,2BAAAC,uFAAAC,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAjC,MAAA,GAAFlD,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAiN7EvC,MAAA,CAAAwC,gBAAA,CAAAH,MAAuB,CAAC;IAAA,EAAC;IAjNkDvF,EAAE,CAAA2F,gBAAA,IAAAZ,yDAAA,wBAAAJ,UAqN9F,CAAC;IArN2F3E,EAAE,CAAAwC,YAAA,CAsNrF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,MAAA,GAtNkFlD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAmD,UAAA,eAAAD,MAAA,CAAAE,QA8MxE,CAAC,WAAAF,MAAA,CAAA0C,MACP,CAAC,YAAA1C,MAAA,CAAA2C,QACE,CAAC;IAhNwE7F,EAAE,CAAA2C,SAAA,CAqN9F,CAAC;IArN2F3C,EAAE,CAAA8F,UAAA,CAAA5C,MAAA,CAAA6C,oBAqN9F,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAA5D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6D,GAAA,GArN2FjG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAsC,cAAA,YA0NhD,CAAC;IA1N6CtC,EAAE,CAAAuC,MAAA,EA4N/F,CAAC;IA5N4FvC,EAAE,CAAAsC,cAAA,cA4NnB,CAAC;IA5NgBtC,EAAE,CAAAqF,UAAA,2BAAAa,mFAAAX,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAS,GAAA;MAAA,MAAA/C,MAAA,GAAFlD,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CA4NhDvC,MAAA,CAAAiD,kBAAA,CAAAZ,MAAyB,CAAC;IAAA,EAAC;IA5NmBvF,EAAE,CAAAwC,YAAA,CA4NnB,CAAC;IA5NgBxC,EAAE,CAAAuC,MAAA,EA8NjG,CAAC;IA9N8FvC,EAAE,CAAAwC,YAAA,CA8N3F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,MAAA,GA9NwFlD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAA2C,SAAA,CA4N/F,CAAC;IA5N4F3C,EAAE,CAAAoG,kBAAA,MAAAlD,MAAA,CAAAI,MAAA,CAAA+C,OAAA,KA4N/F,CAAC;IA5N4FrG,EAAE,CAAA2C,SAAA,CA4NnE,CAAC;IA5NgE3C,EAAE,CAAAmD,UAAA,aAAAD,MAAA,CAAAE,QA4NnE,CAAC;IA5NgEpD,EAAE,CAAA2C,SAAA,CA8NjG,CAAC;IA9N8F3C,EAAE,CAAAoG,kBAAA,MAAAlD,MAAA,CAAAI,MAAA,CAAApB,IAAA,KA8NjG,CAAC;EAAA;AAAA;AAAA,MAAAoE,GAAA;AAAA,SAAAC,WAAA3B,MAAA,EAAAC,KAAA;EAAA,OAAA2B,IAAA,CAAAC,eAAA;AAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA3E,EAAA,EAAAC,EAAA;EAAAC,SAAA,EAAAF,EAAA;EAAA4E,KAAA,EAAA3E;AAAA;AAAA,SAAA4E,gFAAAxE,EAAA,EAAAC,GAAA;AAAA,SAAAwE,kEAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9N8FpC,EAAE,CAAAsC,cAAA,WAmavD,CAAC;IAnaoDtC,EAAE,CAAAiD,UAAA,IAAA2D,+EAAA,wBAuazF,CAAC;IAvasF5G,EAAE,CAAAwC,YAAA,CAwaxF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA0E,MAAA,GAxaqF9G,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAA2C,SAAA,CAqa3D,CAAC;IArawD3C,EAAE,CAAAmD,UAAA,qBAAA2D,MAAA,CAAAC,SAqa3D,CAAC,4BArawD/G,EAAE,CAAAgH,eAAA,IAAAN,GAAA,EAAAI,MAAA,CAAAG,KAAA,EAAAH,MAAA,CAAAI,MAAA,CAsa1B,CAAC;EAAA;AAAA;AAAA,SAAAC,0DAAA/E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAgF,GAAA,GAtauBpH,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAsC,cAAA,WAub5F,CAAC;IAvbyFtC,EAAE,CAAAqF,UAAA,uBAAAgC,kFAAA9B,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAA4B,GAAA;MAAA,MAAAN,MAAA,GAAF9G,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAob7EqB,MAAA,CAAAQ,QAAA,CAAA/B,MAAe,CAAC;IAAA,EAAC,uBAAAgC,kFAAAhC,MAAA;MApb0DvF,EAAE,CAAAwF,aAAA,CAAA4B,GAAA;MAAA,MAAAN,MAAA,GAAF9G,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAqb7EqB,MAAA,CAAAU,QAAA,CAAAjC,MAAe,CAAC;IAAA,EAAC;IArb0DvF,EAAE,CAAAwC,YAAA,CAubvF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAqF,OAAA,GAAApF,GAAA,CAAAJ,SAAA;IAAA,MAAA6E,MAAA,GAvboF9G,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAmD,UAAA,WAAA2D,MAAA,CAAAxD,MA8a1E,CAAC,SAAAmE,OAAA,CAAAC,IACA,CAAC,UAAAD,OAAA,CAAAE,KACC,CAAC,eAAAF,OAAA,CAAArE,QACO,CAAC,eAAA0D,MAAA,CAAAc,UACJ,CAAC,WAAAd,MAAA,CAAAe,SAAA,KAAAJ,OAAA,CAAAE,KACS,CAAC,cAAAb,MAAA,CAAAgB,GAGnB,CAAC;EAAA;AAAA;AAAA,SAAAC,kEAAA3F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA6D,GAAA,GAtbuEjG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAsC,cAAA,WAwc5F,CAAC;IAxcyFtC,EAAE,CAAAqF,UAAA,6BAAA2C,gGAAAzC,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAS,GAAA;MAAA,MAAAa,MAAA,GAAF9G,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAscvEqB,MAAA,CAAAmB,iBAAA,CAAA1C,MAAwB,CAAC;IAAA,EAAC,4BAAA2C,+FAAA3C,MAAA;MAtc2CvF,EAAE,CAAAwF,aAAA,CAAAS,GAAA;MAAA,MAAAa,MAAA,GAAF9G,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAucxEqB,MAAA,CAAApB,gBAAA,CAAAH,MAAuB,CAAC;IAAA,EAAC;IAvc6CvF,EAAE,CAAAwC,YAAA,CAwcvF,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA0E,MAAA,GAxcoF9G,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAmD,UAAA,UAAA2D,MAAA,CAAAG,KA6b5E,CAAC,WAAAH,MAAA,CAAAxD,MACC,CAAC,aAAAwD,MAAA,CAAA1D,QACG,CAAC,WAAA0D,MAAA,CAAAlB,MACL,CAAC,oBAAAkB,MAAA,CAAAqB,eACiB,CAAC,oBAAArB,MAAA,CAAAsB,eACD,CAAC,cAAAtB,MAAA,CAAAe,SACb,CAAC,aAAAf,MAAA,CAAAjB,QACH,CAAC,oBAAAiB,MAAA,CAAAuB,eACa,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAAlG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IArcqDpC,EAAE,CAAAsC,cAAA,QAia7F,CAAC;IAja0FtC,EAAE,CAAAiD,UAAA,IAAA4D,iEAAA,eAka9E,CAAC;IAla2E7G,EAAE,CAAA2F,gBAAA,IAAAwB,yDAAA,iBAAAZ,UAAA,MAwb9F,CAAC;IAxb2FvG,EAAE,CAAAiD,UAAA,IAAA8E,iEAAA,eA0brD,CAAC;IA1bkD/H,EAAE,CAAAwC,YAAA,CA0c5F,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAA0E,MAAA,GA1cyF9G,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAA2C,SAAA,CAya9F,CAAC;IAza2F3C,EAAE,CAAAwD,aAAA,IAAAsD,MAAA,CAAAC,SAAA,SAya9F,CAAC;IAza2F/G,EAAE,CAAA2C,SAAA,CAwb9F,CAAC;IAxb2F3C,EAAE,CAAA8F,UAAA,CAAAgB,MAAA,CAAAyB,cAwb9F,CAAC;IAxb2FvI,EAAE,CAAA2C,SAAA,EAyc9F,CAAC;IAzc2F3C,EAAE,CAAAwD,aAAA,IAAAsD,MAAA,CAAAsB,eAAA,IAAAtB,MAAA,CAAAqB,eAAA,SAyc9F,CAAC;EAAA;AAAA;AAAA,SAAAK,mDAAApG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+C,GAAA,GAzc2FnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAsC,cAAA,QA4mB7F,CAAC,WAUF,CAAC;IAtnB2FtC,EAAE,CAAAqF,UAAA,mBAAAoD,uEAAA;MAAFzI,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAjC,MAAA,GAAFlD,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAmnBnFvC,MAAA,CAAAwF,OAAA,CAAQ,CAAC;IAAA,EAAC;IAnnBuE1I,EAAE,CAAAwC,YAAA,CAsnBzF,CAAC;IAtnBsFxC,EAAE,CAAAsC,cAAA,WAunBZ,CAAC,cACuB,CAAC;IAxnBftC,EAAE,CAAAqF,UAAA,2BAAAsD,kFAAApD,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAjC,MAAA,GAAFlD,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAwnB1BvC,MAAA,CAAAiD,kBAAA,CAAAZ,MAAyB,CAAC;IAAA,EAAC;IAxnBHvF,EAAE,CAAAwC,YAAA,CAwnBY,CAAC;IAxnBfxC,EAAE,CAAAsC,cAAA,aAynB1D,CAAC;IAznBuDtC,EAAE,CAAAuC,MAAA,OAynBzD,CAAC;IAznBsDvC,EAAE,CAAAwC,YAAA,CAynBlD,CAAC;IAznB+CxC,EAAE,CAAAuC,MAAA,EA2nB/F,CAAC;IA3nB4FvC,EAAE,CAAAwC,YAAA,CA2nB1F,CAAC;IA3nBuFxC,EAAE,CAAAsC,cAAA,WAqoB9F,CAAC;IAroB2FtC,EAAE,CAAAqF,UAAA,mBAAAuD,uEAAA;MAAF5I,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAjC,MAAA,GAAFlD,EAAE,CAAA0C,aAAA;MAAA,OAAF1C,EAAE,CAAAyF,WAAA,CAkoBnFvC,MAAA,CAAA2F,QAAA,CAAS,CAAC;IAAA,EAAC;IAloBsE7I,EAAE,CAAAwC,YAAA,CAqoBzF,CAAC,CACJ,CAAC;EAAA;EAAA,IAAAJ,EAAA;IAAA,MAAAc,MAAA,GAtoByFlD,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAA2C,SAAA,CA+mB5E,CAAC;IA/mByE3C,EAAE,CAAAmD,UAAA,WAAAD,MAAA,CAAAI,MA+mB5E,CAAC,aAAAJ,MAAA,CAAA4F,YAEO,CAAC,cAAA5F,MAAA,CAAA4E,GACT,CAAC,eAAA5E,MAAA,CAAA0E,UAGO,CAAC;IArnBiE5H,EAAE,CAAAqD,WAAA,UAAAH,MAAA,CAAAI,MAAA,CAAAC,SAAA;IAAFvD,EAAE,CAAA2C,SAAA,CAunBjD,CAAC;IAvnB8C3C,EAAE,CAAAqD,WAAA,UAAAH,MAAA,CAAA2E,SAAA,SAAA3E,MAAA,CAAA6F,SAAA;IAAF/I,EAAE,CAAA2C,SAAA,CAwnBjE,CAAC;IAxnB8D3C,EAAE,CAAAmD,UAAA,aAAAD,MAAA,CAAAE,QAwnBjE,CAAC,UAAAF,MAAA,CAAA2E,SAAmB,CAAC;IAxnB0C7H,EAAE,CAAA2C,SAAA,EA2nB/F,CAAC;IA3nB4F3C,EAAE,CAAAoG,kBAAA,MAAAlD,MAAA,CAAA6F,SAAA,KA2nB/F,CAAC;IA3nB4F/I,EAAE,CAAA2C,SAAA,CA8nB5E,CAAC;IA9nByE3C,EAAE,CAAAmD,UAAA,WAAAD,MAAA,CAAAI,MA8nB5E,CAAC,aAAAJ,MAAA,CAAA8F,WAEM,CAAC,cAAA9F,MAAA,CAAA4E,GACR,CAAC,eAAA5E,MAAA,CAAA0E,UAGO,CAAC;IApoBiE5H,EAAE,CAAAqD,WAAA,UAAAH,MAAA,CAAAI,MAAA,kBAAAJ,MAAA,CAAAI,MAAA,CAAAO,SAAA;EAAA;AAAA;AAAA,SAAAoF,yEAAA7G,EAAA,EAAAC,GAAA;AAAA,SAAA6G,2DAAA9G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAFpC,EAAE,CAAAiD,UAAA,IAAAgG,wEAAA,wBAyzBjC,CAAC;EAAA;EAAA,IAAA7G,EAAA;IAzzB8BpC,EAAE,CAAA0C,aAAA;IAAA,MAAAyG,mBAAA,GAAFnJ,EAAE,CAAAoJ,WAAA;IAAFpJ,EAAE,CAAAmD,UAAA,qBAAAgG,mBAAA,CAAAE,QAyzBpC,CAAC;EAAA;AAAA;AAAA,SAAAC,yEAAAlH,EAAA,EAAAC,GAAA;AAAA,SAAAkH,2DAAAnH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAzzBiCpC,EAAE,CAAAiD,UAAA,IAAAqG,wEAAA,wBA2zBhC,CAAC;EAAA;EAAA,IAAAlH,EAAA;IA3zB6BpC,EAAE,CAAA0C,aAAA;IAAA,MAAA8G,oBAAA,GAAFxJ,EAAE,CAAAoJ,WAAA;IAAFpJ,EAAE,CAAAmD,UAAA,qBAAAqG,oBAAA,CAAAH,QA2zBnC,CAAC;EAAA;AAAA;AAAA,SAAAI,6CAAArH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3zBgCpC,EAAE,CAAAiD,UAAA,IAAAiG,0DAAA,eAwzBjF,CAAC,IAAAK,0DAAA,MAER,CAAC;EAAA;EAAA,IAAAnH,EAAA;IAAA,MAAAsH,MAAA,GA1zBqF1J,EAAE,CAAA0C,aAAA;IAAF1C,EAAE,CAAAwD,aAAA,IAAAkG,MAAA,CAAAC,QAAA,QA4zBhG,CAAC;EAAA;AAAA;AAn2BP,MAAMC,yBAAyB,CAAC;EAC5BC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACnC,KAAK,GAAG,IAAI;IACjB,IAAI,CAACvE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACK,SAAS,GAAG,KAAK;IACtB,IAAI,CAACiE,IAAI,GAAG,IAAI;IAChB,IAAI,CAACE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACmC,SAAS,GAAG,IAAI9J,YAAY,CAAC,CAAC;IACnC,IAAI,CAAC+J,SAAS,GAAG,IAAI/J,YAAY,CAAC,CAAC;IACnC,IAAI,CAACgK,KAAK,GAAG,IAAI;EACrB;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAAC,IAAI,CAAC9G,QAAQ,EAAE;MAChB,IAAI,IAAI,CAACsE,IAAI,KAAK,MAAM,EAAE;QACtB,IAAI,CAACsC,SAAS,CAACG,IAAI,CAAC,IAAI,CAACxC,KAAK,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAACoC,SAAS,CAACI,IAAI,CAAC;UAChBC,IAAI,EAAE,CAAC;UACPC,IAAI,EAAE,CAAC,CAAC;UACRC,MAAM,EAAE,CAAC,CAAC;UACVC,MAAM,EAAE;QACZ,CAAC,CAAC,IAAI,CAAC7C,IAAI,CAAC,CAAC;MACjB;IACJ;EACJ;EACA8C,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEnH,MAAM;MAAEqE,KAAK;MAAED;IAAK,CAAC,GAAG+C,OAAO;IACvC,IAAInH,MAAM,IAAIqE,KAAK,IAAID,IAAI,EAAE;MACzB,IAAI,CAACuC,KAAK,GAAG;QACT/H,IAAI,EAAG,GAAE,IAAI,CAACyF,KAAM,EAAC;QACrByC,IAAI,EAAE,IAAI,CAAC9G,MAAM,EAAEO,SAAS;QAC5BwG,IAAI,EAAE,IAAI,CAAC/G,MAAM,EAAEC,SAAS;QAC5B+G,MAAM,EAAE,IAAI,CAAChH,MAAM,EAAEgH,MAAM;QAC3BC,MAAM,EAAE,IAAI,CAACjH,MAAM,EAAEiH;MACzB,CAAC,CAAC,IAAI,CAAC7C,IAAI,CAAC;IAChB;EACJ;EACA;IAAS,IAAI,CAACgD,IAAI,YAAAC,kCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFhB,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACiB,IAAI,kBAD8E7K,EAAE,CAAA8K,iBAAA;MAAApD,IAAA,EACJkC,yBAAyB;MAAAmB,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,uCAAA9I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADvBpC,EAAE,CAAAqF,UAAA,mBAAA8F,mDAAA;YAAA,OACJ9I,GAAA,CAAA6H,SAAA,CAAU,CAAC;UAAA,CAAa,CAAC;QAAA;QAAA,IAAA9H,EAAA;UADvBpC,EAAE,CAAAqD,WAAA,UAAAhB,GAAA,CAAA4H,KAAA;UAAFjK,EAAE,CAAAoL,WAAA,wBAAA/I,GAAA,CAAAqF,IAAA,KACK,MAAe,CAAC,wBAAArF,GAAA,CAAAqF,IAAA,KAAhB,MAAe,CAAC,wBAAArF,GAAA,CAAAqF,IAAA,KAAhB,MAAe,CAAC,6BAAArF,GAAA,CAAAqF,IAAA,KAAhB,QAAe,CAAC,yCAAArF,GAAA,CAAAqF,IAAA,KAAhB,QAAe,CAAC,6BAAArF,GAAA,CAAAqF,IAAA,KAAhB,QAAe,CAAC,yCAAArF,GAAA,CAAAqF,IAAA,KAAhB,QAAe,CAAC,4BAAArF,GAAA,CAAAe,QAAD,CAAC,+BAAAf,GAAA,CAAAyH,MAAD,CAAC;QAAA;MAAA;MAAAuB,MAAA;QAAAvB,MAAA;QAAAxG,MAAA;QAAAqE,KAAA;QAAAvE,QAAA;QAAAK,SAAA;QAAAiE,IAAA;QAAAE,UAAA;MAAA;MAAA0D,OAAA;QAAAvB,SAAA;QAAAC,SAAA;MAAA;MAAAuB,UAAA;MAAAC,QAAA,GADvBxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAC,KAAA,EAAA9J,GAAA;MAAA+J,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAzC,QAAA,WAAA0C,mCAAA3J,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpC,EAAE,CAAAiD,UAAA,IAAAsB,gDAAA,gCAAFvE,EAAE,CAAAgM,sBAEzC,CAAC,IAAAvH,gDAAA,wBAwDzD,CAAC;QAAA;QAAA,IAAArC,EAAA;UAAA,MAAA6J,qBAAA,GA1D8FjM,EAAE,CAAAoJ,WAAA;UAAFpJ,EAAE,CAAA2C,SAAA,EAwD5C,CAAC;UAxDyC3C,EAAE,CAAAmD,UAAA,qBAAAd,GAAA,CAAAuF,UAAA,IAAAqE,qBAwD5C,CAAC,4BAxDyCjM,EAAE,CAAAgH,eAAA,IAAAlF,GAAA,EAAAO,GAAA,CAAAqF,IAAA,EAAArF,GAAA,CAAAsF,KAAA,CAyDrC,CAAC;QAAA;MAAA;MAAAuE,YAAA,GAEJ5K,YAAY,EAA+BD,EAAE,CAAC8K,eAAe,EAAiKpM,gBAAgB;MAAAqM,aAAA;MAAAC,eAAA;IAAA,EAAyN;EAAE;AACvgB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA7DoGtM,EAAE,CAAAuM,iBAAA,CA6DX3C,yBAAyB,EAAc,CAAC;IACvHlC,IAAI,EAAExH,SAAS;IACfsM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCC,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEjM,iBAAiB,CAACwM,IAAI;MACrCN,eAAe,EAAEjM,uBAAuB,CAACwM,MAAM;MAC/CvD,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,6BAA6B,EAAG,iBAAgB;QAChD,6BAA6B,EAAG,iBAAgB;QAChD,6BAA6B,EAAG,iBAAgB;QAChD,kCAAkC,EAAG,mBAAkB;QACvD,8CAA8C,EAAG,mBAAkB;QACnE,kCAAkC,EAAG,mBAAkB;QACvD,8CAA8C,EAAG,mBAAkB;QACnE,iCAAiC,EAAE,UAAU;QAC7C,oCAAoC,EAAE,QAAQ;QAC9C,cAAc,EAAE,OAAO;QACvB,SAAS,EAAE;MACf,CAAC;MACDC,OAAO,EAAE,CAACxL,YAAY,EAAEvB,gBAAgB,CAAC;MACzCwL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEzB,MAAM,EAAE,CAAC;MACvBpC,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEiD,MAAM,EAAE,CAAC;MACToE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEsH,KAAK,EAAE,CAAC;MACRD,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXsE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEoD,SAAS,EAAE,CAAC;MACZiE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEqH,IAAI,EAAE,CAAC;MACPA,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuH,UAAU,EAAE,CAAC;MACbF,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE0J,SAAS,EAAE,CAAC;MACZrC,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE0J,SAAS,EAAE,CAAC;MACZtC,IAAI,EAAEpH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyM,4BAA4B,CAAC;EAC/BlD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjE,MAAM,GAAG,SAAS;IACvB,IAAI,CAACxC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC+E,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACnB,KAAK,GAAG,CAAC;IACd,IAAI,CAACY,SAAS,GAAG,CAAC;IAClB,IAAI,CAAChC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACwC,eAAe,GAAG,EAAE;IACzB,IAAI,CAAC2E,eAAe,GAAG,IAAI/M,YAAY,CAAC,CAAC;IACzC,IAAI,CAACgN,cAAc,GAAG,IAAIhN,YAAY,CAAC,CAAC;IACxC,IAAI,CAAC8F,oBAAoB,GAAG,EAAE;EAClC;EACAL,gBAAgBA,CAACwH,IAAI,EAAE;IACnB,IAAI,IAAI,CAACrH,QAAQ,KAAKqH,IAAI,EAAE;MACxB,IAAI,CAACD,cAAc,CAAC7C,IAAI,CAAC8C,IAAI,CAAC;IAClC;EACJ;EACA/G,kBAAkBA,CAACZ,MAAM,EAAE;IACvB,MAAM4H,MAAM,GAAG5H,MAAM,CAAC4H,MAAM;IAC5B,MAAMxF,KAAK,GAAGyF,IAAI,CAACC,KAAK,CAACnM,QAAQ,CAACiM,MAAM,CAACrI,KAAK,EAAE,IAAI,CAAC+C,SAAS,CAAC,CAAC;IAChE,IAAI,CAACmF,eAAe,CAAC5C,IAAI,CAACzC,KAAK,CAAC;IAChCwF,MAAM,CAACrI,KAAK,GAAG,EAAE;EACrB;EACA0F,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE5E,QAAQ;MAAEwC,eAAe;MAAE/E;IAAO,CAAC,GAAGmH,OAAO;IACrD,IAAI5E,QAAQ,IAAIwC,eAAe,IAAI/E,MAAM,EAAE;MACvC,IAAI,CAACyC,oBAAoB,GAAG,CAAC,GAAG,IAAIuH,GAAG,CAAC,CAAC,GAAG,IAAI,CAACjF,eAAe,EAAE,IAAI,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAAC0H,GAAG,CAACC,IAAI,KAAK;QAC5F1I,KAAK,EAAE0I,IAAI;QACXvI,KAAK,EAAG,GAAEuI,IAAK,IAAG,IAAI,CAAClK,MAAM,CAACmK,cAAe;MACjD,CAAC,CAAC,CAAC;IACP;EACJ;EACA;IAAS,IAAI,CAAC/C,IAAI,YAAAgD,qCAAA9C,CAAA;MAAA,YAAAA,CAAA,IAAwFmC,4BAA4B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAAClC,IAAI,kBA1M8E7K,EAAE,CAAA8K,iBAAA;MAAApD,IAAA,EA0MJqF,4BAA4B;MAAAhC,SAAA;MAAA4C,SAAA;MAAAtC,MAAA;QAAAzF,MAAA;QAAAxC,QAAA;QAAA+E,eAAA;QAAAC,eAAA;QAAA9E,MAAA;QAAA2D,KAAA;QAAAY,SAAA;QAAAhC,QAAA;QAAAwC,eAAA;MAAA;MAAAiD,OAAA;QAAA0B,eAAA;QAAAC,cAAA;MAAA;MAAA1B,UAAA;MAAAC,QAAA,GA1M1BxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAC,KAAA,EAAAjH,GAAA;MAAAkH,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAzC,QAAA,WAAAuE,sCAAAxL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpC,EAAE,CAAAiD,UAAA,IAAAiC,mDAAA,sBA2M5E,CAAC,IAAAc,mDAAA,gBAcD,CAAC;QAAA;QAAA,IAAA5D,EAAA;UAzNyEpC,EAAE,CAAAwD,aAAA,IAAAnB,GAAA,CAAA8F,eAAA,SAuNlG,CAAC;UAvN+FnI,EAAE,CAAA2C,SAAA,CA+NlG,CAAC;UA/N+F3C,EAAE,CAAAwD,aAAA,IAAAnB,GAAA,CAAA+F,eAAA,SA+NlG,CAAC;QAAA;MAAA;MAAA8D,YAAA,GACyDxK,cAAc,EAA+BD,IAAI,CAACoM,iBAAiB,EAA6KpM,IAAI,CAACqM,iBAAiB,EAA0yBtM,WAAW,EAA+BD,EAAE,CAACwM,eAAe,EAAsFxM,EAAE,CAACyM,OAAO;MAAA5B,aAAA;MAAAC,eAAA;IAAA,EAAmR;EAAE;AAC/hD;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlOoGtM,EAAE,CAAAuM,iBAAA,CAkOXQ,4BAA4B,EAAc,CAAC;IAC1HrF,IAAI,EAAExH,SAAS;IACfsM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2BAA2B;MACrCC,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEjM,iBAAiB,CAACwM,IAAI;MACrCN,eAAe,EAAEjM,uBAAuB,CAACwM,MAAM;MAC/CvD,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEoB,KAAK,EAAE;MAAyB,CAAC;MACzCnB,OAAO,EAAE,CAACpL,cAAc,EAAEF,WAAW,CAAC;MACtC+J,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE3F,MAAM,EAAE,CAAC;MACjD8B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXsE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE8H,eAAe,EAAE,CAAC;MAClBT,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+H,eAAe,EAAE,CAAC;MAClBV,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEiD,MAAM,EAAE,CAAC;MACToE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE4G,KAAK,EAAE,CAAC;MACRS,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwH,SAAS,EAAE,CAAC;MACZH,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACX6B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEgI,eAAe,EAAE,CAAC;MAClBX,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE2M,eAAe,EAAE,CAAC;MAClBtF,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE2M,cAAc,EAAE,CAAC;MACjBvF,IAAI,EAAEpH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM4N,4BAA4B,CAAC;EAC/BrE,WAAWA,CAACsE,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAE;IACnD,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC1I,MAAM,GAAG,SAAS;IACvB,IAAI,CAACgC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACb,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC3D,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC+E,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACnB,KAAK,GAAG,CAAC;IACd,IAAI,CAACY,SAAS,GAAG,CAAC;IAClB,IAAI,CAAChC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACwC,eAAe,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACvC,IAAI,CAAC2E,eAAe,GAAG,IAAI/M,YAAY,CAAC,CAAC;IACzC,IAAI,CAACgN,cAAc,GAAG,IAAIhN,YAAY,CAAC,CAAC;IACxC,IAAI,CAACiH,MAAM,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB,IAAI,CAACqB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACT,GAAG,GAAG,KAAK;IAChB,IAAI,CAACyG,QAAQ,GAAG,IAAI7N,OAAO,CAAC,CAAC;IAC7B0N,QAAQ,CAACI,WAAW,CAACJ,QAAQ,CAACK,UAAU,CAACJ,UAAU,CAACK,aAAa,CAAC,EAAEL,UAAU,CAACK,aAAa,CAAC;EACjG;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,cAAc,CAACM,MAAM,EAAEC,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAErL,SAAS,IAAK;MAChF,IAAI,CAACqE,GAAG,GAAGrE,SAAS;MACpB,IAAI,CAACsL,cAAc,CAAC,CAAC;MACrB,IAAI,CAACZ,GAAG,CAACa,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAClH,GAAG,GAAG,IAAI,CAACwG,cAAc,CAACxJ,KAAK;IACpC,IAAI,CAACiK,cAAc,CAAC,CAAC;EACzB;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjH,GAAG,KAAK,KAAK,EAAE;MACpB,IAAI,CAACsG,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACZ,UAAU,CAACK,aAAa,EAAE,oBAAoB,CAAC;IAC/E,CAAC,MACI;MACD,IAAI,CAACN,QAAQ,CAACc,WAAW,CAAC,IAAI,CAACb,UAAU,CAACK,aAAa,EAAE,oBAAoB,CAAC;IAClF;EACJ;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,QAAQ,CAACnE,IAAI,CAAC,CAAC;IACpB,IAAI,CAACmE,QAAQ,CAACa,QAAQ,CAAC,CAAC;EAC5B;EACA9H,QAAQA,CAACK,KAAK,EAAE;IACZ,IAAI,CAACM,iBAAiB,CAACN,KAAK,CAAC;EACjC;EACAH,QAAQA,CAAC6H,IAAI,EAAE;IACX,IAAI,CAAC/H,QAAQ,CAAC,IAAI,CAACO,SAAS,GAAGwH,IAAI,CAAC;EACxC;EACA5I,eAAeA,CAAC6I,CAAC,EAAExK,KAAK,EAAE;IACtB,OAAQ,GAAEA,KAAK,CAAC4C,IAAK,IAAG5C,KAAK,CAAC6C,KAAM,EAAC;EACzC;EACAM,iBAAiBA,CAACN,KAAK,EAAE;IACrB,IAAI,CAACqF,eAAe,CAAC5C,IAAI,CAACzC,KAAK,CAAC;EACpC;EACAjC,gBAAgBA,CAACwH,IAAI,EAAE;IACnB,IAAI,CAACD,cAAc,CAAC7C,IAAI,CAAC8C,IAAI,CAAC;EAClC;EACAqC,YAAYA,CAACtI,KAAK,EAAEpB,QAAQ,EAAE;IAC1B,OAAOuH,IAAI,CAACoC,IAAI,CAACvI,KAAK,GAAGpB,QAAQ,CAAC;EACtC;EACA4J,YAAYA,CAAA,EAAG;IACX,MAAM1G,SAAS,GAAG,IAAI,CAACwG,YAAY,CAAC,IAAI,CAACtI,KAAK,EAAE,IAAI,CAACpB,QAAQ,CAAC;IAC9D,IAAI,CAAC0C,cAAc,GAAG,IAAI,CAACmH,iBAAiB,CAAC,IAAI,CAAC7H,SAAS,EAAEkB,SAAS,CAAC;EAC3E;EACA2G,iBAAiBA,CAAC7H,SAAS,EAAEkB,SAAS,EAAE;IACpC;IACA,MAAM4G,kBAAkB,GAAIC,UAAU,IAAK;MACvC,MAAMC,QAAQ,GAAG;QACbnI,IAAI,EAAE,MAAM;QACZtE,QAAQ,EAAEyE,SAAS,KAAK;MAC5B,CAAC;MACD,MAAMiI,QAAQ,GAAG;QACbpI,IAAI,EAAE,MAAM;QACZtE,QAAQ,EAAEyE,SAAS,KAAKkB;MAC5B,CAAC;MACD,OAAO,CAAC8G,QAAQ,EAAE,GAAGD,UAAU,EAAEE,QAAQ,CAAC;IAC9C,CAAC;IACD,MAAMC,YAAY,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;MACjC,MAAMC,IAAI,GAAG,EAAE;MACf,KAAK,IAAIC,CAAC,GAAGH,KAAK,EAAEG,CAAC,IAAIF,GAAG,EAAEE,CAAC,EAAE,EAAE;QAC/BD,IAAI,CAACE,IAAI,CAAC;UACNzI,KAAK,EAAEwI,CAAC;UACRzI,IAAI,EAAE;QACV,CAAC,CAAC;MACN;MACA,OAAOwI,IAAI;IACf,CAAC;IACD,IAAInH,SAAS,IAAI,CAAC,EAAE;MAChB,OAAO4G,kBAAkB,CAACI,YAAY,CAAC,CAAC,EAAEhH,SAAS,CAAC,CAAC;IACzD,CAAC,MACI;MACD;MACA,MAAMsH,iBAAiB,GAAGA,CAACC,QAAQ,EAAEC,IAAI,KAAK;QAC1C,IAAIC,WAAW,GAAG,EAAE;QACpB,MAAMC,YAAY,GAAG;UACjB/I,IAAI,EAAE;QACV,CAAC;QACD,MAAMgJ,YAAY,GAAG;UACjBhJ,IAAI,EAAE;QACV,CAAC;QACD,MAAMiJ,aAAa,GAAGZ,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC;QACxC,MAAMa,YAAY,GAAGb,YAAY,CAAChH,SAAS,EAAEA,SAAS,CAAC;QACvD,IAAIuH,QAAQ,GAAG,CAAC,EAAE;UACd;UACA,MAAMO,OAAO,GAAGP,QAAQ,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;UACtCE,WAAW,GAAG,CAAC,GAAGT,YAAY,CAAC,CAAC,EAAEc,OAAO,CAAC,EAAEH,YAAY,CAAC;QAC7D,CAAC,MACI,IAAIJ,QAAQ,GAAGC,IAAI,GAAG,CAAC,EAAE;UAC1BC,WAAW,GAAG,CAACC,YAAY,EAAE,GAAGV,YAAY,CAACO,QAAQ,GAAG,CAAC,EAAEA,QAAQ,GAAG,CAAC,CAAC,EAAEI,YAAY,CAAC;QAC3F,CAAC,MACI;UACD;UACA,MAAMI,QAAQ,GAAGR,QAAQ,KAAKC,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC;UAC5DC,WAAW,GAAG,CAACC,YAAY,EAAE,GAAGV,YAAY,CAACe,QAAQ,EAAEP,IAAI,GAAG,CAAC,CAAC,CAAC;QACrE;QACA,OAAO,CAAC,GAAGI,aAAa,EAAE,GAAGH,WAAW,EAAE,GAAGI,YAAY,CAAC;MAC9D,CAAC;MACD,OAAOjB,kBAAkB,CAACU,iBAAiB,CAACxI,SAAS,EAAEkB,SAAS,CAAC,CAAC;IACtE;EACJ;EACAyB,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE5C,SAAS;MAAEhC,QAAQ;MAAEoB;IAAM,CAAC,GAAGwD,OAAO;IAC9C,IAAI5C,SAAS,IAAIhC,QAAQ,IAAIoB,KAAK,EAAE;MAChC,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC,IAAI,CAACW,SAAS,GAAG,CAAC,IAAI,IAAI,CAAChC,QAAQ,GAAG,CAAC,EAAEuH,IAAI,CAAC2D,GAAG,CAAC,IAAI,CAAClJ,SAAS,GAAG,IAAI,CAAChC,QAAQ,EAAE,IAAI,CAACoB,KAAK,CAAC,CAAC;MAC9G,IAAI,CAACwI,YAAY,CAAC,CAAC;IACvB;EACJ;EACA;IAAS,IAAI,CAAC/E,IAAI,YAAAsG,qCAAApG,CAAA;MAAA,YAAAA,CAAA,IAAwFsD,4BAA4B,EA9ZtClO,EAAE,CAAAiR,iBAAA,CA8ZsDjR,EAAE,CAACkR,iBAAiB,GA9Z5ElR,EAAE,CAAAiR,iBAAA,CA8ZuFjR,EAAE,CAACmR,SAAS,GA9ZrGnR,EAAE,CAAAiR,iBAAA,CA8ZgHjR,EAAE,CAACoR,UAAU,GA9Z/HpR,EAAE,CAAAiR,iBAAA,CA8Z0ItP,IAAI,CAAC0P,cAAc;IAAA,CAA4D;EAAE;EAC7T;IAAS,IAAI,CAACxG,IAAI,kBA/Z8E7K,EAAE,CAAA8K,iBAAA;MAAApD,IAAA,EA+ZJwG,4BAA4B;MAAAnD,SAAA;MAAAuG,SAAA,WAAAC,mCAAAnP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/Z1BpC,EAAE,CAAAwR,WAAA,CAAAlL,GAAA;QAAA;QAAA,IAAAlE,EAAA;UAAA,IAAAqP,EAAA;UAAFzR,EAAE,CAAA0R,cAAA,CAAAD,EAAA,GAAFzR,EAAE,CAAA2R,WAAA,QAAAtP,GAAA,CAAAgH,QAAA,GAAAoI,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAvG,MAAA;QAAAzF,MAAA;QAAAgC,UAAA;QAAAb,SAAA;QAAA3D,QAAA;QAAAE,MAAA;QAAA6E,eAAA;QAAAC,eAAA;QAAAnB,KAAA;QAAAY,SAAA;QAAAhC,QAAA;QAAAwC,eAAA;MAAA;MAAAiD,OAAA;QAAA0B,eAAA;QAAAC,cAAA;MAAA;MAAA1B,UAAA;MAAAC,QAAA,GAAFxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAzC,QAAA,WAAAwI,sCAAAzP,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpC,EAAE,CAAAiD,UAAA,IAAAqF,mDAAA,gCAAFtI,EAAE,CAAAgM,sBAganE,CAAC;QAAA;MAAA;MAAAE,YAAA,GA4C2BnM,gBAAgB,EAAoJ6J,yBAAyB,EAA8LmD,4BAA4B;MAAAX,aAAA;MAAAC,eAAA;IAAA,EAAgU;EAAE;AACxxB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA9coGtM,EAAE,CAAAuM,iBAAA,CA8cX2B,4BAA4B,EAAc,CAAC;IAC1HxG,IAAI,EAAExH,SAAS;IACfsM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCC,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEjM,iBAAiB,CAACwM,IAAI;MACrCN,eAAe,EAAEjM,uBAAuB,CAACwM,MAAM;MAC/CvD,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiByD,OAAO,EAAE,CAAC/M,gBAAgB,EAAE6J,yBAAyB,EAAEmD,4BAA4B,CAAC;MACpFxB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7D,IAAI,EAAE1H,EAAE,CAACkR;EAAkB,CAAC,EAAE;IAAExJ,IAAI,EAAE1H,EAAE,CAACmR;EAAU,CAAC,EAAE;IAAEzJ,IAAI,EAAE1H,EAAE,CAACoR;EAAW,CAAC,EAAE;IAAE1J,IAAI,EAAE/F,IAAI,CAAC0P,cAAc;IAAES,UAAU,EAAE,CAAC;MAC1IpK,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8I,QAAQ,EAAE,CAAC;MACpC3B,IAAI,EAAElH,SAAS;MACfgM,IAAI,EAAE,CAAC,mBAAmB,EAAE;QAAEuF,MAAM,EAAE;MAAK,CAAC;IAChD,CAAC,CAAC;IAAEnM,MAAM,EAAE,CAAC;MACT8B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuH,UAAU,EAAE,CAAC;MACbF,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE0G,SAAS,EAAE,CAAC;MACZW,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXsE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEiD,MAAM,EAAE,CAAC;MACToE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE8H,eAAe,EAAE,CAAC;MAClBT,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+H,eAAe,EAAE,CAAC;MAClBV,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE4G,KAAK,EAAE,CAAC;MACRS,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwH,SAAS,EAAE,CAAC;MACZH,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACX6B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEgI,eAAe,EAAE,CAAC;MAClBX,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE2M,eAAe,EAAE,CAAC;MAClBtF,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAE2M,cAAc,EAAE,CAAC;MACjBvF,IAAI,EAAEpH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM0R,2BAA2B,CAAC;EAC9BnI,WAAWA,CAACsE,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,cAAc,EAAE;IACnD,IAAI,CAACH,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAC1G,UAAU,GAAG,IAAI;IACtB,IAAI,CAACxE,QAAQ,GAAG,KAAK;IACrB,IAAI,CAAC6D,KAAK,GAAG,CAAC;IACd,IAAI,CAACY,SAAS,GAAG,CAAC;IAClB,IAAI,CAAChC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACmH,eAAe,GAAG,IAAI/M,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC8I,SAAS,GAAG,CAAC;IAClB,IAAI,CAACD,YAAY,GAAG,KAAK;IACzB,IAAI,CAACE,WAAW,GAAG,KAAK;IACxB,IAAI,CAAClB,GAAG,GAAG,KAAK;IAChB,IAAI,CAACyG,QAAQ,GAAG,IAAI7N,OAAO,CAAC,CAAC;IAC7B0N,QAAQ,CAACI,WAAW,CAACJ,QAAQ,CAACK,UAAU,CAACJ,UAAU,CAACK,aAAa,CAAC,EAAEL,UAAU,CAACK,aAAa,CAAC;EACjG;EACAC,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,cAAc,CAACM,MAAM,EAAEC,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAErL,SAAS,IAAK;MAChF,IAAI,CAACqE,GAAG,GAAGrE,SAAS;MACpB,IAAI,CAACsL,cAAc,CAAC,CAAC;MACrB,IAAI,CAACZ,GAAG,CAACa,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAClH,GAAG,GAAG,IAAI,CAACwG,cAAc,CAACxJ,KAAK;IACpC,IAAI,CAACiK,cAAc,CAAC,CAAC;EACzB;EACAA,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACjH,GAAG,KAAK,KAAK,EAAE;MACpB,IAAI,CAACsG,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACZ,UAAU,CAACK,aAAa,EAAE,oBAAoB,CAAC;IAC/E,CAAC,MACI;MACD,IAAI,CAACN,QAAQ,CAACc,WAAW,CAAC,IAAI,CAACb,UAAU,CAACK,aAAa,EAAE,oBAAoB,CAAC;IAClF;EACJ;EACAS,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,QAAQ,CAACnE,IAAI,CAAC,CAAC;IACpB,IAAI,CAACmE,QAAQ,CAACa,QAAQ,CAAC,CAAC;EAC5B;EACAjJ,kBAAkBA,CAACZ,MAAM,EAAE;IACvB,MAAM4H,MAAM,GAAG5H,MAAM,CAAC4H,MAAM;IAC5B,MAAMxF,KAAK,GAAGzG,QAAQ,CAACiM,MAAM,CAACrI,KAAK,EAAE,IAAI,CAAC+C,SAAS,CAAC;IACpD,IAAI,CAACI,iBAAiB,CAACN,KAAK,CAAC;IAC7BwF,MAAM,CAACrI,KAAK,GAAI,GAAE,IAAI,CAAC+C,SAAU,EAAC;EACtC;EACAa,OAAOA,CAAA,EAAG;IACN,IAAI,CAACT,iBAAiB,CAAC,IAAI,CAACJ,SAAS,GAAG,CAAC,CAAC;EAC9C;EACAgB,QAAQA,CAAA,EAAG;IACP,IAAI,CAACZ,iBAAiB,CAAC,IAAI,CAACJ,SAAS,GAAG,CAAC,CAAC;EAC9C;EACAI,iBAAiBA,CAACN,KAAK,EAAE;IACrB,IAAI,CAACqF,eAAe,CAAC5C,IAAI,CAACzC,KAAK,CAAC;EACpC;EACAsK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAClJ,SAAS,GAAGqE,IAAI,CAACoC,IAAI,CAAC,IAAI,CAACvI,KAAK,GAAG,IAAI,CAACpB,QAAQ,CAAC;IACtD,IAAI,CAACiD,YAAY,GAAG,IAAI,CAACjB,SAAS,KAAK,CAAC;IACxC,IAAI,CAACmB,WAAW,GAAG,IAAI,CAACnB,SAAS,KAAK,IAAI,CAACkB,SAAS;EACxD;EACAyB,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE5C,SAAS;MAAEZ,KAAK;MAAEpB;IAAS,CAAC,GAAG4E,OAAO;IAC9C,IAAI5C,SAAS,IAAIZ,KAAK,IAAIpB,QAAQ,EAAE;MAChC,IAAI,CAACoM,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;IAAS,IAAI,CAACvH,IAAI,YAAAwH,oCAAAtH,CAAA;MAAA,YAAAA,CAAA,IAAwFoH,2BAA2B,EAzmBrChS,EAAE,CAAAiR,iBAAA,CAymBqDjR,EAAE,CAACkR,iBAAiB,GAzmB3ElR,EAAE,CAAAiR,iBAAA,CAymBsFjR,EAAE,CAACmR,SAAS,GAzmBpGnR,EAAE,CAAAiR,iBAAA,CAymB+GjR,EAAE,CAACoR,UAAU,GAzmB9HpR,EAAE,CAAAiR,iBAAA,CAymByItP,IAAI,CAAC0P,cAAc;IAAA,CAA4D;EAAE;EAC5T;IAAS,IAAI,CAACxG,IAAI,kBA1mB8E7K,EAAE,CAAA8K,iBAAA;MAAApD,IAAA,EA0mBJsK,2BAA2B;MAAAjH,SAAA;MAAAuG,SAAA,WAAAa,kCAAA/P,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1mBzBpC,EAAE,CAAAwR,WAAA,CAAAlL,GAAA;QAAA;QAAA,IAAAlE,EAAA;UAAA,IAAAqP,EAAA;UAAFzR,EAAE,CAAA0R,cAAA,CAAAD,EAAA,GAAFzR,EAAE,CAAA2R,WAAA,QAAAtP,GAAA,CAAAgH,QAAA,GAAAoI,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAvG,MAAA;QAAAzD,UAAA;QAAAxE,QAAA;QAAAE,MAAA;QAAA2D,KAAA;QAAAY,SAAA;QAAAhC,QAAA;MAAA;MAAAyF,OAAA;QAAA0B,eAAA;MAAA;MAAAzB,UAAA;MAAAC,QAAA,GAAFxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAzC,QAAA,WAAA+I,qCAAAhQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFpC,EAAE,CAAAiD,UAAA,IAAAuF,kDAAA,iCAAFxI,EAAE,CAAAgM,sBA2mBnE,CAAC;QAAA;MAAA;MAAAE,YAAA,GA6B2BtC,yBAAyB;MAAAwC,aAAA;MAAAC,eAAA;IAAA,EAAmQ;EAAE;AAC7V;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1oBoGtM,EAAE,CAAAuM,iBAAA,CA0oBXyF,2BAA2B,EAAc,CAAC;IACzHtK,IAAI,EAAExH,SAAS;IACfsM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEjM,iBAAiB,CAACwM,IAAI;MACrCN,eAAe,EAAEjM,uBAAuB,CAACwM,MAAM;MAC/CvD,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiByD,OAAO,EAAE,CAAClD,yBAAyB,CAAC;MACpC2B,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7D,IAAI,EAAE1H,EAAE,CAACkR;EAAkB,CAAC,EAAE;IAAExJ,IAAI,EAAE1H,EAAE,CAACmR;EAAU,CAAC,EAAE;IAAEzJ,IAAI,EAAE1H,EAAE,CAACoR;EAAW,CAAC,EAAE;IAAE1J,IAAI,EAAE/F,IAAI,CAAC0P,cAAc;IAAES,UAAU,EAAE,CAAC;MAC1IpK,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8I,QAAQ,EAAE,CAAC;MACpC3B,IAAI,EAAElH,SAAS;MACfgM,IAAI,EAAE,CAAC,mBAAmB,EAAE;QAAEuF,MAAM,EAAE;MAAK,CAAC;IAChD,CAAC,CAAC;IAAEnK,UAAU,EAAE,CAAC;MACbF,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE+C,QAAQ,EAAE,CAAC;MACXsE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEiD,MAAM,EAAE,CAAC;MACToE,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE4G,KAAK,EAAE,CAAC;MACRS,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwH,SAAS,EAAE,CAAC;MACZH,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACX6B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAE2M,eAAe,EAAE,CAAC;MAClBtF,IAAI,EAAEpH;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM+R,qBAAqB,GAAG,YAAY;AAC1C,MAAMC,qBAAqB,CAAC;EACxBC,iBAAiBA,CAACzN,KAAK,EAAEiE,SAAS,EAAE;IAChC,IAAIjE,KAAK,GAAGiE,SAAS,EAAE;MACnB,OAAOA,SAAS;IACpB,CAAC,MACI,IAAIjE,KAAK,GAAG,CAAC,EAAE;MAChB,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAOA,KAAK;IAChB;EACJ;EACAmD,iBAAiBA,CAACN,KAAK,EAAE;IACrB,MAAMoB,SAAS,GAAG,IAAI,CAACwG,YAAY,CAAC,IAAI,CAACiD,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC;IAClE,MAAMC,UAAU,GAAG,IAAI,CAACH,iBAAiB,CAAC5K,KAAK,EAAEoB,SAAS,CAAC;IAC3D,IAAI2J,UAAU,KAAK,IAAI,CAACC,WAAW,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;MACrD,IAAI,CAACD,WAAW,GAAGD,UAAU;MAC7B,IAAI,CAACG,iBAAiB,CAAC1I,IAAI,CAAC,IAAI,CAACwI,WAAW,CAAC;IACjD;EACJ;EACAjN,gBAAgBA,CAACwH,IAAI,EAAE;IACnB,IAAI,CAACuF,UAAU,GAAGvF,IAAI;IACtB,IAAI,CAAC4F,gBAAgB,CAAC3I,IAAI,CAAC+C,IAAI,CAAC;IAChC,MAAMnE,SAAS,GAAG,IAAI,CAACwG,YAAY,CAAC,IAAI,CAACiD,OAAO,EAAE,IAAI,CAACC,UAAU,CAAC;IAClE,IAAI,IAAI,CAACE,WAAW,GAAG5J,SAAS,EAAE;MAC9B,IAAI,CAACd,iBAAiB,CAACc,SAAS,CAAC;IACrC;EACJ;EACAgK,aAAaA,CAAC9L,KAAK,EAAE;IACjB,MAAM8B,SAAS,GAAG,IAAI,CAACwG,YAAY,CAACtI,KAAK,EAAE,IAAI,CAACwL,UAAU,CAAC;IAC3D,IAAI,IAAI,CAACE,WAAW,GAAG5J,SAAS,EAAE;MAC9BiK,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACjL,iBAAiB,CAACc,SAAS,CAAC;QACjC,IAAI,CAACoF,GAAG,CAACgF,YAAY,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;EACA5D,YAAYA,CAACtI,KAAK,EAAEpB,QAAQ,EAAE;IAC1B,OAAOuH,IAAI,CAACoC,IAAI,CAACvI,KAAK,GAAGpB,QAAQ,CAAC;EACtC;EACAgE,WAAWA,CAACuJ,IAAI,EAAEjF,GAAG,EAAEkF,iBAAiB,EAAEC,eAAe,EAAEhF,cAAc,EAAE;IACvE,IAAI,CAAC8E,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACjF,GAAG,GAAGA,GAAG;IACd,IAAI,CAACkF,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAChF,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACiF,aAAa,GAAGlB,qBAAqB;IAC1C,IAAI,CAACS,gBAAgB,GAAG,IAAI7S,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC4S,iBAAiB,GAAG,IAAI5S,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACuT,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC7N,MAAM,GAAG,SAAS;IACvB,IAAI,CAAC8N,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACzC,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACjK,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiJ,UAAU,GAAG,KAAK;IACvB,IAAI,CAACiB,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACtB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACG,WAAW,GAAG,CAAC;IACpB,IAAI,CAACF,UAAU,GAAG,EAAE;IACpB,IAAI,CAACsB,cAAc,GAAG,IAAI;IAC1B,IAAI,CAAC7G,IAAI,GAAG,SAAS;IACrB,IAAI,CAACpF,GAAG,GAAG,KAAK;IAChB,IAAI,CAACyG,QAAQ,GAAG,IAAI7N,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACsT,MAAM,GAAG,IAAIrT,aAAa,CAAC,CAAC,CAAC;EACtC;EACAgO,QAAQA,CAAA,EAAG;IACP,IAAI,CAACyE,IAAI,CAACa,YAAY,CAACpF,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAC,MAAM;MAClE,IAAI,CAACxL,MAAM,GAAG,IAAI,CAAC8P,IAAI,CAACc,aAAa,CAAC,YAAY,CAAC;MACnD,IAAI,CAAC/F,GAAG,CAACgF,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACa,MAAM,CAACnF,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAC7H,KAAK,IAAI;MAC1D,IAAI,CAAC8L,aAAa,CAAC9L,KAAK,CAAC;IAC7B,CAAC,CAAC;IACF,IAAI,CAACoM,iBAAiB,CACjBvE,SAAS,CAAC9N,iBAAiB,CAAC,CAC5B6N,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAC9BO,SAAS,CAACqF,EAAE,IAAI;MACjB,IAAI,IAAI,CAACN,YAAY,EAAE;QACnB,IAAI,CAAC3G,IAAI,GAAGiH,EAAE,KAAKlT,gBAAgB,CAACmT,EAAE,GAAG,OAAO,GAAG,SAAS;QAC5D,IAAI,CAACjG,GAAG,CAACgF,YAAY,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC7E,cAAc,CAACM,MAAM,EAAEC,IAAI,CAACjO,SAAS,CAAC,IAAI,CAAC2N,QAAQ,CAAC,CAAC,CAACO,SAAS,CAAErL,SAAS,IAAK;MAChF,IAAI,CAACqE,GAAG,GAAGrE,SAAS;MACpB,IAAI,CAAC0K,GAAG,CAACa,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAAClH,GAAG,GAAG,IAAI,CAACwG,cAAc,CAACxJ,KAAK;EACxC;EACAqK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACZ,QAAQ,CAACnE,IAAI,CAAC,CAAC;IACpB,IAAI,CAACmE,QAAQ,CAACa,QAAQ,CAAC,CAAC;EAC5B;EACA5E,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEqJ,kBAAkB;MAAEtB,OAAO;MAAEC,UAAU;MAAE7M;IAAO,CAAC,GAAG6E,OAAO;IACnE,IAAI+H,OAAO,EAAE;MACT,IAAI,CAACwB,MAAM,CAAC5J,IAAI,CAAC,IAAI,CAACoI,OAAO,CAAC;IAClC;IACA,IAAIsB,kBAAkB,IAAItB,OAAO,IAAIC,UAAU,EAAE;MAC7C,IAAI,CAACsB,cAAc,GACd,IAAI,CAACD,kBAAkB,IAAI,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACC,UAAU,IAAM,IAAI,CAACD,OAAO,GAAG,CAAC,IAAI,CAAC,IAAI,CAACsB,kBAAmB;IACrH;IACA,IAAIlO,MAAM,EAAE;MACR,IAAI,CAACsH,IAAI,GAAGtH,MAAM,CAACyO,YAAY;IACnC;EACJ;EACA;IAAS,IAAI,CAAC3J,IAAI,YAAA4J,8BAAA1J,CAAA;MAAA,YAAAA,CAAA,IAAwF0H,qBAAqB,EArzB/BtS,EAAE,CAAAiR,iBAAA,CAqzB+CrP,IAAI,CAAC2S,aAAa,GArzBnEvU,EAAE,CAAAiR,iBAAA,CAqzB8EjR,EAAE,CAACkR,iBAAiB,GArzBpGlR,EAAE,CAAAiR,iBAAA,CAqzB+GlQ,IAAI,CAACyT,mBAAmB,GArzBzIxU,EAAE,CAAAiR,iBAAA,CAqzBoJpQ,EAAE,CAAC4T,eAAe,GArzBxKzU,EAAE,CAAAiR,iBAAA,CAqzBmLtP,IAAI,CAAC0P,cAAc;IAAA,CAA4D;EAAE;EACtW;IAAS,IAAI,CAACxG,IAAI,kBAtzB8E7K,EAAE,CAAA8K,iBAAA;MAAApD,IAAA,EAszBJ4K,qBAAqB;MAAAvH,SAAA;MAAA4C,SAAA;MAAA3C,QAAA;MAAAC,YAAA,WAAAyJ,mCAAAtS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAtzBnBpC,EAAE,CAAAoL,WAAA,0BAAA/I,GAAA,CAAAsH,QAszBgB,CAAC,4BAAAtH,GAAA,CAAAuQ,UAAD,CAAC,yBAAAvQ,GAAA,CAAAsH,QAAA,IAAAtH,GAAA,CAAA6K,IAAA,KAAC,OAAF,CAAC,uBAAA7K,GAAA,CAAAyF,GAAA,KAAb,KAAY,CAAC;QAAA;MAAA;MAAAuD,MAAA;QAAAmI,WAAA;QAAAC,YAAA;QAAA7N,MAAA;QAAA8N,iBAAA;QAAAC,iBAAA;QAAAC,iBAAA;QAAAjK,QAAA;QAAAiJ,UAAA;QAAAiB,YAAA;QAAAC,kBAAA;QAAAtB,OAAA;QAAAG,WAAA;QAAAF,UAAA;MAAA;MAAAnH,OAAA;QAAAwH,gBAAA;QAAAD,iBAAA;MAAA;MAAA8B,QAAA;MAAApJ,UAAA;MAAAC,QAAA,GAtzBnBxL,EAAE,CAAAyL,oBAAA,EAAFzL,EAAE,CAAA0L,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAzC,QAAA,WAAAuL,+BAAAxS,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA+C,GAAA,GAAFnF,EAAE,CAAAoF,gBAAA;UAAFpF,EAAE,CAAAiD,UAAA,IAAAwG,4CAAA,MAuzB7E,CAAC;UAvzB0EzJ,EAAE,CAAAsC,cAAA,gCAw0BlG,CAAC;UAx0B+FtC,EAAE,CAAAqF,UAAA,6BAAAwP,+EAAAtP,MAAA;YAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;YAAA,OAAFnF,EAAE,CAAAyF,WAAA,CAu0B7EpD,GAAA,CAAA4F,iBAAA,CAAA1C,MAAwB,CAAC;UAAA,EAAC;UAv0BiDvF,EAAE,CAAAwC,YAAA,CAw0B3E,CAAC;UAx0BwExC,EAAE,CAAAsC,cAAA,iCAw1BlG,CAAC;UAx1B+FtC,EAAE,CAAAqF,UAAA,6BAAAyP,gFAAAvP,MAAA;YAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;YAAA,OAAFnF,EAAE,CAAAyF,WAAA,CAs1B7EpD,GAAA,CAAA4F,iBAAA,CAAA1C,MAAwB,CAAC;UAAA,EAAC,4BAAAwP,+EAAAxP,MAAA;YAt1BiDvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;YAAA,OAAFnF,EAAE,CAAAyF,WAAA,CAu1B9EpD,GAAA,CAAAqD,gBAAA,CAAAH,MAAuB,CAAC;UAAA,EAAC;UAv1BmDvF,EAAE,CAAAwC,YAAA,CAw1B1E,CAAC;QAAA;QAAA,IAAAJ,EAAA;UAx1BuEpC,EAAE,CAAAwD,aAAA,IAAAnB,GAAA,CAAA0R,cAAA,SA6zBlG,CAAC;UA7zB+F/T,EAAE,CAAA2C,SAAA,CAi0B1E,CAAC;UAj0BuE3C,EAAE,CAAAmD,UAAA,aAAAd,GAAA,CAAAuQ,UAi0B1E,CAAC,eAAAvQ,GAAA,CAAAoR,YACG,CAAC,WAAApR,GAAA,CAAAiB,MACX,CAAC,aAAAjB,GAAA,CAAAoQ,UACK,CAAC,UAAApQ,GAAA,CAAAmQ,OACP,CAAC,cAAAnQ,GAAA,CAAAsQ,WACO,CAAC;UAt0BqE3S,EAAE,CAAA2C,SAAA,EA20BlF,CAAC;UA30B+E3C,EAAE,CAAAmD,UAAA,WAAAd,GAAA,CAAA6K,IA20BlF,CAAC,eAAA7K,GAAA,CAAAoR,YACW,CAAC,cAAApR,GAAA,CAAAmR,WACH,CAAC,aAAAnR,GAAA,CAAAuQ,UACH,CAAC,WAAAvQ,GAAA,CAAAiB,MACP,CAAC,oBAAAjB,GAAA,CAAAsR,iBACmB,CAAC,oBAAAtR,GAAA,CAAAuR,iBACD,CAAC,UAAAvR,GAAA,CAAAmQ,OACrB,CAAC,cAAAnQ,GAAA,CAAAsQ,WACO,CAAC,aAAAtQ,GAAA,CAAAoQ,UACH,CAAC,oBAAApQ,GAAA,CAAAqR,iBACa,CAAC;QAAA;MAAA;MAAAxH,YAAA,GAIoBnM,gBAAgB,EAAoJiS,2BAA2B,EAA+K9D,4BAA4B;MAAA9B,aAAA;MAAAC,eAAA;IAAA,EAAuV;EAAE;AAClyB;AACAvM,UAAU,CAAC,CACPgB,UAAU,CAAC,CAAC,CACf,EAAEwR,qBAAqB,CAAC0C,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACrDlV,UAAU,CAAC,CACPgB,UAAU,CAAC,CAAC,CACf,EAAEwR,qBAAqB,CAAC0C,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAChElV,UAAU,CAAC,CACPgB,UAAU,CAAC,CAAC,EACZK,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAChElV,UAAU,CAAC,CACPgB,UAAU,CAAC,CAAC,EACZK,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAChElV,UAAU,CAAC,CACPgB,UAAU,CAAC,CAAC,EACZK,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AACvDlV,UAAU,CAAC,CACPqB,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACzDlV,UAAU,CAAC,CACPqB,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AAC3DlV,UAAU,CAAC,CACPqB,YAAY,CAAC,CAAC,CACjB,EAAEmR,qBAAqB,CAAC0C,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;AACjElV,UAAU,CAAC,CACPsB,WAAW,CAAC,CAAC,CAChB,EAAEkR,qBAAqB,CAAC0C,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AACtDlV,UAAU,CAAC,CACPsB,WAAW,CAAC,CAAC,CAChB,EAAEkR,qBAAqB,CAAC0C,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AAC1DlV,UAAU,CAAC,CACPsB,WAAW,CAAC,CAAC,CAChB,EAAEkR,qBAAqB,CAAC0C,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACzD;EAAA,QAAA1I,SAAA,oBAAAA,SAAA,KA/3BoGtM,EAAE,CAAAuM,iBAAA,CA+3BX+F,qBAAqB,EAAc,CAAC;IACnH5K,IAAI,EAAExH,SAAS;IACfsM,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzBkI,QAAQ,EAAE,cAAc;MACxBjI,mBAAmB,EAAE,KAAK;MAC1BN,aAAa,EAAEjM,iBAAiB,CAACwM,IAAI;MACrCN,eAAe,EAAEjM,uBAAuB,CAACwM,MAAM;MAC/CvD,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACFoB,KAAK,EAAE,gBAAgB;QACvB,+BAA+B,EAAE,UAAU;QAC3C,iCAAiC,EAAE,YAAY;QAC/C,6BAA6B,EAAG,+BAA8B;QAC9D,4BAA4B,EAAG;MACnC,CAAC;MACDnB,OAAO,EAAE,CAAC/M,gBAAgB,EAAEiS,2BAA2B,EAAE9D,4BAA4B,CAAC;MACtF3C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7D,IAAI,EAAE9F,IAAI,CAAC2S;EAAc,CAAC,EAAE;IAAE7M,IAAI,EAAE1H,EAAE,CAACkR;EAAkB,CAAC,EAAE;IAAExJ,IAAI,EAAE3G,IAAI,CAACyT;EAAoB,CAAC,EAAE;IAAE9M,IAAI,EAAE7G,EAAE,CAAC4T;EAAgB,CAAC,EAAE;IAAE/M,IAAI,EAAE/F,IAAI,CAAC0P,cAAc;IAAES,UAAU,EAAE,CAAC;MACzLpK,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuS,gBAAgB,EAAE,CAAC;MAC5CpL,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAEuS,iBAAiB,EAAE,CAAC;MACpBnL,IAAI,EAAEpH;IACV,CAAC,CAAC;IAAEkT,WAAW,EAAE,CAAC;MACd9L,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEoT,YAAY,EAAE,CAAC;MACf/L,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuF,MAAM,EAAE,CAAC;MACT8B,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEqT,iBAAiB,EAAE,CAAC;MACpBhM,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEsT,iBAAiB,EAAE,CAAC;MACpBjM,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuT,iBAAiB,EAAE,CAAC;MACpBlM,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEsJ,QAAQ,EAAE,CAAC;MACXjC,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEuS,UAAU,EAAE,CAAC;MACblL,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEwT,YAAY,EAAE,CAAC;MACfnM,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEyT,kBAAkB,EAAE,CAAC;MACrBpM,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEmS,OAAO,EAAE,CAAC;MACV9K,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEsS,WAAW,EAAE,CAAC;MACdjL,IAAI,EAAErH;IACV,CAAC,CAAC;IAAEoS,UAAU,EAAE,CAAC;MACb/K,IAAI,EAAErH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM4U,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACvK,IAAI,YAAAwK,2BAAAtK,CAAA;MAAA,YAAAA,CAAA,IAAwFqK,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAACE,IAAI,kBA79B8EnV,EAAE,CAAAoV,gBAAA;MAAA1N,IAAA,EA69BSuN,kBAAkB;MAAAnI,OAAA,GAAYwF,qBAAqB,EACtJN,2BAA2B,EAC3BjF,4BAA4B,EAC5BnD,yBAAyB,EACzBsE,4BAA4B;MAAAmH,OAAA,GAAa/C,qBAAqB;IAAA,EAAI;EAAE;EAC5E;IAAS,IAAI,CAACgD,IAAI,kBAl+B8EtV,EAAE,CAAAuV,gBAAA;MAAAzI,OAAA,GAk+BuCwF,qBAAqB,EACtJN,2BAA2B,EAC3BjF,4BAA4B,EAC5BnD,yBAAyB,EACzBsE,4BAA4B;IAAA,EAAI;EAAE;AAC9C;AACA;EAAA,QAAA5B,SAAA,oBAAAA,SAAA,KAx+BoGtM,EAAE,CAAAuM,iBAAA,CAw+BX0I,kBAAkB,EAAc,CAAC;IAChHvN,IAAI,EAAEjH,QAAQ;IACd+L,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CACLwF,qBAAqB,EACrBN,2BAA2B,EAC3BjF,4BAA4B,EAC5BnD,yBAAyB,EACzBsE,4BAA4B,CAC/B;MACDmH,OAAO,EAAE,CAAC/C,qBAAqB;IACnC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASA,qBAAqB,EAAEpE,4BAA4B,EAAEtE,yBAAyB,EAAEqL,kBAAkB,EAAElI,4BAA4B,EAAEiF,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}