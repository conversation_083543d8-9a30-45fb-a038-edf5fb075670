{"ast": null, "code": "import ary from './ary.js';\n\n/**\n * Creates a function that accepts up to one argument, ignoring any\n * additional arguments.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Function\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n * @example\n *\n * _.map(['6', '8', '10'], _.unary(parseInt));\n * // => [6, 8, 10]\n */\nfunction unary(func) {\n  return ary(func, 1);\n}\nexport default unary;", "map": {"version": 3, "names": ["ary", "unary", "func"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/unary.js"], "sourcesContent": ["import ary from './ary.js';\n\n/**\n * Creates a function that accepts up to one argument, ignoring any\n * additional arguments.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Function\n * @param {Function} func The function to cap arguments for.\n * @returns {Function} Returns the new capped function.\n * @example\n *\n * _.map(['6', '8', '10'], _.unary(parseInt));\n * // => [6, 8, 10]\n */\nfunction unary(func) {\n  return ary(func, 1);\n}\n\nexport default unary;\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;;AAE1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAOF,GAAG,CAACE,IAAI,EAAE,CAAC,CAAC;AACrB;AAEA,eAAeD,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}