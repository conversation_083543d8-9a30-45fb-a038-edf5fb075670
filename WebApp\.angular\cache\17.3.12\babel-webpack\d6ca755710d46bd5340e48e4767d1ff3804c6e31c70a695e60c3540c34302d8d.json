{"ast": null, "code": "import startOfDay from \"../startOfDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same day (and year and month)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport default function isSameDay(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfDay = startOfDay(dirtyDateLeft);\n  var dateRightStartOfDay = startOfDay(dirtyDateRight);\n  return dateLeftStartOfDay.getTime() === dateRightStartOfDay.getTime();\n}", "map": {"version": 3, "names": ["startOfDay", "requiredArgs", "isSameDay", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfDay", "dateRightStartOfDay", "getTime"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/isSameDay/index.js"], "sourcesContent": ["import startOfDay from \"../startOfDay/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same day (and year and month)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport default function isSameDay(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfDay = startOfDay(dirtyDateLeft);\n  var dateRightStartOfDay = startOfDay(dirtyDateRight);\n  return dateLeftStartOfDay.getTime() === dateRightStartOfDay.getTime();\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,SAASA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAC/DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,kBAAkB,GAAGN,UAAU,CAACG,aAAa,CAAC;EAClD,IAAII,mBAAmB,GAAGP,UAAU,CAACI,cAAc,CAAC;EACpD,OAAOE,kBAAkB,CAACE,OAAO,CAAC,CAAC,KAAKD,mBAAmB,CAACC,OAAO,CAAC,CAAC;AACvE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}