<!-- Plugin Sidebar - Slides in from the right -->
<div *ngIf="showSidebar" class="flex-1 w-full h-full overflow-y-auto transition-all duration-300 ease-in-out" [ngClass]="{
    'translate-x-0': showSidebar,
    'translate-x-full': !showSidebar,
    'bg-[#2b2b33] text-white border-l border-[#3a3a45]': themeService.isDarkMode(),
    'bg-[var(--background-white)] text-[var(--text-dark)] border-l border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
  }">

  <!-- Plugin Sidebar Header - Sticky -->
  <div
    class="flex items-center justify-between p-4 sticky top-0 z-10 shadow-sm backdrop-blur-sm transition-all duration-200"
    [ngClass]="{
      'bg-[#2b2b33] border-b border-[#3a3a45]': themeService.isDarkMode(),
      'bg-[var(--background-white)] border-b border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
    }">
    <h3 class="font-semibold flex items-center gap-2"
      [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
      <i class="ri-plug-line"
        [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
      <span>{{pluginSidebarTitle}}</span>
    </h3>
    <button (click)="onCloseSidebar()" [ngClass]="{
        'bg-[#44444f] text-white hover:bg-[#57576a]': themeService.isDarkMode(),
        'bg-[var(--hover-blue-gray)] text-[var(--primary-purple)] hover:bg-[var(--primary-purple)] hover:text-white': !themeService.isDarkMode()
      }"
      class="transition-colors p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[var(--primary-purple)] border-none">
      <i class="ri-close-line text-xl" [ngClass]="{
        'text-white': themeService.isDarkMode(),
        'text-[var(--primary-purple)]': !themeService.isDarkMode()
      }"></i>
    </button>
  </div>

  <!-- Plugin Content -->
  <div class="p-4">
    <!-- Agent Info Section -->
    <div *ngIf="selectedAgent" class="mb-6">
      <div class="flex items-center gap-2 mb-2">
        <i class="ri-user-2-line"
          [ngClass]="{'text-[#00c39a]': themeService.isDarkMode(), 'text-[var(--primary-purple)]': !themeService.isDarkMode()}"></i>
        <h4 class="font-medium"
          [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
          {{selectedAgent}}
        </h4>
      </div>
      <p class="text-xs"
        [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Plugins connected to this agent
      </p>
    </div>

    <!-- Loading State -->
    <div *ngIf="isLoading" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center animate-pulse" [ngClass]="{
             'bg-[#3a3a45]': themeService.isDarkMode(),
             'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
           }">
        <i class="ri-loader-4-line text-2xl animate-spin" [ngClass]="{
             'text-[#00c39a]': themeService.isDarkMode(),
             'text-[var(--primary-purple)]': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <p class="font-medium"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        Loading Plugins...
      </p>
      <p class="text-xs mt-1"
        [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Fetching plugins for {{selectedAgent}}
      </p>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError && !isLoading" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" [ngClass]="{
             'bg-red-900': themeService.isDarkMode(),
             'bg-red-100': !themeService.isDarkMode()
           }">
        <i class="ri-error-warning-line text-2xl" [ngClass]="{
             'text-red-300': themeService.isDarkMode(),
             'text-red-600': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <h4 class="font-medium mb-2"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        Failed to Load Plugins
      </h4>
      <p class="text-sm"
        [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        {{errorMessage}}
      </p>
      <p class="text-xs mt-2"
        [ngClass]="{'text-gray-400': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        Showing demo data instead
      </p>
    </div>

    <!-- Plugin List - New Design -->
    <div *ngIf="agentPlugins.length > 0 && !isLoading" class="space-y-6">
      <div *ngFor="let plugin of agentPlugins" class="plugin-container">

        <!-- Plugin Header Card -->
        <div class="p-4 rounded-lg border transition-all duration-200 cursor-pointer" [ngClass]="{
               'bg-[#3a3a45] border-[#4a4a55] hover:border-[#00c39a]': themeService.isDarkMode(),
               'bg-[var(--background-white)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
             }" (click)="selectPlugin(plugin)">

          <!-- Plugin Name and Type -->
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-lg font-bold"
              [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
              {{plugin.pluginName}}
            </h3>
            <span class="text-xs px-2 py-1 rounded-full font-medium" [ngClass]="getPluginTypeBadgeClass(plugin.type)">
              {{plugin.type || 'Unknown'}}
            </span>
          </div>

          <!-- Available Functions Header -->
          <div class="mb-3">
            <h4 class="text-sm font-medium mb-2"
              [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
              Available Functions
            </h4>
          </div>

          <!-- Functions List -->
          <div *ngIf="getFunctionList(plugin.functions).length > 0" class="space-y-3">
            <div *ngFor="let func of getFunctionList(plugin.functions)"
              class="p-3 rounded-md border transition-all duration-200" [ngClass]="{
                   'bg-[#2d2d38] border-[#3a3a45] hover:border-[#00c39a]': themeService.isDarkMode(),
                   'bg-[var(--background-light-gray)] border-[var(--hover-blue-gray)] hover:border-[var(--primary-purple)]': !themeService.isDarkMode()
                 }">

              <!-- Function Name -->
              <div class="font-medium text-sm mb-1"
                [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
                {{func.name}}
              </div>

              <!-- Function Description -->
              <div class="text-xs leading-relaxed"
                [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
                {{func.description}}
                <span class="inline-block">{{'.'.repeat(50)}}</span>
              </div>
            </div>
          </div>

          <!-- No Functions Available -->
          <div *ngIf="getFunctionList(plugin.functions).length === 0" class="p-3 rounded-md text-center" [ngClass]="{
                 'bg-[#2d2d38] border border-[#3a3a45]': themeService.isDarkMode(),
                 'bg-[var(--background-light-gray)] border border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
               }">
            <p class="text-sm"
              [ngClass]="{'text-gray-400': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
              No functions available for this plugin
            </p>
          </div>

          <!-- Plugin URL (if available) -->
          <div *ngIf="plugin.url" class="mt-3 pt-3 border-t" [ngClass]="{
                 'border-[#3a3a45]': themeService.isDarkMode(),
                 'border-[var(--hover-blue-gray)]': !themeService.isDarkMode()
               }">
            <div class="flex items-center gap-2 text-xs"
              [ngClass]="{'text-gray-400': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
              <i class="ri-external-link-line"></i>
              <span class="truncate">{{plugin.url}}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div *ngIf="agentPlugins.length === 0 && !isLoading && !hasError" class="text-center py-12">
      <div class="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" [ngClass]="{
             'bg-[#3a3a45]': themeService.isDarkMode(),
             'bg-[var(--hover-blue-gray)]': !themeService.isDarkMode()
           }">
        <i class="ri-plug-line text-2xl" [ngClass]="{
             'text-gray-400': themeService.isDarkMode(),
             'text-[var(--text-medium-gray)]': !themeService.isDarkMode()
           }">
        </i>
      </div>
      <h4 class="font-medium mb-2"
        [ngClass]="{'text-white': themeService.isDarkMode(), 'text-[var(--text-dark)]': !themeService.isDarkMode()}">
        No Plugins Connected
      </h4>
      <p class="text-sm"
        [ngClass]="{'text-gray-300': themeService.isDarkMode(), 'text-[var(--text-medium-gray)]': !themeService.isDarkMode()}">
        <span *ngIf="selectedAgent">{{selectedAgent}} doesn't have any plugins configured.</span>
        <span *ngIf="!selectedAgent">Select an agent to view its plugins.</span>
      </p>
    </div>
  </div>
</div>
