{"ast": null, "code": "import baseFindIndex from './_baseFindIndex.js';\nimport baseIsNaN from './_baseIsNaN.js';\nimport strictLastIndexOf from './_strictLastIndexOf.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n  nativeMin = Math.min;\n\n/**\n * This method is like `_.indexOf` except that it iterates over elements of\n * `array` from right to left.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=array.length-1] The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.lastIndexOf([1, 2, 1, 2], 2);\n * // => 3\n *\n * // Search from the `fromIndex`.\n * _.lastIndexOf([1, 2, 1, 2], 2, 2);\n * // => 1\n */\nfunction lastIndexOf(array, value, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = length;\n  if (fromIndex !== undefined) {\n    index = toInteger(fromIndex);\n    index = index < 0 ? nativeMax(length + index, 0) : nativeMin(index, length - 1);\n  }\n  return value === value ? strictLastIndexOf(array, value, index) : baseFindIndex(array, baseIsNaN, index, true);\n}\nexport default lastIndexOf;", "map": {"version": 3, "names": ["baseFindIndex", "baseIsNaN", "strictLastIndexOf", "toInteger", "nativeMax", "Math", "max", "nativeMin", "min", "lastIndexOf", "array", "value", "fromIndex", "length", "index", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/lastIndexOf.js"], "sourcesContent": ["import baseFindIndex from './_baseFindIndex.js';\nimport baseIsNaN from './_baseIsNaN.js';\nimport strictLastIndexOf from './_strictLastIndexOf.js';\nimport toInteger from './toInteger.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * This method is like `_.indexOf` except that it iterates over elements of\n * `array` from right to left.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=array.length-1] The index to search from.\n * @returns {number} Returns the index of the matched value, else `-1`.\n * @example\n *\n * _.lastIndexOf([1, 2, 1, 2], 2);\n * // => 3\n *\n * // Search from the `fromIndex`.\n * _.lastIndexOf([1, 2, 1, 2], 2, 2);\n * // => 1\n */\nfunction lastIndexOf(array, value, fromIndex) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return -1;\n  }\n  var index = length;\n  if (fromIndex !== undefined) {\n    index = toInteger(fromIndex);\n    index = index < 0 ? nativeMax(length + index, 0) : nativeMin(index, length - 1);\n  }\n  return value === value\n    ? strictLastIndexOf(array, value, index)\n    : baseFindIndex(array, baseIsNaN, index, true);\n}\n\nexport default lastIndexOf;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,SAAS,GAAGF,IAAI,CAACG,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC5C,IAAIC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC,CAAC;EACX;EACA,IAAIC,KAAK,GAAGD,MAAM;EAClB,IAAID,SAAS,KAAKG,SAAS,EAAE;IAC3BD,KAAK,GAAGX,SAAS,CAACS,SAAS,CAAC;IAC5BE,KAAK,GAAGA,KAAK,GAAG,CAAC,GAAGV,SAAS,CAACS,MAAM,GAAGC,KAAK,EAAE,CAAC,CAAC,GAAGP,SAAS,CAACO,KAAK,EAAED,MAAM,GAAG,CAAC,CAAC;EACjF;EACA,OAAOF,KAAK,KAAKA,KAAK,GAClBT,iBAAiB,CAACQ,KAAK,EAAEC,KAAK,EAAEG,KAAK,CAAC,GACtCd,aAAa,CAACU,KAAK,EAAET,SAAS,EAAEa,KAAK,EAAE,IAAI,CAAC;AAClD;AAEA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}