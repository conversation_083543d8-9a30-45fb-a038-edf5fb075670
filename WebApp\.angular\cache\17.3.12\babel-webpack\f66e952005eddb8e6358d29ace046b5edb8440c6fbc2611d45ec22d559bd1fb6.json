{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { HeaderNames } from \"./HeaderNames\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\n/** @private */\nexport class WebSocketTransport {\n  constructor(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor, headers) {\n    this._logger = logger;\n    this._accessTokenFactory = accessTokenFactory;\n    this._logMessageContent = logMessageContent;\n    this._webSocketConstructor = webSocketConstructor;\n    this._httpClient = httpClient;\n    this.onreceive = null;\n    this.onclose = null;\n    this._headers = headers;\n  }\n  connect(url, transferFormat) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      Arg.isRequired(url, \"url\");\n      Arg.isRequired(transferFormat, \"transferFormat\");\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n      _this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\n      let token;\n      if (_this._accessTokenFactory) {\n        token = yield _this._accessTokenFactory();\n      }\n      return new Promise((resolve, reject) => {\n        url = url.replace(/^http/, \"ws\");\n        let webSocket;\n        const cookies = _this._httpClient.getCookieString(url);\n        let opened = false;\n        if (Platform.isNode || Platform.isReactNative) {\n          const headers = {};\n          const [name, value] = getUserAgentHeader();\n          headers[name] = value;\n          if (token) {\n            headers[HeaderNames.Authorization] = `Bearer ${token}`;\n          }\n          if (cookies) {\n            headers[HeaderNames.Cookie] = cookies;\n          }\n          // Only pass headers when in non-browser environments\n          webSocket = new _this._webSocketConstructor(url, undefined, {\n            headers: {\n              ...headers,\n              ..._this._headers\n            }\n          });\n        } else {\n          if (token) {\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\n          }\n        }\n        if (!webSocket) {\n          // Chrome is not happy with passing 'undefined' as protocol\n          webSocket = new _this._webSocketConstructor(url);\n        }\n        if (transferFormat === TransferFormat.Binary) {\n          webSocket.binaryType = \"arraybuffer\";\n        }\n        webSocket.onopen = _event => {\n          _this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\n          _this._webSocket = webSocket;\n          opened = true;\n          resolve();\n        };\n        webSocket.onerror = event => {\n          let error = null;\n          // ErrorEvent is a browser only type we need to check if the type exists before using it\n          if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\n            error = event.error;\n          } else {\n            error = \"There was an error with the transport\";\n          }\n          _this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\n        };\n        webSocket.onmessage = message => {\n          _this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, _this._logMessageContent)}.`);\n          if (_this.onreceive) {\n            try {\n              _this.onreceive(message.data);\n            } catch (error) {\n              _this._close(error);\n              return;\n            }\n          }\n        };\n        webSocket.onclose = event => {\n          // Don't call close handler if connection was never established\n          // We'll reject the connect call instead\n          if (opened) {\n            _this._close(event);\n          } else {\n            let error = null;\n            // ErrorEvent is a browser only type we need to check if the type exists before using it\n            if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\n              error = event.error;\n            } else {\n              error = \"WebSocket failed to connect. The connection could not be found on the server,\" + \" either the endpoint may not be a SignalR endpoint,\" + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\" + \" If you have multiple servers check that sticky sessions are enabled.\";\n            }\n            reject(new Error(error));\n          }\n        };\n      });\n    })();\n  }\n  send(data) {\n    if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\n      this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\n      this._webSocket.send(data);\n      return Promise.resolve();\n    }\n    return Promise.reject(\"WebSocket is not in the OPEN state\");\n  }\n  stop() {\n    if (this._webSocket) {\n      // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\n      // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\n      this._close(undefined);\n    }\n    return Promise.resolve();\n  }\n  _close(event) {\n    // webSocket will be null if the transport did not start successfully\n    if (this._webSocket) {\n      // Clear websocket handlers because we are considering the socket closed now\n      this._webSocket.onclose = () => {};\n      this._webSocket.onmessage = () => {};\n      this._webSocket.onerror = () => {};\n      this._webSocket.close();\n      this._webSocket = undefined;\n    }\n    this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\n    if (this.onclose) {\n      if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\n        this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\n      } else if (event instanceof Error) {\n        this.onclose(event);\n      } else {\n        this.onclose();\n      }\n    }\n  }\n  _isCloseEvent(event) {\n    return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "LogLevel", "TransferFormat", "Arg", "getDataDetail", "getUserAgentHeader", "Platform", "WebSocketTransport", "constructor", "httpClient", "accessTokenFactory", "logger", "logMessageContent", "webSocketConstructor", "headers", "_logger", "_accessTokenFactory", "_logMessageContent", "_webSocketConstructor", "_httpClient", "onreceive", "onclose", "_headers", "connect", "url", "transferFormat", "_this", "_asyncToGenerator", "isRequired", "isIn", "log", "Trace", "token", "Promise", "resolve", "reject", "replace", "webSocket", "cookies", "getCookieString", "opened", "isNode", "isReactNative", "name", "value", "Authorization", "<PERSON><PERSON>", "undefined", "indexOf", "encodeURIComponent", "Binary", "binaryType", "onopen", "_event", "Information", "_webSocket", "onerror", "event", "error", "ErrorEvent", "onmessage", "message", "data", "_close", "Error", "send", "readyState", "OPEN", "stop", "close", "_isCloseEvent", "<PERSON><PERSON><PERSON>", "code", "reason"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/WebSocketTransport.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n/** @private */\r\nexport class WebSocketTransport {\r\n    constructor(httpClient, accessTokenFactory, logger, logMessageContent, webSocketConstructor, headers) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n        let token;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n            webSocket.onopen = (_event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n            webSocket.onerror = (event) => {\r\n                let error = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                }\r\n                else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n            webSocket.onmessage = (message) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    }\r\n                    catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n            webSocket.onclose = (event) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                }\r\n                else {\r\n                    let error = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    }\r\n                    else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the endpoint may not be a SignalR endpoint,\"\r\n                            + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n    send(data) {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n    stop() {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n    _close(event) {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => { };\r\n            this._webSocket.onmessage = () => { };\r\n            this._webSocket.onerror = () => { };\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            }\r\n            else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            }\r\n            else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n    _isCloseEvent(event) {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,QAAQ,QAAQ,SAAS;AAC1E;AACA,OAAO,MAAMC,kBAAkB,CAAC;EAC5BC,WAAWA,CAACC,UAAU,EAAEC,kBAAkB,EAAEC,MAAM,EAAEC,iBAAiB,EAAEC,oBAAoB,EAAEC,OAAO,EAAE;IAClG,IAAI,CAACC,OAAO,GAAGJ,MAAM;IACrB,IAAI,CAACK,mBAAmB,GAAGN,kBAAkB;IAC7C,IAAI,CAACO,kBAAkB,GAAGL,iBAAiB;IAC3C,IAAI,CAACM,qBAAqB,GAAGL,oBAAoB;IACjD,IAAI,CAACM,WAAW,GAAGV,UAAU;IAC7B,IAAI,CAACW,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAGR,OAAO;EAC3B;EACMS,OAAOA,CAACC,GAAG,EAAEC,cAAc,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/BxB,GAAG,CAACyB,UAAU,CAACJ,GAAG,EAAE,KAAK,CAAC;MAC1BrB,GAAG,CAACyB,UAAU,CAACH,cAAc,EAAE,gBAAgB,CAAC;MAChDtB,GAAG,CAAC0B,IAAI,CAACJ,cAAc,EAAEvB,cAAc,EAAE,gBAAgB,CAAC;MAC1DwB,KAAI,CAACX,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAAC8B,KAAK,EAAE,oCAAoC,CAAC;MACtE,IAAIC,KAAK;MACT,IAAIN,KAAI,CAACV,mBAAmB,EAAE;QAC1BgB,KAAK,SAASN,KAAI,CAACV,mBAAmB,CAAC,CAAC;MAC5C;MACA,OAAO,IAAIiB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpCX,GAAG,GAAGA,GAAG,CAACY,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC;QAChC,IAAIC,SAAS;QACb,MAAMC,OAAO,GAAGZ,KAAI,CAACP,WAAW,CAACoB,eAAe,CAACf,GAAG,CAAC;QACrD,IAAIgB,MAAM,GAAG,KAAK;QAClB,IAAIlC,QAAQ,CAACmC,MAAM,IAAInC,QAAQ,CAACoC,aAAa,EAAE;UAC3C,MAAM5B,OAAO,GAAG,CAAC,CAAC;UAClB,MAAM,CAAC6B,IAAI,EAAEC,KAAK,CAAC,GAAGvC,kBAAkB,CAAC,CAAC;UAC1CS,OAAO,CAAC6B,IAAI,CAAC,GAAGC,KAAK;UACrB,IAAIZ,KAAK,EAAE;YACPlB,OAAO,CAACd,WAAW,CAAC6C,aAAa,CAAC,GAAI,UAASb,KAAM,EAAC;UAC1D;UACA,IAAIM,OAAO,EAAE;YACTxB,OAAO,CAACd,WAAW,CAAC8C,MAAM,CAAC,GAAGR,OAAO;UACzC;UACA;UACAD,SAAS,GAAG,IAAIX,KAAI,CAACR,qBAAqB,CAACM,GAAG,EAAEuB,SAAS,EAAE;YACvDjC,OAAO,EAAE;cAAE,GAAGA,OAAO;cAAE,GAAGY,KAAI,CAACJ;YAAS;UAC5C,CAAC,CAAC;QACN,CAAC,MACI;UACD,IAAIU,KAAK,EAAE;YACPR,GAAG,IAAI,CAACA,GAAG,CAACwB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAK,gBAAeC,kBAAkB,CAACjB,KAAK,CAAE,EAAC;UAC3F;QACJ;QACA,IAAI,CAACK,SAAS,EAAE;UACZ;UACAA,SAAS,GAAG,IAAIX,KAAI,CAACR,qBAAqB,CAACM,GAAG,CAAC;QACnD;QACA,IAAIC,cAAc,KAAKvB,cAAc,CAACgD,MAAM,EAAE;UAC1Cb,SAAS,CAACc,UAAU,GAAG,aAAa;QACxC;QACAd,SAAS,CAACe,MAAM,GAAIC,MAAM,IAAK;UAC3B3B,KAAI,CAACX,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAACqD,WAAW,EAAG,0BAAyB9B,GAAI,GAAE,CAAC;UACxEE,KAAI,CAAC6B,UAAU,GAAGlB,SAAS;UAC3BG,MAAM,GAAG,IAAI;UACbN,OAAO,CAAC,CAAC;QACb,CAAC;QACDG,SAAS,CAACmB,OAAO,GAAIC,KAAK,IAAK;UAC3B,IAAIC,KAAK,GAAG,IAAI;UAChB;UACA,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAIF,KAAK,YAAYE,UAAU,EAAE;YAClED,KAAK,GAAGD,KAAK,CAACC,KAAK;UACvB,CAAC,MACI;YACDA,KAAK,GAAG,uCAAuC;UACnD;UACAhC,KAAI,CAACX,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAACqD,WAAW,EAAG,0BAAyBI,KAAM,GAAE,CAAC;QAC9E,CAAC;QACDrB,SAAS,CAACuB,SAAS,GAAIC,OAAO,IAAK;UAC/BnC,KAAI,CAACX,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAAC8B,KAAK,EAAG,yCAAwC3B,aAAa,CAACyD,OAAO,CAACC,IAAI,EAAEpC,KAAI,CAACT,kBAAkB,CAAE,GAAE,CAAC;UAClI,IAAIS,KAAI,CAACN,SAAS,EAAE;YAChB,IAAI;cACAM,KAAI,CAACN,SAAS,CAACyC,OAAO,CAACC,IAAI,CAAC;YAChC,CAAC,CACD,OAAOJ,KAAK,EAAE;cACVhC,KAAI,CAACqC,MAAM,CAACL,KAAK,CAAC;cAClB;YACJ;UACJ;QACJ,CAAC;QACDrB,SAAS,CAAChB,OAAO,GAAIoC,KAAK,IAAK;UAC3B;UACA;UACA,IAAIjB,MAAM,EAAE;YACRd,KAAI,CAACqC,MAAM,CAACN,KAAK,CAAC;UACtB,CAAC,MACI;YACD,IAAIC,KAAK,GAAG,IAAI;YAChB;YACA,IAAI,OAAOC,UAAU,KAAK,WAAW,IAAIF,KAAK,YAAYE,UAAU,EAAE;cAClED,KAAK,GAAGD,KAAK,CAACC,KAAK;YACvB,CAAC,MACI;cACDA,KAAK,GAAG,+EAA+E,GACjF,qDAAqD,GACrD,2FAA2F,GAC3F,uEAAuE;YACjF;YACAvB,MAAM,CAAC,IAAI6B,KAAK,CAACN,KAAK,CAAC,CAAC;UAC5B;QACJ,CAAC;MACL,CAAC,CAAC;IAAC;EACP;EACAO,IAAIA,CAACH,IAAI,EAAE;IACP,IAAI,IAAI,CAACP,UAAU,IAAI,IAAI,CAACA,UAAU,CAACW,UAAU,KAAK,IAAI,CAAChD,qBAAqB,CAACiD,IAAI,EAAE;MACnF,IAAI,CAACpD,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAAC8B,KAAK,EAAG,wCAAuC3B,aAAa,CAAC0D,IAAI,EAAE,IAAI,CAAC7C,kBAAkB,CAAE,GAAE,CAAC;MACzH,IAAI,CAACsC,UAAU,CAACU,IAAI,CAACH,IAAI,CAAC;MAC1B,OAAO7B,OAAO,CAACC,OAAO,CAAC,CAAC;IAC5B;IACA,OAAOD,OAAO,CAACE,MAAM,CAAC,oCAAoC,CAAC;EAC/D;EACAiC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACb,UAAU,EAAE;MACjB;MACA;MACA,IAAI,CAACQ,MAAM,CAAChB,SAAS,CAAC;IAC1B;IACA,OAAOd,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACA6B,MAAMA,CAACN,KAAK,EAAE;IACV;IACA,IAAI,IAAI,CAACF,UAAU,EAAE;MACjB;MACA,IAAI,CAACA,UAAU,CAAClC,OAAO,GAAG,MAAM,CAAE,CAAC;MACnC,IAAI,CAACkC,UAAU,CAACK,SAAS,GAAG,MAAM,CAAE,CAAC;MACrC,IAAI,CAACL,UAAU,CAACC,OAAO,GAAG,MAAM,CAAE,CAAC;MACnC,IAAI,CAACD,UAAU,CAACc,KAAK,CAAC,CAAC;MACvB,IAAI,CAACd,UAAU,GAAGR,SAAS;IAC/B;IACA,IAAI,CAAChC,OAAO,CAACe,GAAG,CAAC7B,QAAQ,CAAC8B,KAAK,EAAE,uCAAuC,CAAC;IACzE,IAAI,IAAI,CAACV,OAAO,EAAE;MACd,IAAI,IAAI,CAACiD,aAAa,CAACb,KAAK,CAAC,KAAKA,KAAK,CAACc,QAAQ,KAAK,KAAK,IAAId,KAAK,CAACe,IAAI,KAAK,IAAI,CAAC,EAAE;QAChF,IAAI,CAACnD,OAAO,CAAC,IAAI2C,KAAK,CAAE,sCAAqCP,KAAK,CAACe,IAAK,KAAIf,KAAK,CAACgB,MAAM,IAAI,iBAAkB,IAAG,CAAC,CAAC;MACvH,CAAC,MACI,IAAIhB,KAAK,YAAYO,KAAK,EAAE;QAC7B,IAAI,CAAC3C,OAAO,CAACoC,KAAK,CAAC;MACvB,CAAC,MACI;QACD,IAAI,CAACpC,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACAiD,aAAaA,CAACb,KAAK,EAAE;IACjB,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACc,QAAQ,KAAK,SAAS,IAAI,OAAOd,KAAK,CAACe,IAAI,KAAK,QAAQ;EACzF;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}