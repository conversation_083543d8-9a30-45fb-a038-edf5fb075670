{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, Directive, Injectable, Optional, NgZone, ViewChildren, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { fromEvent, Subject, ReplaySubject, BehaviorSubject, combineLatest, merge, EMPTY, of } from 'rxjs';\nimport { takeUntil, map, distinctUntilChanged, debounceTime, skip, filter, switchMap, startWith, delay, mergeMap } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { InputBoolean, arraysEqual, isNil, measureScrollbar } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownDirective, NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport { NgTemplateOutlet, NgIf, NgForOf, AsyncPipe, NgStyle } from '@angular/common';\nimport * as i6 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i7 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i5 from 'ng-zorro-antd/checkbox';\nimport { NzCheckboxModule } from 'ng-zorro-antd/checkbox';\nimport * as i2$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzRadioComponent } from 'ng-zorro-antd/radio';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i3 from 'ng-zorro-antd/menu';\nimport * as i8 from 'ng-zorro-antd/core/transition-patch';\nimport * as i9 from 'ng-zorro-antd/core/wave';\nimport * as i3$1 from '@angular/cdk/scrolling';\nimport { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';\nimport * as i2$2 from 'ng-zorro-antd/empty';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport * as i1$2 from 'ng-zorro-antd/cdk/resize-observer';\nimport * as i1$3 from '@angular/cdk/platform';\nimport * as i6$1 from 'ng-zorro-antd/pagination';\nimport { NzPaginationModule } from 'ng-zorro-antd/pagination';\nimport { NzSpinComponent } from 'ng-zorro-antd/spin';\nimport * as i1$4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i5$1 from '@angular/cdk/bidi';\nconst _c0 = [\"*\"];\nfunction NzTableFilterComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableFilterComponent_ng_container_2_li_7_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_ng_container_2_li_7_label_1_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_li_7_label_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableFilterComponent_ng_container_2_li_7_label_2_Template_label_ngModelChange_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const f_r4 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const f_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngModel\", f_r4.checked);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_li_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 12);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_li_7_Template_li_click_0_listener() {\n      const f_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.check(f_r4));\n    });\n    i0.ɵɵtemplate(1, NzTableFilterComponent_ng_container_2_li_7_label_1_Template, 1, 1, \"label\", 13)(2, NzTableFilterComponent_ng_container_2_li_7_label_2_Template, 1, 1, \"label\", 14);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const f_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"nzSelected\", f_r4.checked);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.filterMultiple);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filterMultiple);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(f_r4.text);\n  }\n}\nfunction NzTableFilterComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"nz-filter-trigger\", 4);\n    i0.ɵɵlistener(\"nzVisibleChange\", function NzTableFilterComponent_ng_container_2_Template_nz_filter_trigger_nzVisibleChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onVisibleChange($event));\n    });\n    i0.ɵɵelement(2, \"span\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 0)(5, \"div\", 6)(6, \"ul\", 7);\n    i0.ɵɵtemplate(7, NzTableFilterComponent_ng_container_2_li_7_Template, 5, 4, \"li\", 8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 9)(9, \"button\", 10);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.reset());\n    });\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function NzTableFilterComponent_ng_container_2_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.confirm());\n    });\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const filterMenu_r7 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzVisible\", ctx_r1.isVisible)(\"nzActive\", ctx_r1.isChecked)(\"nzDropdownMenu\", filterMenu_r7);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listOfParsedFilter)(\"ngForTrackBy\", ctx_r1.trackByValue);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", !ctx_r1.isChecked);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.locale.filterReset, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.locale.filterConfirm);\n  }\n}\nfunction NzTableSelectionComponent_label_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 3);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTableSelectionComponent_label_0_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"ant-table-selection-select-all-custom\", ctx_r1.showRowSelection);\n    i0.ɵɵproperty(\"ngModel\", ctx_r1.checked)(\"nzDisabled\", ctx_r1.disabled)(\"nzIndeterminate\", ctx_r1.indeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.label);\n  }\n}\nfunction NzTableSelectionComponent_div_1_li_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 9);\n    i0.ɵɵlistener(\"click\", function NzTableSelectionComponent_div_1_li_6_Template_li_click_0_listener() {\n      const selection_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      return i0.ɵɵresetView(selection_r4.onSelect());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selection_r4 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", selection_r4.text, \" \");\n  }\n}\nfunction NzTableSelectionComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"span\", 5);\n    i0.ɵɵelement(2, \"span\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"nz-dropdown-menu\", null, 0)(5, \"ul\", 7);\n    i0.ɵɵtemplate(6, NzTableSelectionComponent_div_1_li_6_Template, 2, 1, \"li\", 8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const selectionMenu_r5 = i0.ɵɵreference(4);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzDropdownMenu\", selectionMenu_r5);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.listOfSelections);\n  }\n}\nfunction NzTableSortersComponent_ng_template_1_Template(rf, ctx) {}\nfunction NzTableSortersComponent_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"ascend\");\n  }\n}\nfunction NzTableSortersComponent_span_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵclassProp(\"active\", ctx_r0.sortOrder === \"descend\");\n  }\n}\nconst _c1 = [\"nzChecked\", \"\"];\nfunction NzTdAddOnComponent_ng_container_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"expandChange\", function NzTdAddOnComponent_ng_container_0_ng_template_2_Template_button_expandChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onExpandChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"expand\", ctx_r1.nzExpand)(\"spaceMode\", !ctx_r1.nzShowExpand);\n  }\n}\nfunction NzTdAddOnComponent_ng_container_0_ng_container_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTdAddOnComponent_ng_container_0_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTdAddOnComponent_ng_container_0_ng_container_4_ng_template_1_Template, 0, 0, \"ng-template\", 6);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzExpandIcon);\n  }\n}\nfunction NzTdAddOnComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"nz-row-indent\", 3);\n    i0.ɵɵtemplate(2, NzTdAddOnComponent_ng_container_0_ng_template_2_Template, 1, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzTdAddOnComponent_ng_container_0_ng_container_4_Template, 2, 1, \"ng-container\", 4);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const rowExpand_r3 = i0.ɵɵreference(3);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"indentSize\", ctx_r1.nzIndentSize);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nzExpandIcon)(\"ngIfElse\", rowExpand_r3);\n  }\n}\nfunction NzTdAddOnComponent_label_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 7);\n    i0.ɵɵlistener(\"ngModelChange\", function NzTdAddOnComponent_label_1_Template_label_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onCheckedChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"nzDisabled\", ctx_r1.nzDisabled)(\"ngModel\", ctx_r1.nzChecked)(\"nzIndeterminate\", ctx_r1.nzIndeterminate);\n    i0.ɵɵattribute(\"aria-label\", ctx_r1.nzLabel);\n  }\n}\nconst _c2 = [\"nzColumnKey\", \"\"];\nconst _c3 = [[[\"\", \"nz-th-extra\", \"\"]], [[\"nz-filter-trigger\"]], \"*\"];\nconst _c4 = [\"[nz-th-extra]\", \"nz-filter-trigger\", \"*\"];\nfunction NzThAddOnComponent_nz_table_filter_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-table-filter\", 5);\n    i0.ɵɵlistener(\"filterChange\", function NzThAddOnComponent_nz_table_filter_0_Template_nz_table_filter_filterChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.onFilterValueChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const notFilterTemplate_r3 = i0.ɵɵreference(2);\n    const extraTemplate_r4 = i0.ɵɵreference(4);\n    i0.ɵɵproperty(\"contentTemplate\", notFilterTemplate_r3)(\"extraTemplate\", extraTemplate_r4)(\"customFilter\", ctx_r1.nzCustomFilter)(\"filterMultiple\", ctx_r1.nzFilterMultiple)(\"listOfFilter\", ctx_r1.nzFilters);\n  }\n}\nfunction NzThAddOnComponent_ng_template_1_ng_template_0_Template(rf, ctx) {}\nfunction NzThAddOnComponent_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzThAddOnComponent_ng_template_1_ng_template_0_Template, 0, 0, \"ng-template\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const sortTemplate_r5 = i0.ɵɵreference(6);\n    const contentTemplate_r6 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.nzShowSort ? sortTemplate_r5 : contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n    i0.ɵɵprojection(1, 1);\n  }\n}\nfunction NzThAddOnComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-sorters\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r6 = i0.ɵɵreference(8);\n    i0.ɵɵproperty(\"sortOrder\", ctx_r1.sortOrder)(\"sortDirections\", ctx_r1.sortDirections)(\"contentTemplate\", contentTemplate_r6);\n  }\n}\nfunction NzThAddOnComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nconst _c5 = [\"nzSelections\", \"\"];\nconst _c6 = [\"nz-table-content\", \"\"];\nfunction NzTableContentComponent_col_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"col\");\n  }\n  if (rf & 2) {\n    const width_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"width\", width_r1)(\"min-width\", width_r1);\n  }\n}\nfunction NzTableContentComponent_thead_1_ng_template_1_Template(rf, ctx) {}\nfunction NzTableContentComponent_thead_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"thead\", 3);\n    i0.ɵɵtemplate(1, NzTableContentComponent_thead_1_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r1.theadTemplate);\n  }\n}\nfunction NzTableContentComponent_ng_template_2_Template(rf, ctx) {}\nconst _c7 = [\"tdElement\"];\nconst _c8 = [\"nz-table-fixed-row\", \"\"];\nfunction NzTableFixedRowComponent_div_2_ng_template_2_Template(rf, ctx) {}\nfunction NzTableFixedRowComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4);\n    i0.ɵɵpipe(1, \"async\");\n    i0.ɵɵtemplate(2, NzTableFixedRowComponent_div_2_ng_template_2_Template, 0, 0, \"ng-template\", 5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const contentTemplate_r2 = i0.ɵɵreference(5);\n    i0.ɵɵstyleProp(\"width\", i0.ɵɵpipeBind1(1, 3, ctx_r0.hostWidth$), \"px\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r2);\n  }\n}\nfunction NzTableFixedRowComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c9 = [\"nz-table-measure-row\", \"\"];\nfunction NzTrMeasureComponent_td_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"td\", 2, 0);\n  }\n}\nfunction NzTbodyComponent_ng_container_0_tr_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 3);\n    i0.ɵɵlistener(\"listOfAutoWidth\", function NzTbodyComponent_ng_container_0_tr_1_Template_tr_listOfAutoWidth_0_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onListOfAutoWidthChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const listOfMeasureColumn_r3 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵproperty(\"listOfMeasureColumn\", listOfMeasureColumn_r3);\n  }\n}\nfunction NzTbodyComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTbodyComponent_ng_container_0_tr_1_Template, 1, 1, \"tr\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const listOfMeasureColumn_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInsideTable && listOfMeasureColumn_r3.length);\n  }\n}\nfunction NzTbodyComponent_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\", 4);\n    i0.ɵɵelement(1, \"nz-embed-empty\", 5);\n    i0.ɵɵpipe(2, \"async\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"specificContent\", i0.ɵɵpipeBind1(2, 1, ctx_r1.noResult$));\n  }\n}\nconst _c10 = [\"tableHeaderElement\"];\nconst _c11 = [\"tableBodyElement\"];\nconst _c12 = (a0, a1) => ({\n  $implicit: a0,\n  index: a1\n});\nfunction NzTableInnerScrollComponent_ng_container_0_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 8, 1);\n    i0.ɵɵelement(2, \"table\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"contentTemplate\", ctx_r0.contentTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_ng_template_1_Template(rf, ctx) {}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_ng_template_1_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const item_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.virtualTemplate)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction2(2, _c12, item_r2, i_r3));\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"cdk-virtual-scroll-viewport\", 10, 1)(2, \"table\", 11)(3, \"tbody\");\n    i0.ɵɵtemplate(4, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_Template, 2, 5, \"ng-container\", 12);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"height\", ctx_r0.data.length ? ctx_r0.scrollY : ctx_r0.noDateVirtualHeight);\n    i0.ɵɵproperty(\"itemSize\", ctx_r0.virtualItemSize)(\"maxBufferPx\", ctx_r0.virtualMaxBufferPx)(\"minBufferPx\", ctx_r0.virtualMinBufferPx);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"cdkVirtualForOf\", ctx_r0.data)(\"cdkVirtualForTrackBy\", ctx_r0.virtualForTrackBy);\n  }\n}\nfunction NzTableInnerScrollComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 4, 0);\n    i0.ɵɵelement(3, \"table\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, NzTableInnerScrollComponent_ng_container_0_div_4_Template, 3, 4, \"div\", 6)(5, NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_Template, 5, 9, \"cdk-virtual-scroll-viewport\", 7);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.headerStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.virtualTemplate);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.virtualTemplate);\n  }\n}\nfunction NzTableInnerScrollComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14, 1);\n    i0.ɵɵelement(2, \"table\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngStyle\", ctx_r0.bodyStyleMap);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"scrollX\", ctx_r0.scrollX)(\"listOfColWidth\", ctx_r0.listOfColWidth)(\"theadTemplate\", ctx_r0.theadTemplate)(\"contentTemplate\", ctx_r0.contentTemplate);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.title);\n  }\n}\nfunction NzTableTitleFooterComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r0.footer);\n  }\n}\nfunction NzTableComponent_ng_container_1_ng_template_1_Template(rf, ctx) {}\nfunction NzTableComponent_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableComponent_ng_container_1_ng_template_1_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_nz_table_title_footer_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"title\", ctx_r1.nzTitle);\n  }\n}\nfunction NzTableComponent_nz_table_inner_scroll_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-scroll\", 12);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const tableMainElement_r3 = i0.ɵɵreference(3);\n    const contentTemplate_r4 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"data\", ctx_r1.data)(\"scrollX\", ctx_r1.scrollX)(\"scrollY\", ctx_r1.scrollY)(\"contentTemplate\", contentTemplate_r4)(\"listOfColWidth\", ctx_r1.listOfAutoColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"verticalScrollBarWidth\", ctx_r1.verticalScrollBarWidth)(\"virtualTemplate\", ctx_r1.nzVirtualScrollDirective ? ctx_r1.nzVirtualScrollDirective.templateRef : null)(\"virtualItemSize\", ctx_r1.nzVirtualItemSize)(\"virtualMaxBufferPx\", ctx_r1.nzVirtualMaxBufferPx)(\"virtualMinBufferPx\", ctx_r1.nzVirtualMinBufferPx)(\"tableMainElement\", tableMainElement_r3)(\"virtualForTrackBy\", ctx_r1.nzVirtualForTrackBy);\n  }\n}\nfunction NzTableComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-inner-default\", 13);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    const contentTemplate_r4 = i0.ɵɵreference(13);\n    i0.ɵɵproperty(\"tableLayout\", ctx_r1.nzTableLayout)(\"listOfColWidth\", ctx_r1.listOfManualColWidth)(\"theadTemplate\", ctx_r1.theadTemplate)(\"contentTemplate\", contentTemplate_r4);\n  }\n}\nfunction NzTableComponent_nz_table_title_footer_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-table-title-footer\", 14);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"footer\", ctx_r1.nzFooter);\n  }\n}\nfunction NzTableComponent_ng_container_9_ng_template_1_Template(rf, ctx) {}\nfunction NzTableComponent_ng_container_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTableComponent_ng_container_9_ng_template_1_Template, 0, 0, \"ng-template\", 10);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const paginationTemplate_r1 = i0.ɵɵreference(11);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", paginationTemplate_r1);\n  }\n}\nfunction NzTableComponent_ng_template_10_nz_pagination_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"nz-pagination\", 16);\n    i0.ɵɵlistener(\"nzPageSizeChange\", function NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageSizeChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageSizeChange($event));\n    })(\"nzPageIndexChange\", function NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageIndexChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.onPageIndexChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"hidden\", !ctx_r1.showPagination)(\"nzShowSizeChanger\", ctx_r1.nzShowSizeChanger)(\"nzPageSizeOptions\", ctx_r1.nzPageSizeOptions)(\"nzItemRender\", ctx_r1.nzItemRender)(\"nzShowQuickJumper\", ctx_r1.nzShowQuickJumper)(\"nzHideOnSinglePage\", ctx_r1.nzHideOnSinglePage)(\"nzShowTotal\", ctx_r1.nzShowTotal)(\"nzSize\", ctx_r1.nzPaginationType === \"small\" ? \"small\" : ctx_r1.nzSize === \"default\" ? \"default\" : \"small\")(\"nzPageSize\", ctx_r1.nzPageSize)(\"nzTotal\", ctx_r1.nzTotal)(\"nzSimple\", ctx_r1.nzSimple)(\"nzPageIndex\", ctx_r1.nzPageIndex);\n  }\n}\nfunction NzTableComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzTableComponent_ng_template_10_nz_pagination_0_Template, 1, 12, \"nz-pagination\", 15);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.nzShowPagination && ctx_r1.data.length);\n  }\n}\nfunction NzTableComponent_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nconst _c13 = [\"contentTemplate\"];\nfunction NzTheadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzTheadComponent_ng_container_2_ng_template_1_Template(rf, ctx) {}\nfunction NzTheadComponent_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzTheadComponent_ng_container_2_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const contentTemplate_r1 = i0.ɵɵreference(1);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", contentTemplate_r1);\n  }\n}\nconst NZ_CONFIG_MODULE_NAME$1 = 'filterTrigger';\nclass NzFilterTriggerComponent {\n  onVisibleChange(visible) {\n    this.nzVisible = visible;\n    this.nzVisibleChange.next(visible);\n  }\n  hide() {\n    this.nzVisible = false;\n    this.cdr.markForCheck();\n  }\n  show() {\n    this.nzVisible = true;\n    this.cdr.markForCheck();\n  }\n  constructor(nzConfigService, ngZone, cdr, destroy$) {\n    this.nzConfigService = nzConfigService;\n    this.ngZone = ngZone;\n    this.cdr = cdr;\n    this.destroy$ = destroy$;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME$1;\n    this.nzActive = false;\n    this.nzVisible = false;\n    this.nzBackdrop = false;\n    this.nzVisibleChange = new EventEmitter();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.nzDropdown.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        event.stopPropagation();\n      });\n    });\n  }\n  static {\n    this.ɵfac = function NzFilterTriggerComponent_Factory(t) {\n      return new (t || NzFilterTriggerComponent)(i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzFilterTriggerComponent,\n      selectors: [[\"nz-filter-trigger\"]],\n      viewQuery: function NzFilterTriggerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzDropDownDirective, 7, ElementRef);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzDropdown = _t.first);\n        }\n      },\n      inputs: {\n        nzActive: \"nzActive\",\n        nzDropdownMenu: \"nzDropdownMenu\",\n        nzVisible: \"nzVisible\",\n        nzBackdrop: \"nzBackdrop\"\n      },\n      outputs: {\n        nzVisibleChange: \"nzVisibleChange\"\n      },\n      exportAs: [\"nzFilterTrigger\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 8,\n      consts: [[\"nz-dropdown\", \"\", \"nzTrigger\", \"click\", \"nzPlacement\", \"bottomRight\", 1, \"ant-table-filter-trigger\", 3, \"nzVisibleChange\", \"nzBackdrop\", \"nzClickHide\", \"nzDropdownMenu\", \"nzVisible\"]],\n      template: function NzFilterTriggerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵlistener(\"nzVisibleChange\", function NzFilterTriggerComponent_Template_span_nzVisibleChange_0_listener($event) {\n            return ctx.onVisibleChange($event);\n          });\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"active\", ctx.nzActive)(\"ant-table-filter-open\", ctx.nzVisible);\n          i0.ɵɵproperty(\"nzBackdrop\", ctx.nzBackdrop)(\"nzClickHide\", false)(\"nzDropdownMenu\", ctx.nzDropdownMenu)(\"nzVisible\", ctx.nzVisible);\n        }\n      },\n      dependencies: [NzDropDownModule, i4.NzDropDownDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([WithConfig(), InputBoolean()], NzFilterTriggerComponent.prototype, \"nzBackdrop\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzFilterTriggerComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-filter-trigger',\n      exportAs: `nzFilterTrigger`,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span\n      nz-dropdown\n      class=\"ant-table-filter-trigger\"\n      nzTrigger=\"click\"\n      nzPlacement=\"bottomRight\"\n      [nzBackdrop]=\"nzBackdrop\"\n      [nzClickHide]=\"false\"\n      [nzDropdownMenu]=\"nzDropdownMenu\"\n      [class.active]=\"nzActive\"\n      [class.ant-table-filter-open]=\"nzVisible\"\n      [nzVisible]=\"nzVisible\"\n      (nzVisibleChange)=\"onVisibleChange($event)\"\n    >\n      <ng-content></ng-content>\n    </span>\n  `,\n      providers: [NzDestroyService],\n      imports: [NzDropDownModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1.NzConfigService\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzActive: [{\n      type: Input\n    }],\n    nzDropdownMenu: [{\n      type: Input\n    }],\n    nzVisible: [{\n      type: Input\n    }],\n    nzBackdrop: [{\n      type: Input\n    }],\n    nzVisibleChange: [{\n      type: Output\n    }],\n    nzDropdown: [{\n      type: ViewChild,\n      args: [NzDropDownDirective, {\n        static: true,\n        read: ElementRef\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFilterComponent {\n  trackByValue(_, item) {\n    return item.value;\n  }\n  check(filter) {\n    if (this.filterMultiple) {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => {\n        if (item === filter) {\n          return {\n            ...item,\n            checked: !filter.checked\n          };\n        } else {\n          return item;\n        }\n      });\n      filter.checked = !filter.checked;\n    } else {\n      this.listOfParsedFilter = this.listOfParsedFilter.map(item => ({\n        ...item,\n        checked: item === filter\n      }));\n    }\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n  }\n  confirm() {\n    this.isVisible = false;\n    this.emitFilterData();\n  }\n  reset() {\n    this.isVisible = false;\n    this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter, true);\n    this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    this.emitFilterData();\n  }\n  onVisibleChange(value) {\n    this.isVisible = value;\n    if (!value) {\n      this.emitFilterData();\n    } else {\n      this.listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    }\n  }\n  emitFilterData() {\n    const listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n    if (!arraysEqual(this.listOfChecked, listOfChecked)) {\n      if (this.filterMultiple) {\n        this.filterChange.emit(listOfChecked);\n      } else {\n        this.filterChange.emit(listOfChecked.length > 0 ? listOfChecked[0] : null);\n      }\n    }\n  }\n  parseListOfFilter(listOfFilter, reset) {\n    return listOfFilter.map(item => {\n      const checked = reset ? false : !!item.byDefault;\n      return {\n        text: item.text,\n        value: item.value,\n        checked\n      };\n    });\n  }\n  getCheckedStatus(listOfParsedFilter) {\n    return listOfParsedFilter.some(item => item.checked);\n  }\n  constructor(cdr, i18n) {\n    this.cdr = cdr;\n    this.i18n = i18n;\n    this.contentTemplate = null;\n    this.customFilter = false;\n    this.extraTemplate = null;\n    this.filterMultiple = true;\n    this.listOfFilter = [];\n    this.filterChange = new EventEmitter();\n    this.destroy$ = new Subject();\n    this.isChecked = false;\n    this.isVisible = false;\n    this.listOfParsedFilter = [];\n    this.listOfChecked = [];\n  }\n  ngOnInit() {\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Table');\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      listOfFilter\n    } = changes;\n    if (listOfFilter && this.listOfFilter && this.listOfFilter.length) {\n      this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter);\n      this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableFilterComponent_Factory(t) {\n      return new (t || NzTableFilterComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$1.NzI18nService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableFilterComponent,\n      selectors: [[\"nz-table-filter\"]],\n      hostAttrs: [1, \"ant-table-filter-column\"],\n      inputs: {\n        contentTemplate: \"contentTemplate\",\n        customFilter: \"customFilter\",\n        extraTemplate: \"extraTemplate\",\n        filterMultiple: \"filterMultiple\",\n        listOfFilter: \"listOfFilter\"\n      },\n      outputs: {\n        filterChange: \"filterChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 3,\n      consts: [[\"filterMenu\", \"nzDropdownMenu\"], [1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"nzVisibleChange\", \"nzVisible\", \"nzActive\", \"nzDropdownMenu\"], [\"nz-icon\", \"\", \"nzType\", \"filter\", \"nzTheme\", \"fill\"], [1, \"ant-table-filter-dropdown\"], [\"nz-menu\", \"\"], [\"nz-menu-item\", \"\", 3, \"nzSelected\", \"click\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"ant-table-filter-dropdown-btns\"], [\"nz-button\", \"\", \"nzType\", \"link\", \"nzSize\", \"small\", 3, \"click\", \"disabled\"], [\"nz-button\", \"\", \"nzType\", \"primary\", \"nzSize\", \"small\", 3, \"click\"], [\"nz-menu-item\", \"\", 3, \"click\", \"nzSelected\"], [\"nz-radio\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"ngModel\", \"ngModelChange\", 4, \"ngIf\"], [\"nz-radio\", \"\", 3, \"ngModelChange\", \"ngModel\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\"]],\n      template: function NzTableFilterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 1);\n          i0.ɵɵtemplate(1, NzTableFilterComponent_ng_template_1_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, NzTableFilterComponent_ng_container_2_Template, 13, 8, \"ng-container\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.customFilter)(\"ngIfElse\", ctx.extraTemplate);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NgIf, NzFilterTriggerComponent, NzIconModule, i2$1.NzIconDirective, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropdownMenuComponent, NgForOf, NzRadioComponent, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel, NzButtonModule, i7.NzButtonComponent, i8.ɵNzTransitionPatchDirective, i9.NzWaveDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFilterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-filter',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </span>\n    <ng-container *ngIf=\"!customFilter; else extraTemplate\">\n      <nz-filter-trigger\n        [nzVisible]=\"isVisible\"\n        [nzActive]=\"isChecked\"\n        [nzDropdownMenu]=\"filterMenu\"\n        (nzVisibleChange)=\"onVisibleChange($event)\"\n      >\n        <span nz-icon nzType=\"filter\" nzTheme=\"fill\"></span>\n      </nz-filter-trigger>\n      <nz-dropdown-menu #filterMenu=\"nzDropdownMenu\">\n        <div class=\"ant-table-filter-dropdown\">\n          <ul nz-menu>\n            <li\n              nz-menu-item\n              [nzSelected]=\"f.checked\"\n              *ngFor=\"let f of listOfParsedFilter; trackBy: trackByValue\"\n              (click)=\"check(f)\"\n            >\n              <label nz-radio *ngIf=\"!filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <label nz-checkbox *ngIf=\"filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <span>{{ f.text }}</span>\n            </li>\n          </ul>\n          <div class=\"ant-table-filter-dropdown-btns\">\n            <button nz-button nzType=\"link\" nzSize=\"small\" (click)=\"reset()\" [disabled]=\"!isChecked\">\n              {{ locale.filterReset }}\n            </button>\n            <button nz-button nzType=\"primary\" nzSize=\"small\" (click)=\"confirm()\">{{ locale.filterConfirm }}</button>\n          </div>\n        </div>\n      </nz-dropdown-menu>\n    </ng-container>\n  `,\n      host: {\n        class: 'ant-table-filter-column'\n      },\n      imports: [NgTemplateOutlet, NgIf, NzFilterTriggerComponent, NzIconModule, NzDropDownModule, NgForOf, NzRadioComponent, NzCheckboxModule, FormsModule, NzButtonModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$1.NzI18nService\n  }], {\n    contentTemplate: [{\n      type: Input\n    }],\n    customFilter: [{\n      type: Input\n    }],\n    extraTemplate: [{\n      type: Input\n    }],\n    filterMultiple: [{\n      type: Input\n    }],\n    listOfFilter: [{\n      type: Input\n    }],\n    filterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowExpandButtonDirective {\n  constructor() {\n    this.expand = false;\n    this.spaceMode = false;\n    this.expandChange = new EventEmitter();\n  }\n  onHostClick() {\n    if (!this.spaceMode) {\n      this.expand = !this.expand;\n      this.expandChange.next(this.expand);\n    }\n  }\n  static {\n    this.ɵfac = function NzRowExpandButtonDirective_Factory(t) {\n      return new (t || NzRowExpandButtonDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzRowExpandButtonDirective,\n      selectors: [[\"button\", \"nz-row-expand-button\", \"\"]],\n      hostAttrs: [1, \"ant-table-row-expand-icon\"],\n      hostVars: 7,\n      hostBindings: function NzRowExpandButtonDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function NzRowExpandButtonDirective_click_HostBindingHandler() {\n            return ctx.onHostClick();\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"type\", \"button\");\n          i0.ɵɵclassProp(\"ant-table-row-expand-icon-expanded\", !ctx.spaceMode && ctx.expand === true)(\"ant-table-row-expand-icon-collapsed\", !ctx.spaceMode && ctx.expand === false)(\"ant-table-row-expand-icon-spaced\", ctx.spaceMode);\n        }\n      },\n      inputs: {\n        expand: \"expand\",\n        spaceMode: \"spaceMode\"\n      },\n      outputs: {\n        expandChange: \"expandChange\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowExpandButtonDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'button[nz-row-expand-button]',\n      host: {\n        class: 'ant-table-row-expand-icon',\n        '[type]': `'button'`,\n        '[class.ant-table-row-expand-icon-expanded]': `!spaceMode && expand === true`,\n        '[class.ant-table-row-expand-icon-collapsed]': `!spaceMode && expand === false`,\n        '[class.ant-table-row-expand-icon-spaced]': 'spaceMode',\n        '(click)': 'onHostClick()'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    expand: [{\n      type: Input\n    }],\n    spaceMode: [{\n      type: Input\n    }],\n    expandChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowIndentDirective {\n  constructor() {\n    this.indentSize = 0;\n  }\n  static {\n    this.ɵfac = function NzRowIndentDirective_Factory(t) {\n      return new (t || NzRowIndentDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzRowIndentDirective,\n      selectors: [[\"nz-row-indent\"]],\n      hostAttrs: [1, \"ant-table-row-indent\"],\n      hostVars: 2,\n      hostBindings: function NzRowIndentDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"padding-left\", ctx.indentSize, \"px\");\n        }\n      },\n      inputs: {\n        indentSize: \"indentSize\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzRowIndentDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'nz-row-indent',\n      host: {\n        class: 'ant-table-row-indent',\n        '[style.padding-left.px]': 'indentSize'\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    indentSize: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSelectionComponent {\n  constructor() {\n    this.listOfSelections = [];\n    this.checked = false;\n    this.disabled = false;\n    this.indeterminate = false;\n    this.label = null;\n    this.showCheckbox = false;\n    this.showRowSelection = false;\n    this.checkedChange = new EventEmitter();\n  }\n  onCheckedChange(checked) {\n    this.checked = checked;\n    this.checkedChange.emit(checked);\n  }\n  static {\n    this.ɵfac = function NzTableSelectionComponent_Factory(t) {\n      return new (t || NzTableSelectionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableSelectionComponent,\n      selectors: [[\"nz-table-selection\"]],\n      hostAttrs: [1, \"ant-table-selection\"],\n      inputs: {\n        listOfSelections: \"listOfSelections\",\n        checked: \"checked\",\n        disabled: \"disabled\",\n        indeterminate: \"indeterminate\",\n        label: \"label\",\n        showCheckbox: \"showCheckbox\",\n        showRowSelection: \"showRowSelection\"\n      },\n      outputs: {\n        checkedChange: \"checkedChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"selectionMenu\", \"nzDropdownMenu\"], [\"nz-checkbox\", \"\", 3, \"ant-table-selection-select-all-custom\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\", \"ngModelChange\", 4, \"ngIf\"], [\"class\", \"ant-table-selection-extra\", 4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"ngModel\", \"nzDisabled\", \"nzIndeterminate\"], [1, \"ant-table-selection-extra\"], [\"nz-dropdown\", \"\", \"nzPlacement\", \"bottomLeft\", 1, \"ant-table-selection-down\", 3, \"nzDropdownMenu\"], [\"nz-icon\", \"\", \"nzType\", \"down\"], [\"nz-menu\", \"\", 1, \"ant-table-selection-menu\"], [\"nz-menu-item\", \"\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"nz-menu-item\", \"\", 3, \"click\"]],\n      template: function NzTableSelectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableSelectionComponent_label_0_Template, 1, 6, \"label\", 1)(1, NzTableSelectionComponent_div_1_Template, 7, 2, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.showCheckbox);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.showRowSelection);\n        }\n      },\n      dependencies: [NgIf, FormsModule, i6.NgControlStatus, i6.NgModel, NzCheckboxModule, i5.NzCheckboxComponent, NzDropDownModule, i3.NzMenuDirective, i3.NzMenuItemComponent, i4.NzDropDownDirective, i4.NzDropdownMenuComponent, NzIconModule, i2$1.NzIconDirective, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-selection',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <label\n      *ngIf=\"showCheckbox\"\n      nz-checkbox\n      [class.ant-table-selection-select-all-custom]=\"showRowSelection\"\n      [ngModel]=\"checked\"\n      [nzDisabled]=\"disabled\"\n      [nzIndeterminate]=\"indeterminate\"\n      [attr.aria-label]=\"label\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <div class=\"ant-table-selection-extra\" *ngIf=\"showRowSelection\">\n      <span nz-dropdown class=\"ant-table-selection-down\" nzPlacement=\"bottomLeft\" [nzDropdownMenu]=\"selectionMenu\">\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n      <nz-dropdown-menu #selectionMenu=\"nzDropdownMenu\">\n        <ul nz-menu class=\"ant-table-selection-menu\">\n          <li nz-menu-item *ngFor=\"let selection of listOfSelections\" (click)=\"selection.onSelect()\">\n            {{ selection.text }}\n          </li>\n        </ul>\n      </nz-dropdown-menu>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-selection'\n      },\n      imports: [NgIf, FormsModule, NzCheckboxModule, NzDropDownModule, NzIconModule, NgForOf],\n      standalone: true\n    }]\n  }], () => [], {\n    listOfSelections: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    indeterminate: [{\n      type: Input\n    }],\n    label: [{\n      type: Input\n    }],\n    showCheckbox: [{\n      type: Input\n    }],\n    showRowSelection: [{\n      type: Input\n    }],\n    checkedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSortersComponent {\n  constructor() {\n    this.sortDirections = ['ascend', 'descend', null];\n    this.sortOrder = null;\n    this.contentTemplate = null;\n    this.isUp = false;\n    this.isDown = false;\n  }\n  ngOnChanges(changes) {\n    const {\n      sortDirections\n    } = changes;\n    if (sortDirections) {\n      this.isUp = this.sortDirections.indexOf('ascend') !== -1;\n      this.isDown = this.sortDirections.indexOf('descend') !== -1;\n    }\n  }\n  static {\n    this.ɵfac = function NzTableSortersComponent_Factory(t) {\n      return new (t || NzTableSortersComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableSortersComponent,\n      selectors: [[\"nz-table-sorters\"]],\n      hostAttrs: [1, \"ant-table-column-sorters\"],\n      inputs: {\n        sortDirections: \"sortDirections\",\n        sortOrder: \"sortOrder\",\n        contentTemplate: \"contentTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 5,\n      consts: [[1, \"ant-table-column-title\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-column-sorter\"], [1, \"ant-table-column-sorter-inner\"], [\"nz-icon\", \"\", \"nzType\", \"caret-up\", \"class\", \"ant-table-column-sorter-up\", 3, \"active\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"caret-down\", \"class\", \"ant-table-column-sorter-down\", 3, \"active\", 4, \"ngIf\"], [\"nz-icon\", \"\", \"nzType\", \"caret-up\", 1, \"ant-table-column-sorter-up\"], [\"nz-icon\", \"\", \"nzType\", \"caret-down\", 1, \"ant-table-column-sorter-down\"]],\n      template: function NzTableSortersComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"span\", 0);\n          i0.ɵɵtemplate(1, NzTableSortersComponent_ng_template_1_Template, 0, 0, \"ng-template\", 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(2, \"span\", 2)(3, \"span\", 3);\n          i0.ɵɵtemplate(4, NzTableSortersComponent_span_4_Template, 1, 2, \"span\", 4)(5, NzTableSortersComponent_span_5_Template, 1, 2, \"span\", 5);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-table-column-sorter-full\", ctx.isDown && ctx.isUp);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.isUp);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isDown);\n        }\n      },\n      dependencies: [NzIconModule, i2$1.NzIconDirective, NgTemplateOutlet, NgIf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableSortersComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-sorters',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <span class=\"ant-table-column-title\"><ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template></span>\n    <span class=\"ant-table-column-sorter\" [class.ant-table-column-sorter-full]=\"isDown && isUp\">\n      <span class=\"ant-table-column-sorter-inner\">\n        <span\n          nz-icon\n          nzType=\"caret-up\"\n          *ngIf=\"isUp\"\n          class=\"ant-table-column-sorter-up\"\n          [class.active]=\"sortOrder === 'ascend'\"\n        ></span>\n        <span\n          nz-icon\n          nzType=\"caret-down\"\n          *ngIf=\"isDown\"\n          class=\"ant-table-column-sorter-down\"\n          [class.active]=\"sortOrder === 'descend'\"\n        ></span>\n      </span>\n    </span>\n  `,\n      host: {\n        class: 'ant-table-column-sorters'\n      },\n      imports: [NzIconModule, NgTemplateOutlet, NgIf],\n      standalone: true\n    }]\n  }], () => [], {\n    sortDirections: [{\n      type: Input\n    }],\n    sortOrder: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellFixedDirective {\n  setAutoLeftWidth(autoLeft) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'left', autoLeft);\n  }\n  setAutoRightWidth(autoRight) {\n    this.renderer.setStyle(this.elementRef.nativeElement, 'right', autoRight);\n  }\n  setIsFirstRight(isFirstRight) {\n    this.setFixClass(isFirstRight, 'ant-table-cell-fix-right-first');\n  }\n  setIsLastLeft(isLastLeft) {\n    this.setFixClass(isLastLeft, 'ant-table-cell-fix-left-last');\n  }\n  setFixClass(flag, className) {\n    // the setFixClass function may call many times, so remove it first.\n    this.renderer.removeClass(this.elementRef.nativeElement, className);\n    if (flag) {\n      this.renderer.addClass(this.elementRef.nativeElement, className);\n    }\n  }\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.nzRight = false;\n    this.nzLeft = false;\n    this.colspan = null;\n    this.colSpan = null;\n    this.changes$ = new Subject();\n    this.isAutoLeft = false;\n    this.isAutoRight = false;\n    this.isFixedLeft = false;\n    this.isFixedRight = false;\n    this.isFixed = false;\n  }\n  ngOnChanges() {\n    this.setIsFirstRight(false);\n    this.setIsLastLeft(false);\n    this.isAutoLeft = this.nzLeft === '' || this.nzLeft === true;\n    this.isAutoRight = this.nzRight === '' || this.nzRight === true;\n    this.isFixedLeft = this.nzLeft !== false;\n    this.isFixedRight = this.nzRight !== false;\n    this.isFixed = this.isFixedLeft || this.isFixedRight;\n    const validatePx = value => {\n      if (typeof value === 'string' && value !== '') {\n        return value;\n      } else {\n        return null;\n      }\n    };\n    this.setAutoLeftWidth(validatePx(this.nzLeft));\n    this.setAutoRightWidth(validatePx(this.nzRight));\n    this.changes$.next();\n  }\n  static {\n    this.ɵfac = function NzCellFixedDirective_Factory(t) {\n      return new (t || NzCellFixedDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellFixedDirective,\n      selectors: [[\"td\", \"nzRight\", \"\"], [\"th\", \"nzRight\", \"\"], [\"td\", \"nzLeft\", \"\"], [\"th\", \"nzLeft\", \"\"]],\n      hostVars: 6,\n      hostBindings: function NzCellFixedDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"position\", ctx.isFixed ? \"sticky\" : null);\n          i0.ɵɵclassProp(\"ant-table-cell-fix-right\", ctx.isFixedRight)(\"ant-table-cell-fix-left\", ctx.isFixedLeft);\n        }\n      },\n      inputs: {\n        nzRight: \"nzRight\",\n        nzLeft: \"nzLeft\",\n        colspan: \"colspan\",\n        colSpan: \"colSpan\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellFixedDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzRight],th[nzRight],td[nzLeft],th[nzLeft]',\n      host: {\n        '[class.ant-table-cell-fix-right]': `isFixedRight`,\n        '[class.ant-table-cell-fix-left]': `isFixedLeft`,\n        '[style.position]': `isFixed? 'sticky' : null`\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzRight: [{\n      type: Input\n    }],\n    nzLeft: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableStyleService {\n  setTheadTemplate(template) {\n    this.theadTemplate$.next(template);\n  }\n  setHasFixLeft(hasFixLeft) {\n    this.hasFixLeft$.next(hasFixLeft);\n  }\n  setHasFixRight(hasFixRight) {\n    this.hasFixRight$.next(hasFixRight);\n  }\n  setTableWidthConfig(widthConfig) {\n    this.tableWidthConfigPx$.next(widthConfig);\n  }\n  setListOfTh(listOfTh) {\n    let columnCount = 0;\n    listOfTh.forEach(th => {\n      columnCount += th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n    });\n    const listOfThPx = listOfTh.map(item => item.nzWidth);\n    this.columnCount$.next(columnCount);\n    this.listOfThWidthConfigPx$.next(listOfThPx);\n  }\n  setListOfMeasureColumn(listOfTh) {\n    const listOfKeys = [];\n    listOfTh.forEach(th => {\n      const length = th.colspan && +th.colspan || th.colSpan && +th.colSpan || 1;\n      for (let i = 0; i < length; i++) {\n        listOfKeys.push(`measure_key_${i}`);\n      }\n    });\n    this.listOfMeasureColumn$.next(listOfKeys);\n  }\n  setListOfAutoWidth(listOfAutoWidth) {\n    this.listOfAutoWidthPx$.next(listOfAutoWidth.map(width => `${width}px`));\n  }\n  setShowEmpty(showEmpty) {\n    this.showEmpty$.next(showEmpty);\n  }\n  setNoResult(noResult) {\n    this.noResult$.next(noResult);\n  }\n  setScroll(scrollX, scrollY) {\n    const enableAutoMeasure = !!(scrollX || scrollY);\n    if (!enableAutoMeasure) {\n      this.setListOfAutoWidth([]);\n    }\n    this.enableAutoMeasure$.next(enableAutoMeasure);\n  }\n  constructor() {\n    this.theadTemplate$ = new ReplaySubject(1);\n    this.hasFixLeft$ = new ReplaySubject(1);\n    this.hasFixRight$ = new ReplaySubject(1);\n    this.hostWidth$ = new ReplaySubject(1);\n    this.columnCount$ = new ReplaySubject(1);\n    this.showEmpty$ = new ReplaySubject(1);\n    this.noResult$ = new ReplaySubject(1);\n    this.listOfThWidthConfigPx$ = new BehaviorSubject([]);\n    this.tableWidthConfigPx$ = new BehaviorSubject([]);\n    this.manualWidthConfigPx$ = combineLatest([this.tableWidthConfigPx$, this.listOfThWidthConfigPx$]).pipe(map(([widthConfig, listOfWidth]) => widthConfig.length ? widthConfig : listOfWidth));\n    this.listOfAutoWidthPx$ = new ReplaySubject(1);\n    this.listOfListOfThWidthPx$ = merge( /** init with manual width **/\n    this.manualWidthConfigPx$, combineLatest([this.listOfAutoWidthPx$, this.manualWidthConfigPx$]).pipe(map(([autoWidth, manualWidth]) => {\n      /** use autoWidth until column length match **/\n      if (autoWidth.length === manualWidth.length) {\n        return autoWidth.map((width, index) => {\n          if (width === '0px') {\n            return manualWidth[index] || null;\n          } else {\n            return manualWidth[index] || width;\n          }\n        });\n      } else {\n        return manualWidth;\n      }\n    })));\n    this.listOfMeasureColumn$ = new ReplaySubject(1);\n    this.listOfListOfThWidth$ = this.listOfAutoWidthPx$.pipe(map(list => list.map(width => parseInt(width, 10))));\n    this.enableAutoMeasure$ = new ReplaySubject(1);\n  }\n  static {\n    this.ɵfac = function NzTableStyleService_Factory(t) {\n      return new (t || NzTableStyleService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzTableStyleService,\n      factory: NzTableStyleService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableStyleService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableCellDirective {\n  constructor(nzTableStyleService) {\n    this.isInsideTable = false;\n    this.isInsideTable = !!nzTableStyleService;\n  }\n  static {\n    this.ɵfac = function NzTableCellDirective_Factory(t) {\n      return new (t || NzTableCellDirective)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTableCellDirective,\n      selectors: [[\"th\", 9, \"nz-disable-th\", 3, \"mat-cell\", \"\"], [\"td\", 9, \"nz-disable-td\", 3, \"mat-cell\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzTableCellDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell\", ctx.isInsideTable);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableCellDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th:not(.nz-disable-th):not([mat-cell]), td:not(.nz-disable-td):not([mat-cell])',\n      host: {\n        '[class.ant-table-cell]': 'isInsideTable'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableDataService {\n  updatePageSize(size) {\n    this.pageSize$.next(size);\n  }\n  updateFrontPagination(pagination) {\n    this.frontPagination$.next(pagination);\n  }\n  updatePageIndex(index) {\n    this.pageIndex$.next(index);\n  }\n  updateListOfData(list) {\n    this.listOfData$.next(list);\n  }\n  updateListOfCustomColumn(list) {\n    this.listOfCustomColumn$.next(list);\n  }\n  constructor() {\n    this.destroy$ = new Subject();\n    this.pageIndex$ = new BehaviorSubject(1);\n    this.frontPagination$ = new BehaviorSubject(true);\n    this.pageSize$ = new BehaviorSubject(10);\n    this.listOfData$ = new BehaviorSubject([]);\n    this.listOfCustomColumn$ = new BehaviorSubject([]);\n    this.pageIndexDistinct$ = this.pageIndex$.pipe(distinctUntilChanged());\n    this.pageSizeDistinct$ = this.pageSize$.pipe(distinctUntilChanged());\n    this.listOfCalcOperator$ = new BehaviorSubject([]);\n    this.queryParams$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfCalcOperator$]).pipe(debounceTime(0), skip(1), map(([pageIndex, pageSize, listOfCalc]) => ({\n      pageIndex,\n      pageSize,\n      sort: listOfCalc.filter(item => item.sortFn).map(item => ({\n        key: item.key,\n        value: item.sortOrder\n      })),\n      filter: listOfCalc.filter(item => item.filterFn).map(item => ({\n        key: item.key,\n        value: item.filterValue\n      }))\n    })));\n    this.listOfDataAfterCalc$ = combineLatest([this.listOfData$, this.listOfCalcOperator$]).pipe(map(([listOfData, listOfCalcOperator]) => {\n      let listOfDataAfterCalc = [...listOfData];\n      const listOfFilterOperator = listOfCalcOperator.filter(item => {\n        const {\n          filterValue,\n          filterFn\n        } = item;\n        const isReset = filterValue === null || filterValue === undefined || Array.isArray(filterValue) && filterValue.length === 0;\n        return !isReset && typeof filterFn === 'function';\n      });\n      for (const item of listOfFilterOperator) {\n        const {\n          filterFn,\n          filterValue\n        } = item;\n        listOfDataAfterCalc = listOfDataAfterCalc.filter(data => filterFn(filterValue, data));\n      }\n      const listOfSortOperator = listOfCalcOperator.filter(item => item.sortOrder !== null && typeof item.sortFn === 'function').sort((a, b) => +b.sortPriority - +a.sortPriority);\n      if (listOfCalcOperator.length) {\n        listOfDataAfterCalc.sort((record1, record2) => {\n          for (const item of listOfSortOperator) {\n            const {\n              sortFn,\n              sortOrder\n            } = item;\n            if (sortFn && sortOrder) {\n              const compareResult = sortFn(record1, record2, sortOrder);\n              if (compareResult !== 0) {\n                return sortOrder === 'ascend' ? compareResult : -compareResult;\n              }\n            }\n          }\n          return 0;\n        });\n      }\n      return listOfDataAfterCalc;\n    }));\n    this.listOfFrontEndCurrentPageData$ = combineLatest([this.pageIndexDistinct$, this.pageSizeDistinct$, this.listOfDataAfterCalc$]).pipe(takeUntil(this.destroy$), filter(value => {\n      const [pageIndex, pageSize, listOfData] = value;\n      const maxPageIndex = Math.ceil(listOfData.length / pageSize) || 1;\n      return pageIndex <= maxPageIndex;\n    }), map(([pageIndex, pageSize, listOfData]) => listOfData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)));\n    this.listOfCurrentPageData$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfFrontEndCurrentPageData$ : this.listOfDataAfterCalc$));\n    this.total$ = this.frontPagination$.pipe(switchMap(pagination => pagination ? this.listOfDataAfterCalc$ : this.listOfData$), map(list => list.length), distinctUntilChanged());\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableDataService_Factory(t) {\n      return new (t || NzTableDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzTableDataService,\n      factory: NzTableDataService.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableDataService, [{\n    type: Injectable\n  }], () => [], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCustomColumnDirective {\n  constructor(el, renderer, nzTableDataService) {\n    this.el = el;\n    this.renderer = renderer;\n    this.nzTableDataService = nzTableDataService;\n    this.nzCellControl = null;\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    this.nzTableDataService.listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(item => {\n      if (item.length) {\n        item.forEach((v, i) => {\n          if (v.value === this.nzCellControl) {\n            if (!v.default) {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'none');\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'display', 'block');\n            }\n            this.renderer.setStyle(this.el.nativeElement, 'order', i);\n            if (!v?.fixWidth) {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 1 ${v.width}px`);\n            } else {\n              this.renderer.setStyle(this.el.nativeElement, 'flex', `1 0 ${v.width}px`);\n            }\n          }\n        });\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzCustomColumnDirective_Factory(t) {\n      return new (t || NzCustomColumnDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzTableDataService));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCustomColumnDirective,\n      selectors: [[\"td\", \"nzCellControl\", \"\"], [\"th\", \"nzCellControl\", \"\"]],\n      inputs: {\n        nzCellControl: \"nzCellControl\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCustomColumnDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'td[nzCellControl],th[nzCellControl]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: NzTableDataService\n  }], {\n    nzCellControl: [{\n      type: Input\n    }]\n  });\n})();\nclass NzTdAddOnComponent {\n  constructor() {\n    this.nzChecked = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzLabel = null;\n    this.nzIndentSize = 0;\n    this.nzShowExpand = false;\n    this.nzShowCheckbox = false;\n    this.nzExpand = false;\n    this.nzExpandIcon = null;\n    this.nzCheckedChange = new EventEmitter();\n    this.nzExpandChange = new EventEmitter();\n    this.isNzShowExpandChanged = false;\n    this.isNzShowCheckboxChanged = false;\n  }\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  onExpandChange(expand) {\n    this.nzExpand = expand;\n    this.nzExpandChange.emit(expand);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzExpand,\n      nzChecked,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzExpand) && !this.isNzShowExpandChanged) {\n      this.nzShowExpand = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static {\n    this.ɵfac = function NzTdAddOnComponent_Factory(t) {\n      return new (t || NzTdAddOnComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTdAddOnComponent,\n      selectors: [[\"td\", \"nzChecked\", \"\"], [\"td\", \"nzDisabled\", \"\"], [\"td\", \"nzIndeterminate\", \"\"], [\"td\", \"nzIndentSize\", \"\"], [\"td\", \"nzExpand\", \"\"], [\"td\", \"nzShowExpand\", \"\"], [\"td\", \"nzShowCheckbox\", \"\"]],\n      hostVars: 4,\n      hostBindings: function NzTdAddOnComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell-with-append\", ctx.nzShowExpand || ctx.nzIndentSize > 0)(\"ant-table-selection-column\", ctx.nzShowCheckbox);\n        }\n      },\n      inputs: {\n        nzChecked: \"nzChecked\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzLabel: \"nzLabel\",\n        nzIndentSize: \"nzIndentSize\",\n        nzShowExpand: \"nzShowExpand\",\n        nzShowCheckbox: \"nzShowCheckbox\",\n        nzExpand: \"nzExpand\",\n        nzExpandIcon: \"nzExpandIcon\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\",\n        nzExpandChange: \"nzExpandChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 2,\n      consts: [[\"rowExpand\", \"\"], [4, \"ngIf\"], [\"nz-checkbox\", \"\", 3, \"nzDisabled\", \"ngModel\", \"nzIndeterminate\", \"ngModelChange\", 4, \"ngIf\"], [3, \"indentSize\"], [4, \"ngIf\", \"ngIfElse\"], [\"nz-row-expand-button\", \"\", 3, \"expandChange\", \"expand\", \"spaceMode\"], [3, \"ngTemplateOutlet\"], [\"nz-checkbox\", \"\", 3, \"ngModelChange\", \"nzDisabled\", \"ngModel\", \"nzIndeterminate\"]],\n      template: function NzTdAddOnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTdAddOnComponent_ng_container_0_Template, 5, 3, \"ng-container\", 1)(1, NzTdAddOnComponent_label_1_Template, 1, 4, \"label\", 2);\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowExpand || ctx.nzIndentSize > 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowCheckbox);\n        }\n      },\n      dependencies: [NzRowIndentDirective, NzRowExpandButtonDirective, NgIf, NgTemplateOutlet, NzCheckboxModule, i5.NzCheckboxComponent, FormsModule, i6.NgControlStatus, i6.NgModel],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzShowExpand\", void 0);\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([InputBoolean()], NzTdAddOnComponent.prototype, \"nzExpand\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTdAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'td[nzChecked], td[nzDisabled], td[nzIndeterminate], td[nzIndentSize], td[nzExpand], td[nzShowExpand], td[nzShowCheckbox]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"nzShowExpand || nzIndentSize > 0\">\n      <nz-row-indent [indentSize]=\"nzIndentSize\"></nz-row-indent>\n      <ng-template #rowExpand>\n        <button\n          nz-row-expand-button\n          [expand]=\"nzExpand\"\n          (expandChange)=\"onExpandChange($event)\"\n          [spaceMode]=\"!nzShowExpand\"\n        ></button>\n      </ng-template>\n      <ng-container *ngIf=\"nzExpandIcon; else rowExpand\">\n        <ng-template [ngTemplateOutlet]=\"nzExpandIcon\"></ng-template>\n      </ng-container>\n    </ng-container>\n    <label\n      nz-checkbox\n      *ngIf=\"nzShowCheckbox\"\n      [nzDisabled]=\"nzDisabled\"\n      [ngModel]=\"nzChecked\"\n      [nzIndeterminate]=\"nzIndeterminate\"\n      [attr.aria-label]=\"nzLabel\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[class.ant-table-cell-with-append]': `nzShowExpand || nzIndentSize > 0`,\n        '[class.ant-table-selection-column]': `nzShowCheckbox`\n      },\n      imports: [NzRowIndentDirective, NzRowExpandButtonDirective, NgIf, NgTemplateOutlet, NzCheckboxModule, FormsModule],\n      standalone: true\n    }]\n  }], null, {\n    nzChecked: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzIndentSize: [{\n      type: Input\n    }],\n    nzShowExpand: [{\n      type: Input\n    }],\n    nzShowCheckbox: [{\n      type: Input\n    }],\n    nzExpand: [{\n      type: Input\n    }],\n    nzExpandIcon: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzExpandChange: [{\n      type: Output\n    }]\n  });\n})();\nclass NzThAddOnComponent {\n  getNextSortDirection(sortDirections, current) {\n    const index = sortDirections.indexOf(current);\n    if (index === sortDirections.length - 1) {\n      return sortDirections[0];\n    } else {\n      return sortDirections[index + 1];\n    }\n  }\n  setSortOrder(order) {\n    this.sortOrderChange$.next(order);\n  }\n  clearSortOrder() {\n    if (this.sortOrder !== null) {\n      this.setSortOrder(null);\n    }\n  }\n  onFilterValueChange(value) {\n    this.nzFilterChange.emit(value);\n    this.nzFilterValue = value;\n    this.updateCalcOperator();\n  }\n  updateCalcOperator() {\n    this.calcOperatorChange$.next();\n  }\n  constructor(host, cdr, ngZone, destroy$) {\n    this.host = host;\n    this.cdr = cdr;\n    this.ngZone = ngZone;\n    this.destroy$ = destroy$;\n    this.manualClickOrder$ = new Subject();\n    this.calcOperatorChange$ = new Subject();\n    this.nzFilterValue = null;\n    this.sortOrder = null;\n    this.sortDirections = ['ascend', 'descend', null];\n    this.sortOrderChange$ = new Subject();\n    this.isNzShowSortChanged = false;\n    this.isNzShowFilterChanged = false;\n    this.nzFilterMultiple = true;\n    this.nzSortOrder = null;\n    this.nzSortPriority = false;\n    this.nzSortDirections = ['ascend', 'descend', null];\n    this.nzFilters = [];\n    this.nzSortFn = null;\n    this.nzFilterFn = null;\n    this.nzShowSort = false;\n    this.nzShowFilter = false;\n    this.nzCustomFilter = false;\n    this.nzCheckedChange = new EventEmitter();\n    this.nzSortOrderChange = new EventEmitter();\n    this.nzFilterChange = new EventEmitter();\n  }\n  ngOnInit() {\n    this.ngZone.runOutsideAngular(() => fromEvent(this.host.nativeElement, 'click').pipe(filter(() => this.nzShowSort), takeUntil(this.destroy$)).subscribe(() => {\n      const nextOrder = this.getNextSortDirection(this.sortDirections, this.sortOrder);\n      this.ngZone.run(() => {\n        this.setSortOrder(nextOrder);\n        this.manualClickOrder$.next(this);\n      });\n    }));\n    this.sortOrderChange$.pipe(takeUntil(this.destroy$)).subscribe(order => {\n      if (this.sortOrder !== order) {\n        this.sortOrder = order;\n        this.nzSortOrderChange.emit(order);\n      }\n      this.updateCalcOperator();\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzSortDirections,\n      nzFilters,\n      nzSortOrder,\n      nzSortFn,\n      nzFilterFn,\n      nzSortPriority,\n      nzFilterMultiple,\n      nzShowSort,\n      nzShowFilter\n    } = changes;\n    if (nzSortDirections) {\n      if (this.nzSortDirections && this.nzSortDirections.length) {\n        this.sortDirections = this.nzSortDirections;\n      }\n    }\n    if (nzSortOrder) {\n      this.sortOrder = this.nzSortOrder;\n      this.setSortOrder(this.nzSortOrder);\n    }\n    if (nzShowSort) {\n      this.isNzShowSortChanged = true;\n    }\n    if (nzShowFilter) {\n      this.isNzShowFilterChanged = true;\n    }\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    if ((isFirstChange(nzSortOrder) || isFirstChange(nzSortFn)) && !this.isNzShowSortChanged) {\n      this.nzShowSort = true;\n    }\n    if (isFirstChange(nzFilters) && !this.isNzShowFilterChanged) {\n      this.nzShowFilter = true;\n    }\n    if ((nzFilters || nzFilterMultiple) && this.nzShowFilter) {\n      const listOfValue = this.nzFilters.filter(item => item.byDefault).map(item => item.value);\n      this.nzFilterValue = this.nzFilterMultiple ? listOfValue : listOfValue[0] || null;\n    }\n    if (nzSortFn || nzFilterFn || nzSortPriority || nzFilters) {\n      this.updateCalcOperator();\n    }\n  }\n  static {\n    this.ɵfac = function NzThAddOnComponent_Factory(t) {\n      return new (t || NzThAddOnComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i2.NzDestroyService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzThAddOnComponent,\n      selectors: [[\"th\", \"nzColumnKey\", \"\"], [\"th\", \"nzSortFn\", \"\"], [\"th\", \"nzSortOrder\", \"\"], [\"th\", \"nzFilters\", \"\"], [\"th\", \"nzShowSort\", \"\"], [\"th\", \"nzShowFilter\", \"\"], [\"th\", \"nzCustomFilter\", \"\"]],\n      hostVars: 4,\n      hostBindings: function NzThAddOnComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-column-has-sorters\", ctx.nzShowSort)(\"ant-table-column-sort\", ctx.sortOrder === \"descend\" || ctx.sortOrder === \"ascend\");\n        }\n      },\n      inputs: {\n        nzColumnKey: \"nzColumnKey\",\n        nzFilterMultiple: \"nzFilterMultiple\",\n        nzSortOrder: \"nzSortOrder\",\n        nzSortPriority: \"nzSortPriority\",\n        nzSortDirections: \"nzSortDirections\",\n        nzFilters: \"nzFilters\",\n        nzSortFn: \"nzSortFn\",\n        nzFilterFn: \"nzFilterFn\",\n        nzShowSort: \"nzShowSort\",\n        nzShowFilter: \"nzShowFilter\",\n        nzCustomFilter: \"nzCustomFilter\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\",\n        nzSortOrderChange: \"nzSortOrderChange\",\n        nzFilterChange: \"nzFilterChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzDestroyService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c2,\n      ngContentSelectors: _c4,\n      decls: 9,\n      vars: 2,\n      consts: [[\"notFilterTemplate\", \"\"], [\"extraTemplate\", \"\"], [\"sortTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\", \"filterChange\", 4, \"ngIf\", \"ngIfElse\"], [3, \"filterChange\", \"contentTemplate\", \"extraTemplate\", \"customFilter\", \"filterMultiple\", \"listOfFilter\"], [3, \"ngTemplateOutlet\"], [3, \"sortOrder\", \"sortDirections\", \"contentTemplate\"]],\n      template: function NzThAddOnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵtemplate(0, NzThAddOnComponent_nz_table_filter_0_Template, 1, 5, \"nz-table-filter\", 4)(1, NzThAddOnComponent_ng_template_1_Template, 1, 1, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(3, NzThAddOnComponent_ng_template_3_Template, 2, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(5, NzThAddOnComponent_ng_template_5_Template, 1, 3, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(7, NzThAddOnComponent_ng_template_7_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const notFilterTemplate_r3 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzShowFilter || ctx.nzCustomFilter)(\"ngIfElse\", notFilterTemplate_r3);\n        }\n      },\n      dependencies: [NzTableFilterComponent, NgIf, NgTemplateOutlet, NzTableSortersComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzShowSort\", void 0);\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzShowFilter\", void 0);\n__decorate([InputBoolean()], NzThAddOnComponent.prototype, \"nzCustomFilter\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThAddOnComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <nz-table-filter\n      *ngIf=\"nzShowFilter || nzCustomFilter; else notFilterTemplate\"\n      [contentTemplate]=\"notFilterTemplate\"\n      [extraTemplate]=\"extraTemplate\"\n      [customFilter]=\"nzCustomFilter\"\n      [filterMultiple]=\"nzFilterMultiple\"\n      [listOfFilter]=\"nzFilters\"\n      (filterChange)=\"onFilterValueChange($event)\"\n    ></nz-table-filter>\n    <ng-template #notFilterTemplate>\n      <ng-template [ngTemplateOutlet]=\"nzShowSort ? sortTemplate : contentTemplate\"></ng-template>\n    </ng-template>\n    <ng-template #extraTemplate>\n      <ng-content select=\"[nz-th-extra]\"></ng-content>\n      <ng-content select=\"nz-filter-trigger\"></ng-content>\n    </ng-template>\n    <ng-template #sortTemplate>\n      <nz-table-sorters\n        [sortOrder]=\"sortOrder\"\n        [sortDirections]=\"sortDirections\"\n        [contentTemplate]=\"contentTemplate\"\n      ></nz-table-sorters>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        '[class.ant-table-column-has-sorters]': 'nzShowSort',\n        '[class.ant-table-column-sort]': `sortOrder === 'descend' || sortOrder === 'ascend'`\n      },\n      providers: [NzDestroyService],\n      imports: [NzTableFilterComponent, NgIf, NgTemplateOutlet, NzTableSortersComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i2.NzDestroyService\n  }], {\n    nzColumnKey: [{\n      type: Input\n    }],\n    nzFilterMultiple: [{\n      type: Input\n    }],\n    nzSortOrder: [{\n      type: Input\n    }],\n    nzSortPriority: [{\n      type: Input\n    }],\n    nzSortDirections: [{\n      type: Input\n    }],\n    nzFilters: [{\n      type: Input\n    }],\n    nzSortFn: [{\n      type: Input\n    }],\n    nzFilterFn: [{\n      type: Input\n    }],\n    nzShowSort: [{\n      type: Input\n    }],\n    nzShowFilter: [{\n      type: Input\n    }],\n    nzCustomFilter: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }],\n    nzFilterChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzThMeasureDirective {\n  constructor(renderer, elementRef) {\n    this.renderer = renderer;\n    this.elementRef = elementRef;\n    this.changes$ = new Subject();\n    this.nzWidth = null;\n    this.colspan = null;\n    this.colSpan = null;\n    this.rowspan = null;\n    this.rowSpan = null;\n  }\n  ngOnChanges(changes) {\n    const {\n      nzWidth,\n      colspan,\n      rowspan,\n      colSpan,\n      rowSpan\n    } = changes;\n    if (colspan || colSpan) {\n      const col = this.colspan || this.colSpan;\n      if (!isNil(col)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'colspan', `${col}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'colspan');\n      }\n    }\n    if (rowspan || rowSpan) {\n      const row = this.rowspan || this.rowSpan;\n      if (!isNil(row)) {\n        this.renderer.setAttribute(this.elementRef.nativeElement, 'rowspan', `${row}`);\n      } else {\n        this.renderer.removeAttribute(this.elementRef.nativeElement, 'rowspan');\n      }\n    }\n    if (nzWidth || colspan) {\n      this.changes$.next();\n    }\n  }\n  static {\n    this.ɵfac = function NzThMeasureDirective_Factory(t) {\n      return new (t || NzThMeasureDirective)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzThMeasureDirective,\n      selectors: [[\"th\"]],\n      inputs: {\n        nzWidth: \"nzWidth\",\n        colspan: \"colspan\",\n        colSpan: \"colSpan\",\n        rowspan: \"rowspan\",\n        rowSpan: \"rowSpan\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThMeasureDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzWidth: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }],\n    colSpan: [{\n      type: Input\n    }],\n    rowspan: [{\n      type: Input\n    }],\n    rowSpan: [{\n      type: Input\n    }]\n  });\n})();\nclass NzThSelectionComponent {\n  constructor() {\n    this.nzSelections = [];\n    this.nzChecked = false;\n    this.nzDisabled = false;\n    this.nzIndeterminate = false;\n    this.nzLabel = null;\n    this.nzShowCheckbox = false;\n    this.nzShowRowSelection = false;\n    this.nzCheckedChange = new EventEmitter();\n    this.isNzShowExpandChanged = false;\n    this.isNzShowCheckboxChanged = false;\n  }\n  onCheckedChange(checked) {\n    this.nzChecked = checked;\n    this.nzCheckedChange.emit(checked);\n  }\n  ngOnChanges(changes) {\n    const isFirstChange = value => value && value.firstChange && value.currentValue !== undefined;\n    const {\n      nzChecked,\n      nzSelections,\n      nzShowExpand,\n      nzShowCheckbox\n    } = changes;\n    if (nzShowExpand) {\n      this.isNzShowExpandChanged = true;\n    }\n    if (nzShowCheckbox) {\n      this.isNzShowCheckboxChanged = true;\n    }\n    if (isFirstChange(nzSelections) && !this.isNzShowExpandChanged) {\n      this.nzShowRowSelection = true;\n    }\n    if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n      this.nzShowCheckbox = true;\n    }\n  }\n  static {\n    this.ɵfac = function NzThSelectionComponent_Factory(t) {\n      return new (t || NzThSelectionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzThSelectionComponent,\n      selectors: [[\"th\", \"nzSelections\", \"\"], [\"th\", \"nzChecked\", \"\"], [\"th\", \"nzShowCheckbox\", \"\"], [\"th\", \"nzShowRowSelection\", \"\"]],\n      hostAttrs: [1, \"ant-table-selection-column\"],\n      inputs: {\n        nzSelections: \"nzSelections\",\n        nzChecked: \"nzChecked\",\n        nzDisabled: \"nzDisabled\",\n        nzIndeterminate: \"nzIndeterminate\",\n        nzLabel: \"nzLabel\",\n        nzShowCheckbox: \"nzShowCheckbox\",\n        nzShowRowSelection: \"nzShowRowSelection\"\n      },\n      outputs: {\n        nzCheckedChange: \"nzCheckedChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      attrs: _c5,\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 7,\n      consts: [[3, \"checkedChange\", \"checked\", \"disabled\", \"indeterminate\", \"label\", \"listOfSelections\", \"showCheckbox\", \"showRowSelection\"]],\n      template: function NzThSelectionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"nz-table-selection\", 0);\n          i0.ɵɵlistener(\"checkedChange\", function NzThSelectionComponent_Template_nz_table_selection_checkedChange_0_listener($event) {\n            return ctx.onCheckedChange($event);\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"checked\", ctx.nzChecked)(\"disabled\", ctx.nzDisabled)(\"indeterminate\", ctx.nzIndeterminate)(\"label\", ctx.nzLabel)(\"listOfSelections\", ctx.nzSelections)(\"showCheckbox\", ctx.nzShowCheckbox)(\"showRowSelection\", ctx.nzShowRowSelection);\n        }\n      },\n      dependencies: [NzTableSelectionComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzThSelectionComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([InputBoolean()], NzThSelectionComponent.prototype, \"nzShowRowSelection\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzThSelectionComponent, [{\n    type: Component,\n    args: [{\n      selector: 'th[nzSelections],th[nzChecked],th[nzShowCheckbox],th[nzShowRowSelection]',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      template: `\n    <nz-table-selection\n      [checked]=\"nzChecked\"\n      [disabled]=\"nzDisabled\"\n      [indeterminate]=\"nzIndeterminate\"\n      [label]=\"nzLabel\"\n      [listOfSelections]=\"nzSelections\"\n      [showCheckbox]=\"nzShowCheckbox\"\n      [showRowSelection]=\"nzShowRowSelection\"\n      (checkedChange)=\"onCheckedChange($event)\"\n    ></nz-table-selection>\n    <ng-content></ng-content>\n  `,\n      host: {\n        class: 'ant-table-selection-column'\n      },\n      imports: [NzTableSelectionComponent],\n      standalone: true\n    }]\n  }], () => [], {\n    nzSelections: [{\n      type: Input\n    }],\n    nzChecked: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzIndeterminate: [{\n      type: Input\n    }],\n    nzLabel: [{\n      type: Input\n    }],\n    nzShowCheckbox: [{\n      type: Input\n    }],\n    nzShowRowSelection: [{\n      type: Input\n    }],\n    nzCheckedChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellAlignDirective {\n  constructor() {\n    this.nzAlign = null;\n  }\n  static {\n    this.ɵfac = function NzCellAlignDirective_Factory(t) {\n      return new (t || NzCellAlignDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellAlignDirective,\n      selectors: [[\"th\", \"nzAlign\", \"\"], [\"td\", \"nzAlign\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellAlignDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"text-align\", ctx.nzAlign);\n        }\n      },\n      inputs: {\n        nzAlign: \"nzAlign\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellAlignDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzAlign],td[nzAlign]',\n      host: {\n        '[style.text-align]': 'nzAlign'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzAlign: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCellEllipsisDirective {\n  constructor() {\n    this.nzEllipsis = true;\n  }\n  static {\n    this.ɵfac = function NzCellEllipsisDirective_Factory(t) {\n      return new (t || NzCellEllipsisDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellEllipsisDirective,\n      selectors: [[\"th\", \"nzEllipsis\", \"\"], [\"td\", \"nzEllipsis\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellEllipsisDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-cell-ellipsis\", ctx.nzEllipsis);\n        }\n      },\n      inputs: {\n        nzEllipsis: \"nzEllipsis\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzCellEllipsisDirective.prototype, \"nzEllipsis\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellEllipsisDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzEllipsis],td[nzEllipsis]',\n      host: {\n        '[class.ant-table-cell-ellipsis]': 'nzEllipsis'\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzEllipsis: [{\n      type: Input\n    }]\n  });\n})();\nclass NzCellBreakWordDirective {\n  constructor() {\n    this.nzBreakWord = true;\n  }\n  static {\n    this.ɵfac = function NzCellBreakWordDirective_Factory(t) {\n      return new (t || NzCellBreakWordDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzCellBreakWordDirective,\n      selectors: [[\"th\", \"nzBreakWord\", \"\"], [\"td\", \"nzBreakWord\", \"\"]],\n      hostVars: 2,\n      hostBindings: function NzCellBreakWordDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"word-break\", ctx.nzBreakWord ? \"break-all\" : \"\");\n        }\n      },\n      inputs: {\n        nzBreakWord: \"nzBreakWord\"\n      },\n      standalone: true\n    });\n  }\n}\n__decorate([InputBoolean()], NzCellBreakWordDirective.prototype, \"nzBreakWord\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzCellBreakWordDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'th[nzBreakWord],td[nzBreakWord]',\n      host: {\n        '[style.word-break]': `nzBreakWord ? 'break-all' : ''`\n      },\n      standalone: true\n    }]\n  }], null, {\n    nzBreakWord: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableContentComponent {\n  constructor() {\n    this.tableLayout = 'auto';\n    this.theadTemplate = null;\n    this.contentTemplate = null;\n    this.listOfColWidth = [];\n    this.scrollX = null;\n  }\n  static {\n    this.ɵfac = function NzTableContentComponent_Factory(t) {\n      return new (t || NzTableContentComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableContentComponent,\n      selectors: [[\"table\", \"nz-table-content\", \"\"]],\n      hostVars: 8,\n      hostBindings: function NzTableContentComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"table-layout\", ctx.tableLayout)(\"width\", ctx.scrollX)(\"min-width\", ctx.scrollX ? \"100%\" : null);\n          i0.ɵɵclassProp(\"ant-table-fixed\", ctx.scrollX);\n        }\n      },\n      inputs: {\n        tableLayout: \"tableLayout\",\n        theadTemplate: \"theadTemplate\",\n        contentTemplate: \"contentTemplate\",\n        listOfColWidth: \"listOfColWidth\",\n        scrollX: \"scrollX\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c6,\n      ngContentSelectors: _c0,\n      decls: 4,\n      vars: 3,\n      consts: [[3, \"width\", \"minWidth\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"ant-table-thead\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [1, \"ant-table-thead\"]],\n      template: function NzTableContentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTableContentComponent_col_0_Template, 1, 4, \"col\", 0)(1, NzTableContentComponent_thead_1_Template, 2, 1, \"thead\", 1)(2, NzTableContentComponent_ng_template_2_Template, 0, 0, \"ng-template\", 2);\n          i0.ɵɵprojection(3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.listOfColWidth);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.theadTemplate);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngTemplateOutlet\", ctx.contentTemplate);\n        }\n      },\n      dependencies: [NgTemplateOutlet, NgIf, NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableContentComponent, [{\n    type: Component,\n    args: [{\n      selector: 'table[nz-table-content]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <col [style.width]=\"width\" [style.minWidth]=\"width\" *ngFor=\"let width of listOfColWidth\" />\n    <thead class=\"ant-table-thead\" *ngIf=\"theadTemplate\">\n      <ng-template [ngTemplateOutlet]=\"theadTemplate\"></ng-template>\n    </thead>\n    <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    <ng-content></ng-content>\n  `,\n      host: {\n        '[style.table-layout]': 'tableLayout',\n        '[class.ant-table-fixed]': 'scrollX',\n        '[style.width]': 'scrollX',\n        '[style.min-width]': `scrollX ? '100%' : null`\n      },\n      imports: [NgTemplateOutlet, NgIf, NgForOf],\n      standalone: true\n    }]\n  }], null, {\n    tableLayout: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFixedRowComponent {\n  constructor(nzTableStyleService, renderer) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.renderer = renderer;\n    this.hostWidth$ = new BehaviorSubject(null);\n    this.enableAutoMeasure$ = new BehaviorSubject(false);\n    this.destroy$ = new Subject();\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      const {\n        enableAutoMeasure$,\n        hostWidth$\n      } = this.nzTableStyleService;\n      enableAutoMeasure$.pipe(takeUntil(this.destroy$)).subscribe(this.enableAutoMeasure$);\n      hostWidth$.pipe(takeUntil(this.destroy$)).subscribe(this.hostWidth$);\n    }\n  }\n  ngAfterViewInit() {\n    this.nzTableStyleService.columnCount$.pipe(takeUntil(this.destroy$)).subscribe(count => {\n      this.renderer.setAttribute(this.tdElement.nativeElement, 'colspan', `${count}`);\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableFixedRowComponent_Factory(t) {\n      return new (t || NzTableFixedRowComponent)(i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(i0.Renderer2));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableFixedRowComponent,\n      selectors: [[\"tr\", \"nz-table-fixed-row\", \"\"], [\"tr\", \"nzExpand\", \"\"]],\n      viewQuery: function NzTableFixedRowComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c7, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tdElement = _t.first);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c8,\n      ngContentSelectors: _c0,\n      decls: 6,\n      vars: 4,\n      consts: [[\"tdElement\", \"\"], [\"contentTemplate\", \"\"], [1, \"nz-disable-td\", \"ant-table-cell\"], [\"class\", \"ant-table-expanded-row-fixed\", \"style\", \"position: sticky; left: 0px; overflow: hidden;\", 3, \"width\", 4, \"ngIf\", \"ngIfElse\"], [1, \"ant-table-expanded-row-fixed\", 2, \"position\", \"sticky\", \"left\", \"0px\", \"overflow\", \"hidden\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTableFixedRowComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"td\", 2, 0);\n          i0.ɵɵtemplate(2, NzTableFixedRowComponent_div_2_Template, 3, 5, \"div\", 3);\n          i0.ɵɵpipe(3, \"async\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(4, NzTableFixedRowComponent_ng_template_4_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const contentTemplate_r2 = i0.ɵɵreference(5);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(3, 2, ctx.enableAutoMeasure$))(\"ngIfElse\", contentTemplate_r2);\n        }\n      },\n      dependencies: [NgIf, AsyncPipe, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableFixedRowComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-fixed-row], tr[nzExpand]',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <td class=\"nz-disable-td ant-table-cell\" #tdElement>\n      <div\n        class=\"ant-table-expanded-row-fixed\"\n        *ngIf=\"enableAutoMeasure$ | async; else contentTemplate\"\n        style=\"position: sticky; left: 0px; overflow: hidden;\"\n        [style.width.px]=\"hostWidth$ | async\"\n      >\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n      </div>\n    </td>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      imports: [NgIf, AsyncPipe, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService\n  }, {\n    type: i0.Renderer2\n  }], {\n    tdElement: [{\n      type: ViewChild,\n      args: ['tdElement', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableInnerDefaultComponent {\n  constructor() {\n    this.tableLayout = 'auto';\n    this.listOfColWidth = [];\n    this.theadTemplate = null;\n    this.contentTemplate = null;\n  }\n  static {\n    this.ɵfac = function NzTableInnerDefaultComponent_Factory(t) {\n      return new (t || NzTableInnerDefaultComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableInnerDefaultComponent,\n      selectors: [[\"nz-table-inner-default\"]],\n      hostAttrs: [1, \"ant-table-container\"],\n      inputs: {\n        tableLayout: \"tableLayout\",\n        listOfColWidth: \"listOfColWidth\",\n        theadTemplate: \"theadTemplate\",\n        contentTemplate: \"contentTemplate\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 4,\n      consts: [[1, \"ant-table-content\"], [\"nz-table-content\", \"\", 3, \"contentTemplate\", \"tableLayout\", \"listOfColWidth\", \"theadTemplate\"]],\n      template: function NzTableInnerDefaultComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"table\", 1);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"contentTemplate\", ctx.contentTemplate)(\"tableLayout\", ctx.tableLayout)(\"listOfColWidth\", ctx.listOfColWidth)(\"theadTemplate\", ctx.theadTemplate);\n        }\n      },\n      dependencies: [NzTableContentComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerDefaultComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-default',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <div class=\"ant-table-content\">\n      <table\n        nz-table-content\n        [contentTemplate]=\"contentTemplate\"\n        [tableLayout]=\"tableLayout\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n      ></table>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent],\n      standalone: true\n    }]\n  }], () => [], {\n    tableLayout: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTrMeasureComponent {\n  constructor(nzResizeObserver, ngZone) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.ngZone = ngZone;\n    this.listOfMeasureColumn = [];\n    this.listOfAutoWidth = new EventEmitter();\n    this.destroy$ = new Subject();\n  }\n  trackByFunc(_, key) {\n    return key;\n  }\n  ngAfterViewInit() {\n    this.listOfTdElement.changes.pipe(startWith(this.listOfTdElement)).pipe(switchMap(list => combineLatest(list.toArray().map(item => this.nzResizeObserver.observe(item).pipe(map(([entry]) => {\n      const {\n        width\n      } = entry.target.getBoundingClientRect();\n      return Math.floor(width);\n    }))))), debounceTime(16), takeUntil(this.destroy$)).subscribe(data => {\n      // Caretaker note: we don't have to re-enter the Angular zone each time the stream emits.\n      // The below check is necessary to be sure that zone is not nooped through `BootstrapOptions`\n      // (`bootstrapModule(AppModule, { ngZone: 'noop' }))`. The `ngZone instanceof NgZone` may return\n      // `false` if zone is nooped, since `ngZone` will be an instance of the `NoopNgZone`.\n      // The `ResizeObserver` might be also patched through `zone.js/dist/zone-patch-resize-observer`,\n      // thus calling `ngZone.run` again will cause another change detection.\n      if (this.ngZone instanceof NgZone && NgZone.isInAngularZone()) {\n        this.listOfAutoWidth.next(data);\n      } else {\n        this.ngZone.run(() => this.listOfAutoWidth.next(data));\n      }\n    });\n  }\n  ngOnDestroy() {\n    this.destroy$.next(true);\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTrMeasureComponent_Factory(t) {\n      return new (t || NzTrMeasureComponent)(i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTrMeasureComponent,\n      selectors: [[\"tr\", \"nz-table-measure-row\", \"\"]],\n      viewQuery: function NzTrMeasureComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c7, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfTdElement = _t);\n        }\n      },\n      hostAttrs: [1, \"ant-table-measure-now\"],\n      inputs: {\n        listOfMeasureColumn: \"listOfMeasureColumn\"\n      },\n      outputs: {\n        listOfAutoWidth: \"listOfAutoWidth\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c9,\n      decls: 1,\n      vars: 2,\n      consts: [[\"tdElement\", \"\"], [\"class\", \"nz-disable-td\", \"style\", \"padding: 0px; border: 0px; height: 0px;\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [1, \"nz-disable-td\", 2, \"padding\", \"0px\", \"border\", \"0px\", \"height\", \"0px\"]],\n      template: function NzTrMeasureComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTrMeasureComponent_td_0_Template, 2, 0, \"td\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.listOfMeasureColumn)(\"ngForTrackBy\", ctx.trackByFunc);\n        }\n      },\n      dependencies: [NgForOf],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrMeasureComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tr[nz-table-measure-row]',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <td\n      #tdElement\n      class=\"nz-disable-td\"\n      style=\"padding: 0px; border: 0px; height: 0px;\"\n      *ngFor=\"let th of listOfMeasureColumn; trackBy: trackByFunc\"\n    ></td>\n  `,\n      host: {\n        class: 'ant-table-measure-now'\n      },\n      imports: [NgForOf],\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i0.NgZone\n  }], {\n    listOfMeasureColumn: [{\n      type: Input\n    }],\n    listOfAutoWidth: [{\n      type: Output\n    }],\n    listOfTdElement: [{\n      type: ViewChildren,\n      args: ['tdElement']\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTbodyComponent {\n  constructor(nzTableStyleService) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.isInsideTable = false;\n    this.showEmpty$ = new BehaviorSubject(false);\n    this.noResult$ = new BehaviorSubject(undefined);\n    this.listOfMeasureColumn$ = new BehaviorSubject([]);\n    this.destroy$ = new Subject();\n    this.isInsideTable = !!this.nzTableStyleService;\n    if (this.nzTableStyleService) {\n      const {\n        showEmpty$,\n        noResult$,\n        listOfMeasureColumn$\n      } = this.nzTableStyleService;\n      noResult$.pipe(takeUntil(this.destroy$)).subscribe(this.noResult$);\n      listOfMeasureColumn$.pipe(takeUntil(this.destroy$)).subscribe(this.listOfMeasureColumn$);\n      showEmpty$.pipe(takeUntil(this.destroy$)).subscribe(this.showEmpty$);\n    }\n  }\n  onListOfAutoWidthChange(listOfAutoWidth) {\n    this.nzTableStyleService.setListOfAutoWidth(listOfAutoWidth);\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTbodyComponent_Factory(t) {\n      return new (t || NzTbodyComponent)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTbodyComponent,\n      selectors: [[\"tbody\"]],\n      hostVars: 2,\n      hostBindings: function NzTbodyComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-tbody\", ctx.isInsideTable);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 5,\n      vars: 6,\n      consts: [[4, \"ngIf\"], [\"class\", \"ant-table-placeholder\", \"nz-table-fixed-row\", \"\", 4, \"ngIf\"], [\"nz-table-measure-row\", \"\", 3, \"listOfMeasureColumn\", \"listOfAutoWidth\", 4, \"ngIf\"], [\"nz-table-measure-row\", \"\", 3, \"listOfAutoWidth\", \"listOfMeasureColumn\"], [\"nz-table-fixed-row\", \"\", 1, \"ant-table-placeholder\"], [\"nzComponentName\", \"table\", 3, \"specificContent\"]],\n      template: function NzTbodyComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTbodyComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵprojection(2);\n          i0.ɵɵtemplate(3, NzTbodyComponent_tr_3_Template, 3, 3, \"tr\", 1);\n          i0.ɵɵpipe(4, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.listOfMeasureColumn$));\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(4, 4, ctx.showEmpty$));\n        }\n      },\n      dependencies: [NgIf, AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule, i2$2.NzEmbedEmptyComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTbodyComponent, [{\n    type: Component,\n    args: [{\n      selector: 'tbody',\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"listOfMeasureColumn$ | async as listOfMeasureColumn\">\n      <tr\n        nz-table-measure-row\n        *ngIf=\"isInsideTable && listOfMeasureColumn.length\"\n        [listOfMeasureColumn]=\"listOfMeasureColumn\"\n        (listOfAutoWidth)=\"onListOfAutoWidthChange($event)\"\n      ></tr>\n    </ng-container>\n    <ng-content></ng-content>\n    <tr class=\"ant-table-placeholder\" nz-table-fixed-row *ngIf=\"showEmpty$ | async\">\n      <nz-embed-empty nzComponentName=\"table\" [specificContent]=\"(noResult$ | async)!\"></nz-embed-empty>\n    </tr>\n  `,\n      host: {\n        '[class.ant-table-tbody]': 'isInsideTable'\n      },\n      imports: [NgIf, AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], null);\n})();\nclass NzTableInnerScrollComponent {\n  setScrollPositionClassName(clear = false) {\n    const {\n      scrollWidth,\n      scrollLeft,\n      clientWidth\n    } = this.tableBodyElement.nativeElement;\n    const leftClassName = 'ant-table-ping-left';\n    const rightClassName = 'ant-table-ping-right';\n    if (scrollWidth === clientWidth && scrollWidth !== 0 || clear) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n    } else if (scrollLeft === 0) {\n      this.renderer.removeClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    } else if (scrollWidth === scrollLeft + clientWidth) {\n      this.renderer.removeClass(this.tableMainElement, rightClassName);\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n    } else {\n      this.renderer.addClass(this.tableMainElement, leftClassName);\n      this.renderer.addClass(this.tableMainElement, rightClassName);\n    }\n  }\n  constructor(renderer, ngZone, platform, resizeService) {\n    this.renderer = renderer;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.resizeService = resizeService;\n    this.data = [];\n    this.scrollX = null;\n    this.scrollY = null;\n    this.contentTemplate = null;\n    this.widthConfig = [];\n    this.listOfColWidth = [];\n    this.theadTemplate = null;\n    this.virtualTemplate = null;\n    this.virtualItemSize = 0;\n    this.virtualMaxBufferPx = 200;\n    this.virtualMinBufferPx = 100;\n    this.virtualForTrackBy = index => index;\n    this.headerStyleMap = {};\n    this.bodyStyleMap = {};\n    this.verticalScrollBarWidth = 0;\n    this.noDateVirtualHeight = '182px';\n    this.data$ = new Subject();\n    this.scroll$ = new Subject();\n    this.destroy$ = new Subject();\n  }\n  ngOnChanges(changes) {\n    const {\n      scrollX,\n      scrollY,\n      data\n    } = changes;\n    if (scrollX || scrollY) {\n      const hasVerticalScrollBar = this.verticalScrollBarWidth !== 0;\n      this.headerStyleMap = {\n        overflowX: 'hidden',\n        overflowY: this.scrollY && hasVerticalScrollBar ? 'scroll' : 'hidden'\n      };\n      this.bodyStyleMap = {\n        overflowY: this.scrollY ? 'scroll' : 'hidden',\n        overflowX: this.scrollX ? 'auto' : null,\n        maxHeight: this.scrollY\n      };\n      // Caretaker note: we have to emit the value outside of the Angular zone, thus DOM timer (`delay(0)`) and `scroll`\n      // event listener will be also added outside of the Angular zone.\n      this.ngZone.runOutsideAngular(() => this.scroll$.next());\n    }\n    if (data) {\n      // See the comment above.\n      this.ngZone.runOutsideAngular(() => this.data$.next());\n    }\n  }\n  ngAfterViewInit() {\n    if (this.platform.isBrowser) {\n      this.ngZone.runOutsideAngular(() => {\n        const scrollEvent$ = this.scroll$.pipe(startWith(null), delay(0), switchMap(() => fromEvent(this.tableBodyElement.nativeElement, 'scroll').pipe(startWith(true))), takeUntil(this.destroy$));\n        const resize$ = this.resizeService.subscribe().pipe(takeUntil(this.destroy$));\n        const data$ = this.data$.pipe(takeUntil(this.destroy$));\n        const setClassName$ = merge(scrollEvent$, resize$, data$, this.scroll$).pipe(startWith(true), delay(0), takeUntil(this.destroy$));\n        setClassName$.subscribe(() => this.setScrollPositionClassName());\n        scrollEvent$.pipe(filter(() => !!this.scrollY)).subscribe(() => this.tableHeaderElement.nativeElement.scrollLeft = this.tableBodyElement.nativeElement.scrollLeft);\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.setScrollPositionClassName(true);\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTableInnerScrollComponent_Factory(t) {\n      return new (t || NzTableInnerScrollComponent)(i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$3.Platform), i0.ɵɵdirectiveInject(i2.NzResizeService));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableInnerScrollComponent,\n      selectors: [[\"nz-table-inner-scroll\"]],\n      viewQuery: function NzTableInnerScrollComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c10, 5, ElementRef);\n          i0.ɵɵviewQuery(_c11, 5, ElementRef);\n          i0.ɵɵviewQuery(CdkVirtualScrollViewport, 5, CdkVirtualScrollViewport);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableHeaderElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.tableBodyElement = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.cdkVirtualScrollViewport = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-table-container\"],\n      inputs: {\n        data: \"data\",\n        scrollX: \"scrollX\",\n        scrollY: \"scrollY\",\n        contentTemplate: \"contentTemplate\",\n        widthConfig: \"widthConfig\",\n        listOfColWidth: \"listOfColWidth\",\n        theadTemplate: \"theadTemplate\",\n        virtualTemplate: \"virtualTemplate\",\n        virtualItemSize: \"virtualItemSize\",\n        virtualMaxBufferPx: \"virtualMaxBufferPx\",\n        virtualMinBufferPx: \"virtualMinBufferPx\",\n        tableMainElement: \"tableMainElement\",\n        virtualForTrackBy: \"virtualForTrackBy\",\n        verticalScrollBarWidth: \"verticalScrollBarWidth\"\n      },\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[\"tableHeaderElement\", \"\"], [\"tableBodyElement\", \"\"], [4, \"ngIf\"], [\"class\", \"ant-table-content\", 3, \"ngStyle\", 4, \"ngIf\"], [1, \"ant-table-header\", \"nz-table-hide-scrollbar\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\"], [\"class\", \"ant-table-body\", 3, \"ngStyle\", 4, \"ngIf\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\", \"height\", 4, \"ngIf\"], [1, \"ant-table-body\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"contentTemplate\"], [3, \"itemSize\", \"maxBufferPx\", \"minBufferPx\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\"], [4, \"cdkVirtualFor\", \"cdkVirtualForOf\", \"cdkVirtualForTrackBy\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [1, \"ant-table-content\", 3, \"ngStyle\"], [\"nz-table-content\", \"\", \"tableLayout\", \"fixed\", 3, \"scrollX\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\"]],\n      template: function NzTableInnerScrollComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableInnerScrollComponent_ng_container_0_Template, 6, 6, \"ng-container\", 2)(1, NzTableInnerScrollComponent_div_1_Template, 3, 5, \"div\", 3);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollY);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.scrollY);\n        }\n      },\n      dependencies: [NzTableContentComponent, NgIf, NgStyle, ScrollingModule, i3$1.CdkFixedSizeVirtualScroll, i3$1.CdkVirtualForOf, i3$1.CdkVirtualScrollViewport, NgTemplateOutlet, NzTbodyComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableInnerScrollComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-inner-scroll',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *ngIf=\"scrollY\">\n      <div #tableHeaderElement [ngStyle]=\"headerStyleMap\" class=\"ant-table-header nz-table-hide-scrollbar\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n        ></table>\n      </div>\n      <div #tableBodyElement *ngIf=\"!virtualTemplate\" class=\"ant-table-body\" [ngStyle]=\"bodyStyleMap\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [contentTemplate]=\"contentTemplate\"\n        ></table>\n      </div>\n      <cdk-virtual-scroll-viewport\n        #tableBodyElement\n        *ngIf=\"virtualTemplate\"\n        [itemSize]=\"virtualItemSize\"\n        [maxBufferPx]=\"virtualMaxBufferPx\"\n        [minBufferPx]=\"virtualMinBufferPx\"\n        [style.height]=\"data.length ? scrollY : noDateVirtualHeight\"\n      >\n        <table nz-table-content tableLayout=\"fixed\" [scrollX]=\"scrollX\" [listOfColWidth]=\"listOfColWidth\">\n          <tbody>\n            <ng-container *cdkVirtualFor=\"let item of data; let i = index; trackBy: virtualForTrackBy\">\n              <ng-template\n                [ngTemplateOutlet]=\"virtualTemplate\"\n                [ngTemplateOutletContext]=\"{ $implicit: item, index: i }\"\n              ></ng-template>\n            </ng-container>\n          </tbody>\n        </table>\n      </cdk-virtual-scroll-viewport>\n    </ng-container>\n    <div class=\"ant-table-content\" #tableBodyElement *ngIf=\"!scrollY\" [ngStyle]=\"bodyStyleMap\">\n      <table\n        nz-table-content\n        tableLayout=\"fixed\"\n        [scrollX]=\"scrollX\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n        [contentTemplate]=\"contentTemplate\"\n      ></table>\n    </div>\n  `,\n      host: {\n        class: 'ant-table-container'\n      },\n      imports: [NzTableContentComponent, NgIf, NgStyle, ScrollingModule, NgTemplateOutlet, NzTbodyComponent],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.Renderer2\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$3.Platform\n  }, {\n    type: i2.NzResizeService\n  }], {\n    data: [{\n      type: Input\n    }],\n    scrollX: [{\n      type: Input\n    }],\n    scrollY: [{\n      type: Input\n    }],\n    contentTemplate: [{\n      type: Input\n    }],\n    widthConfig: [{\n      type: Input\n    }],\n    listOfColWidth: [{\n      type: Input\n    }],\n    theadTemplate: [{\n      type: Input\n    }],\n    virtualTemplate: [{\n      type: Input\n    }],\n    virtualItemSize: [{\n      type: Input\n    }],\n    virtualMaxBufferPx: [{\n      type: Input\n    }],\n    virtualMinBufferPx: [{\n      type: Input\n    }],\n    tableMainElement: [{\n      type: Input\n    }],\n    virtualForTrackBy: [{\n      type: Input\n    }],\n    tableHeaderElement: [{\n      type: ViewChild,\n      args: ['tableHeaderElement', {\n        read: ElementRef\n      }]\n    }],\n    tableBodyElement: [{\n      type: ViewChild,\n      args: ['tableBodyElement', {\n        read: ElementRef\n      }]\n    }],\n    cdkVirtualScrollViewport: [{\n      type: ViewChild,\n      args: [CdkVirtualScrollViewport, {\n        read: CdkVirtualScrollViewport\n      }]\n    }],\n    verticalScrollBarWidth: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableVirtualScrollDirective {\n  constructor(templateRef) {\n    this.templateRef = templateRef;\n  }\n  static ngTemplateContextGuard(_dir, _ctx) {\n    return true;\n  }\n  static {\n    this.ɵfac = function NzTableVirtualScrollDirective_Factory(t) {\n      return new (t || NzTableVirtualScrollDirective)(i0.ɵɵdirectiveInject(i0.TemplateRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTableVirtualScrollDirective,\n      selectors: [[\"\", \"nz-virtual-scroll\", \"\"]],\n      exportAs: [\"nzVirtualScroll\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableVirtualScrollDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nz-virtual-scroll]',\n      exportAs: 'nzVirtualScroll',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.TemplateRef\n  }], null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableTitleFooterComponent {\n  constructor() {\n    this.title = null;\n    this.footer = null;\n  }\n  static {\n    this.ɵfac = function NzTableTitleFooterComponent_Factory(t) {\n      return new (t || NzTableTitleFooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableTitleFooterComponent,\n      selectors: [[\"nz-table-title-footer\"]],\n      hostVars: 4,\n      hostBindings: function NzTableTitleFooterComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-title\", ctx.title !== null)(\"ant-table-footer\", ctx.footer !== null);\n        }\n      },\n      inputs: {\n        title: \"title\",\n        footer: \"footer\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 2,\n      vars: 2,\n      consts: [[4, \"nzStringTemplateOutlet\"]],\n      template: function NzTableTitleFooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzTableTitleFooterComponent_ng_container_0_Template, 2, 1, \"ng-container\", 0)(1, NzTableTitleFooterComponent_ng_container_1_Template, 2, 1, \"ng-container\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.title);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"nzStringTemplateOutlet\", ctx.footer);\n        }\n      },\n      dependencies: [NzOutletModule, i1$4.NzStringTemplateOutletDirective],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableTitleFooterComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table-title-footer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-container *nzStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n    <ng-container *nzStringTemplateOutlet=\"footer\">{{ footer }}</ng-container>\n  `,\n      host: {\n        '[class.ant-table-title]': `title !== null`,\n        '[class.ant-table-footer]': `footer !== null`\n      },\n      imports: [NzOutletModule],\n      standalone: true\n    }]\n  }], null, {\n    title: [{\n      type: Input\n    }],\n    footer: [{\n      type: Input\n    }]\n  });\n})();\nconst NZ_CONFIG_MODULE_NAME = 'table';\nclass NzTableComponent {\n  onPageSizeChange(size) {\n    this.nzTableDataService.updatePageSize(size);\n  }\n  onPageIndexChange(index) {\n    this.nzTableDataService.updatePageIndex(index);\n  }\n  constructor(elementRef, nzResizeObserver, nzConfigService, cdr, nzTableStyleService, nzTableDataService, directionality) {\n    this.elementRef = elementRef;\n    this.nzResizeObserver = nzResizeObserver;\n    this.nzConfigService = nzConfigService;\n    this.cdr = cdr;\n    this.nzTableStyleService = nzTableStyleService;\n    this.nzTableDataService = nzTableDataService;\n    this.directionality = directionality;\n    this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n    this.nzTableLayout = 'auto';\n    this.nzShowTotal = null;\n    this.nzItemRender = null;\n    this.nzTitle = null;\n    this.nzFooter = null;\n    this.nzNoResult = undefined;\n    this.nzPageSizeOptions = [10, 20, 30, 40, 50];\n    this.nzVirtualItemSize = 0;\n    this.nzVirtualMaxBufferPx = 200;\n    this.nzVirtualMinBufferPx = 100;\n    this.nzVirtualForTrackBy = index => index;\n    this.nzLoadingDelay = 0;\n    this.nzPageIndex = 1;\n    this.nzPageSize = 10;\n    this.nzTotal = 0;\n    this.nzWidthConfig = [];\n    this.nzData = [];\n    this.nzCustomColumn = [];\n    this.nzPaginationPosition = 'bottom';\n    this.nzScroll = {\n      x: null,\n      y: null\n    };\n    this.nzPaginationType = 'default';\n    this.nzFrontPagination = true;\n    this.nzTemplateMode = false;\n    this.nzShowPagination = true;\n    this.nzLoading = false;\n    this.nzOuterBordered = false;\n    this.nzLoadingIndicator = null;\n    this.nzBordered = false;\n    this.nzSize = 'default';\n    this.nzShowSizeChanger = false;\n    this.nzHideOnSinglePage = false;\n    this.nzShowQuickJumper = false;\n    this.nzSimple = false;\n    this.nzPageSizeChange = new EventEmitter();\n    this.nzPageIndexChange = new EventEmitter();\n    this.nzQueryParams = new EventEmitter();\n    this.nzCurrentPageDataChange = new EventEmitter();\n    this.nzCustomColumnChange = new EventEmitter();\n    /** public data for ngFor tr */\n    this.data = [];\n    this.scrollX = null;\n    this.scrollY = null;\n    this.theadTemplate = null;\n    this.listOfAutoColWidth = [];\n    this.listOfManualColWidth = [];\n    this.hasFixLeft = false;\n    this.hasFixRight = false;\n    this.showPagination = true;\n    this.destroy$ = new Subject();\n    this.templateMode$ = new BehaviorSubject(false);\n    this.dir = 'ltr';\n    this.verticalScrollBarWidth = 0;\n    this.nzConfigService.getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME).pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnInit() {\n    const {\n      pageIndexDistinct$,\n      pageSizeDistinct$,\n      listOfCurrentPageData$,\n      total$,\n      queryParams$,\n      listOfCustomColumn$\n    } = this.nzTableDataService;\n    const {\n      theadTemplate$,\n      hasFixLeft$,\n      hasFixRight$\n    } = this.nzTableStyleService;\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.cdr.detectChanges();\n    });\n    queryParams$.pipe(takeUntil(this.destroy$)).subscribe(this.nzQueryParams);\n    pageIndexDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageIndex => {\n      if (pageIndex !== this.nzPageIndex) {\n        this.nzPageIndex = pageIndex;\n        this.nzPageIndexChange.next(pageIndex);\n      }\n    });\n    pageSizeDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageSize => {\n      if (pageSize !== this.nzPageSize) {\n        this.nzPageSize = pageSize;\n        this.nzPageSizeChange.next(pageSize);\n      }\n    });\n    total$.pipe(takeUntil(this.destroy$), filter(() => this.nzFrontPagination)).subscribe(total => {\n      if (total !== this.nzTotal) {\n        this.nzTotal = total;\n        this.cdr.markForCheck();\n      }\n    });\n    listOfCurrentPageData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.data = data;\n      this.nzCurrentPageDataChange.next(data);\n      this.cdr.markForCheck();\n    });\n    listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n      this.nzCustomColumn = data;\n      this.nzCustomColumnChange.next(data);\n      this.cdr.markForCheck();\n    });\n    theadTemplate$.pipe(takeUntil(this.destroy$)).subscribe(theadTemplate => {\n      this.theadTemplate = theadTemplate;\n      this.cdr.markForCheck();\n    });\n    hasFixLeft$.pipe(takeUntil(this.destroy$)).subscribe(hasFixLeft => {\n      this.hasFixLeft = hasFixLeft;\n      this.cdr.markForCheck();\n    });\n    hasFixRight$.pipe(takeUntil(this.destroy$)).subscribe(hasFixRight => {\n      this.hasFixRight = hasFixRight;\n      this.cdr.markForCheck();\n    });\n    combineLatest([total$, this.templateMode$]).pipe(map(([total, templateMode]) => total === 0 && !templateMode), takeUntil(this.destroy$)).subscribe(empty => {\n      this.nzTableStyleService.setShowEmpty(empty);\n    });\n    this.verticalScrollBarWidth = measureScrollbar('vertical');\n    this.nzTableStyleService.listOfListOfThWidthPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n      this.listOfAutoColWidth = listOfWidth;\n      this.cdr.markForCheck();\n    });\n    this.nzTableStyleService.manualWidthConfigPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n      this.listOfManualColWidth = listOfWidth;\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnChanges(changes) {\n    const {\n      nzScroll,\n      nzPageIndex,\n      nzPageSize,\n      nzFrontPagination,\n      nzData,\n      nzCustomColumn,\n      nzWidthConfig,\n      nzNoResult,\n      nzTemplateMode\n    } = changes;\n    if (nzPageIndex) {\n      this.nzTableDataService.updatePageIndex(this.nzPageIndex);\n    }\n    if (nzPageSize) {\n      this.nzTableDataService.updatePageSize(this.nzPageSize);\n    }\n    if (nzData) {\n      this.nzData = this.nzData || [];\n      this.nzTableDataService.updateListOfData(this.nzData);\n    }\n    if (nzCustomColumn) {\n      this.nzCustomColumn = this.nzCustomColumn || [];\n      this.nzTableDataService.updateListOfCustomColumn(this.nzCustomColumn);\n    }\n    if (nzFrontPagination) {\n      this.nzTableDataService.updateFrontPagination(this.nzFrontPagination);\n    }\n    if (nzScroll) {\n      this.setScrollOnChanges();\n    }\n    if (nzWidthConfig) {\n      this.nzTableStyleService.setTableWidthConfig(this.nzWidthConfig);\n    }\n    if (nzTemplateMode) {\n      this.templateMode$.next(this.nzTemplateMode);\n    }\n    if (nzNoResult) {\n      this.nzTableStyleService.setNoResult(this.nzNoResult);\n    }\n    this.updateShowPagination();\n  }\n  ngAfterViewInit() {\n    this.nzResizeObserver.observe(this.elementRef).pipe(map(([entry]) => {\n      const {\n        width\n      } = entry.target.getBoundingClientRect();\n      const scrollBarWidth = this.scrollY ? this.verticalScrollBarWidth : 0;\n      return Math.floor(width - scrollBarWidth);\n    }), takeUntil(this.destroy$)).subscribe(this.nzTableStyleService.hostWidth$);\n    if (this.nzTableInnerScrollComponent && this.nzTableInnerScrollComponent.cdkVirtualScrollViewport) {\n      this.cdkVirtualScrollViewport = this.nzTableInnerScrollComponent.cdkVirtualScrollViewport;\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  setScrollOnChanges() {\n    this.scrollX = this.nzScroll && this.nzScroll.x || null;\n    this.scrollY = this.nzScroll && this.nzScroll.y || null;\n    this.nzTableStyleService.setScroll(this.scrollX, this.scrollY);\n  }\n  updateShowPagination() {\n    this.showPagination = this.nzHideOnSinglePage && this.nzData.length > this.nzPageSize || this.nzData.length > 0 && !this.nzHideOnSinglePage || !this.nzFrontPagination && this.nzTotal > this.nzPageSize;\n  }\n  static {\n    this.ɵfac = function NzTableComponent_Factory(t) {\n      return new (t || NzTableComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1$2.NzResizeObserver), i0.ɵɵdirectiveInject(i1.NzConfigService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(NzTableStyleService), i0.ɵɵdirectiveInject(NzTableDataService), i0.ɵɵdirectiveInject(i5$1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTableComponent,\n      selectors: [[\"nz-table\"]],\n      contentQueries: function NzTableComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTableVirtualScrollDirective, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzVirtualScrollDirective = _t.first);\n        }\n      },\n      viewQuery: function NzTableComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(NzTableInnerScrollComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.nzTableInnerScrollComponent = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-table-wrapper\"],\n      hostVars: 4,\n      hostBindings: function NzTableComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-wrapper-rtl\", ctx.dir === \"rtl\")(\"ant-table-custom-column\", ctx.nzCustomColumn.length);\n        }\n      },\n      inputs: {\n        nzTableLayout: \"nzTableLayout\",\n        nzShowTotal: \"nzShowTotal\",\n        nzItemRender: \"nzItemRender\",\n        nzTitle: \"nzTitle\",\n        nzFooter: \"nzFooter\",\n        nzNoResult: \"nzNoResult\",\n        nzPageSizeOptions: \"nzPageSizeOptions\",\n        nzVirtualItemSize: \"nzVirtualItemSize\",\n        nzVirtualMaxBufferPx: \"nzVirtualMaxBufferPx\",\n        nzVirtualMinBufferPx: \"nzVirtualMinBufferPx\",\n        nzVirtualForTrackBy: \"nzVirtualForTrackBy\",\n        nzLoadingDelay: \"nzLoadingDelay\",\n        nzPageIndex: \"nzPageIndex\",\n        nzPageSize: \"nzPageSize\",\n        nzTotal: \"nzTotal\",\n        nzWidthConfig: \"nzWidthConfig\",\n        nzData: \"nzData\",\n        nzCustomColumn: \"nzCustomColumn\",\n        nzPaginationPosition: \"nzPaginationPosition\",\n        nzScroll: \"nzScroll\",\n        nzPaginationType: \"nzPaginationType\",\n        nzFrontPagination: \"nzFrontPagination\",\n        nzTemplateMode: \"nzTemplateMode\",\n        nzShowPagination: \"nzShowPagination\",\n        nzLoading: \"nzLoading\",\n        nzOuterBordered: \"nzOuterBordered\",\n        nzLoadingIndicator: \"nzLoadingIndicator\",\n        nzBordered: \"nzBordered\",\n        nzSize: \"nzSize\",\n        nzShowSizeChanger: \"nzShowSizeChanger\",\n        nzHideOnSinglePage: \"nzHideOnSinglePage\",\n        nzShowQuickJumper: \"nzShowQuickJumper\",\n        nzSimple: \"nzSimple\"\n      },\n      outputs: {\n        nzPageSizeChange: \"nzPageSizeChange\",\n        nzPageIndexChange: \"nzPageIndexChange\",\n        nzQueryParams: \"nzQueryParams\",\n        nzCurrentPageDataChange: \"nzCurrentPageDataChange\",\n        nzCustomColumnChange: \"nzCustomColumnChange\"\n      },\n      exportAs: [\"nzTable\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzTableStyleService, NzTableDataService]), i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 14,\n      vars: 27,\n      consts: [[\"tableMainElement\", \"\"], [\"defaultTemplate\", \"\"], [\"paginationTemplate\", \"\"], [\"contentTemplate\", \"\"], [3, \"nzDelay\", \"nzSpinning\", \"nzIndicator\"], [4, \"ngIf\"], [1, \"ant-table\"], [3, \"title\", 4, \"ngIf\"], [3, \"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"listOfColWidth\", \"theadTemplate\", \"verticalScrollBarWidth\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\", 4, \"ngIf\", \"ngIfElse\"], [3, \"footer\", 4, \"ngIf\"], [3, \"ngTemplateOutlet\"], [3, \"title\"], [3, \"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"listOfColWidth\", \"theadTemplate\", \"verticalScrollBarWidth\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\"], [3, \"tableLayout\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\"], [3, \"footer\"], [\"class\", \"ant-table-pagination ant-table-pagination-right\", 3, \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\", \"nzPageSizeChange\", \"nzPageIndexChange\", 4, \"ngIf\"], [1, \"ant-table-pagination\", \"ant-table-pagination-right\", 3, \"nzPageSizeChange\", \"nzPageIndexChange\", \"hidden\", \"nzShowSizeChanger\", \"nzPageSizeOptions\", \"nzItemRender\", \"nzShowQuickJumper\", \"nzHideOnSinglePage\", \"nzShowTotal\", \"nzSize\", \"nzPageSize\", \"nzTotal\", \"nzSimple\", \"nzPageIndex\"]],\n      template: function NzTableComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"nz-spin\", 4);\n          i0.ɵɵtemplate(1, NzTableComponent_ng_container_1_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementStart(2, \"div\", 6, 0);\n          i0.ɵɵtemplate(4, NzTableComponent_nz_table_title_footer_4_Template, 1, 1, \"nz-table-title-footer\", 7)(5, NzTableComponent_nz_table_inner_scroll_5_Template, 1, 13, \"nz-table-inner-scroll\", 8)(6, NzTableComponent_ng_template_6_Template, 1, 4, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(8, NzTableComponent_nz_table_title_footer_8_Template, 1, 1, \"nz-table-title-footer\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(9, NzTableComponent_ng_container_9_Template, 2, 1, \"ng-container\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, NzTableComponent_ng_template_10_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(12, NzTableComponent_ng_template_12_Template, 1, 0, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const defaultTemplate_r6 = i0.ɵɵreference(7);\n          i0.ɵɵproperty(\"nzDelay\", ctx.nzLoadingDelay)(\"nzSpinning\", ctx.nzLoading)(\"nzIndicator\", ctx.nzLoadingIndicator);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"top\");\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"ant-table-rtl\", ctx.dir === \"rtl\")(\"ant-table-fixed-header\", ctx.nzData.length && ctx.scrollY)(\"ant-table-fixed-column\", ctx.scrollX)(\"ant-table-has-fix-left\", ctx.hasFixLeft)(\"ant-table-has-fix-right\", ctx.hasFixRight)(\"ant-table-bordered\", ctx.nzBordered)(\"nz-table-out-bordered\", ctx.nzOuterBordered && !ctx.nzBordered)(\"ant-table-middle\", ctx.nzSize === \"middle\")(\"ant-table-small\", ctx.nzSize === \"small\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzTitle);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.scrollY || ctx.scrollX)(\"ngIfElse\", defaultTemplate_r6);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzFooter);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.nzPaginationPosition === \"both\" || ctx.nzPaginationPosition === \"bottom\");\n        }\n      },\n      dependencies: [NzSpinComponent, NgIf, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule, i6$1.NzPaginationComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzFrontPagination\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzTemplateMode\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzShowPagination\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzLoading\", void 0);\n__decorate([InputBoolean()], NzTableComponent.prototype, \"nzOuterBordered\", void 0);\n__decorate([WithConfig()], NzTableComponent.prototype, \"nzLoadingIndicator\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzBordered\", void 0);\n__decorate([WithConfig()], NzTableComponent.prototype, \"nzSize\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([WithConfig(), InputBoolean()], NzTableComponent.prototype, \"nzSimple\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-table',\n      exportAs: 'nzTable',\n      providers: [NzTableStyleService, NzTableDataService],\n      preserveWhitespaces: false,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <nz-spin [nzDelay]=\"nzLoadingDelay\" [nzSpinning]=\"nzLoading\" [nzIndicator]=\"nzLoadingIndicator\">\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'top'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n      <div\n        #tableMainElement\n        class=\"ant-table\"\n        [class.ant-table-rtl]=\"dir === 'rtl'\"\n        [class.ant-table-fixed-header]=\"nzData.length && scrollY\"\n        [class.ant-table-fixed-column]=\"scrollX\"\n        [class.ant-table-has-fix-left]=\"hasFixLeft\"\n        [class.ant-table-has-fix-right]=\"hasFixRight\"\n        [class.ant-table-bordered]=\"nzBordered\"\n        [class.nz-table-out-bordered]=\"nzOuterBordered && !nzBordered\"\n        [class.ant-table-middle]=\"nzSize === 'middle'\"\n        [class.ant-table-small]=\"nzSize === 'small'\"\n      >\n        <nz-table-title-footer [title]=\"nzTitle\" *ngIf=\"nzTitle\"></nz-table-title-footer>\n        <nz-table-inner-scroll\n          *ngIf=\"scrollY || scrollX; else defaultTemplate\"\n          [data]=\"data\"\n          [scrollX]=\"scrollX\"\n          [scrollY]=\"scrollY\"\n          [contentTemplate]=\"contentTemplate\"\n          [listOfColWidth]=\"listOfAutoColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [verticalScrollBarWidth]=\"verticalScrollBarWidth\"\n          [virtualTemplate]=\"nzVirtualScrollDirective ? nzVirtualScrollDirective.templateRef : null\"\n          [virtualItemSize]=\"nzVirtualItemSize\"\n          [virtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [virtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n          [tableMainElement]=\"tableMainElement\"\n          [virtualForTrackBy]=\"nzVirtualForTrackBy\"\n        ></nz-table-inner-scroll>\n        <ng-template #defaultTemplate>\n          <nz-table-inner-default\n            [tableLayout]=\"nzTableLayout\"\n            [listOfColWidth]=\"listOfManualColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [contentTemplate]=\"contentTemplate\"\n          ></nz-table-inner-default>\n        </ng-template>\n        <nz-table-title-footer [footer]=\"nzFooter\" *ngIf=\"nzFooter\"></nz-table-title-footer>\n      </div>\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'bottom'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n    </nz-spin>\n    <ng-template #paginationTemplate>\n      <nz-pagination\n        *ngIf=\"nzShowPagination && data.length\"\n        [hidden]=\"!showPagination\"\n        class=\"ant-table-pagination ant-table-pagination-right\"\n        [nzShowSizeChanger]=\"nzShowSizeChanger\"\n        [nzPageSizeOptions]=\"nzPageSizeOptions\"\n        [nzItemRender]=\"nzItemRender!\"\n        [nzShowQuickJumper]=\"nzShowQuickJumper\"\n        [nzHideOnSinglePage]=\"nzHideOnSinglePage\"\n        [nzShowTotal]=\"nzShowTotal\"\n        [nzSize]=\"nzPaginationType === 'small' ? 'small' : nzSize === 'default' ? 'default' : 'small'\"\n        [nzPageSize]=\"nzPageSize\"\n        [nzTotal]=\"nzTotal\"\n        [nzSimple]=\"nzSimple\"\n        [nzPageIndex]=\"nzPageIndex\"\n        (nzPageSizeChange)=\"onPageSizeChange($event)\"\n        (nzPageIndexChange)=\"onPageIndexChange($event)\"\n      ></nz-pagination>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n      host: {\n        class: 'ant-table-wrapper',\n        '[class.ant-table-wrapper-rtl]': 'dir === \"rtl\"',\n        '[class.ant-table-custom-column]': `nzCustomColumn.length`\n      },\n      imports: [NzSpinComponent, NgIf, NgTemplateOutlet, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableInnerDefaultComponent, NzPaginationModule],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1$2.NzResizeObserver\n  }, {\n    type: i1.NzConfigService\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: NzTableStyleService\n  }, {\n    type: NzTableDataService\n  }, {\n    type: i5$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    nzTableLayout: [{\n      type: Input\n    }],\n    nzShowTotal: [{\n      type: Input\n    }],\n    nzItemRender: [{\n      type: Input\n    }],\n    nzTitle: [{\n      type: Input\n    }],\n    nzFooter: [{\n      type: Input\n    }],\n    nzNoResult: [{\n      type: Input\n    }],\n    nzPageSizeOptions: [{\n      type: Input\n    }],\n    nzVirtualItemSize: [{\n      type: Input\n    }],\n    nzVirtualMaxBufferPx: [{\n      type: Input\n    }],\n    nzVirtualMinBufferPx: [{\n      type: Input\n    }],\n    nzVirtualForTrackBy: [{\n      type: Input\n    }],\n    nzLoadingDelay: [{\n      type: Input\n    }],\n    nzPageIndex: [{\n      type: Input\n    }],\n    nzPageSize: [{\n      type: Input\n    }],\n    nzTotal: [{\n      type: Input\n    }],\n    nzWidthConfig: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzCustomColumn: [{\n      type: Input\n    }],\n    nzPaginationPosition: [{\n      type: Input\n    }],\n    nzScroll: [{\n      type: Input\n    }],\n    nzPaginationType: [{\n      type: Input\n    }],\n    nzFrontPagination: [{\n      type: Input\n    }],\n    nzTemplateMode: [{\n      type: Input\n    }],\n    nzShowPagination: [{\n      type: Input\n    }],\n    nzLoading: [{\n      type: Input\n    }],\n    nzOuterBordered: [{\n      type: Input\n    }],\n    nzLoadingIndicator: [{\n      type: Input\n    }],\n    nzBordered: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzShowSizeChanger: [{\n      type: Input\n    }],\n    nzHideOnSinglePage: [{\n      type: Input\n    }],\n    nzShowQuickJumper: [{\n      type: Input\n    }],\n    nzSimple: [{\n      type: Input\n    }],\n    nzPageSizeChange: [{\n      type: Output\n    }],\n    nzPageIndexChange: [{\n      type: Output\n    }],\n    nzQueryParams: [{\n      type: Output\n    }],\n    nzCurrentPageDataChange: [{\n      type: Output\n    }],\n    nzCustomColumnChange: [{\n      type: Output\n    }],\n    nzVirtualScrollDirective: [{\n      type: ContentChild,\n      args: [NzTableVirtualScrollDirective, {\n        static: false\n      }]\n    }],\n    nzTableInnerScrollComponent: [{\n      type: ViewChild,\n      args: [NzTableInnerScrollComponent]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrDirective {\n  constructor(nzTableStyleService) {\n    this.nzTableStyleService = nzTableStyleService;\n    this.destroy$ = new Subject();\n    this.listOfFixedColumns$ = new ReplaySubject(1);\n    this.listOfColumns$ = new ReplaySubject(1);\n    this.listOfFixedColumnsChanges$ = this.listOfFixedColumns$.pipe(switchMap(list => merge(...[this.listOfFixedColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfFixedColumns$))), takeUntil(this.destroy$));\n    this.listOfFixedLeftColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzLeft !== false)));\n    this.listOfFixedRightColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzRight !== false)));\n    this.listOfColumnsChanges$ = this.listOfColumns$.pipe(switchMap(list => merge(...[this.listOfColumns$, ...list.map(c => c.changes$)]).pipe(mergeMap(() => this.listOfColumns$))), takeUntil(this.destroy$));\n    this.isInsideTable = false;\n    this.isInsideTable = !!nzTableStyleService;\n  }\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      this.listOfCellFixedDirective.changes.pipe(startWith(this.listOfCellFixedDirective), takeUntil(this.destroy$)).subscribe(this.listOfFixedColumns$);\n      this.listOfNzThDirective.changes.pipe(startWith(this.listOfNzThDirective), takeUntil(this.destroy$)).subscribe(this.listOfColumns$);\n      /** set last left and first right **/\n      this.listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeft => {\n        listOfFixedLeft.forEach(cell => cell.setIsLastLeft(cell === listOfFixedLeft[listOfFixedLeft.length - 1]));\n      });\n      this.listOfFixedRightColumnChanges$.subscribe(listOfFixedRight => {\n        listOfFixedRight.forEach(cell => cell.setIsFirstRight(cell === listOfFixedRight[0]));\n      });\n      /** calculate fixed nzLeft and nzRight **/\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedLeftColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfLeftCell]) => {\n        listOfLeftCell.forEach((cell, index) => {\n          if (cell.isAutoLeft) {\n            const currentArray = listOfLeftCell.slice(0, index);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(0, count).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoLeftWidth(`${width}px`);\n          }\n        });\n      });\n      combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedRightColumnChanges$]).pipe(takeUntil(this.destroy$)).subscribe(([listOfAutoWidth, listOfRightCell]) => {\n        listOfRightCell.forEach((_, index) => {\n          const cell = listOfRightCell[listOfRightCell.length - index - 1];\n          if (cell.isAutoRight) {\n            const currentArray = listOfRightCell.slice(listOfRightCell.length - index, listOfRightCell.length);\n            const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n            const width = listOfAutoWidth.slice(listOfAutoWidth.length - count, listOfAutoWidth.length).reduce((pre, cur) => pre + cur, 0);\n            cell.setAutoRightWidth(`${width}px`);\n          }\n        });\n      });\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTrDirective_Factory(t) {\n      return new (t || NzTrDirective)(i0.ɵɵdirectiveInject(NzTableStyleService, 8));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTrDirective,\n      selectors: [[\"tr\", 3, \"mat-row\", \"\", 3, \"mat-header-row\", \"\", 3, \"nz-table-measure-row\", \"\", 3, \"nzExpand\", \"\", 3, \"nz-table-fixed-row\", \"\"]],\n      contentQueries: function NzTrDirective_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzThMeasureDirective, 4);\n          i0.ɵɵcontentQuery(dirIndex, NzCellFixedDirective, 4);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfCellFixedDirective = _t);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function NzTrDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-table-row\", ctx.isInsideTable);\n        }\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr:not([mat-row]):not([mat-header-row]):not([nz-table-measure-row]):not([nzExpand]):not([nz-table-fixed-row])',\n      host: {\n        '[class.ant-table-row]': 'isInsideTable'\n      },\n      standalone: true\n    }]\n  }], () => [{\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    listOfNzThDirective: [{\n      type: ContentChildren,\n      args: [NzThMeasureDirective]\n    }],\n    listOfCellFixedDirective: [{\n      type: ContentChildren,\n      args: [NzCellFixedDirective]\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTheadComponent {\n  constructor(elementRef, renderer, nzTableStyleService, nzTableDataService) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.nzTableStyleService = nzTableStyleService;\n    this.nzTableDataService = nzTableDataService;\n    this.destroy$ = new Subject();\n    this.isInsideTable = false;\n    this.nzSortOrderChange = new EventEmitter();\n    this.isInsideTable = !!this.nzTableStyleService;\n  }\n  ngOnInit() {\n    if (this.nzTableStyleService) {\n      this.nzTableStyleService.setTheadTemplate(this.templateRef);\n    }\n  }\n  ngAfterContentInit() {\n    if (this.nzTableStyleService) {\n      const firstTableRow$ = this.listOfNzTrDirective.changes.pipe(startWith(this.listOfNzTrDirective), map(item => item && item.first));\n      const listOfColumnsChanges$ = firstTableRow$.pipe(switchMap(firstTableRow => firstTableRow ? firstTableRow.listOfColumnsChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfColumnsChanges$.subscribe(data => this.nzTableStyleService.setListOfTh(data));\n      /** TODO: need reset the measure row when scrollX change **/\n      this.nzTableStyleService.enableAutoMeasure$.pipe(switchMap(enable => enable ? listOfColumnsChanges$ : of([]))).pipe(takeUntil(this.destroy$)).subscribe(data => this.nzTableStyleService.setListOfMeasureColumn(data));\n      const listOfFixedLeftColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedLeftColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      const listOfFixedRightColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => firstTr ? firstTr.listOfFixedRightColumnChanges$ : EMPTY), takeUntil(this.destroy$));\n      listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeftColumn => {\n        this.nzTableStyleService.setHasFixLeft(listOfFixedLeftColumn.length !== 0);\n      });\n      listOfFixedRightColumnChanges$.subscribe(listOfFixedRightColumn => {\n        this.nzTableStyleService.setHasFixRight(listOfFixedRightColumn.length !== 0);\n      });\n    }\n    if (this.nzTableDataService) {\n      const listOfColumn$ = this.listOfNzThAddOnComponent.changes.pipe(startWith(this.listOfNzThAddOnComponent));\n      const manualSort$ = listOfColumn$.pipe(switchMap(() => merge(...this.listOfNzThAddOnComponent.map(th => th.manualClickOrder$))), takeUntil(this.destroy$));\n      manualSort$.subscribe(data => {\n        const emitValue = {\n          key: data.nzColumnKey,\n          value: data.sortOrder\n        };\n        this.nzSortOrderChange.emit(emitValue);\n        if (data.nzSortFn && data.nzSortPriority === false) {\n          this.listOfNzThAddOnComponent.filter(th => th !== data).forEach(th => th.clearSortOrder());\n        }\n      });\n      const listOfCalcOperator$ = listOfColumn$.pipe(switchMap(list => merge(...[listOfColumn$, ...list.map(c => c.calcOperatorChange$)]).pipe(mergeMap(() => listOfColumn$))), map(list => list.filter(item => !!item.nzSortFn || !!item.nzFilterFn).map(item => {\n        const {\n          nzSortFn,\n          sortOrder,\n          nzFilterFn,\n          nzFilterValue,\n          nzSortPriority,\n          nzColumnKey\n        } = item;\n        return {\n          key: nzColumnKey,\n          sortFn: nzSortFn,\n          sortPriority: nzSortPriority,\n          sortOrder: sortOrder,\n          filterFn: nzFilterFn,\n          filterValue: nzFilterValue\n        };\n      })),\n      // TODO: after checked error here\n      delay(0), takeUntil(this.destroy$));\n      listOfCalcOperator$.subscribe(list => {\n        this.nzTableDataService.listOfCalcOperator$.next(list);\n      });\n    }\n  }\n  ngAfterViewInit() {\n    if (this.nzTableStyleService) {\n      this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzTheadComponent_Factory(t) {\n      return new (t || NzTheadComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(NzTableStyleService, 8), i0.ɵɵdirectiveInject(NzTableDataService, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzTheadComponent,\n      selectors: [[\"thead\", 9, \"ant-table-thead\"]],\n      contentQueries: function NzTheadComponent_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, NzTrDirective, 5);\n          i0.ɵɵcontentQuery(dirIndex, NzThAddOnComponent, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzTrDirective = _t);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listOfNzThAddOnComponent = _t);\n        }\n      },\n      viewQuery: function NzTheadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c13, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.templateRef = _t.first);\n        }\n      },\n      outputs: {\n        nzSortOrderChange: \"nzSortOrderChange\"\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 3,\n      vars: 1,\n      consts: [[\"contentTemplate\", \"\"], [4, \"ngIf\"], [3, \"ngTemplateOutlet\"]],\n      template: function NzTheadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzTheadComponent_ng_template_0_Template, 1, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzTheadComponent_ng_container_2_Template, 2, 1, \"ng-container\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isInsideTable);\n        }\n      },\n      dependencies: [NgIf, NgTemplateOutlet],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTheadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'thead:not(.ant-table-thead)',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    <ng-container *ngIf=\"!isInsideTable\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </ng-container>\n  `,\n      imports: [NgIf, NgTemplateOutlet],\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: NzTableStyleService,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: NzTableDataService,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    templateRef: [{\n      type: ViewChild,\n      args: ['contentTemplate', {\n        static: true\n      }]\n    }],\n    listOfNzTrDirective: [{\n      type: ContentChildren,\n      args: [NzTrDirective, {\n        descendants: true\n      }]\n    }],\n    listOfNzThAddOnComponent: [{\n      type: ContentChildren,\n      args: [NzThAddOnComponent, {\n        descendants: true\n      }]\n    }],\n    nzSortOrderChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrExpandDirective {\n  constructor() {\n    this.nzExpand = true;\n  }\n  static {\n    this.ɵfac = function NzTrExpandDirective_Factory(t) {\n      return new (t || NzTrExpandDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzTrExpandDirective,\n      selectors: [[\"tr\", \"nzExpand\", \"\"]],\n      hostAttrs: [1, \"ant-table-expanded-row\"],\n      hostVars: 1,\n      hostBindings: function NzTrExpandDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"hidden\", !ctx.nzExpand);\n        }\n      },\n      inputs: {\n        nzExpand: \"nzExpand\"\n      },\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTrExpandDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr[nzExpand]',\n      host: {\n        class: 'ant-table-expanded-row',\n        '[hidden]': `!nzExpand`\n      },\n      standalone: true\n    }]\n  }], () => [], {\n    nzExpand: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableModule {\n  static {\n    this.ɵfac = function NzTableModule_Factory(t) {\n      return new (t || NzTableModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzTableModule,\n      imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n      exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzTableComponent, NzThAddOnComponent, NzTdAddOnComponent, NzTbodyComponent, NzTableTitleFooterComponent, NzTableInnerScrollComponent, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzFilterTriggerComponent, NzThSelectionComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzTableModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTrExpandDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzTableContentComponent, NzTableTitleFooterComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTrMeasureComponent, NzRowIndentDirective, NzRowExpandButtonDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzTableSortersComponent, NzTableFilterComponent, NzTableSelectionComponent, NzCellEllipsisDirective, NzFilterTriggerComponent, NzTableFixedRowComponent, NzThSelectionComponent],\n      exports: [NzTableComponent, NzThAddOnComponent, NzTableCellDirective, NzThMeasureDirective, NzTdAddOnComponent, NzTheadComponent, NzTbodyComponent, NzTrDirective, NzTableVirtualScrollDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzTrExpandDirective, NzCellBreakWordDirective, NzCellAlignDirective, NzCellEllipsisDirective, NzTableFixedRowComponent, NzThSelectionComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCellAlignDirective, NzCellBreakWordDirective, NzCellEllipsisDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzRowExpandButtonDirective, NzRowIndentDirective, NzTableCellDirective, NzTableComponent, NzTableContentComponent, NzTableDataService, NzTableFilterComponent, NzTableFixedRowComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTableModule, NzTableSelectionComponent, NzTableSortersComponent, NzTableStyleService, NzTableTitleFooterComponent, NzTableVirtualScrollDirective, NzTbodyComponent, NzTdAddOnComponent, NzThAddOnComponent, NzThMeasureDirective, NzThSelectionComponent, NzTheadComponent, NzTrDirective, NzTrExpandDirective, NzTrMeasureComponent };", "map": {"version": 3, "names": ["i0", "EventEmitter", "ElementRef", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Input", "Output", "ViewChild", "Directive", "Injectable", "Optional", "NgZone", "ViewChildren", "ContentChild", "ContentChildren", "NgModule", "__decorate", "fromEvent", "Subject", "ReplaySubject", "BehaviorSubject", "combineLatest", "merge", "EMPTY", "of", "takeUntil", "map", "distinctUntilChanged", "debounceTime", "skip", "filter", "switchMap", "startWith", "delay", "mergeMap", "i1", "WithConfig", "i2", "NzDestroyService", "InputBoolean", "arraysEqual", "isNil", "measureScrollbar", "i4", "NzDropDownDirective", "NzDropDownModule", "NgTemplateOutlet", "NgIf", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AsyncPipe", "NgStyle", "i6", "FormsModule", "i7", "NzButtonModule", "i5", "NzCheckboxModule", "i2$1", "NzIconModule", "NzRadioComponent", "i1$1", "i3", "i8", "i9", "i3$1", "CdkVirtualScrollViewport", "ScrollingModule", "i2$2", "NzEmptyModule", "i1$2", "i1$3", "i6$1", "NzPaginationModule", "NzSpinComponent", "i1$4", "NzOutletModule", "i5$1", "_c0", "NzTableFilterComponent_ng_template_1_Template", "rf", "ctx", "NzTableFilterComponent_ng_container_2_li_7_label_1_Template", "_r5", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "NzTableFilterComponent_ng_container_2_li_7_label_1_Template_label_ngModelChange_0_listener", "ɵɵrestoreView", "f_r4", "ɵɵnextContext", "$implicit", "ctx_r1", "ɵɵresetView", "check", "ɵɵelementEnd", "ɵɵproperty", "checked", "NzTableFilterComponent_ng_container_2_li_7_label_2_Template", "_r6", "NzTableFilterComponent_ng_container_2_li_7_label_2_Template_label_ngModelChange_0_listener", "NzTableFilterComponent_ng_container_2_li_7_Template", "_r3", "NzTableFilterComponent_ng_container_2_li_7_Template_li_click_0_listener", "ɵɵtemplate", "ɵɵtext", "ɵɵadvance", "filterMultiple", "ɵɵtextInterpolate", "text", "NzTableFilterComponent_ng_container_2_Template", "_r1", "ɵɵelementContainerStart", "NzTableFilterComponent_ng_container_2_Template_nz_filter_trigger_nzVisibleChange_1_listener", "$event", "onVisibleChange", "ɵɵelement", "NzTableFilterComponent_ng_container_2_Template_button_click_9_listener", "reset", "NzTableFilterComponent_ng_container_2_Template_button_click_11_listener", "confirm", "ɵɵelementContainerEnd", "filterMenu_r7", "ɵɵreference", "isVisible", "isChecked", "listOfParsedFilter", "trackByValue", "ɵɵtextInterpolate1", "locale", "filterReset", "filterConfirm", "NzTableSelectionComponent_label_0_Template", "NzTableSelectionComponent_label_0_Template_label_ngModelChange_0_listener", "onCheckedChange", "ɵɵclassProp", "showRowSelection", "disabled", "indeterminate", "ɵɵattribute", "label", "NzTableSelectionComponent_div_1_li_6_Template", "NzTableSelectionComponent_div_1_li_6_Template_li_click_0_listener", "selection_r4", "onSelect", "NzTableSelectionComponent_div_1_Template", "selectionMenu_r5", "listOfSelections", "NzTableSortersComponent_ng_template_1_Template", "NzTableSortersComponent_span_4_Template", "ctx_r0", "sortOrder", "NzTableSortersComponent_span_5_Template", "_c1", "NzTdAddOnComponent_ng_container_0_ng_template_2_Template", "NzTdAddOnComponent_ng_container_0_ng_template_2_Template_button_expandChange_0_listener", "onExpandChange", "nzExpand", "nzShowExpand", "NzTdAddOnComponent_ng_container_0_ng_container_4_ng_template_1_Template", "NzTdAddOnComponent_ng_container_0_ng_container_4_Template", "nzExpandIcon", "NzTdAddOnComponent_ng_container_0_Template", "ɵɵtemplateRefExtractor", "rowExpand_r3", "nzIndentSize", "NzTdAddOnComponent_label_1_Template", "_r4", "NzTdAddOnComponent_label_1_Template_label_ngModelChange_0_listener", "nzDisabled", "nzChecked", "nzIndeterminate", "nzLabel", "_c2", "_c3", "_c4", "NzThAddOnComponent_nz_table_filter_0_Template", "NzThAddOnComponent_nz_table_filter_0_Template_nz_table_filter_filterChange_0_listener", "onFilterValueChange", "notFilterTemplate_r3", "extraTemplate_r4", "nzCustomFilter", "nzFilterMultiple", "nzFilters", "NzThAddOnComponent_ng_template_1_ng_template_0_Template", "NzThAddOnComponent_ng_template_1_Template", "sortTemplate_r5", "contentTemplate_r6", "nzShowSort", "NzThAddOnComponent_ng_template_3_Template", "ɵɵprojection", "NzThAddOnComponent_ng_template_5_Template", "sortDirections", "NzThAddOnComponent_ng_template_7_Template", "_c5", "_c6", "NzTableContentComponent_col_0_Template", "width_r1", "ɵɵstyleProp", "NzTableContentComponent_thead_1_ng_template_1_Template", "NzTableContentComponent_thead_1_Template", "theadTemplate", "NzTableContentComponent_ng_template_2_Template", "_c7", "_c8", "NzTableFixedRowComponent_div_2_ng_template_2_Template", "NzTableFixedRowComponent_div_2_Template", "ɵɵpipe", "contentTemplate_r2", "ɵɵpipeBind1", "hostWidth$", "NzTableFixedRowComponent_ng_template_4_Template", "_c9", "NzTrMeasureComponent_td_0_Template", "NzTbodyComponent_ng_container_0_tr_1_Template", "NzTbodyComponent_ng_container_0_tr_1_Template_tr_listOfAutoWidth_0_listener", "onListOfAutoWidthChange", "listOfMeasureColumn_r3", "ngIf", "NzTbodyComponent_ng_container_0_Template", "isInsideTable", "length", "NzTbodyComponent_tr_3_Template", "noResult$", "_c10", "_c11", "_c12", "a0", "a1", "index", "NzTableInnerScrollComponent_ng_container_0_div_4_Template", "bodyStyleMap", "scrollX", "listOfColWidth", "contentTemplate", "NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_ng_template_1_Template", "NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_ng_container_4_Template", "item_r2", "i_r3", "virtualTemplate", "ɵɵpureFunction2", "NzTableInnerScrollComponent_ng_container_0_cdk_virtual_scroll_viewport_5_Template", "data", "scrollY", "noDateVirtualHeight", "virtualItemSize", "virtualMaxBufferPx", "virtualMinBufferPx", "virtualForTrackBy", "NzTableInnerScrollComponent_ng_container_0_Template", "headerStyleMap", "NzTableInnerScrollComponent_div_1_Template", "NzTableTitleFooterComponent_ng_container_0_Template", "title", "NzTableTitleFooterComponent_ng_container_1_Template", "footer", "NzTableComponent_ng_container_1_ng_template_1_Template", "NzTableComponent_ng_container_1_Template", "paginationTemplate_r1", "NzTableComponent_nz_table_title_footer_4_Template", "nzTitle", "NzTableComponent_nz_table_inner_scroll_5_Template", "tableMainElement_r3", "contentTemplate_r4", "listOfAutoColWidth", "verticalScrollBarWidth", "nzVirtualScrollDirective", "templateRef", "nzVirtualItemSize", "nzVirtualMaxBufferPx", "nzVirtualMinBufferPx", "nzVirtualForTrackBy", "NzTableComponent_ng_template_6_Template", "nzTableLayout", "listOfManualCol<PERSON>idth", "NzTableComponent_nz_table_title_footer_8_Template", "nz<PERSON><PERSON>er", "NzTableComponent_ng_container_9_ng_template_1_Template", "NzTableComponent_ng_container_9_Template", "NzTableComponent_ng_template_10_nz_pagination_0_Template", "NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageSizeChange_0_listener", "onPageSizeChange", "NzTableComponent_ng_template_10_nz_pagination_0_Template_nz_pagination_nzPageIndexChange_0_listener", "onPageIndexChange", "showPagination", "nzShowSizeChanger", "nzPageSizeOptions", "nzItemRender", "nzShowQuickJumper", "nzHideOnSinglePage", "nzShowTotal", "nzPaginationType", "nzSize", "nzPageSize", "nzTotal", "nzSimple", "nzPageIndex", "NzTableComponent_ng_template_10_Template", "nzShowPagination", "NzTableComponent_ng_template_12_Template", "_c13", "NzTheadComponent_ng_template_0_Template", "NzTheadComponent_ng_container_2_ng_template_1_Template", "NzTheadComponent_ng_container_2_Template", "contentTemplate_r1", "NZ_CONFIG_MODULE_NAME$1", "NzFilterTriggerComponent", "visible", "nzVisible", "nzVisibleChange", "next", "hide", "cdr", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "show", "constructor", "nzConfigService", "ngZone", "destroy$", "_nzModuleName", "nzActive", "nzBackdrop", "ngOnInit", "runOutsideAngular", "nzDropdown", "nativeElement", "pipe", "subscribe", "event", "stopPropagation", "ɵfac", "NzFilterTriggerComponent_Factory", "t", "ɵɵdirectiveInject", "NzConfigService", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "NzFilterTriggerComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "inputs", "nzDropdownMenu", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "NzFilterTriggerComponent_Template", "ɵɵprojectionDef", "NzFilterTriggerComponent_Template_span_nzVisibleChange_0_listener", "dependencies", "encapsulation", "changeDetection", "prototype", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "OnPush", "preserveWhitespaces", "None", "providers", "imports", "static", "read", "NzTableFilterComponent", "_", "item", "value", "getCheckedStatus", "emitFilterData", "parseList<PERSON>f<PERSON><PERSON><PERSON>", "listOfFilter", "listOfChecked", "filterChange", "emit", "<PERSON><PERSON><PERSON><PERSON>", "some", "i18n", "customFilter", "extraTemplate", "localeChange", "getLocaleData", "ngOnChanges", "changes", "ngOnDestroy", "complete", "NzTableFilterComponent_Factory", "NzI18nService", "hostAttrs", "ɵɵNgOnChangesFeature", "NzTableFilterComponent_Template", "NzIconDirective", "NzMenuDirective", "NzMenuItemComponent", "NzDropdownMenuComponent", "NzCheckboxComponent", "NgControlStatus", "NgModel", "NzButtonComponent", "ɵNzTransitionPatchDirective", "NzWaveDirective", "host", "class", "NzRowExpandButtonDirective", "expand", "spaceMode", "expandChange", "onHostClick", "NzRowExpandButtonDirective_Factory", "ɵdir", "ɵɵdefineDirective", "hostVars", "hostBindings", "NzRowExpandButtonDirective_HostBindings", "NzRowExpandButtonDirective_click_HostBindingHandler", "ɵɵhostProperty", "NzRowIndentDirective", "indentSize", "NzRowIndentDirective_Factory", "NzRowIndentDirective_HostBindings", "NzTableSelectionComponent", "showCheckbox", "checkedChange", "NzTableSelectionComponent_Factory", "NzTableSelectionComponent_Template", "NzTableSortersComponent", "isUp", "isDown", "indexOf", "NzTableSortersComponent_Factory", "NzTableSortersComponent_Template", "NzCellFixedDirective", "setAutoLeftWidth", "autoLeft", "renderer", "setStyle", "elementRef", "setAutoRightWidth", "autoRight", "setIsFirstRight", "isFirstRight", "setFixClass", "setIsLastLeft", "isLastLeft", "flag", "className", "removeClass", "addClass", "nzRight", "nzLeft", "colspan", "colSpan", "changes$", "isAutoLeft", "isAutoRight", "isFixedLeft", "isFixedRight", "isFixed", "validatePx", "NzCellFixedDirective_Factory", "Renderer2", "NzCellFixedDirective_HostBindings", "NzTableStyleService", "setTheadTemplate", "theadTemplate$", "setHasFixLeft", "hasFixLeft", "hasFixLeft$", "setHasFixRight", "hasFixRight", "hasFixRight$", "setTableWidthConfig", "widthConfig", "tableWidthConfigPx$", "setListOfTh", "listOfTh", "columnCount", "for<PERSON>ach", "th", "listOfThPx", "nzWidth", "columnCount$", "listOfThWidthConfigPx$", "setListOfMeasureColumn", "listOfKeys", "i", "push", "listOfMeasureColumn$", "setListOfAutoWidth", "listOfAutoWidth", "listOfAutoWidthPx$", "width", "setShowEmpty", "showEmpty", "showEmpty$", "setNoResult", "noResult", "setScroll", "enableAutoMeasure", "enableAutoMeasure$", "manualWidthConfigPx$", "listOfWidth", "listOfListOfThWidthPx$", "autoWidth", "manualWidth", "listOfListOfThWidth$", "list", "parseInt", "NzTableStyleService_Factory", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "NzTableCellDirective", "nzTableStyleService", "NzTableCellDirective_Factory", "NzTableCellDirective_HostBindings", "decorators", "NzTableDataService", "updatePageSize", "size", "pageSize$", "updateFrontPagination", "pagination", "frontPagination$", "updatePageIndex", "pageIndex$", "updateListOfData", "listOfData$", "updateListOfCustomColumn", "listOfCustomColumn$", "pageIndexDistinct$", "pageSizeDistinct$", "listOfCalcOperator$", "queryParams$", "pageIndex", "pageSize", "listOfCalc", "sort", "sortFn", "key", "filterFn", "filterValue", "listOfDataAfterCalc$", "listOfData", "listOfCalcOperator", "listOfDataAfterCalc", "listOfFilterOperator", "isReset", "undefined", "Array", "isArray", "listOfSortOperator", "a", "b", "sortPriority", "record1", "record2", "compareResult", "listOfFrontEndCurrentPageData$", "maxPageIndex", "Math", "ceil", "slice", "listOfCurrentPageData$", "total$", "NzTableDataService_Factory", "NzCustomColumnDirective", "el", "nzTableDataService", "nzCellControl", "v", "default", "fixWidth", "NzCustomColumnDirective_Factory", "NzTdAddOnComponent", "nzShowCheckbox", "nzCheckedChange", "nzExpandChange", "isNzShowExpandChanged", "isNzShowCheckboxChanged", "isFirstChange", "firstChange", "currentValue", "NzTdAddOnComponent_Factory", "NzTdAddOnComponent_HostBindings", "attrs", "NzTdAddOnComponent_Template", "NzThAddOnComponent", "getNextSortDirection", "current", "setSortOrder", "order", "sortOrderChange$", "clearSortOrder", "nzFilterChange", "nzFilterValue", "updateCalcOperator", "calcOperatorChange$", "manualClickOrder$", "isNzShowSortChanged", "isNzShowFilterChanged", "nzSortOrder", "nzSortPriority", "nzSortDirections", "nzSortFn", "nzFilterFn", "nzShowFilter", "nzSortOrderChange", "nextOrder", "run", "listOfValue", "NzThAddOnComponent_Factory", "NzThAddOnComponent_HostBindings", "nzColumnKey", "NzThAddOnComponent_Template", "NzThMeasureDirective", "rowspan", "rowSpan", "col", "setAttribute", "removeAttribute", "row", "NzThMeasureDirective_Factory", "NzThSelectionComponent", "nzSelections", "nzShowRowSelection", "NzThSelectionComponent_Factory", "NzThSelectionComponent_Template", "NzThSelectionComponent_Template_nz_table_selection_checkedChange_0_listener", "NzCellAlignDirective", "nzAlign", "NzCellAlignDirective_Factory", "NzCellAlignDirective_HostBindings", "NzCellEllipsisDirective", "nzEllipsis", "NzCellEllipsisDirective_Factory", "NzCellEllipsisDirective_HostBindings", "NzCellBreakWordDirective", "nzBreakWord", "NzCellBreakWordDirective_Factory", "NzCellBreakWordDirective_HostBindings", "NzTableContentComponent", "tableLayout", "NzTableContentComponent_Factory", "NzTableContentComponent_HostBindings", "NzTableContentComponent_Template", "NzTableFixedRowComponent", "ngAfterViewInit", "count", "tdElement", "NzTableFixedRowComponent_Factory", "NzTableFixedRowComponent_Query", "NzTableFixedRowComponent_Template", "NzTableInnerDefaultComponent", "NzTableInnerDefaultComponent_Factory", "NzTableInnerDefaultComponent_Template", "NzTrMeasureComponent", "nzResizeObserver", "listOfMeasureColumn", "trackByFunc", "listOfTdElement", "toArray", "observe", "entry", "target", "getBoundingClientRect", "floor", "isInAngularZone", "NzTrMeasureComponent_Factory", "NzResizeObserver", "NzTrMeasureComponent_Query", "NzTrMeasureComponent_Template", "NzTbodyComponent", "NzTbodyComponent_Factory", "NzTbodyComponent_HostBindings", "NzTbodyComponent_Template", "NzEmbedEmptyComponent", "NzTableInnerScrollComponent", "setScrollPositionClassName", "clear", "scrollWidth", "scrollLeft", "clientWidth", "tableBodyElement", "leftClassName", "rightClassName", "tableMainElement", "platform", "resizeService", "data$", "scroll$", "hasVerticalScrollBar", "overflowX", "overflowY", "maxHeight", "<PERSON><PERSON><PERSON><PERSON>", "scrollEvent$", "resize$", "setClassName$", "tableHeaderElement", "NzTableInnerScrollComponent_Factory", "Platform", "NzResizeService", "NzTableInnerScrollComponent_Query", "cdkVirtualScrollViewport", "NzTableInnerScrollComponent_Template", "CdkFixedSizeVirtualScroll", "CdkVirtualForOf", "NzTableVirtualScrollDirective", "ngTemplateContextGuard", "_dir", "_ctx", "NzTableVirtualScrollDirective_Factory", "TemplateRef", "NzTableTitleFooterComponent", "NzTableTitleFooterComponent_Factory", "NzTableTitleFooterComponent_HostBindings", "NzTableTitleFooterComponent_Template", "NzStringTemplateOutletDirective", "NZ_CONFIG_MODULE_NAME", "NzTableComponent", "directionality", "nzNoResult", "nzLoadingDelay", "nzWidthConfig", "nzData", "nzCustomColumn", "nzPaginationPosition", "nzScroll", "x", "y", "nzFrontPagination", "nzTemplateMode", "nzLoading", "nzOuterBordered", "nzLoadingIndicator", "nzBordered", "nzPageSizeChange", "nzPageIndexChange", "nzQueryParams", "nzCurrentPageDataChange", "nzCustomColumnChange", "templateMode$", "dir", "getConfigChangeEventForComponent", "change", "direction", "detectChanges", "total", "templateMode", "empty", "setScrollOnChanges", "updateShowPagination", "scrollBarWidth", "nzTableInnerScrollComponent", "NzTableComponent_Factory", "Directionality", "contentQueries", "NzTableComponent_ContentQueries", "dirIndex", "ɵɵcontentQuery", "NzTableComponent_Query", "NzTableComponent_HostBindings", "NzTableComponent_Template", "defaultTemplate_r6", "NzPaginationComponent", "NzTrDirective", "listOfFixedColumns$", "listOfColumns$", "listOfFixedColumnsChanges$", "c", "listOfFixedLeftColumnChanges$", "listOfFixedRightColumnChanges$", "listOfColumnsChanges$", "ngAfterContentInit", "listOfCellFixedDirective", "listOfNzThDirective", "listOfFixedLeft", "cell", "listOfFixedRight", "listOfLeftCell", "currentArray", "reduce", "pre", "cur", "listOfRightCell", "NzTrDirective_Factory", "NzTrDirective_ContentQueries", "NzTrDirective_HostBindings", "NzTheadComponent", "firstTableRow$", "listOfNzTrDirective", "firstTableRow", "enable", "firstTr", "listOfFixedLeftColumn", "listOfFixedRightColumn", "listOfColumn$", "listOfNzThAddOnComponent", "manualSort$", "emitValue", "<PERSON><PERSON><PERSON><PERSON>", "parentNode", "NzTheadComponent_Factory", "NzTheadComponent_ContentQueries", "NzTheadComponent_Query", "NzTheadComponent_Template", "descendants", "NzTrExpandDirective", "NzTrExpandDirective_Factory", "NzTrExpandDirective_HostBindings", "NzTableModule", "NzTableModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-table.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, ElementRef, Component, ChangeDetectionStrategy, ViewEncapsulation, Input, Output, ViewChild, Directive, Injectable, Optional, NgZone, ViewChildren, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { fromEvent, Subject, ReplaySubject, BehaviorSubject, combineLatest, merge, EMPTY, of } from 'rxjs';\nimport { takeUntil, map, distinctUntilChanged, debounceTime, skip, filter, switchMap, startWith, delay, mergeMap } from 'rxjs/operators';\nimport * as i1 from 'ng-zorro-antd/core/config';\nimport { WithConfig } from 'ng-zorro-antd/core/config';\nimport * as i2 from 'ng-zorro-antd/core/services';\nimport { NzDestroyService } from 'ng-zorro-antd/core/services';\nimport { InputBoolean, arraysEqual, isNil, measureScrollbar } from 'ng-zorro-antd/core/util';\nimport * as i4 from 'ng-zorro-antd/dropdown';\nimport { NzDropDownDirective, NzDropDownModule } from 'ng-zorro-antd/dropdown';\nimport { NgTemplateOutlet, NgIf, NgForOf, AsyncPipe, NgStyle } from '@angular/common';\nimport * as i6 from '@angular/forms';\nimport { FormsModule } from '@angular/forms';\nimport * as i7 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i5 from 'ng-zorro-antd/checkbox';\nimport { NzCheckboxModule } from 'ng-zorro-antd/checkbox';\nimport * as i2$1 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzRadioComponent } from 'ng-zorro-antd/radio';\nimport * as i1$1 from 'ng-zorro-antd/i18n';\nimport * as i3 from 'ng-zorro-antd/menu';\nimport * as i8 from 'ng-zorro-antd/core/transition-patch';\nimport * as i9 from 'ng-zorro-antd/core/wave';\nimport * as i3$1 from '@angular/cdk/scrolling';\nimport { CdkVirtualScrollViewport, ScrollingModule } from '@angular/cdk/scrolling';\nimport * as i2$2 from 'ng-zorro-antd/empty';\nimport { NzEmptyModule } from 'ng-zorro-antd/empty';\nimport * as i1$2 from 'ng-zorro-antd/cdk/resize-observer';\nimport * as i1$3 from '@angular/cdk/platform';\nimport * as i6$1 from 'ng-zorro-antd/pagination';\nimport { NzPaginationModule } from 'ng-zorro-antd/pagination';\nimport { NzSpinComponent } from 'ng-zorro-antd/spin';\nimport * as i1$4 from 'ng-zorro-antd/core/outlet';\nimport { NzOutletModule } from 'ng-zorro-antd/core/outlet';\nimport * as i5$1 from '@angular/cdk/bidi';\n\nconst NZ_CONFIG_MODULE_NAME$1 = 'filterTrigger';\nclass NzFilterTriggerComponent {\n    onVisibleChange(visible) {\n        this.nzVisible = visible;\n        this.nzVisibleChange.next(visible);\n    }\n    hide() {\n        this.nzVisible = false;\n        this.cdr.markForCheck();\n    }\n    show() {\n        this.nzVisible = true;\n        this.cdr.markForCheck();\n    }\n    constructor(nzConfigService, ngZone, cdr, destroy$) {\n        this.nzConfigService = nzConfigService;\n        this.ngZone = ngZone;\n        this.cdr = cdr;\n        this.destroy$ = destroy$;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME$1;\n        this.nzActive = false;\n        this.nzVisible = false;\n        this.nzBackdrop = false;\n        this.nzVisibleChange = new EventEmitter();\n    }\n    ngOnInit() {\n        this.ngZone.runOutsideAngular(() => {\n            fromEvent(this.nzDropdown.nativeElement, 'click')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => {\n                event.stopPropagation();\n            });\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFilterTriggerComponent, deps: [{ token: i1.NzConfigService }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: i2.NzDestroyService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzFilterTriggerComponent, isStandalone: true, selector: \"nz-filter-trigger\", inputs: { nzActive: \"nzActive\", nzDropdownMenu: \"nzDropdownMenu\", nzVisible: \"nzVisible\", nzBackdrop: \"nzBackdrop\" }, outputs: { nzVisibleChange: \"nzVisibleChange\" }, providers: [NzDestroyService], viewQueries: [{ propertyName: \"nzDropdown\", first: true, predicate: NzDropDownDirective, descendants: true, read: ElementRef, static: true }], exportAs: [\"nzFilterTrigger\"], ngImport: i0, template: `\n    <span\n      nz-dropdown\n      class=\"ant-table-filter-trigger\"\n      nzTrigger=\"click\"\n      nzPlacement=\"bottomRight\"\n      [nzBackdrop]=\"nzBackdrop\"\n      [nzClickHide]=\"false\"\n      [nzDropdownMenu]=\"nzDropdownMenu\"\n      [class.active]=\"nzActive\"\n      [class.ant-table-filter-open]=\"nzVisible\"\n      [nzVisible]=\"nzVisible\"\n      (nzVisibleChange)=\"onVisibleChange($event)\"\n    >\n      <ng-content></ng-content>\n    </span>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzDropDownModule }, { kind: \"directive\", type: i4.NzDropDownDirective, selector: \"[nz-dropdown]\", inputs: [\"nzDropdownMenu\", \"nzTrigger\", \"nzMatchWidthElement\", \"nzBackdrop\", \"nzClickHide\", \"nzDisabled\", \"nzVisible\", \"nzOverlayClassName\", \"nzOverlayStyle\", \"nzPlacement\"], outputs: [\"nzVisibleChange\"], exportAs: [\"nzDropdown\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzFilterTriggerComponent.prototype, \"nzBackdrop\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzFilterTriggerComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-filter-trigger',\n                    exportAs: `nzFilterTrigger`,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <span\n      nz-dropdown\n      class=\"ant-table-filter-trigger\"\n      nzTrigger=\"click\"\n      nzPlacement=\"bottomRight\"\n      [nzBackdrop]=\"nzBackdrop\"\n      [nzClickHide]=\"false\"\n      [nzDropdownMenu]=\"nzDropdownMenu\"\n      [class.active]=\"nzActive\"\n      [class.ant-table-filter-open]=\"nzVisible\"\n      [nzVisible]=\"nzVisible\"\n      (nzVisibleChange)=\"onVisibleChange($event)\"\n    >\n      <ng-content></ng-content>\n    </span>\n  `,\n                    providers: [NzDestroyService],\n                    imports: [NzDropDownModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1.NzConfigService }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: i2.NzDestroyService }], propDecorators: { nzActive: [{\n                type: Input\n            }], nzDropdownMenu: [{\n                type: Input\n            }], nzVisible: [{\n                type: Input\n            }], nzBackdrop: [{\n                type: Input\n            }], nzVisibleChange: [{\n                type: Output\n            }], nzDropdown: [{\n                type: ViewChild,\n                args: [NzDropDownDirective, { static: true, read: ElementRef }]\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFilterComponent {\n    trackByValue(_, item) {\n        return item.value;\n    }\n    check(filter) {\n        if (this.filterMultiple) {\n            this.listOfParsedFilter = this.listOfParsedFilter.map(item => {\n                if (item === filter) {\n                    return { ...item, checked: !filter.checked };\n                }\n                else {\n                    return item;\n                }\n            });\n            filter.checked = !filter.checked;\n        }\n        else {\n            this.listOfParsedFilter = this.listOfParsedFilter.map(item => ({ ...item, checked: item === filter }));\n        }\n        this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n    }\n    confirm() {\n        this.isVisible = false;\n        this.emitFilterData();\n    }\n    reset() {\n        this.isVisible = false;\n        this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter, true);\n        this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n        this.emitFilterData();\n    }\n    onVisibleChange(value) {\n        this.isVisible = value;\n        if (!value) {\n            this.emitFilterData();\n        }\n        else {\n            this.listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n        }\n    }\n    emitFilterData() {\n        const listOfChecked = this.listOfParsedFilter.filter(item => item.checked).map(item => item.value);\n        if (!arraysEqual(this.listOfChecked, listOfChecked)) {\n            if (this.filterMultiple) {\n                this.filterChange.emit(listOfChecked);\n            }\n            else {\n                this.filterChange.emit(listOfChecked.length > 0 ? listOfChecked[0] : null);\n            }\n        }\n    }\n    parseListOfFilter(listOfFilter, reset) {\n        return listOfFilter.map(item => {\n            const checked = reset ? false : !!item.byDefault;\n            return { text: item.text, value: item.value, checked };\n        });\n    }\n    getCheckedStatus(listOfParsedFilter) {\n        return listOfParsedFilter.some(item => item.checked);\n    }\n    constructor(cdr, i18n) {\n        this.cdr = cdr;\n        this.i18n = i18n;\n        this.contentTemplate = null;\n        this.customFilter = false;\n        this.extraTemplate = null;\n        this.filterMultiple = true;\n        this.listOfFilter = [];\n        this.filterChange = new EventEmitter();\n        this.destroy$ = new Subject();\n        this.isChecked = false;\n        this.isVisible = false;\n        this.listOfParsedFilter = [];\n        this.listOfChecked = [];\n    }\n    ngOnInit() {\n        this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n            this.locale = this.i18n.getLocaleData('Table');\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { listOfFilter } = changes;\n        if (listOfFilter && this.listOfFilter && this.listOfFilter.length) {\n            this.listOfParsedFilter = this.parseListOfFilter(this.listOfFilter);\n            this.isChecked = this.getCheckedStatus(this.listOfParsedFilter);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableFilterComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i1$1.NzI18nService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableFilterComponent, isStandalone: true, selector: \"nz-table-filter\", inputs: { contentTemplate: \"contentTemplate\", customFilter: \"customFilter\", extraTemplate: \"extraTemplate\", filterMultiple: \"filterMultiple\", listOfFilter: \"listOfFilter\" }, outputs: { filterChange: \"filterChange\" }, host: { classAttribute: \"ant-table-filter-column\" }, usesOnChanges: true, ngImport: i0, template: `\n    <span class=\"ant-table-column-title\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </span>\n    <ng-container *ngIf=\"!customFilter; else extraTemplate\">\n      <nz-filter-trigger\n        [nzVisible]=\"isVisible\"\n        [nzActive]=\"isChecked\"\n        [nzDropdownMenu]=\"filterMenu\"\n        (nzVisibleChange)=\"onVisibleChange($event)\"\n      >\n        <span nz-icon nzType=\"filter\" nzTheme=\"fill\"></span>\n      </nz-filter-trigger>\n      <nz-dropdown-menu #filterMenu=\"nzDropdownMenu\">\n        <div class=\"ant-table-filter-dropdown\">\n          <ul nz-menu>\n            <li\n              nz-menu-item\n              [nzSelected]=\"f.checked\"\n              *ngFor=\"let f of listOfParsedFilter; trackBy: trackByValue\"\n              (click)=\"check(f)\"\n            >\n              <label nz-radio *ngIf=\"!filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <label nz-checkbox *ngIf=\"filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <span>{{ f.text }}</span>\n            </li>\n          </ul>\n          <div class=\"ant-table-filter-dropdown-btns\">\n            <button nz-button nzType=\"link\" nzSize=\"small\" (click)=\"reset()\" [disabled]=\"!isChecked\">\n              {{ locale.filterReset }}\n            </button>\n            <button nz-button nzType=\"primary\" nzSize=\"small\" (click)=\"confirm()\">{{ locale.filterConfirm }}</button>\n          </div>\n        </div>\n      </nz-dropdown-menu>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"component\", type: NzFilterTriggerComponent, selector: \"nz-filter-trigger\", inputs: [\"nzActive\", \"nzDropdownMenu\", \"nzVisible\", \"nzBackdrop\"], outputs: [\"nzVisibleChange\"], exportAs: [\"nzFilterTrigger\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i2$1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzDropDownModule }, { kind: \"directive\", type: i3.NzMenuDirective, selector: \"[nz-menu]\", inputs: [\"nzInlineIndent\", \"nzTheme\", \"nzMode\", \"nzInlineCollapsed\", \"nzSelectable\"], outputs: [\"nzClick\"], exportAs: [\"nzMenu\"] }, { kind: \"component\", type: i3.NzMenuItemComponent, selector: \"[nz-menu-item]\", inputs: [\"nzPaddingLeft\", \"nzDisabled\", \"nzSelected\", \"nzDanger\", \"nzMatchRouterExact\", \"nzMatchRouter\"], exportAs: [\"nzMenuItem\"] }, { kind: \"component\", type: i4.NzDropdownMenuComponent, selector: \"nz-dropdown-menu\", exportAs: [\"nzDropdownMenu\"] }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"component\", type: NzRadioComponent, selector: \"[nz-radio],[nz-radio-button]\", inputs: [\"nzValue\", \"nzDisabled\", \"nzAutoFocus\", \"nz-radio-button\"], exportAs: [\"nzRadio\"] }, { kind: \"ngmodule\", type: NzCheckboxModule }, { kind: \"component\", type: i5.NzCheckboxComponent, selector: \"[nz-checkbox]\", inputs: [\"nzValue\", \"nzAutoFocus\", \"nzDisabled\", \"nzIndeterminate\", \"nzChecked\", \"nzId\"], outputs: [\"nzCheckedChange\"], exportAs: [\"nzCheckbox\"] }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i6.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i6.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"ngmodule\", type: NzButtonModule }, { kind: \"component\", type: i7.NzButtonComponent, selector: \"button[nz-button], a[nz-button]\", inputs: [\"nzBlock\", \"nzGhost\", \"nzSearch\", \"nzLoading\", \"nzDanger\", \"disabled\", \"tabIndex\", \"nzType\", \"nzShape\", \"nzSize\"], exportAs: [\"nzButton\"] }, { kind: \"directive\", type: i8.ɵNzTransitionPatchDirective, selector: \"[nz-button], nz-button-group, [nz-icon], [nz-menu-item], [nz-submenu], nz-select-top-control, nz-select-placeholder, nz-input-group\", inputs: [\"hidden\"] }, { kind: \"directive\", type: i9.NzWaveDirective, selector: \"[nz-wave],button[nz-button]:not([nzType=\\\"link\\\"]):not([nzType=\\\"text\\\"])\", inputs: [\"nzWaveExtraNode\"], exportAs: [\"nzWave\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableFilterComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-filter',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <span class=\"ant-table-column-title\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </span>\n    <ng-container *ngIf=\"!customFilter; else extraTemplate\">\n      <nz-filter-trigger\n        [nzVisible]=\"isVisible\"\n        [nzActive]=\"isChecked\"\n        [nzDropdownMenu]=\"filterMenu\"\n        (nzVisibleChange)=\"onVisibleChange($event)\"\n      >\n        <span nz-icon nzType=\"filter\" nzTheme=\"fill\"></span>\n      </nz-filter-trigger>\n      <nz-dropdown-menu #filterMenu=\"nzDropdownMenu\">\n        <div class=\"ant-table-filter-dropdown\">\n          <ul nz-menu>\n            <li\n              nz-menu-item\n              [nzSelected]=\"f.checked\"\n              *ngFor=\"let f of listOfParsedFilter; trackBy: trackByValue\"\n              (click)=\"check(f)\"\n            >\n              <label nz-radio *ngIf=\"!filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <label nz-checkbox *ngIf=\"filterMultiple\" [ngModel]=\"f.checked\" (ngModelChange)=\"check(f)\"></label>\n              <span>{{ f.text }}</span>\n            </li>\n          </ul>\n          <div class=\"ant-table-filter-dropdown-btns\">\n            <button nz-button nzType=\"link\" nzSize=\"small\" (click)=\"reset()\" [disabled]=\"!isChecked\">\n              {{ locale.filterReset }}\n            </button>\n            <button nz-button nzType=\"primary\" nzSize=\"small\" (click)=\"confirm()\">{{ locale.filterConfirm }}</button>\n          </div>\n        </div>\n      </nz-dropdown-menu>\n    </ng-container>\n  `,\n                    host: { class: 'ant-table-filter-column' },\n                    imports: [\n                        NgTemplateOutlet,\n                        NgIf,\n                        NzFilterTriggerComponent,\n                        NzIconModule,\n                        NzDropDownModule,\n                        NgForOf,\n                        NzRadioComponent,\n                        NzCheckboxModule,\n                        FormsModule,\n                        NzButtonModule\n                    ],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i1$1.NzI18nService }], propDecorators: { contentTemplate: [{\n                type: Input\n            }], customFilter: [{\n                type: Input\n            }], extraTemplate: [{\n                type: Input\n            }], filterMultiple: [{\n                type: Input\n            }], listOfFilter: [{\n                type: Input\n            }], filterChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowExpandButtonDirective {\n    constructor() {\n        this.expand = false;\n        this.spaceMode = false;\n        this.expandChange = new EventEmitter();\n    }\n    onHostClick() {\n        if (!this.spaceMode) {\n            this.expand = !this.expand;\n            this.expandChange.next(this.expand);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzRowExpandButtonDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzRowExpandButtonDirective, isStandalone: true, selector: \"button[nz-row-expand-button]\", inputs: { expand: \"expand\", spaceMode: \"spaceMode\" }, outputs: { expandChange: \"expandChange\" }, host: { listeners: { \"click\": \"onHostClick()\" }, properties: { \"type\": \"'button'\", \"class.ant-table-row-expand-icon-expanded\": \"!spaceMode && expand === true\", \"class.ant-table-row-expand-icon-collapsed\": \"!spaceMode && expand === false\", \"class.ant-table-row-expand-icon-spaced\": \"spaceMode\" }, classAttribute: \"ant-table-row-expand-icon\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzRowExpandButtonDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'button[nz-row-expand-button]',\n                    host: {\n                        class: 'ant-table-row-expand-icon',\n                        '[type]': `'button'`,\n                        '[class.ant-table-row-expand-icon-expanded]': `!spaceMode && expand === true`,\n                        '[class.ant-table-row-expand-icon-collapsed]': `!spaceMode && expand === false`,\n                        '[class.ant-table-row-expand-icon-spaced]': 'spaceMode',\n                        '(click)': 'onHostClick()'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { expand: [{\n                type: Input\n            }], spaceMode: [{\n                type: Input\n            }], expandChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzRowIndentDirective {\n    constructor() {\n        this.indentSize = 0;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzRowIndentDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzRowIndentDirective, isStandalone: true, selector: \"nz-row-indent\", inputs: { indentSize: \"indentSize\" }, host: { properties: { \"style.padding-left.px\": \"indentSize\" }, classAttribute: \"ant-table-row-indent\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzRowIndentDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'nz-row-indent',\n                    host: {\n                        class: 'ant-table-row-indent',\n                        '[style.padding-left.px]': 'indentSize'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { indentSize: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSelectionComponent {\n    constructor() {\n        this.listOfSelections = [];\n        this.checked = false;\n        this.disabled = false;\n        this.indeterminate = false;\n        this.label = null;\n        this.showCheckbox = false;\n        this.showRowSelection = false;\n        this.checkedChange = new EventEmitter();\n    }\n    onCheckedChange(checked) {\n        this.checked = checked;\n        this.checkedChange.emit(checked);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableSelectionComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableSelectionComponent, isStandalone: true, selector: \"nz-table-selection\", inputs: { listOfSelections: \"listOfSelections\", checked: \"checked\", disabled: \"disabled\", indeterminate: \"indeterminate\", label: \"label\", showCheckbox: \"showCheckbox\", showRowSelection: \"showRowSelection\" }, outputs: { checkedChange: \"checkedChange\" }, host: { classAttribute: \"ant-table-selection\" }, ngImport: i0, template: `\n    <label\n      *ngIf=\"showCheckbox\"\n      nz-checkbox\n      [class.ant-table-selection-select-all-custom]=\"showRowSelection\"\n      [ngModel]=\"checked\"\n      [nzDisabled]=\"disabled\"\n      [nzIndeterminate]=\"indeterminate\"\n      [attr.aria-label]=\"label\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <div class=\"ant-table-selection-extra\" *ngIf=\"showRowSelection\">\n      <span nz-dropdown class=\"ant-table-selection-down\" nzPlacement=\"bottomLeft\" [nzDropdownMenu]=\"selectionMenu\">\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n      <nz-dropdown-menu #selectionMenu=\"nzDropdownMenu\">\n        <ul nz-menu class=\"ant-table-selection-menu\">\n          <li nz-menu-item *ngFor=\"let selection of listOfSelections\" (click)=\"selection.onSelect()\">\n            {{ selection.text }}\n          </li>\n        </ul>\n      </nz-dropdown-menu>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i6.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i6.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }, { kind: \"ngmodule\", type: NzCheckboxModule }, { kind: \"component\", type: i5.NzCheckboxComponent, selector: \"[nz-checkbox]\", inputs: [\"nzValue\", \"nzAutoFocus\", \"nzDisabled\", \"nzIndeterminate\", \"nzChecked\", \"nzId\"], outputs: [\"nzCheckedChange\"], exportAs: [\"nzCheckbox\"] }, { kind: \"ngmodule\", type: NzDropDownModule }, { kind: \"directive\", type: i3.NzMenuDirective, selector: \"[nz-menu]\", inputs: [\"nzInlineIndent\", \"nzTheme\", \"nzMode\", \"nzInlineCollapsed\", \"nzSelectable\"], outputs: [\"nzClick\"], exportAs: [\"nzMenu\"] }, { kind: \"component\", type: i3.NzMenuItemComponent, selector: \"[nz-menu-item]\", inputs: [\"nzPaddingLeft\", \"nzDisabled\", \"nzSelected\", \"nzDanger\", \"nzMatchRouterExact\", \"nzMatchRouter\"], exportAs: [\"nzMenuItem\"] }, { kind: \"directive\", type: i4.NzDropDownDirective, selector: \"[nz-dropdown]\", inputs: [\"nzDropdownMenu\", \"nzTrigger\", \"nzMatchWidthElement\", \"nzBackdrop\", \"nzClickHide\", \"nzDisabled\", \"nzVisible\", \"nzOverlayClassName\", \"nzOverlayStyle\", \"nzPlacement\"], outputs: [\"nzVisibleChange\"], exportAs: [\"nzDropdown\"] }, { kind: \"component\", type: i4.NzDropdownMenuComponent, selector: \"nz-dropdown-menu\", exportAs: [\"nzDropdownMenu\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i2$1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableSelectionComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-selection',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <label\n      *ngIf=\"showCheckbox\"\n      nz-checkbox\n      [class.ant-table-selection-select-all-custom]=\"showRowSelection\"\n      [ngModel]=\"checked\"\n      [nzDisabled]=\"disabled\"\n      [nzIndeterminate]=\"indeterminate\"\n      [attr.aria-label]=\"label\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <div class=\"ant-table-selection-extra\" *ngIf=\"showRowSelection\">\n      <span nz-dropdown class=\"ant-table-selection-down\" nzPlacement=\"bottomLeft\" [nzDropdownMenu]=\"selectionMenu\">\n        <span nz-icon nzType=\"down\"></span>\n      </span>\n      <nz-dropdown-menu #selectionMenu=\"nzDropdownMenu\">\n        <ul nz-menu class=\"ant-table-selection-menu\">\n          <li nz-menu-item *ngFor=\"let selection of listOfSelections\" (click)=\"selection.onSelect()\">\n            {{ selection.text }}\n          </li>\n        </ul>\n      </nz-dropdown-menu>\n    </div>\n  `,\n                    host: { class: 'ant-table-selection' },\n                    imports: [NgIf, FormsModule, NzCheckboxModule, NzDropDownModule, NzIconModule, NgForOf],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { listOfSelections: [{\n                type: Input\n            }], checked: [{\n                type: Input\n            }], disabled: [{\n                type: Input\n            }], indeterminate: [{\n                type: Input\n            }], label: [{\n                type: Input\n            }], showCheckbox: [{\n                type: Input\n            }], showRowSelection: [{\n                type: Input\n            }], checkedChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableSortersComponent {\n    constructor() {\n        this.sortDirections = ['ascend', 'descend', null];\n        this.sortOrder = null;\n        this.contentTemplate = null;\n        this.isUp = false;\n        this.isDown = false;\n    }\n    ngOnChanges(changes) {\n        const { sortDirections } = changes;\n        if (sortDirections) {\n            this.isUp = this.sortDirections.indexOf('ascend') !== -1;\n            this.isDown = this.sortDirections.indexOf('descend') !== -1;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableSortersComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableSortersComponent, isStandalone: true, selector: \"nz-table-sorters\", inputs: { sortDirections: \"sortDirections\", sortOrder: \"sortOrder\", contentTemplate: \"contentTemplate\" }, host: { classAttribute: \"ant-table-column-sorters\" }, usesOnChanges: true, ngImport: i0, template: `\n    <span class=\"ant-table-column-title\"><ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template></span>\n    <span class=\"ant-table-column-sorter\" [class.ant-table-column-sorter-full]=\"isDown && isUp\">\n      <span class=\"ant-table-column-sorter-inner\">\n        <span\n          nz-icon\n          nzType=\"caret-up\"\n          *ngIf=\"isUp\"\n          class=\"ant-table-column-sorter-up\"\n          [class.active]=\"sortOrder === 'ascend'\"\n        ></span>\n        <span\n          nz-icon\n          nzType=\"caret-down\"\n          *ngIf=\"isDown\"\n          class=\"ant-table-column-sorter-down\"\n          [class.active]=\"sortOrder === 'descend'\"\n        ></span>\n      </span>\n    </span>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i2$1.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableSortersComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-sorters',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <span class=\"ant-table-column-title\"><ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template></span>\n    <span class=\"ant-table-column-sorter\" [class.ant-table-column-sorter-full]=\"isDown && isUp\">\n      <span class=\"ant-table-column-sorter-inner\">\n        <span\n          nz-icon\n          nzType=\"caret-up\"\n          *ngIf=\"isUp\"\n          class=\"ant-table-column-sorter-up\"\n          [class.active]=\"sortOrder === 'ascend'\"\n        ></span>\n        <span\n          nz-icon\n          nzType=\"caret-down\"\n          *ngIf=\"isDown\"\n          class=\"ant-table-column-sorter-down\"\n          [class.active]=\"sortOrder === 'descend'\"\n        ></span>\n      </span>\n    </span>\n  `,\n                    host: { class: 'ant-table-column-sorters' },\n                    imports: [NzIconModule, NgTemplateOutlet, NgIf],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { sortDirections: [{\n                type: Input\n            }], sortOrder: [{\n                type: Input\n            }], contentTemplate: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellFixedDirective {\n    setAutoLeftWidth(autoLeft) {\n        this.renderer.setStyle(this.elementRef.nativeElement, 'left', autoLeft);\n    }\n    setAutoRightWidth(autoRight) {\n        this.renderer.setStyle(this.elementRef.nativeElement, 'right', autoRight);\n    }\n    setIsFirstRight(isFirstRight) {\n        this.setFixClass(isFirstRight, 'ant-table-cell-fix-right-first');\n    }\n    setIsLastLeft(isLastLeft) {\n        this.setFixClass(isLastLeft, 'ant-table-cell-fix-left-last');\n    }\n    setFixClass(flag, className) {\n        // the setFixClass function may call many times, so remove it first.\n        this.renderer.removeClass(this.elementRef.nativeElement, className);\n        if (flag) {\n            this.renderer.addClass(this.elementRef.nativeElement, className);\n        }\n    }\n    constructor(renderer, elementRef) {\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.nzRight = false;\n        this.nzLeft = false;\n        this.colspan = null;\n        this.colSpan = null;\n        this.changes$ = new Subject();\n        this.isAutoLeft = false;\n        this.isAutoRight = false;\n        this.isFixedLeft = false;\n        this.isFixedRight = false;\n        this.isFixed = false;\n    }\n    ngOnChanges() {\n        this.setIsFirstRight(false);\n        this.setIsLastLeft(false);\n        this.isAutoLeft = this.nzLeft === '' || this.nzLeft === true;\n        this.isAutoRight = this.nzRight === '' || this.nzRight === true;\n        this.isFixedLeft = this.nzLeft !== false;\n        this.isFixedRight = this.nzRight !== false;\n        this.isFixed = this.isFixedLeft || this.isFixedRight;\n        const validatePx = (value) => {\n            if (typeof value === 'string' && value !== '') {\n                return value;\n            }\n            else {\n                return null;\n            }\n        };\n        this.setAutoLeftWidth(validatePx(this.nzLeft));\n        this.setAutoRightWidth(validatePx(this.nzRight));\n        this.changes$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellFixedDirective, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCellFixedDirective, isStandalone: true, selector: \"td[nzRight],th[nzRight],td[nzLeft],th[nzLeft]\", inputs: { nzRight: \"nzRight\", nzLeft: \"nzLeft\", colspan: \"colspan\", colSpan: \"colSpan\" }, host: { properties: { \"class.ant-table-cell-fix-right\": \"isFixedRight\", \"class.ant-table-cell-fix-left\": \"isFixedLeft\", \"style.position\": \"isFixed? 'sticky' : null\" } }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellFixedDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'td[nzRight],th[nzRight],td[nzLeft],th[nzLeft]',\n                    host: {\n                        '[class.ant-table-cell-fix-right]': `isFixedRight`,\n                        '[class.ant-table-cell-fix-left]': `isFixedLeft`,\n                        '[style.position]': `isFixed? 'sticky' : null`\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i0.ElementRef }], propDecorators: { nzRight: [{\n                type: Input\n            }], nzLeft: [{\n                type: Input\n            }], colspan: [{\n                type: Input\n            }], colSpan: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableStyleService {\n    setTheadTemplate(template) {\n        this.theadTemplate$.next(template);\n    }\n    setHasFixLeft(hasFixLeft) {\n        this.hasFixLeft$.next(hasFixLeft);\n    }\n    setHasFixRight(hasFixRight) {\n        this.hasFixRight$.next(hasFixRight);\n    }\n    setTableWidthConfig(widthConfig) {\n        this.tableWidthConfigPx$.next(widthConfig);\n    }\n    setListOfTh(listOfTh) {\n        let columnCount = 0;\n        listOfTh.forEach(th => {\n            columnCount += (th.colspan && +th.colspan) || (th.colSpan && +th.colSpan) || 1;\n        });\n        const listOfThPx = listOfTh.map(item => item.nzWidth);\n        this.columnCount$.next(columnCount);\n        this.listOfThWidthConfigPx$.next(listOfThPx);\n    }\n    setListOfMeasureColumn(listOfTh) {\n        const listOfKeys = [];\n        listOfTh.forEach(th => {\n            const length = (th.colspan && +th.colspan) || (th.colSpan && +th.colSpan) || 1;\n            for (let i = 0; i < length; i++) {\n                listOfKeys.push(`measure_key_${i}`);\n            }\n        });\n        this.listOfMeasureColumn$.next(listOfKeys);\n    }\n    setListOfAutoWidth(listOfAutoWidth) {\n        this.listOfAutoWidthPx$.next(listOfAutoWidth.map(width => `${width}px`));\n    }\n    setShowEmpty(showEmpty) {\n        this.showEmpty$.next(showEmpty);\n    }\n    setNoResult(noResult) {\n        this.noResult$.next(noResult);\n    }\n    setScroll(scrollX, scrollY) {\n        const enableAutoMeasure = !!(scrollX || scrollY);\n        if (!enableAutoMeasure) {\n            this.setListOfAutoWidth([]);\n        }\n        this.enableAutoMeasure$.next(enableAutoMeasure);\n    }\n    constructor() {\n        this.theadTemplate$ = new ReplaySubject(1);\n        this.hasFixLeft$ = new ReplaySubject(1);\n        this.hasFixRight$ = new ReplaySubject(1);\n        this.hostWidth$ = new ReplaySubject(1);\n        this.columnCount$ = new ReplaySubject(1);\n        this.showEmpty$ = new ReplaySubject(1);\n        this.noResult$ = new ReplaySubject(1);\n        this.listOfThWidthConfigPx$ = new BehaviorSubject([]);\n        this.tableWidthConfigPx$ = new BehaviorSubject([]);\n        this.manualWidthConfigPx$ = combineLatest([this.tableWidthConfigPx$, this.listOfThWidthConfigPx$]).pipe(map(([widthConfig, listOfWidth]) => (widthConfig.length ? widthConfig : listOfWidth)));\n        this.listOfAutoWidthPx$ = new ReplaySubject(1);\n        this.listOfListOfThWidthPx$ = merge(\n        /** init with manual width **/\n        this.manualWidthConfigPx$, combineLatest([this.listOfAutoWidthPx$, this.manualWidthConfigPx$]).pipe(map(([autoWidth, manualWidth]) => {\n            /** use autoWidth until column length match **/\n            if (autoWidth.length === manualWidth.length) {\n                return autoWidth.map((width, index) => {\n                    if (width === '0px') {\n                        return manualWidth[index] || null;\n                    }\n                    else {\n                        return manualWidth[index] || width;\n                    }\n                });\n            }\n            else {\n                return manualWidth;\n            }\n        })));\n        this.listOfMeasureColumn$ = new ReplaySubject(1);\n        this.listOfListOfThWidth$ = this.listOfAutoWidthPx$.pipe(map(list => list.map(width => parseInt(width, 10))));\n        this.enableAutoMeasure$ = new ReplaySubject(1);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableStyleService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableStyleService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableStyleService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableCellDirective {\n    constructor(nzTableStyleService) {\n        this.isInsideTable = false;\n        this.isInsideTable = !!nzTableStyleService;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableCellDirective, deps: [{ token: NzTableStyleService, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableCellDirective, isStandalone: true, selector: \"th:not(.nz-disable-th):not([mat-cell]), td:not(.nz-disable-td):not([mat-cell])\", host: { properties: { \"class.ant-table-cell\": \"isInsideTable\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableCellDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'th:not(.nz-disable-th):not([mat-cell]), td:not(.nz-disable-td):not([mat-cell])',\n                    host: {\n                        '[class.ant-table-cell]': 'isInsideTable'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: NzTableStyleService, decorators: [{\n                    type: Optional\n                }] }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableDataService {\n    updatePageSize(size) {\n        this.pageSize$.next(size);\n    }\n    updateFrontPagination(pagination) {\n        this.frontPagination$.next(pagination);\n    }\n    updatePageIndex(index) {\n        this.pageIndex$.next(index);\n    }\n    updateListOfData(list) {\n        this.listOfData$.next(list);\n    }\n    updateListOfCustomColumn(list) {\n        this.listOfCustomColumn$.next(list);\n    }\n    constructor() {\n        this.destroy$ = new Subject();\n        this.pageIndex$ = new BehaviorSubject(1);\n        this.frontPagination$ = new BehaviorSubject(true);\n        this.pageSize$ = new BehaviorSubject(10);\n        this.listOfData$ = new BehaviorSubject([]);\n        this.listOfCustomColumn$ = new BehaviorSubject([]);\n        this.pageIndexDistinct$ = this.pageIndex$.pipe(distinctUntilChanged());\n        this.pageSizeDistinct$ = this.pageSize$.pipe(distinctUntilChanged());\n        this.listOfCalcOperator$ = new BehaviorSubject([]);\n        this.queryParams$ = combineLatest([\n            this.pageIndexDistinct$,\n            this.pageSizeDistinct$,\n            this.listOfCalcOperator$\n        ]).pipe(debounceTime(0), skip(1), map(([pageIndex, pageSize, listOfCalc]) => ({\n            pageIndex,\n            pageSize,\n            sort: listOfCalc\n                .filter(item => item.sortFn)\n                .map(item => ({\n                key: item.key,\n                value: item.sortOrder\n            })),\n            filter: listOfCalc\n                .filter(item => item.filterFn)\n                .map(item => ({\n                key: item.key,\n                value: item.filterValue\n            }))\n        })));\n        this.listOfDataAfterCalc$ = combineLatest([this.listOfData$, this.listOfCalcOperator$]).pipe(map(([listOfData, listOfCalcOperator]) => {\n            let listOfDataAfterCalc = [...listOfData];\n            const listOfFilterOperator = listOfCalcOperator.filter(item => {\n                const { filterValue, filterFn } = item;\n                const isReset = filterValue === null ||\n                    filterValue === undefined ||\n                    (Array.isArray(filterValue) && filterValue.length === 0);\n                return !isReset && typeof filterFn === 'function';\n            });\n            for (const item of listOfFilterOperator) {\n                const { filterFn, filterValue } = item;\n                listOfDataAfterCalc = listOfDataAfterCalc.filter(data => filterFn(filterValue, data));\n            }\n            const listOfSortOperator = listOfCalcOperator\n                .filter(item => item.sortOrder !== null && typeof item.sortFn === 'function')\n                .sort((a, b) => +b.sortPriority - +a.sortPriority);\n            if (listOfCalcOperator.length) {\n                listOfDataAfterCalc.sort((record1, record2) => {\n                    for (const item of listOfSortOperator) {\n                        const { sortFn, sortOrder } = item;\n                        if (sortFn && sortOrder) {\n                            const compareResult = sortFn(record1, record2, sortOrder);\n                            if (compareResult !== 0) {\n                                return sortOrder === 'ascend' ? compareResult : -compareResult;\n                            }\n                        }\n                    }\n                    return 0;\n                });\n            }\n            return listOfDataAfterCalc;\n        }));\n        this.listOfFrontEndCurrentPageData$ = combineLatest([\n            this.pageIndexDistinct$,\n            this.pageSizeDistinct$,\n            this.listOfDataAfterCalc$\n        ]).pipe(takeUntil(this.destroy$), filter(value => {\n            const [pageIndex, pageSize, listOfData] = value;\n            const maxPageIndex = Math.ceil(listOfData.length / pageSize) || 1;\n            return pageIndex <= maxPageIndex;\n        }), map(([pageIndex, pageSize, listOfData]) => listOfData.slice((pageIndex - 1) * pageSize, pageIndex * pageSize)));\n        this.listOfCurrentPageData$ = this.frontPagination$.pipe(switchMap(pagination => (pagination ? this.listOfFrontEndCurrentPageData$ : this.listOfDataAfterCalc$)));\n        this.total$ = this.frontPagination$.pipe(switchMap(pagination => (pagination ? this.listOfDataAfterCalc$ : this.listOfData$)), map(list => list.length), distinctUntilChanged());\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableDataService, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableDataService }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableDataService, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCustomColumnDirective {\n    constructor(el, renderer, nzTableDataService) {\n        this.el = el;\n        this.renderer = renderer;\n        this.nzTableDataService = nzTableDataService;\n        this.nzCellControl = null;\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        this.nzTableDataService.listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(item => {\n            if (item.length) {\n                item.forEach((v, i) => {\n                    if (v.value === this.nzCellControl) {\n                        if (!v.default) {\n                            this.renderer.setStyle(this.el.nativeElement, 'display', 'none');\n                        }\n                        else {\n                            this.renderer.setStyle(this.el.nativeElement, 'display', 'block');\n                        }\n                        this.renderer.setStyle(this.el.nativeElement, 'order', i);\n                        if (!v?.fixWidth) {\n                            this.renderer.setStyle(this.el.nativeElement, 'flex', `1 1 ${v.width}px`);\n                        }\n                        else {\n                            this.renderer.setStyle(this.el.nativeElement, 'flex', `1 0 ${v.width}px`);\n                        }\n                    }\n                });\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCustomColumnDirective, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: NzTableDataService }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCustomColumnDirective, isStandalone: true, selector: \"td[nzCellControl],th[nzCellControl]\", inputs: { nzCellControl: \"nzCellControl\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCustomColumnDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'td[nzCellControl],th[nzCellControl]',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: NzTableDataService }], propDecorators: { nzCellControl: [{\n                type: Input\n            }] } });\n\nclass NzTdAddOnComponent {\n    constructor() {\n        this.nzChecked = false;\n        this.nzDisabled = false;\n        this.nzIndeterminate = false;\n        this.nzLabel = null;\n        this.nzIndentSize = 0;\n        this.nzShowExpand = false;\n        this.nzShowCheckbox = false;\n        this.nzExpand = false;\n        this.nzExpandIcon = null;\n        this.nzCheckedChange = new EventEmitter();\n        this.nzExpandChange = new EventEmitter();\n        this.isNzShowExpandChanged = false;\n        this.isNzShowCheckboxChanged = false;\n    }\n    onCheckedChange(checked) {\n        this.nzChecked = checked;\n        this.nzCheckedChange.emit(checked);\n    }\n    onExpandChange(expand) {\n        this.nzExpand = expand;\n        this.nzExpandChange.emit(expand);\n    }\n    ngOnChanges(changes) {\n        const isFirstChange = (value) => value && value.firstChange && value.currentValue !== undefined;\n        const { nzExpand, nzChecked, nzShowExpand, nzShowCheckbox } = changes;\n        if (nzShowExpand) {\n            this.isNzShowExpandChanged = true;\n        }\n        if (nzShowCheckbox) {\n            this.isNzShowCheckboxChanged = true;\n        }\n        if (isFirstChange(nzExpand) && !this.isNzShowExpandChanged) {\n            this.nzShowExpand = true;\n        }\n        if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n            this.nzShowCheckbox = true;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTdAddOnComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTdAddOnComponent, isStandalone: true, selector: \"td[nzChecked], td[nzDisabled], td[nzIndeterminate], td[nzIndentSize], td[nzExpand], td[nzShowExpand], td[nzShowCheckbox]\", inputs: { nzChecked: \"nzChecked\", nzDisabled: \"nzDisabled\", nzIndeterminate: \"nzIndeterminate\", nzLabel: \"nzLabel\", nzIndentSize: \"nzIndentSize\", nzShowExpand: \"nzShowExpand\", nzShowCheckbox: \"nzShowCheckbox\", nzExpand: \"nzExpand\", nzExpandIcon: \"nzExpandIcon\" }, outputs: { nzCheckedChange: \"nzCheckedChange\", nzExpandChange: \"nzExpandChange\" }, host: { properties: { \"class.ant-table-cell-with-append\": \"nzShowExpand || nzIndentSize > 0\", \"class.ant-table-selection-column\": \"nzShowCheckbox\" } }, usesOnChanges: true, ngImport: i0, template: `\n    <ng-container *ngIf=\"nzShowExpand || nzIndentSize > 0\">\n      <nz-row-indent [indentSize]=\"nzIndentSize\"></nz-row-indent>\n      <ng-template #rowExpand>\n        <button\n          nz-row-expand-button\n          [expand]=\"nzExpand\"\n          (expandChange)=\"onExpandChange($event)\"\n          [spaceMode]=\"!nzShowExpand\"\n        ></button>\n      </ng-template>\n      <ng-container *ngIf=\"nzExpandIcon; else rowExpand\">\n        <ng-template [ngTemplateOutlet]=\"nzExpandIcon\"></ng-template>\n      </ng-container>\n    </ng-container>\n    <label\n      nz-checkbox\n      *ngIf=\"nzShowCheckbox\"\n      [nzDisabled]=\"nzDisabled\"\n      [ngModel]=\"nzChecked\"\n      [nzIndeterminate]=\"nzIndeterminate\"\n      [attr.aria-label]=\"nzLabel\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <ng-content></ng-content>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NzRowIndentDirective, selector: \"nz-row-indent\", inputs: [\"indentSize\"] }, { kind: \"directive\", type: NzRowExpandButtonDirective, selector: \"button[nz-row-expand-button]\", inputs: [\"expand\", \"spaceMode\"], outputs: [\"expandChange\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"ngmodule\", type: NzCheckboxModule }, { kind: \"component\", type: i5.NzCheckboxComponent, selector: \"[nz-checkbox]\", inputs: [\"nzValue\", \"nzAutoFocus\", \"nzDisabled\", \"nzIndeterminate\", \"nzChecked\", \"nzId\"], outputs: [\"nzCheckedChange\"], exportAs: [\"nzCheckbox\"] }, { kind: \"ngmodule\", type: FormsModule }, { kind: \"directive\", type: i6.NgControlStatus, selector: \"[formControlName],[ngModel],[formControl]\" }, { kind: \"directive\", type: i6.NgModel, selector: \"[ngModel]:not([formControlName]):not([formControl])\", inputs: [\"name\", \"disabled\", \"ngModel\", \"ngModelOptions\"], outputs: [\"ngModelChange\"], exportAs: [\"ngModel\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzTdAddOnComponent.prototype, \"nzShowExpand\", void 0);\n__decorate([\n    InputBoolean()\n], NzTdAddOnComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([\n    InputBoolean()\n], NzTdAddOnComponent.prototype, \"nzExpand\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTdAddOnComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'td[nzChecked], td[nzDisabled], td[nzIndeterminate], td[nzIndentSize], td[nzExpand], td[nzShowExpand], td[nzShowCheckbox]',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-container *ngIf=\"nzShowExpand || nzIndentSize > 0\">\n      <nz-row-indent [indentSize]=\"nzIndentSize\"></nz-row-indent>\n      <ng-template #rowExpand>\n        <button\n          nz-row-expand-button\n          [expand]=\"nzExpand\"\n          (expandChange)=\"onExpandChange($event)\"\n          [spaceMode]=\"!nzShowExpand\"\n        ></button>\n      </ng-template>\n      <ng-container *ngIf=\"nzExpandIcon; else rowExpand\">\n        <ng-template [ngTemplateOutlet]=\"nzExpandIcon\"></ng-template>\n      </ng-container>\n    </ng-container>\n    <label\n      nz-checkbox\n      *ngIf=\"nzShowCheckbox\"\n      [nzDisabled]=\"nzDisabled\"\n      [ngModel]=\"nzChecked\"\n      [nzIndeterminate]=\"nzIndeterminate\"\n      [attr.aria-label]=\"nzLabel\"\n      (ngModelChange)=\"onCheckedChange($event)\"\n    ></label>\n    <ng-content></ng-content>\n  `,\n                    host: {\n                        '[class.ant-table-cell-with-append]': `nzShowExpand || nzIndentSize > 0`,\n                        '[class.ant-table-selection-column]': `nzShowCheckbox`\n                    },\n                    imports: [NzRowIndentDirective, NzRowExpandButtonDirective, NgIf, NgTemplateOutlet, NzCheckboxModule, FormsModule],\n                    standalone: true\n                }]\n        }], propDecorators: { nzChecked: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzIndeterminate: [{\n                type: Input\n            }], nzLabel: [{\n                type: Input\n            }], nzIndentSize: [{\n                type: Input\n            }], nzShowExpand: [{\n                type: Input\n            }], nzShowCheckbox: [{\n                type: Input\n            }], nzExpand: [{\n                type: Input\n            }], nzExpandIcon: [{\n                type: Input\n            }], nzCheckedChange: [{\n                type: Output\n            }], nzExpandChange: [{\n                type: Output\n            }] } });\n\nclass NzThAddOnComponent {\n    getNextSortDirection(sortDirections, current) {\n        const index = sortDirections.indexOf(current);\n        if (index === sortDirections.length - 1) {\n            return sortDirections[0];\n        }\n        else {\n            return sortDirections[index + 1];\n        }\n    }\n    setSortOrder(order) {\n        this.sortOrderChange$.next(order);\n    }\n    clearSortOrder() {\n        if (this.sortOrder !== null) {\n            this.setSortOrder(null);\n        }\n    }\n    onFilterValueChange(value) {\n        this.nzFilterChange.emit(value);\n        this.nzFilterValue = value;\n        this.updateCalcOperator();\n    }\n    updateCalcOperator() {\n        this.calcOperatorChange$.next();\n    }\n    constructor(host, cdr, ngZone, destroy$) {\n        this.host = host;\n        this.cdr = cdr;\n        this.ngZone = ngZone;\n        this.destroy$ = destroy$;\n        this.manualClickOrder$ = new Subject();\n        this.calcOperatorChange$ = new Subject();\n        this.nzFilterValue = null;\n        this.sortOrder = null;\n        this.sortDirections = ['ascend', 'descend', null];\n        this.sortOrderChange$ = new Subject();\n        this.isNzShowSortChanged = false;\n        this.isNzShowFilterChanged = false;\n        this.nzFilterMultiple = true;\n        this.nzSortOrder = null;\n        this.nzSortPriority = false;\n        this.nzSortDirections = ['ascend', 'descend', null];\n        this.nzFilters = [];\n        this.nzSortFn = null;\n        this.nzFilterFn = null;\n        this.nzShowSort = false;\n        this.nzShowFilter = false;\n        this.nzCustomFilter = false;\n        this.nzCheckedChange = new EventEmitter();\n        this.nzSortOrderChange = new EventEmitter();\n        this.nzFilterChange = new EventEmitter();\n    }\n    ngOnInit() {\n        this.ngZone.runOutsideAngular(() => fromEvent(this.host.nativeElement, 'click')\n            .pipe(filter(() => this.nzShowSort), takeUntil(this.destroy$))\n            .subscribe(() => {\n            const nextOrder = this.getNextSortDirection(this.sortDirections, this.sortOrder);\n            this.ngZone.run(() => {\n                this.setSortOrder(nextOrder);\n                this.manualClickOrder$.next(this);\n            });\n        }));\n        this.sortOrderChange$.pipe(takeUntil(this.destroy$)).subscribe(order => {\n            if (this.sortOrder !== order) {\n                this.sortOrder = order;\n                this.nzSortOrderChange.emit(order);\n            }\n            this.updateCalcOperator();\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { nzSortDirections, nzFilters, nzSortOrder, nzSortFn, nzFilterFn, nzSortPriority, nzFilterMultiple, nzShowSort, nzShowFilter } = changes;\n        if (nzSortDirections) {\n            if (this.nzSortDirections && this.nzSortDirections.length) {\n                this.sortDirections = this.nzSortDirections;\n            }\n        }\n        if (nzSortOrder) {\n            this.sortOrder = this.nzSortOrder;\n            this.setSortOrder(this.nzSortOrder);\n        }\n        if (nzShowSort) {\n            this.isNzShowSortChanged = true;\n        }\n        if (nzShowFilter) {\n            this.isNzShowFilterChanged = true;\n        }\n        const isFirstChange = (value) => value && value.firstChange && value.currentValue !== undefined;\n        if ((isFirstChange(nzSortOrder) || isFirstChange(nzSortFn)) && !this.isNzShowSortChanged) {\n            this.nzShowSort = true;\n        }\n        if (isFirstChange(nzFilters) && !this.isNzShowFilterChanged) {\n            this.nzShowFilter = true;\n        }\n        if ((nzFilters || nzFilterMultiple) && this.nzShowFilter) {\n            const listOfValue = this.nzFilters.filter(item => item.byDefault).map(item => item.value);\n            this.nzFilterValue = this.nzFilterMultiple ? listOfValue : listOfValue[0] || null;\n        }\n        if (nzSortFn || nzFilterFn || nzSortPriority || nzFilters) {\n            this.updateCalcOperator();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThAddOnComponent, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.NzDestroyService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzThAddOnComponent, isStandalone: true, selector: \"th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]\", inputs: { nzColumnKey: \"nzColumnKey\", nzFilterMultiple: \"nzFilterMultiple\", nzSortOrder: \"nzSortOrder\", nzSortPriority: \"nzSortPriority\", nzSortDirections: \"nzSortDirections\", nzFilters: \"nzFilters\", nzSortFn: \"nzSortFn\", nzFilterFn: \"nzFilterFn\", nzShowSort: \"nzShowSort\", nzShowFilter: \"nzShowFilter\", nzCustomFilter: \"nzCustomFilter\" }, outputs: { nzCheckedChange: \"nzCheckedChange\", nzSortOrderChange: \"nzSortOrderChange\", nzFilterChange: \"nzFilterChange\" }, host: { properties: { \"class.ant-table-column-has-sorters\": \"nzShowSort\", \"class.ant-table-column-sort\": \"sortOrder === 'descend' || sortOrder === 'ascend'\" } }, providers: [NzDestroyService], usesOnChanges: true, ngImport: i0, template: `\n    <nz-table-filter\n      *ngIf=\"nzShowFilter || nzCustomFilter; else notFilterTemplate\"\n      [contentTemplate]=\"notFilterTemplate\"\n      [extraTemplate]=\"extraTemplate\"\n      [customFilter]=\"nzCustomFilter\"\n      [filterMultiple]=\"nzFilterMultiple\"\n      [listOfFilter]=\"nzFilters\"\n      (filterChange)=\"onFilterValueChange($event)\"\n    ></nz-table-filter>\n    <ng-template #notFilterTemplate>\n      <ng-template [ngTemplateOutlet]=\"nzShowSort ? sortTemplate : contentTemplate\"></ng-template>\n    </ng-template>\n    <ng-template #extraTemplate>\n      <ng-content select=\"[nz-th-extra]\"></ng-content>\n      <ng-content select=\"nz-filter-trigger\"></ng-content>\n    </ng-template>\n    <ng-template #sortTemplate>\n      <nz-table-sorters\n        [sortOrder]=\"sortOrder\"\n        [sortDirections]=\"sortDirections\"\n        [contentTemplate]=\"contentTemplate\"\n      ></nz-table-sorters>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzTableFilterComponent, selector: \"nz-table-filter\", inputs: [\"contentTemplate\", \"customFilter\", \"extraTemplate\", \"filterMultiple\", \"listOfFilter\"], outputs: [\"filterChange\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzTableSortersComponent, selector: \"nz-table-sorters\", inputs: [\"sortDirections\", \"sortOrder\", \"contentTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzThAddOnComponent.prototype, \"nzShowSort\", void 0);\n__decorate([\n    InputBoolean()\n], NzThAddOnComponent.prototype, \"nzShowFilter\", void 0);\n__decorate([\n    InputBoolean()\n], NzThAddOnComponent.prototype, \"nzCustomFilter\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThAddOnComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'th[nzColumnKey], th[nzSortFn], th[nzSortOrder], th[nzFilters], th[nzShowSort], th[nzShowFilter], th[nzCustomFilter]',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <nz-table-filter\n      *ngIf=\"nzShowFilter || nzCustomFilter; else notFilterTemplate\"\n      [contentTemplate]=\"notFilterTemplate\"\n      [extraTemplate]=\"extraTemplate\"\n      [customFilter]=\"nzCustomFilter\"\n      [filterMultiple]=\"nzFilterMultiple\"\n      [listOfFilter]=\"nzFilters\"\n      (filterChange)=\"onFilterValueChange($event)\"\n    ></nz-table-filter>\n    <ng-template #notFilterTemplate>\n      <ng-template [ngTemplateOutlet]=\"nzShowSort ? sortTemplate : contentTemplate\"></ng-template>\n    </ng-template>\n    <ng-template #extraTemplate>\n      <ng-content select=\"[nz-th-extra]\"></ng-content>\n      <ng-content select=\"nz-filter-trigger\"></ng-content>\n    </ng-template>\n    <ng-template #sortTemplate>\n      <nz-table-sorters\n        [sortOrder]=\"sortOrder\"\n        [sortDirections]=\"sortDirections\"\n        [contentTemplate]=\"contentTemplate\"\n      ></nz-table-sorters>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n                    host: {\n                        '[class.ant-table-column-has-sorters]': 'nzShowSort',\n                        '[class.ant-table-column-sort]': `sortOrder === 'descend' || sortOrder === 'ascend'`\n                    },\n                    providers: [NzDestroyService],\n                    imports: [NzTableFilterComponent, NgIf, NgTemplateOutlet, NzTableSortersComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.NzDestroyService }], propDecorators: { nzColumnKey: [{\n                type: Input\n            }], nzFilterMultiple: [{\n                type: Input\n            }], nzSortOrder: [{\n                type: Input\n            }], nzSortPriority: [{\n                type: Input\n            }], nzSortDirections: [{\n                type: Input\n            }], nzFilters: [{\n                type: Input\n            }], nzSortFn: [{\n                type: Input\n            }], nzFilterFn: [{\n                type: Input\n            }], nzShowSort: [{\n                type: Input\n            }], nzShowFilter: [{\n                type: Input\n            }], nzCustomFilter: [{\n                type: Input\n            }], nzCheckedChange: [{\n                type: Output\n            }], nzSortOrderChange: [{\n                type: Output\n            }], nzFilterChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzThMeasureDirective {\n    constructor(renderer, elementRef) {\n        this.renderer = renderer;\n        this.elementRef = elementRef;\n        this.changes$ = new Subject();\n        this.nzWidth = null;\n        this.colspan = null;\n        this.colSpan = null;\n        this.rowspan = null;\n        this.rowSpan = null;\n    }\n    ngOnChanges(changes) {\n        const { nzWidth, colspan, rowspan, colSpan, rowSpan } = changes;\n        if (colspan || colSpan) {\n            const col = this.colspan || this.colSpan;\n            if (!isNil(col)) {\n                this.renderer.setAttribute(this.elementRef.nativeElement, 'colspan', `${col}`);\n            }\n            else {\n                this.renderer.removeAttribute(this.elementRef.nativeElement, 'colspan');\n            }\n        }\n        if (rowspan || rowSpan) {\n            const row = this.rowspan || this.rowSpan;\n            if (!isNil(row)) {\n                this.renderer.setAttribute(this.elementRef.nativeElement, 'rowspan', `${row}`);\n            }\n            else {\n                this.renderer.removeAttribute(this.elementRef.nativeElement, 'rowspan');\n            }\n        }\n        if (nzWidth || colspan) {\n            this.changes$.next();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThMeasureDirective, deps: [{ token: i0.Renderer2 }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzThMeasureDirective, isStandalone: true, selector: \"th\", inputs: { nzWidth: \"nzWidth\", colspan: \"colspan\", colSpan: \"colSpan\", rowspan: \"rowspan\", rowSpan: \"rowSpan\" }, usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThMeasureDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'th',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i0.ElementRef }], propDecorators: { nzWidth: [{\n                type: Input\n            }], colspan: [{\n                type: Input\n            }], colSpan: [{\n                type: Input\n            }], rowspan: [{\n                type: Input\n            }], rowSpan: [{\n                type: Input\n            }] } });\n\nclass NzThSelectionComponent {\n    constructor() {\n        this.nzSelections = [];\n        this.nzChecked = false;\n        this.nzDisabled = false;\n        this.nzIndeterminate = false;\n        this.nzLabel = null;\n        this.nzShowCheckbox = false;\n        this.nzShowRowSelection = false;\n        this.nzCheckedChange = new EventEmitter();\n        this.isNzShowExpandChanged = false;\n        this.isNzShowCheckboxChanged = false;\n    }\n    onCheckedChange(checked) {\n        this.nzChecked = checked;\n        this.nzCheckedChange.emit(checked);\n    }\n    ngOnChanges(changes) {\n        const isFirstChange = (value) => value && value.firstChange && value.currentValue !== undefined;\n        const { nzChecked, nzSelections, nzShowExpand, nzShowCheckbox } = changes;\n        if (nzShowExpand) {\n            this.isNzShowExpandChanged = true;\n        }\n        if (nzShowCheckbox) {\n            this.isNzShowCheckboxChanged = true;\n        }\n        if (isFirstChange(nzSelections) && !this.isNzShowExpandChanged) {\n            this.nzShowRowSelection = true;\n        }\n        if (isFirstChange(nzChecked) && !this.isNzShowCheckboxChanged) {\n            this.nzShowCheckbox = true;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThSelectionComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzThSelectionComponent, isStandalone: true, selector: \"th[nzSelections],th[nzChecked],th[nzShowCheckbox],th[nzShowRowSelection]\", inputs: { nzSelections: \"nzSelections\", nzChecked: \"nzChecked\", nzDisabled: \"nzDisabled\", nzIndeterminate: \"nzIndeterminate\", nzLabel: \"nzLabel\", nzShowCheckbox: \"nzShowCheckbox\", nzShowRowSelection: \"nzShowRowSelection\" }, outputs: { nzCheckedChange: \"nzCheckedChange\" }, host: { classAttribute: \"ant-table-selection-column\" }, usesOnChanges: true, ngImport: i0, template: `\n    <nz-table-selection\n      [checked]=\"nzChecked\"\n      [disabled]=\"nzDisabled\"\n      [indeterminate]=\"nzIndeterminate\"\n      [label]=\"nzLabel\"\n      [listOfSelections]=\"nzSelections\"\n      [showCheckbox]=\"nzShowCheckbox\"\n      [showRowSelection]=\"nzShowRowSelection\"\n      (checkedChange)=\"onCheckedChange($event)\"\n    ></nz-table-selection>\n    <ng-content></ng-content>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzTableSelectionComponent, selector: \"nz-table-selection\", inputs: [\"listOfSelections\", \"checked\", \"disabled\", \"indeterminate\", \"label\", \"showCheckbox\", \"showRowSelection\"], outputs: [\"checkedChange\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzThSelectionComponent.prototype, \"nzShowCheckbox\", void 0);\n__decorate([\n    InputBoolean()\n], NzThSelectionComponent.prototype, \"nzShowRowSelection\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzThSelectionComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'th[nzSelections],th[nzChecked],th[nzShowCheckbox],th[nzShowRowSelection]',\n                    preserveWhitespaces: false,\n                    encapsulation: ViewEncapsulation.None,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    template: `\n    <nz-table-selection\n      [checked]=\"nzChecked\"\n      [disabled]=\"nzDisabled\"\n      [indeterminate]=\"nzIndeterminate\"\n      [label]=\"nzLabel\"\n      [listOfSelections]=\"nzSelections\"\n      [showCheckbox]=\"nzShowCheckbox\"\n      [showRowSelection]=\"nzShowRowSelection\"\n      (checkedChange)=\"onCheckedChange($event)\"\n    ></nz-table-selection>\n    <ng-content></ng-content>\n  `,\n                    host: { class: 'ant-table-selection-column' },\n                    imports: [NzTableSelectionComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { nzSelections: [{\n                type: Input\n            }], nzChecked: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzIndeterminate: [{\n                type: Input\n            }], nzLabel: [{\n                type: Input\n            }], nzShowCheckbox: [{\n                type: Input\n            }], nzShowRowSelection: [{\n                type: Input\n            }], nzCheckedChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzCellAlignDirective {\n    constructor() {\n        this.nzAlign = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellAlignDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCellAlignDirective, isStandalone: true, selector: \"th[nzAlign],td[nzAlign]\", inputs: { nzAlign: \"nzAlign\" }, host: { properties: { \"style.text-align\": \"nzAlign\" } }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellAlignDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'th[nzAlign],td[nzAlign]',\n                    host: {\n                        '[style.text-align]': 'nzAlign'\n                    },\n                    standalone: true\n                }]\n        }], propDecorators: { nzAlign: [{\n                type: Input\n            }] } });\n\nclass NzCellEllipsisDirective {\n    constructor() {\n        this.nzEllipsis = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellEllipsisDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCellEllipsisDirective, isStandalone: true, selector: \"th[nzEllipsis],td[nzEllipsis]\", inputs: { nzEllipsis: \"nzEllipsis\" }, host: { properties: { \"class.ant-table-cell-ellipsis\": \"nzEllipsis\" } }, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzCellEllipsisDirective.prototype, \"nzEllipsis\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellEllipsisDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'th[nzEllipsis],td[nzEllipsis]',\n                    host: {\n                        '[class.ant-table-cell-ellipsis]': 'nzEllipsis'\n                    },\n                    standalone: true\n                }]\n        }], propDecorators: { nzEllipsis: [{\n                type: Input\n            }] } });\n\nclass NzCellBreakWordDirective {\n    constructor() {\n        this.nzBreakWord = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellBreakWordDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzCellBreakWordDirective, isStandalone: true, selector: \"th[nzBreakWord],td[nzBreakWord]\", inputs: { nzBreakWord: \"nzBreakWord\" }, host: { properties: { \"style.word-break\": \"nzBreakWord ? 'break-all' : ''\" } }, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzCellBreakWordDirective.prototype, \"nzBreakWord\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzCellBreakWordDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'th[nzBreakWord],td[nzBreakWord]',\n                    host: {\n                        '[style.word-break]': `nzBreakWord ? 'break-all' : ''`\n                    },\n                    standalone: true\n                }]\n        }], propDecorators: { nzBreakWord: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableContentComponent {\n    constructor() {\n        this.tableLayout = 'auto';\n        this.theadTemplate = null;\n        this.contentTemplate = null;\n        this.listOfColWidth = [];\n        this.scrollX = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableContentComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableContentComponent, isStandalone: true, selector: \"table[nz-table-content]\", inputs: { tableLayout: \"tableLayout\", theadTemplate: \"theadTemplate\", contentTemplate: \"contentTemplate\", listOfColWidth: \"listOfColWidth\", scrollX: \"scrollX\" }, host: { properties: { \"style.table-layout\": \"tableLayout\", \"class.ant-table-fixed\": \"scrollX\", \"style.width\": \"scrollX\", \"style.min-width\": \"scrollX ? '100%' : null\" } }, ngImport: i0, template: `\n    <col [style.width]=\"width\" [style.minWidth]=\"width\" *ngFor=\"let width of listOfColWidth\" />\n    <thead class=\"ant-table-thead\" *ngIf=\"theadTemplate\">\n      <ng-template [ngTemplateOutlet]=\"theadTemplate\"></ng-template>\n    </thead>\n    <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    <ng-content></ng-content>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableContentComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'table[nz-table-content]',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <col [style.width]=\"width\" [style.minWidth]=\"width\" *ngFor=\"let width of listOfColWidth\" />\n    <thead class=\"ant-table-thead\" *ngIf=\"theadTemplate\">\n      <ng-template [ngTemplateOutlet]=\"theadTemplate\"></ng-template>\n    </thead>\n    <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    <ng-content></ng-content>\n  `,\n                    host: {\n                        '[style.table-layout]': 'tableLayout',\n                        '[class.ant-table-fixed]': 'scrollX',\n                        '[style.width]': 'scrollX',\n                        '[style.min-width]': `scrollX ? '100%' : null`\n                    },\n                    imports: [NgTemplateOutlet, NgIf, NgForOf],\n                    standalone: true\n                }]\n        }], propDecorators: { tableLayout: [{\n                type: Input\n            }], theadTemplate: [{\n                type: Input\n            }], contentTemplate: [{\n                type: Input\n            }], listOfColWidth: [{\n                type: Input\n            }], scrollX: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableFixedRowComponent {\n    constructor(nzTableStyleService, renderer) {\n        this.nzTableStyleService = nzTableStyleService;\n        this.renderer = renderer;\n        this.hostWidth$ = new BehaviorSubject(null);\n        this.enableAutoMeasure$ = new BehaviorSubject(false);\n        this.destroy$ = new Subject();\n    }\n    ngOnInit() {\n        if (this.nzTableStyleService) {\n            const { enableAutoMeasure$, hostWidth$ } = this.nzTableStyleService;\n            enableAutoMeasure$.pipe(takeUntil(this.destroy$)).subscribe(this.enableAutoMeasure$);\n            hostWidth$.pipe(takeUntil(this.destroy$)).subscribe(this.hostWidth$);\n        }\n    }\n    ngAfterViewInit() {\n        this.nzTableStyleService.columnCount$.pipe(takeUntil(this.destroy$)).subscribe(count => {\n            this.renderer.setAttribute(this.tdElement.nativeElement, 'colspan', `${count}`);\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableFixedRowComponent, deps: [{ token: NzTableStyleService }, { token: i0.Renderer2 }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableFixedRowComponent, isStandalone: true, selector: \"tr[nz-table-fixed-row], tr[nzExpand]\", viewQueries: [{ propertyName: \"tdElement\", first: true, predicate: [\"tdElement\"], descendants: true, static: true }], ngImport: i0, template: `\n    <td class=\"nz-disable-td ant-table-cell\" #tdElement>\n      <div\n        class=\"ant-table-expanded-row-fixed\"\n        *ngIf=\"enableAutoMeasure$ | async; else contentTemplate\"\n        style=\"position: sticky; left: 0px; overflow: hidden;\"\n        [style.width.px]=\"hostWidth$ | async\"\n      >\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n      </div>\n    </td>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableFixedRowComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'tr[nz-table-fixed-row], tr[nzExpand]',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <td class=\"nz-disable-td ant-table-cell\" #tdElement>\n      <div\n        class=\"ant-table-expanded-row-fixed\"\n        *ngIf=\"enableAutoMeasure$ | async; else contentTemplate\"\n        style=\"position: sticky; left: 0px; overflow: hidden;\"\n        [style.width.px]=\"hostWidth$ | async\"\n      >\n        <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n      </div>\n    </td>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n                    imports: [NgIf, AsyncPipe, NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: NzTableStyleService }, { type: i0.Renderer2 }], propDecorators: { tdElement: [{\n                type: ViewChild,\n                args: ['tdElement', { static: true }]\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableInnerDefaultComponent {\n    constructor() {\n        this.tableLayout = 'auto';\n        this.listOfColWidth = [];\n        this.theadTemplate = null;\n        this.contentTemplate = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableInnerDefaultComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableInnerDefaultComponent, isStandalone: true, selector: \"nz-table-inner-default\", inputs: { tableLayout: \"tableLayout\", listOfColWidth: \"listOfColWidth\", theadTemplate: \"theadTemplate\", contentTemplate: \"contentTemplate\" }, host: { classAttribute: \"ant-table-container\" }, ngImport: i0, template: `\n    <div class=\"ant-table-content\">\n      <table\n        nz-table-content\n        [contentTemplate]=\"contentTemplate\"\n        [tableLayout]=\"tableLayout\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n      ></table>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzTableContentComponent, selector: \"table[nz-table-content]\", inputs: [\"tableLayout\", \"theadTemplate\", \"contentTemplate\", \"listOfColWidth\", \"scrollX\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableInnerDefaultComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-inner-default',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <div class=\"ant-table-content\">\n      <table\n        nz-table-content\n        [contentTemplate]=\"contentTemplate\"\n        [tableLayout]=\"tableLayout\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n      ></table>\n    </div>\n  `,\n                    host: { class: 'ant-table-container' },\n                    imports: [NzTableContentComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { tableLayout: [{\n                type: Input\n            }], listOfColWidth: [{\n                type: Input\n            }], theadTemplate: [{\n                type: Input\n            }], contentTemplate: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTrMeasureComponent {\n    constructor(nzResizeObserver, ngZone) {\n        this.nzResizeObserver = nzResizeObserver;\n        this.ngZone = ngZone;\n        this.listOfMeasureColumn = [];\n        this.listOfAutoWidth = new EventEmitter();\n        this.destroy$ = new Subject();\n    }\n    trackByFunc(_, key) {\n        return key;\n    }\n    ngAfterViewInit() {\n        this.listOfTdElement.changes\n            .pipe(startWith(this.listOfTdElement))\n            .pipe(switchMap(list => combineLatest(list.toArray().map((item) => this.nzResizeObserver.observe(item).pipe(map(([entry]) => {\n            const { width } = entry.target.getBoundingClientRect();\n            return Math.floor(width);\n        }))))), debounceTime(16), takeUntil(this.destroy$))\n            .subscribe(data => {\n            // Caretaker note: we don't have to re-enter the Angular zone each time the stream emits.\n            // The below check is necessary to be sure that zone is not nooped through `BootstrapOptions`\n            // (`bootstrapModule(AppModule, { ngZone: 'noop' }))`. The `ngZone instanceof NgZone` may return\n            // `false` if zone is nooped, since `ngZone` will be an instance of the `NoopNgZone`.\n            // The `ResizeObserver` might be also patched through `zone.js/dist/zone-patch-resize-observer`,\n            // thus calling `ngZone.run` again will cause another change detection.\n            if (this.ngZone instanceof NgZone && NgZone.isInAngularZone()) {\n                this.listOfAutoWidth.next(data);\n            }\n            else {\n                this.ngZone.run(() => this.listOfAutoWidth.next(data));\n            }\n        });\n    }\n    ngOnDestroy() {\n        this.destroy$.next(true);\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrMeasureComponent, deps: [{ token: i1$2.NzResizeObserver }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTrMeasureComponent, isStandalone: true, selector: \"tr[nz-table-measure-row]\", inputs: { listOfMeasureColumn: \"listOfMeasureColumn\" }, outputs: { listOfAutoWidth: \"listOfAutoWidth\" }, host: { classAttribute: \"ant-table-measure-now\" }, viewQueries: [{ propertyName: \"listOfTdElement\", predicate: [\"tdElement\"], descendants: true }], ngImport: i0, template: `\n    <td\n      #tdElement\n      class=\"nz-disable-td\"\n      style=\"padding: 0px; border: 0px; height: 0px;\"\n      *ngFor=\"let th of listOfMeasureColumn; trackBy: trackByFunc\"\n    ></td>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrMeasureComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'tr[nz-table-measure-row]',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <td\n      #tdElement\n      class=\"nz-disable-td\"\n      style=\"padding: 0px; border: 0px; height: 0px;\"\n      *ngFor=\"let th of listOfMeasureColumn; trackBy: trackByFunc\"\n    ></td>\n  `,\n                    host: { class: 'ant-table-measure-now' },\n                    imports: [NgForOf],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i1$2.NzResizeObserver }, { type: i0.NgZone }], propDecorators: { listOfMeasureColumn: [{\n                type: Input\n            }], listOfAutoWidth: [{\n                type: Output\n            }], listOfTdElement: [{\n                type: ViewChildren,\n                args: ['tdElement']\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTbodyComponent {\n    constructor(nzTableStyleService) {\n        this.nzTableStyleService = nzTableStyleService;\n        this.isInsideTable = false;\n        this.showEmpty$ = new BehaviorSubject(false);\n        this.noResult$ = new BehaviorSubject(undefined);\n        this.listOfMeasureColumn$ = new BehaviorSubject([]);\n        this.destroy$ = new Subject();\n        this.isInsideTable = !!this.nzTableStyleService;\n        if (this.nzTableStyleService) {\n            const { showEmpty$, noResult$, listOfMeasureColumn$ } = this.nzTableStyleService;\n            noResult$.pipe(takeUntil(this.destroy$)).subscribe(this.noResult$);\n            listOfMeasureColumn$.pipe(takeUntil(this.destroy$)).subscribe(this.listOfMeasureColumn$);\n            showEmpty$.pipe(takeUntil(this.destroy$)).subscribe(this.showEmpty$);\n        }\n    }\n    onListOfAutoWidthChange(listOfAutoWidth) {\n        this.nzTableStyleService.setListOfAutoWidth(listOfAutoWidth);\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTbodyComponent, deps: [{ token: NzTableStyleService, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTbodyComponent, isStandalone: true, selector: \"tbody\", host: { properties: { \"class.ant-table-tbody\": \"isInsideTable\" } }, ngImport: i0, template: `\n    <ng-container *ngIf=\"listOfMeasureColumn$ | async as listOfMeasureColumn\">\n      <tr\n        nz-table-measure-row\n        *ngIf=\"isInsideTable && listOfMeasureColumn.length\"\n        [listOfMeasureColumn]=\"listOfMeasureColumn\"\n        (listOfAutoWidth)=\"onListOfAutoWidthChange($event)\"\n      ></tr>\n    </ng-container>\n    <ng-content></ng-content>\n    <tr class=\"ant-table-placeholder\" nz-table-fixed-row *ngIf=\"showEmpty$ | async\">\n      <nz-embed-empty nzComponentName=\"table\" [specificContent]=\"(noResult$ | async)!\"></nz-embed-empty>\n    </tr>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"pipe\", type: AsyncPipe, name: \"async\" }, { kind: \"component\", type: NzTrMeasureComponent, selector: \"tr[nz-table-measure-row]\", inputs: [\"listOfMeasureColumn\"], outputs: [\"listOfAutoWidth\"] }, { kind: \"component\", type: NzTableFixedRowComponent, selector: \"tr[nz-table-fixed-row], tr[nzExpand]\" }, { kind: \"ngmodule\", type: NzEmptyModule }, { kind: \"component\", type: i2$2.NzEmbedEmptyComponent, selector: \"nz-embed-empty\", inputs: [\"nzComponentName\", \"specificContent\"], exportAs: [\"nzEmbedEmpty\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTbodyComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'tbody',\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-container *ngIf=\"listOfMeasureColumn$ | async as listOfMeasureColumn\">\n      <tr\n        nz-table-measure-row\n        *ngIf=\"isInsideTable && listOfMeasureColumn.length\"\n        [listOfMeasureColumn]=\"listOfMeasureColumn\"\n        (listOfAutoWidth)=\"onListOfAutoWidthChange($event)\"\n      ></tr>\n    </ng-container>\n    <ng-content></ng-content>\n    <tr class=\"ant-table-placeholder\" nz-table-fixed-row *ngIf=\"showEmpty$ | async\">\n      <nz-embed-empty nzComponentName=\"table\" [specificContent]=\"(noResult$ | async)!\"></nz-embed-empty>\n    </tr>\n  `,\n                    host: {\n                        '[class.ant-table-tbody]': 'isInsideTable'\n                    },\n                    imports: [NgIf, AsyncPipe, NzTrMeasureComponent, NzTableFixedRowComponent, NzEmptyModule],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: NzTableStyleService, decorators: [{\n                    type: Optional\n                }] }] });\n\nclass NzTableInnerScrollComponent {\n    setScrollPositionClassName(clear = false) {\n        const { scrollWidth, scrollLeft, clientWidth } = this.tableBodyElement.nativeElement;\n        const leftClassName = 'ant-table-ping-left';\n        const rightClassName = 'ant-table-ping-right';\n        if ((scrollWidth === clientWidth && scrollWidth !== 0) || clear) {\n            this.renderer.removeClass(this.tableMainElement, leftClassName);\n            this.renderer.removeClass(this.tableMainElement, rightClassName);\n        }\n        else if (scrollLeft === 0) {\n            this.renderer.removeClass(this.tableMainElement, leftClassName);\n            this.renderer.addClass(this.tableMainElement, rightClassName);\n        }\n        else if (scrollWidth === scrollLeft + clientWidth) {\n            this.renderer.removeClass(this.tableMainElement, rightClassName);\n            this.renderer.addClass(this.tableMainElement, leftClassName);\n        }\n        else {\n            this.renderer.addClass(this.tableMainElement, leftClassName);\n            this.renderer.addClass(this.tableMainElement, rightClassName);\n        }\n    }\n    constructor(renderer, ngZone, platform, resizeService) {\n        this.renderer = renderer;\n        this.ngZone = ngZone;\n        this.platform = platform;\n        this.resizeService = resizeService;\n        this.data = [];\n        this.scrollX = null;\n        this.scrollY = null;\n        this.contentTemplate = null;\n        this.widthConfig = [];\n        this.listOfColWidth = [];\n        this.theadTemplate = null;\n        this.virtualTemplate = null;\n        this.virtualItemSize = 0;\n        this.virtualMaxBufferPx = 200;\n        this.virtualMinBufferPx = 100;\n        this.virtualForTrackBy = index => index;\n        this.headerStyleMap = {};\n        this.bodyStyleMap = {};\n        this.verticalScrollBarWidth = 0;\n        this.noDateVirtualHeight = '182px';\n        this.data$ = new Subject();\n        this.scroll$ = new Subject();\n        this.destroy$ = new Subject();\n    }\n    ngOnChanges(changes) {\n        const { scrollX, scrollY, data } = changes;\n        if (scrollX || scrollY) {\n            const hasVerticalScrollBar = this.verticalScrollBarWidth !== 0;\n            this.headerStyleMap = {\n                overflowX: 'hidden',\n                overflowY: this.scrollY && hasVerticalScrollBar ? 'scroll' : 'hidden'\n            };\n            this.bodyStyleMap = {\n                overflowY: this.scrollY ? 'scroll' : 'hidden',\n                overflowX: this.scrollX ? 'auto' : null,\n                maxHeight: this.scrollY\n            };\n            // Caretaker note: we have to emit the value outside of the Angular zone, thus DOM timer (`delay(0)`) and `scroll`\n            // event listener will be also added outside of the Angular zone.\n            this.ngZone.runOutsideAngular(() => this.scroll$.next());\n        }\n        if (data) {\n            // See the comment above.\n            this.ngZone.runOutsideAngular(() => this.data$.next());\n        }\n    }\n    ngAfterViewInit() {\n        if (this.platform.isBrowser) {\n            this.ngZone.runOutsideAngular(() => {\n                const scrollEvent$ = this.scroll$.pipe(startWith(null), delay(0), switchMap(() => fromEvent(this.tableBodyElement.nativeElement, 'scroll').pipe(startWith(true))), takeUntil(this.destroy$));\n                const resize$ = this.resizeService.subscribe().pipe(takeUntil(this.destroy$));\n                const data$ = this.data$.pipe(takeUntil(this.destroy$));\n                const setClassName$ = merge(scrollEvent$, resize$, data$, this.scroll$).pipe(startWith(true), delay(0), takeUntil(this.destroy$));\n                setClassName$.subscribe(() => this.setScrollPositionClassName());\n                scrollEvent$\n                    .pipe(filter(() => !!this.scrollY))\n                    .subscribe(() => (this.tableHeaderElement.nativeElement.scrollLeft = this.tableBodyElement.nativeElement.scrollLeft));\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.setScrollPositionClassName(true);\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableInnerScrollComponent, deps: [{ token: i0.Renderer2 }, { token: i0.NgZone }, { token: i1$3.Platform }, { token: i2.NzResizeService }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableInnerScrollComponent, isStandalone: true, selector: \"nz-table-inner-scroll\", inputs: { data: \"data\", scrollX: \"scrollX\", scrollY: \"scrollY\", contentTemplate: \"contentTemplate\", widthConfig: \"widthConfig\", listOfColWidth: \"listOfColWidth\", theadTemplate: \"theadTemplate\", virtualTemplate: \"virtualTemplate\", virtualItemSize: \"virtualItemSize\", virtualMaxBufferPx: \"virtualMaxBufferPx\", virtualMinBufferPx: \"virtualMinBufferPx\", tableMainElement: \"tableMainElement\", virtualForTrackBy: \"virtualForTrackBy\", verticalScrollBarWidth: \"verticalScrollBarWidth\" }, host: { classAttribute: \"ant-table-container\" }, viewQueries: [{ propertyName: \"tableHeaderElement\", first: true, predicate: [\"tableHeaderElement\"], descendants: true, read: ElementRef }, { propertyName: \"tableBodyElement\", first: true, predicate: [\"tableBodyElement\"], descendants: true, read: ElementRef }, { propertyName: \"cdkVirtualScrollViewport\", first: true, predicate: CdkVirtualScrollViewport, descendants: true, read: CdkVirtualScrollViewport }], usesOnChanges: true, ngImport: i0, template: `\n    <ng-container *ngIf=\"scrollY\">\n      <div #tableHeaderElement [ngStyle]=\"headerStyleMap\" class=\"ant-table-header nz-table-hide-scrollbar\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n        ></table>\n      </div>\n      <div #tableBodyElement *ngIf=\"!virtualTemplate\" class=\"ant-table-body\" [ngStyle]=\"bodyStyleMap\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [contentTemplate]=\"contentTemplate\"\n        ></table>\n      </div>\n      <cdk-virtual-scroll-viewport\n        #tableBodyElement\n        *ngIf=\"virtualTemplate\"\n        [itemSize]=\"virtualItemSize\"\n        [maxBufferPx]=\"virtualMaxBufferPx\"\n        [minBufferPx]=\"virtualMinBufferPx\"\n        [style.height]=\"data.length ? scrollY : noDateVirtualHeight\"\n      >\n        <table nz-table-content tableLayout=\"fixed\" [scrollX]=\"scrollX\" [listOfColWidth]=\"listOfColWidth\">\n          <tbody>\n            <ng-container *cdkVirtualFor=\"let item of data; let i = index; trackBy: virtualForTrackBy\">\n              <ng-template\n                [ngTemplateOutlet]=\"virtualTemplate\"\n                [ngTemplateOutletContext]=\"{ $implicit: item, index: i }\"\n              ></ng-template>\n            </ng-container>\n          </tbody>\n        </table>\n      </cdk-virtual-scroll-viewport>\n    </ng-container>\n    <div class=\"ant-table-content\" #tableBodyElement *ngIf=\"!scrollY\" [ngStyle]=\"bodyStyleMap\">\n      <table\n        nz-table-content\n        tableLayout=\"fixed\"\n        [scrollX]=\"scrollX\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n        [contentTemplate]=\"contentTemplate\"\n      ></table>\n    </div>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzTableContentComponent, selector: \"table[nz-table-content]\", inputs: [\"tableLayout\", \"theadTemplate\", \"contentTemplate\", \"listOfColWidth\", \"scrollX\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"ngmodule\", type: ScrollingModule }, { kind: \"directive\", type: i3$1.CdkFixedSizeVirtualScroll, selector: \"cdk-virtual-scroll-viewport[itemSize]\", inputs: [\"itemSize\", \"minBufferPx\", \"maxBufferPx\"] }, { kind: \"directive\", type: i3$1.CdkVirtualForOf, selector: \"[cdkVirtualFor][cdkVirtualForOf]\", inputs: [\"cdkVirtualForOf\", \"cdkVirtualForTrackBy\", \"cdkVirtualForTemplate\", \"cdkVirtualForTemplateCacheSize\"] }, { kind: \"component\", type: i3$1.CdkVirtualScrollViewport, selector: \"cdk-virtual-scroll-viewport\", inputs: [\"orientation\", \"appendOnly\"], outputs: [\"scrolledIndexChange\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzTbodyComponent, selector: \"tbody\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableInnerScrollComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-inner-scroll',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-container *ngIf=\"scrollY\">\n      <div #tableHeaderElement [ngStyle]=\"headerStyleMap\" class=\"ant-table-header nz-table-hide-scrollbar\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n        ></table>\n      </div>\n      <div #tableBodyElement *ngIf=\"!virtualTemplate\" class=\"ant-table-body\" [ngStyle]=\"bodyStyleMap\">\n        <table\n          nz-table-content\n          tableLayout=\"fixed\"\n          [scrollX]=\"scrollX\"\n          [listOfColWidth]=\"listOfColWidth\"\n          [contentTemplate]=\"contentTemplate\"\n        ></table>\n      </div>\n      <cdk-virtual-scroll-viewport\n        #tableBodyElement\n        *ngIf=\"virtualTemplate\"\n        [itemSize]=\"virtualItemSize\"\n        [maxBufferPx]=\"virtualMaxBufferPx\"\n        [minBufferPx]=\"virtualMinBufferPx\"\n        [style.height]=\"data.length ? scrollY : noDateVirtualHeight\"\n      >\n        <table nz-table-content tableLayout=\"fixed\" [scrollX]=\"scrollX\" [listOfColWidth]=\"listOfColWidth\">\n          <tbody>\n            <ng-container *cdkVirtualFor=\"let item of data; let i = index; trackBy: virtualForTrackBy\">\n              <ng-template\n                [ngTemplateOutlet]=\"virtualTemplate\"\n                [ngTemplateOutletContext]=\"{ $implicit: item, index: i }\"\n              ></ng-template>\n            </ng-container>\n          </tbody>\n        </table>\n      </cdk-virtual-scroll-viewport>\n    </ng-container>\n    <div class=\"ant-table-content\" #tableBodyElement *ngIf=\"!scrollY\" [ngStyle]=\"bodyStyleMap\">\n      <table\n        nz-table-content\n        tableLayout=\"fixed\"\n        [scrollX]=\"scrollX\"\n        [listOfColWidth]=\"listOfColWidth\"\n        [theadTemplate]=\"theadTemplate\"\n        [contentTemplate]=\"contentTemplate\"\n      ></table>\n    </div>\n  `,\n                    host: { class: 'ant-table-container' },\n                    imports: [NzTableContentComponent, NgIf, NgStyle, ScrollingModule, NgTemplateOutlet, NzTbodyComponent],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.Renderer2 }, { type: i0.NgZone }, { type: i1$3.Platform }, { type: i2.NzResizeService }], propDecorators: { data: [{\n                type: Input\n            }], scrollX: [{\n                type: Input\n            }], scrollY: [{\n                type: Input\n            }], contentTemplate: [{\n                type: Input\n            }], widthConfig: [{\n                type: Input\n            }], listOfColWidth: [{\n                type: Input\n            }], theadTemplate: [{\n                type: Input\n            }], virtualTemplate: [{\n                type: Input\n            }], virtualItemSize: [{\n                type: Input\n            }], virtualMaxBufferPx: [{\n                type: Input\n            }], virtualMinBufferPx: [{\n                type: Input\n            }], tableMainElement: [{\n                type: Input\n            }], virtualForTrackBy: [{\n                type: Input\n            }], tableHeaderElement: [{\n                type: ViewChild,\n                args: ['tableHeaderElement', { read: ElementRef }]\n            }], tableBodyElement: [{\n                type: ViewChild,\n                args: ['tableBodyElement', { read: ElementRef }]\n            }], cdkVirtualScrollViewport: [{\n                type: ViewChild,\n                args: [CdkVirtualScrollViewport, { read: CdkVirtualScrollViewport }]\n            }], verticalScrollBarWidth: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableVirtualScrollDirective {\n    constructor(templateRef) {\n        this.templateRef = templateRef;\n    }\n    static ngTemplateContextGuard(_dir, _ctx) {\n        return true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableVirtualScrollDirective, deps: [{ token: i0.TemplateRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableVirtualScrollDirective, isStandalone: true, selector: \"[nz-virtual-scroll]\", exportAs: [\"nzVirtualScroll\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableVirtualScrollDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nz-virtual-scroll]',\n                    exportAs: 'nzVirtualScroll',\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.TemplateRef }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableTitleFooterComponent {\n    constructor() {\n        this.title = null;\n        this.footer = null;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableTitleFooterComponent, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableTitleFooterComponent, isStandalone: true, selector: \"nz-table-title-footer\", inputs: { title: \"title\", footer: \"footer\" }, host: { properties: { \"class.ant-table-title\": \"title !== null\", \"class.ant-table-footer\": \"footer !== null\" } }, ngImport: i0, template: `\n    <ng-container *nzStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n    <ng-container *nzStringTemplateOutlet=\"footer\">{{ footer }}</ng-container>\n  `, isInline: true, dependencies: [{ kind: \"ngmodule\", type: NzOutletModule }, { kind: \"directive\", type: i1$4.NzStringTemplateOutletDirective, selector: \"[nzStringTemplateOutlet]\", inputs: [\"nzStringTemplateOutletContext\", \"nzStringTemplateOutlet\"], exportAs: [\"nzStringTemplateOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableTitleFooterComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table-title-footer',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-container *nzStringTemplateOutlet=\"title\">{{ title }}</ng-container>\n    <ng-container *nzStringTemplateOutlet=\"footer\">{{ footer }}</ng-container>\n  `,\n                    host: {\n                        '[class.ant-table-title]': `title !== null`,\n                        '[class.ant-table-footer]': `footer !== null`\n                    },\n                    imports: [NzOutletModule],\n                    standalone: true\n                }]\n        }], propDecorators: { title: [{\n                type: Input\n            }], footer: [{\n                type: Input\n            }] } });\n\nconst NZ_CONFIG_MODULE_NAME = 'table';\nclass NzTableComponent {\n    onPageSizeChange(size) {\n        this.nzTableDataService.updatePageSize(size);\n    }\n    onPageIndexChange(index) {\n        this.nzTableDataService.updatePageIndex(index);\n    }\n    constructor(elementRef, nzResizeObserver, nzConfigService, cdr, nzTableStyleService, nzTableDataService, directionality) {\n        this.elementRef = elementRef;\n        this.nzResizeObserver = nzResizeObserver;\n        this.nzConfigService = nzConfigService;\n        this.cdr = cdr;\n        this.nzTableStyleService = nzTableStyleService;\n        this.nzTableDataService = nzTableDataService;\n        this.directionality = directionality;\n        this._nzModuleName = NZ_CONFIG_MODULE_NAME;\n        this.nzTableLayout = 'auto';\n        this.nzShowTotal = null;\n        this.nzItemRender = null;\n        this.nzTitle = null;\n        this.nzFooter = null;\n        this.nzNoResult = undefined;\n        this.nzPageSizeOptions = [10, 20, 30, 40, 50];\n        this.nzVirtualItemSize = 0;\n        this.nzVirtualMaxBufferPx = 200;\n        this.nzVirtualMinBufferPx = 100;\n        this.nzVirtualForTrackBy = index => index;\n        this.nzLoadingDelay = 0;\n        this.nzPageIndex = 1;\n        this.nzPageSize = 10;\n        this.nzTotal = 0;\n        this.nzWidthConfig = [];\n        this.nzData = [];\n        this.nzCustomColumn = [];\n        this.nzPaginationPosition = 'bottom';\n        this.nzScroll = { x: null, y: null };\n        this.nzPaginationType = 'default';\n        this.nzFrontPagination = true;\n        this.nzTemplateMode = false;\n        this.nzShowPagination = true;\n        this.nzLoading = false;\n        this.nzOuterBordered = false;\n        this.nzLoadingIndicator = null;\n        this.nzBordered = false;\n        this.nzSize = 'default';\n        this.nzShowSizeChanger = false;\n        this.nzHideOnSinglePage = false;\n        this.nzShowQuickJumper = false;\n        this.nzSimple = false;\n        this.nzPageSizeChange = new EventEmitter();\n        this.nzPageIndexChange = new EventEmitter();\n        this.nzQueryParams = new EventEmitter();\n        this.nzCurrentPageDataChange = new EventEmitter();\n        this.nzCustomColumnChange = new EventEmitter();\n        /** public data for ngFor tr */\n        this.data = [];\n        this.scrollX = null;\n        this.scrollY = null;\n        this.theadTemplate = null;\n        this.listOfAutoColWidth = [];\n        this.listOfManualColWidth = [];\n        this.hasFixLeft = false;\n        this.hasFixRight = false;\n        this.showPagination = true;\n        this.destroy$ = new Subject();\n        this.templateMode$ = new BehaviorSubject(false);\n        this.dir = 'ltr';\n        this.verticalScrollBarWidth = 0;\n        this.nzConfigService\n            .getConfigChangeEventForComponent(NZ_CONFIG_MODULE_NAME)\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(() => {\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnInit() {\n        const { pageIndexDistinct$, pageSizeDistinct$, listOfCurrentPageData$, total$, queryParams$, listOfCustomColumn$ } = this.nzTableDataService;\n        const { theadTemplate$, hasFixLeft$, hasFixRight$ } = this.nzTableStyleService;\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.cdr.detectChanges();\n        });\n        queryParams$.pipe(takeUntil(this.destroy$)).subscribe(this.nzQueryParams);\n        pageIndexDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageIndex => {\n            if (pageIndex !== this.nzPageIndex) {\n                this.nzPageIndex = pageIndex;\n                this.nzPageIndexChange.next(pageIndex);\n            }\n        });\n        pageSizeDistinct$.pipe(takeUntil(this.destroy$)).subscribe(pageSize => {\n            if (pageSize !== this.nzPageSize) {\n                this.nzPageSize = pageSize;\n                this.nzPageSizeChange.next(pageSize);\n            }\n        });\n        total$\n            .pipe(takeUntil(this.destroy$), filter(() => this.nzFrontPagination))\n            .subscribe(total => {\n            if (total !== this.nzTotal) {\n                this.nzTotal = total;\n                this.cdr.markForCheck();\n            }\n        });\n        listOfCurrentPageData$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n            this.data = data;\n            this.nzCurrentPageDataChange.next(data);\n            this.cdr.markForCheck();\n        });\n        listOfCustomColumn$.pipe(takeUntil(this.destroy$)).subscribe(data => {\n            this.nzCustomColumn = data;\n            this.nzCustomColumnChange.next(data);\n            this.cdr.markForCheck();\n        });\n        theadTemplate$.pipe(takeUntil(this.destroy$)).subscribe(theadTemplate => {\n            this.theadTemplate = theadTemplate;\n            this.cdr.markForCheck();\n        });\n        hasFixLeft$.pipe(takeUntil(this.destroy$)).subscribe(hasFixLeft => {\n            this.hasFixLeft = hasFixLeft;\n            this.cdr.markForCheck();\n        });\n        hasFixRight$.pipe(takeUntil(this.destroy$)).subscribe(hasFixRight => {\n            this.hasFixRight = hasFixRight;\n            this.cdr.markForCheck();\n        });\n        combineLatest([total$, this.templateMode$])\n            .pipe(map(([total, templateMode]) => total === 0 && !templateMode), takeUntil(this.destroy$))\n            .subscribe(empty => {\n            this.nzTableStyleService.setShowEmpty(empty);\n        });\n        this.verticalScrollBarWidth = measureScrollbar('vertical');\n        this.nzTableStyleService.listOfListOfThWidthPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n            this.listOfAutoColWidth = listOfWidth;\n            this.cdr.markForCheck();\n        });\n        this.nzTableStyleService.manualWidthConfigPx$.pipe(takeUntil(this.destroy$)).subscribe(listOfWidth => {\n            this.listOfManualColWidth = listOfWidth;\n            this.cdr.markForCheck();\n        });\n    }\n    ngOnChanges(changes) {\n        const { nzScroll, nzPageIndex, nzPageSize, nzFrontPagination, nzData, nzCustomColumn, nzWidthConfig, nzNoResult, nzTemplateMode } = changes;\n        if (nzPageIndex) {\n            this.nzTableDataService.updatePageIndex(this.nzPageIndex);\n        }\n        if (nzPageSize) {\n            this.nzTableDataService.updatePageSize(this.nzPageSize);\n        }\n        if (nzData) {\n            this.nzData = this.nzData || [];\n            this.nzTableDataService.updateListOfData(this.nzData);\n        }\n        if (nzCustomColumn) {\n            this.nzCustomColumn = this.nzCustomColumn || [];\n            this.nzTableDataService.updateListOfCustomColumn(this.nzCustomColumn);\n        }\n        if (nzFrontPagination) {\n            this.nzTableDataService.updateFrontPagination(this.nzFrontPagination);\n        }\n        if (nzScroll) {\n            this.setScrollOnChanges();\n        }\n        if (nzWidthConfig) {\n            this.nzTableStyleService.setTableWidthConfig(this.nzWidthConfig);\n        }\n        if (nzTemplateMode) {\n            this.templateMode$.next(this.nzTemplateMode);\n        }\n        if (nzNoResult) {\n            this.nzTableStyleService.setNoResult(this.nzNoResult);\n        }\n        this.updateShowPagination();\n    }\n    ngAfterViewInit() {\n        this.nzResizeObserver\n            .observe(this.elementRef)\n            .pipe(map(([entry]) => {\n            const { width } = entry.target.getBoundingClientRect();\n            const scrollBarWidth = this.scrollY ? this.verticalScrollBarWidth : 0;\n            return Math.floor(width - scrollBarWidth);\n        }), takeUntil(this.destroy$))\n            .subscribe(this.nzTableStyleService.hostWidth$);\n        if (this.nzTableInnerScrollComponent && this.nzTableInnerScrollComponent.cdkVirtualScrollViewport) {\n            this.cdkVirtualScrollViewport = this.nzTableInnerScrollComponent.cdkVirtualScrollViewport;\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    setScrollOnChanges() {\n        this.scrollX = (this.nzScroll && this.nzScroll.x) || null;\n        this.scrollY = (this.nzScroll && this.nzScroll.y) || null;\n        this.nzTableStyleService.setScroll(this.scrollX, this.scrollY);\n    }\n    updateShowPagination() {\n        this.showPagination =\n            (this.nzHideOnSinglePage && this.nzData.length > this.nzPageSize) ||\n                (this.nzData.length > 0 && !this.nzHideOnSinglePage) ||\n                (!this.nzFrontPagination && this.nzTotal > this.nzPageSize);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableComponent, deps: [{ token: i0.ElementRef }, { token: i1$2.NzResizeObserver }, { token: i1.NzConfigService }, { token: i0.ChangeDetectorRef }, { token: NzTableStyleService }, { token: NzTableDataService }, { token: i5$1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTableComponent, isStandalone: true, selector: \"nz-table\", inputs: { nzTableLayout: \"nzTableLayout\", nzShowTotal: \"nzShowTotal\", nzItemRender: \"nzItemRender\", nzTitle: \"nzTitle\", nzFooter: \"nzFooter\", nzNoResult: \"nzNoResult\", nzPageSizeOptions: \"nzPageSizeOptions\", nzVirtualItemSize: \"nzVirtualItemSize\", nzVirtualMaxBufferPx: \"nzVirtualMaxBufferPx\", nzVirtualMinBufferPx: \"nzVirtualMinBufferPx\", nzVirtualForTrackBy: \"nzVirtualForTrackBy\", nzLoadingDelay: \"nzLoadingDelay\", nzPageIndex: \"nzPageIndex\", nzPageSize: \"nzPageSize\", nzTotal: \"nzTotal\", nzWidthConfig: \"nzWidthConfig\", nzData: \"nzData\", nzCustomColumn: \"nzCustomColumn\", nzPaginationPosition: \"nzPaginationPosition\", nzScroll: \"nzScroll\", nzPaginationType: \"nzPaginationType\", nzFrontPagination: \"nzFrontPagination\", nzTemplateMode: \"nzTemplateMode\", nzShowPagination: \"nzShowPagination\", nzLoading: \"nzLoading\", nzOuterBordered: \"nzOuterBordered\", nzLoadingIndicator: \"nzLoadingIndicator\", nzBordered: \"nzBordered\", nzSize: \"nzSize\", nzShowSizeChanger: \"nzShowSizeChanger\", nzHideOnSinglePage: \"nzHideOnSinglePage\", nzShowQuickJumper: \"nzShowQuickJumper\", nzSimple: \"nzSimple\" }, outputs: { nzPageSizeChange: \"nzPageSizeChange\", nzPageIndexChange: \"nzPageIndexChange\", nzQueryParams: \"nzQueryParams\", nzCurrentPageDataChange: \"nzCurrentPageDataChange\", nzCustomColumnChange: \"nzCustomColumnChange\" }, host: { properties: { \"class.ant-table-wrapper-rtl\": \"dir === \\\"rtl\\\"\", \"class.ant-table-custom-column\": \"nzCustomColumn.length\" }, classAttribute: \"ant-table-wrapper\" }, providers: [NzTableStyleService, NzTableDataService], queries: [{ propertyName: \"nzVirtualScrollDirective\", first: true, predicate: NzTableVirtualScrollDirective, descendants: true }], viewQueries: [{ propertyName: \"nzTableInnerScrollComponent\", first: true, predicate: NzTableInnerScrollComponent, descendants: true }], exportAs: [\"nzTable\"], usesOnChanges: true, ngImport: i0, template: `\n    <nz-spin [nzDelay]=\"nzLoadingDelay\" [nzSpinning]=\"nzLoading\" [nzIndicator]=\"nzLoadingIndicator\">\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'top'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n      <div\n        #tableMainElement\n        class=\"ant-table\"\n        [class.ant-table-rtl]=\"dir === 'rtl'\"\n        [class.ant-table-fixed-header]=\"nzData.length && scrollY\"\n        [class.ant-table-fixed-column]=\"scrollX\"\n        [class.ant-table-has-fix-left]=\"hasFixLeft\"\n        [class.ant-table-has-fix-right]=\"hasFixRight\"\n        [class.ant-table-bordered]=\"nzBordered\"\n        [class.nz-table-out-bordered]=\"nzOuterBordered && !nzBordered\"\n        [class.ant-table-middle]=\"nzSize === 'middle'\"\n        [class.ant-table-small]=\"nzSize === 'small'\"\n      >\n        <nz-table-title-footer [title]=\"nzTitle\" *ngIf=\"nzTitle\"></nz-table-title-footer>\n        <nz-table-inner-scroll\n          *ngIf=\"scrollY || scrollX; else defaultTemplate\"\n          [data]=\"data\"\n          [scrollX]=\"scrollX\"\n          [scrollY]=\"scrollY\"\n          [contentTemplate]=\"contentTemplate\"\n          [listOfColWidth]=\"listOfAutoColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [verticalScrollBarWidth]=\"verticalScrollBarWidth\"\n          [virtualTemplate]=\"nzVirtualScrollDirective ? nzVirtualScrollDirective.templateRef : null\"\n          [virtualItemSize]=\"nzVirtualItemSize\"\n          [virtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [virtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n          [tableMainElement]=\"tableMainElement\"\n          [virtualForTrackBy]=\"nzVirtualForTrackBy\"\n        ></nz-table-inner-scroll>\n        <ng-template #defaultTemplate>\n          <nz-table-inner-default\n            [tableLayout]=\"nzTableLayout\"\n            [listOfColWidth]=\"listOfManualColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [contentTemplate]=\"contentTemplate\"\n          ></nz-table-inner-default>\n        </ng-template>\n        <nz-table-title-footer [footer]=\"nzFooter\" *ngIf=\"nzFooter\"></nz-table-title-footer>\n      </div>\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'bottom'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n    </nz-spin>\n    <ng-template #paginationTemplate>\n      <nz-pagination\n        *ngIf=\"nzShowPagination && data.length\"\n        [hidden]=\"!showPagination\"\n        class=\"ant-table-pagination ant-table-pagination-right\"\n        [nzShowSizeChanger]=\"nzShowSizeChanger\"\n        [nzPageSizeOptions]=\"nzPageSizeOptions\"\n        [nzItemRender]=\"nzItemRender!\"\n        [nzShowQuickJumper]=\"nzShowQuickJumper\"\n        [nzHideOnSinglePage]=\"nzHideOnSinglePage\"\n        [nzShowTotal]=\"nzShowTotal\"\n        [nzSize]=\"nzPaginationType === 'small' ? 'small' : nzSize === 'default' ? 'default' : 'small'\"\n        [nzPageSize]=\"nzPageSize\"\n        [nzTotal]=\"nzTotal\"\n        [nzSimple]=\"nzSimple\"\n        [nzPageIndex]=\"nzPageIndex\"\n        (nzPageSizeChange)=\"onPageSizeChange($event)\"\n        (nzPageIndexChange)=\"onPageIndexChange($event)\"\n      ></nz-pagination>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `, isInline: true, dependencies: [{ kind: \"component\", type: NzSpinComponent, selector: \"nz-spin\", inputs: [\"nzIndicator\", \"nzSize\", \"nzTip\", \"nzDelay\", \"nzSimple\", \"nzSpinning\"], exportAs: [\"nzSpin\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"component\", type: NzTableTitleFooterComponent, selector: \"nz-table-title-footer\", inputs: [\"title\", \"footer\"] }, { kind: \"component\", type: NzTableInnerScrollComponent, selector: \"nz-table-inner-scroll\", inputs: [\"data\", \"scrollX\", \"scrollY\", \"contentTemplate\", \"widthConfig\", \"listOfColWidth\", \"theadTemplate\", \"virtualTemplate\", \"virtualItemSize\", \"virtualMaxBufferPx\", \"virtualMinBufferPx\", \"tableMainElement\", \"virtualForTrackBy\", \"verticalScrollBarWidth\"] }, { kind: \"component\", type: NzTableInnerDefaultComponent, selector: \"nz-table-inner-default\", inputs: [\"tableLayout\", \"listOfColWidth\", \"theadTemplate\", \"contentTemplate\"] }, { kind: \"ngmodule\", type: NzPaginationModule }, { kind: \"component\", type: i6$1.NzPaginationComponent, selector: \"nz-pagination\", inputs: [\"nzShowTotal\", \"nzItemRender\", \"nzSize\", \"nzPageSizeOptions\", \"nzShowSizeChanger\", \"nzShowQuickJumper\", \"nzSimple\", \"nzDisabled\", \"nzResponsive\", \"nzHideOnSinglePage\", \"nzTotal\", \"nzPageIndex\", \"nzPageSize\"], outputs: [\"nzPageSizeChange\", \"nzPageIndexChange\"], exportAs: [\"nzPagination\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputBoolean()\n], NzTableComponent.prototype, \"nzFrontPagination\", void 0);\n__decorate([\n    InputBoolean()\n], NzTableComponent.prototype, \"nzTemplateMode\", void 0);\n__decorate([\n    InputBoolean()\n], NzTableComponent.prototype, \"nzShowPagination\", void 0);\n__decorate([\n    InputBoolean()\n], NzTableComponent.prototype, \"nzLoading\", void 0);\n__decorate([\n    InputBoolean()\n], NzTableComponent.prototype, \"nzOuterBordered\", void 0);\n__decorate([\n    WithConfig()\n], NzTableComponent.prototype, \"nzLoadingIndicator\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzTableComponent.prototype, \"nzBordered\", void 0);\n__decorate([\n    WithConfig()\n], NzTableComponent.prototype, \"nzSize\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzTableComponent.prototype, \"nzShowSizeChanger\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzTableComponent.prototype, \"nzHideOnSinglePage\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzTableComponent.prototype, \"nzShowQuickJumper\", void 0);\n__decorate([\n    WithConfig(),\n    InputBoolean()\n], NzTableComponent.prototype, \"nzSimple\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'nz-table',\n                    exportAs: 'nzTable',\n                    providers: [NzTableStyleService, NzTableDataService],\n                    preserveWhitespaces: false,\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <nz-spin [nzDelay]=\"nzLoadingDelay\" [nzSpinning]=\"nzLoading\" [nzIndicator]=\"nzLoadingIndicator\">\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'top'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n      <div\n        #tableMainElement\n        class=\"ant-table\"\n        [class.ant-table-rtl]=\"dir === 'rtl'\"\n        [class.ant-table-fixed-header]=\"nzData.length && scrollY\"\n        [class.ant-table-fixed-column]=\"scrollX\"\n        [class.ant-table-has-fix-left]=\"hasFixLeft\"\n        [class.ant-table-has-fix-right]=\"hasFixRight\"\n        [class.ant-table-bordered]=\"nzBordered\"\n        [class.nz-table-out-bordered]=\"nzOuterBordered && !nzBordered\"\n        [class.ant-table-middle]=\"nzSize === 'middle'\"\n        [class.ant-table-small]=\"nzSize === 'small'\"\n      >\n        <nz-table-title-footer [title]=\"nzTitle\" *ngIf=\"nzTitle\"></nz-table-title-footer>\n        <nz-table-inner-scroll\n          *ngIf=\"scrollY || scrollX; else defaultTemplate\"\n          [data]=\"data\"\n          [scrollX]=\"scrollX\"\n          [scrollY]=\"scrollY\"\n          [contentTemplate]=\"contentTemplate\"\n          [listOfColWidth]=\"listOfAutoColWidth\"\n          [theadTemplate]=\"theadTemplate\"\n          [verticalScrollBarWidth]=\"verticalScrollBarWidth\"\n          [virtualTemplate]=\"nzVirtualScrollDirective ? nzVirtualScrollDirective.templateRef : null\"\n          [virtualItemSize]=\"nzVirtualItemSize\"\n          [virtualMaxBufferPx]=\"nzVirtualMaxBufferPx\"\n          [virtualMinBufferPx]=\"nzVirtualMinBufferPx\"\n          [tableMainElement]=\"tableMainElement\"\n          [virtualForTrackBy]=\"nzVirtualForTrackBy\"\n        ></nz-table-inner-scroll>\n        <ng-template #defaultTemplate>\n          <nz-table-inner-default\n            [tableLayout]=\"nzTableLayout\"\n            [listOfColWidth]=\"listOfManualColWidth\"\n            [theadTemplate]=\"theadTemplate\"\n            [contentTemplate]=\"contentTemplate\"\n          ></nz-table-inner-default>\n        </ng-template>\n        <nz-table-title-footer [footer]=\"nzFooter\" *ngIf=\"nzFooter\"></nz-table-title-footer>\n      </div>\n      <ng-container *ngIf=\"nzPaginationPosition === 'both' || nzPaginationPosition === 'bottom'\">\n        <ng-template [ngTemplateOutlet]=\"paginationTemplate\"></ng-template>\n      </ng-container>\n    </nz-spin>\n    <ng-template #paginationTemplate>\n      <nz-pagination\n        *ngIf=\"nzShowPagination && data.length\"\n        [hidden]=\"!showPagination\"\n        class=\"ant-table-pagination ant-table-pagination-right\"\n        [nzShowSizeChanger]=\"nzShowSizeChanger\"\n        [nzPageSizeOptions]=\"nzPageSizeOptions\"\n        [nzItemRender]=\"nzItemRender!\"\n        [nzShowQuickJumper]=\"nzShowQuickJumper\"\n        [nzHideOnSinglePage]=\"nzHideOnSinglePage\"\n        [nzShowTotal]=\"nzShowTotal\"\n        [nzSize]=\"nzPaginationType === 'small' ? 'small' : nzSize === 'default' ? 'default' : 'small'\"\n        [nzPageSize]=\"nzPageSize\"\n        [nzTotal]=\"nzTotal\"\n        [nzSimple]=\"nzSimple\"\n        [nzPageIndex]=\"nzPageIndex\"\n        (nzPageSizeChange)=\"onPageSizeChange($event)\"\n        (nzPageIndexChange)=\"onPageIndexChange($event)\"\n      ></nz-pagination>\n    </ng-template>\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n  `,\n                    host: {\n                        class: 'ant-table-wrapper',\n                        '[class.ant-table-wrapper-rtl]': 'dir === \"rtl\"',\n                        '[class.ant-table-custom-column]': `nzCustomColumn.length`\n                    },\n                    imports: [\n                        NzSpinComponent,\n                        NgIf,\n                        NgTemplateOutlet,\n                        NzTableTitleFooterComponent,\n                        NzTableInnerScrollComponent,\n                        NzTableInnerDefaultComponent,\n                        NzPaginationModule\n                    ],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1$2.NzResizeObserver }, { type: i1.NzConfigService }, { type: i0.ChangeDetectorRef }, { type: NzTableStyleService }, { type: NzTableDataService }, { type: i5$1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { nzTableLayout: [{\n                type: Input\n            }], nzShowTotal: [{\n                type: Input\n            }], nzItemRender: [{\n                type: Input\n            }], nzTitle: [{\n                type: Input\n            }], nzFooter: [{\n                type: Input\n            }], nzNoResult: [{\n                type: Input\n            }], nzPageSizeOptions: [{\n                type: Input\n            }], nzVirtualItemSize: [{\n                type: Input\n            }], nzVirtualMaxBufferPx: [{\n                type: Input\n            }], nzVirtualMinBufferPx: [{\n                type: Input\n            }], nzVirtualForTrackBy: [{\n                type: Input\n            }], nzLoadingDelay: [{\n                type: Input\n            }], nzPageIndex: [{\n                type: Input\n            }], nzPageSize: [{\n                type: Input\n            }], nzTotal: [{\n                type: Input\n            }], nzWidthConfig: [{\n                type: Input\n            }], nzData: [{\n                type: Input\n            }], nzCustomColumn: [{\n                type: Input\n            }], nzPaginationPosition: [{\n                type: Input\n            }], nzScroll: [{\n                type: Input\n            }], nzPaginationType: [{\n                type: Input\n            }], nzFrontPagination: [{\n                type: Input\n            }], nzTemplateMode: [{\n                type: Input\n            }], nzShowPagination: [{\n                type: Input\n            }], nzLoading: [{\n                type: Input\n            }], nzOuterBordered: [{\n                type: Input\n            }], nzLoadingIndicator: [{\n                type: Input\n            }], nzBordered: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzShowSizeChanger: [{\n                type: Input\n            }], nzHideOnSinglePage: [{\n                type: Input\n            }], nzShowQuickJumper: [{\n                type: Input\n            }], nzSimple: [{\n                type: Input\n            }], nzPageSizeChange: [{\n                type: Output\n            }], nzPageIndexChange: [{\n                type: Output\n            }], nzQueryParams: [{\n                type: Output\n            }], nzCurrentPageDataChange: [{\n                type: Output\n            }], nzCustomColumnChange: [{\n                type: Output\n            }], nzVirtualScrollDirective: [{\n                type: ContentChild,\n                args: [NzTableVirtualScrollDirective, { static: false }]\n            }], nzTableInnerScrollComponent: [{\n                type: ViewChild,\n                args: [NzTableInnerScrollComponent]\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrDirective {\n    constructor(nzTableStyleService) {\n        this.nzTableStyleService = nzTableStyleService;\n        this.destroy$ = new Subject();\n        this.listOfFixedColumns$ = new ReplaySubject(1);\n        this.listOfColumns$ = new ReplaySubject(1);\n        this.listOfFixedColumnsChanges$ = this.listOfFixedColumns$.pipe(switchMap(list => merge(...[this.listOfFixedColumns$, ...list.map((c) => c.changes$)]).pipe(mergeMap(() => this.listOfFixedColumns$))), takeUntil(this.destroy$));\n        this.listOfFixedLeftColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzLeft !== false)));\n        this.listOfFixedRightColumnChanges$ = this.listOfFixedColumnsChanges$.pipe(map(list => list.filter(item => item.nzRight !== false)));\n        this.listOfColumnsChanges$ = this.listOfColumns$.pipe(switchMap(list => merge(...[this.listOfColumns$, ...list.map((c) => c.changes$)]).pipe(mergeMap(() => this.listOfColumns$))), takeUntil(this.destroy$));\n        this.isInsideTable = false;\n        this.isInsideTable = !!nzTableStyleService;\n    }\n    ngAfterContentInit() {\n        if (this.nzTableStyleService) {\n            this.listOfCellFixedDirective.changes\n                .pipe(startWith(this.listOfCellFixedDirective), takeUntil(this.destroy$))\n                .subscribe(this.listOfFixedColumns$);\n            this.listOfNzThDirective.changes\n                .pipe(startWith(this.listOfNzThDirective), takeUntil(this.destroy$))\n                .subscribe(this.listOfColumns$);\n            /** set last left and first right **/\n            this.listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeft => {\n                listOfFixedLeft.forEach(cell => cell.setIsLastLeft(cell === listOfFixedLeft[listOfFixedLeft.length - 1]));\n            });\n            this.listOfFixedRightColumnChanges$.subscribe(listOfFixedRight => {\n                listOfFixedRight.forEach(cell => cell.setIsFirstRight(cell === listOfFixedRight[0]));\n            });\n            /** calculate fixed nzLeft and nzRight **/\n            combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedLeftColumnChanges$])\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(([listOfAutoWidth, listOfLeftCell]) => {\n                listOfLeftCell.forEach((cell, index) => {\n                    if (cell.isAutoLeft) {\n                        const currentArray = listOfLeftCell.slice(0, index);\n                        const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n                        const width = listOfAutoWidth.slice(0, count).reduce((pre, cur) => pre + cur, 0);\n                        cell.setAutoLeftWidth(`${width}px`);\n                    }\n                });\n            });\n            combineLatest([this.nzTableStyleService.listOfListOfThWidth$, this.listOfFixedRightColumnChanges$])\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(([listOfAutoWidth, listOfRightCell]) => {\n                listOfRightCell.forEach((_, index) => {\n                    const cell = listOfRightCell[listOfRightCell.length - index - 1];\n                    if (cell.isAutoRight) {\n                        const currentArray = listOfRightCell.slice(listOfRightCell.length - index, listOfRightCell.length);\n                        const count = currentArray.reduce((pre, cur) => pre + (cur.colspan || cur.colSpan || 1), 0);\n                        const width = listOfAutoWidth\n                            .slice(listOfAutoWidth.length - count, listOfAutoWidth.length)\n                            .reduce((pre, cur) => pre + cur, 0);\n                        cell.setAutoRightWidth(`${width}px`);\n                    }\n                });\n            });\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrDirective, deps: [{ token: NzTableStyleService, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTrDirective, isStandalone: true, selector: \"tr:not([mat-row]):not([mat-header-row]):not([nz-table-measure-row]):not([nzExpand]):not([nz-table-fixed-row])\", host: { properties: { \"class.ant-table-row\": \"isInsideTable\" } }, queries: [{ propertyName: \"listOfNzThDirective\", predicate: NzThMeasureDirective }, { propertyName: \"listOfCellFixedDirective\", predicate: NzCellFixedDirective }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'tr:not([mat-row]):not([mat-header-row]):not([nz-table-measure-row]):not([nzExpand]):not([nz-table-fixed-row])',\n                    host: {\n                        '[class.ant-table-row]': 'isInsideTable'\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: NzTableStyleService, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { listOfNzThDirective: [{\n                type: ContentChildren,\n                args: [NzThMeasureDirective]\n            }], listOfCellFixedDirective: [{\n                type: ContentChildren,\n                args: [NzCellFixedDirective]\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/* eslint-disable @angular-eslint/component-selector */\nclass NzTheadComponent {\n    constructor(elementRef, renderer, nzTableStyleService, nzTableDataService) {\n        this.elementRef = elementRef;\n        this.renderer = renderer;\n        this.nzTableStyleService = nzTableStyleService;\n        this.nzTableDataService = nzTableDataService;\n        this.destroy$ = new Subject();\n        this.isInsideTable = false;\n        this.nzSortOrderChange = new EventEmitter();\n        this.isInsideTable = !!this.nzTableStyleService;\n    }\n    ngOnInit() {\n        if (this.nzTableStyleService) {\n            this.nzTableStyleService.setTheadTemplate(this.templateRef);\n        }\n    }\n    ngAfterContentInit() {\n        if (this.nzTableStyleService) {\n            const firstTableRow$ = this.listOfNzTrDirective.changes.pipe(startWith(this.listOfNzTrDirective), map(item => item && item.first));\n            const listOfColumnsChanges$ = firstTableRow$.pipe(switchMap(firstTableRow => (firstTableRow ? firstTableRow.listOfColumnsChanges$ : EMPTY)), takeUntil(this.destroy$));\n            listOfColumnsChanges$.subscribe(data => this.nzTableStyleService.setListOfTh(data));\n            /** TODO: need reset the measure row when scrollX change **/\n            this.nzTableStyleService.enableAutoMeasure$\n                .pipe(switchMap(enable => (enable ? listOfColumnsChanges$ : of([]))))\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(data => this.nzTableStyleService.setListOfMeasureColumn(data));\n            const listOfFixedLeftColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => (firstTr ? firstTr.listOfFixedLeftColumnChanges$ : EMPTY)), takeUntil(this.destroy$));\n            const listOfFixedRightColumnChanges$ = firstTableRow$.pipe(switchMap(firstTr => (firstTr ? firstTr.listOfFixedRightColumnChanges$ : EMPTY)), takeUntil(this.destroy$));\n            listOfFixedLeftColumnChanges$.subscribe(listOfFixedLeftColumn => {\n                this.nzTableStyleService.setHasFixLeft(listOfFixedLeftColumn.length !== 0);\n            });\n            listOfFixedRightColumnChanges$.subscribe(listOfFixedRightColumn => {\n                this.nzTableStyleService.setHasFixRight(listOfFixedRightColumn.length !== 0);\n            });\n        }\n        if (this.nzTableDataService) {\n            const listOfColumn$ = this.listOfNzThAddOnComponent.changes.pipe(startWith(this.listOfNzThAddOnComponent));\n            const manualSort$ = listOfColumn$.pipe(switchMap(() => merge(...this.listOfNzThAddOnComponent.map(th => th.manualClickOrder$))), takeUntil(this.destroy$));\n            manualSort$.subscribe((data) => {\n                const emitValue = { key: data.nzColumnKey, value: data.sortOrder };\n                this.nzSortOrderChange.emit(emitValue);\n                if (data.nzSortFn && data.nzSortPriority === false) {\n                    this.listOfNzThAddOnComponent.filter(th => th !== data).forEach(th => th.clearSortOrder());\n                }\n            });\n            const listOfCalcOperator$ = listOfColumn$.pipe(switchMap(list => merge(...[listOfColumn$, ...list.map((c) => c.calcOperatorChange$)]).pipe(mergeMap(() => listOfColumn$))), map(list => list\n                .filter(item => !!item.nzSortFn || !!item.nzFilterFn)\n                .map(item => {\n                const { nzSortFn, sortOrder, nzFilterFn, nzFilterValue, nzSortPriority, nzColumnKey } = item;\n                return {\n                    key: nzColumnKey,\n                    sortFn: nzSortFn,\n                    sortPriority: nzSortPriority,\n                    sortOrder: sortOrder,\n                    filterFn: nzFilterFn,\n                    filterValue: nzFilterValue\n                };\n            })), \n            // TODO: after checked error here\n            delay(0), takeUntil(this.destroy$));\n            listOfCalcOperator$.subscribe(list => {\n                this.nzTableDataService.listOfCalcOperator$.next(list);\n            });\n        }\n    }\n    ngAfterViewInit() {\n        if (this.nzTableStyleService) {\n            this.renderer.removeChild(this.renderer.parentNode(this.elementRef.nativeElement), this.elementRef.nativeElement);\n        }\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTheadComponent, deps: [{ token: i0.ElementRef }, { token: i0.Renderer2 }, { token: NzTableStyleService, optional: true }, { token: NzTableDataService, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTheadComponent, isStandalone: true, selector: \"thead:not(.ant-table-thead)\", outputs: { nzSortOrderChange: \"nzSortOrderChange\" }, queries: [{ propertyName: \"listOfNzTrDirective\", predicate: NzTrDirective, descendants: true }, { propertyName: \"listOfNzThAddOnComponent\", predicate: NzThAddOnComponent, descendants: true }], viewQueries: [{ propertyName: \"templateRef\", first: true, predicate: [\"contentTemplate\"], descendants: true, static: true }], ngImport: i0, template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    <ng-container *ngIf=\"!isInsideTable\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </ng-container>\n  `, isInline: true, dependencies: [{ kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTheadComponent, decorators: [{\n            type: Component,\n            args: [{\n                    selector: 'thead:not(.ant-table-thead)',\n                    changeDetection: ChangeDetectionStrategy.OnPush,\n                    encapsulation: ViewEncapsulation.None,\n                    template: `\n    <ng-template #contentTemplate>\n      <ng-content></ng-content>\n    </ng-template>\n    <ng-container *ngIf=\"!isInsideTable\">\n      <ng-template [ngTemplateOutlet]=\"contentTemplate\"></ng-template>\n    </ng-container>\n  `,\n                    imports: [NgIf, NgTemplateOutlet],\n                    standalone: true\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.Renderer2 }, { type: NzTableStyleService, decorators: [{\n                    type: Optional\n                }] }, { type: NzTableDataService, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { templateRef: [{\n                type: ViewChild,\n                args: ['contentTemplate', { static: true }]\n            }], listOfNzTrDirective: [{\n                type: ContentChildren,\n                args: [NzTrDirective, { descendants: true }]\n            }], listOfNzThAddOnComponent: [{\n                type: ContentChildren,\n                args: [NzThAddOnComponent, { descendants: true }]\n            }], nzSortOrderChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTrExpandDirective {\n    constructor() {\n        this.nzExpand = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrExpandDirective, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzTrExpandDirective, isStandalone: true, selector: \"tr[nzExpand]\", inputs: { nzExpand: \"nzExpand\" }, host: { properties: { \"hidden\": \"!nzExpand\" }, classAttribute: \"ant-table-expanded-row\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTrExpandDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'tr[nzExpand]',\n                    host: {\n                        class: 'ant-table-expanded-row',\n                        '[hidden]': `!nzExpand`\n                    },\n                    standalone: true\n                }]\n        }], ctorParameters: () => [], propDecorators: { nzExpand: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzTableModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableModule, imports: [NzTableComponent,\n            NzThAddOnComponent,\n            NzTableCellDirective,\n            NzThMeasureDirective,\n            NzTdAddOnComponent,\n            NzTheadComponent,\n            NzTbodyComponent,\n            NzTrDirective,\n            NzTrExpandDirective,\n            NzTableVirtualScrollDirective,\n            NzCellFixedDirective,\n            NzCustomColumnDirective,\n            NzTableContentComponent,\n            NzTableTitleFooterComponent,\n            NzTableInnerDefaultComponent,\n            NzTableInnerScrollComponent,\n            NzTrMeasureComponent,\n            NzRowIndentDirective,\n            NzRowExpandButtonDirective,\n            NzCellBreakWordDirective,\n            NzCellAlignDirective,\n            NzTableSortersComponent,\n            NzTableFilterComponent,\n            NzTableSelectionComponent,\n            NzCellEllipsisDirective,\n            NzFilterTriggerComponent,\n            NzTableFixedRowComponent,\n            NzThSelectionComponent], exports: [NzTableComponent,\n            NzThAddOnComponent,\n            NzTableCellDirective,\n            NzThMeasureDirective,\n            NzTdAddOnComponent,\n            NzTheadComponent,\n            NzTbodyComponent,\n            NzTrDirective,\n            NzTableVirtualScrollDirective,\n            NzCellFixedDirective,\n            NzCustomColumnDirective,\n            NzFilterTriggerComponent,\n            NzTrExpandDirective,\n            NzCellBreakWordDirective,\n            NzCellAlignDirective,\n            NzCellEllipsisDirective,\n            NzTableFixedRowComponent,\n            NzThSelectionComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableModule, imports: [NzTableComponent,\n            NzThAddOnComponent,\n            NzTdAddOnComponent,\n            NzTbodyComponent,\n            NzTableTitleFooterComponent,\n            NzTableInnerScrollComponent,\n            NzTableSortersComponent,\n            NzTableFilterComponent,\n            NzTableSelectionComponent,\n            NzFilterTriggerComponent,\n            NzThSelectionComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzTableModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        NzTableComponent,\n                        NzThAddOnComponent,\n                        NzTableCellDirective,\n                        NzThMeasureDirective,\n                        NzTdAddOnComponent,\n                        NzTheadComponent,\n                        NzTbodyComponent,\n                        NzTrDirective,\n                        NzTrExpandDirective,\n                        NzTableVirtualScrollDirective,\n                        NzCellFixedDirective,\n                        NzCustomColumnDirective,\n                        NzTableContentComponent,\n                        NzTableTitleFooterComponent,\n                        NzTableInnerDefaultComponent,\n                        NzTableInnerScrollComponent,\n                        NzTrMeasureComponent,\n                        NzRowIndentDirective,\n                        NzRowExpandButtonDirective,\n                        NzCellBreakWordDirective,\n                        NzCellAlignDirective,\n                        NzTableSortersComponent,\n                        NzTableFilterComponent,\n                        NzTableSelectionComponent,\n                        NzCellEllipsisDirective,\n                        NzFilterTriggerComponent,\n                        NzTableFixedRowComponent,\n                        NzThSelectionComponent\n                    ],\n                    exports: [\n                        NzTableComponent,\n                        NzThAddOnComponent,\n                        NzTableCellDirective,\n                        NzThMeasureDirective,\n                        NzTdAddOnComponent,\n                        NzTheadComponent,\n                        NzTbodyComponent,\n                        NzTrDirective,\n                        NzTableVirtualScrollDirective,\n                        NzCellFixedDirective,\n                        NzCustomColumnDirective,\n                        NzFilterTriggerComponent,\n                        NzTrExpandDirective,\n                        NzCellBreakWordDirective,\n                        NzCellAlignDirective,\n                        NzCellEllipsisDirective,\n                        NzTableFixedRowComponent,\n                        NzThSelectionComponent\n                    ]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzCellAlignDirective, NzCellBreakWordDirective, NzCellEllipsisDirective, NzCellFixedDirective, NzCustomColumnDirective, NzFilterTriggerComponent, NzRowExpandButtonDirective, NzRowIndentDirective, NzTableCellDirective, NzTableComponent, NzTableContentComponent, NzTableDataService, NzTableFilterComponent, NzTableFixedRowComponent, NzTableInnerDefaultComponent, NzTableInnerScrollComponent, NzTableModule, NzTableSelectionComponent, NzTableSortersComponent, NzTableStyleService, NzTableTitleFooterComponent, NzTableVirtualScrollDirective, NzTbodyComponent, NzTdAddOnComponent, NzThAddOnComponent, NzThMeasureDirective, NzThSelectionComponent, NzTheadComponent, NzTrDirective, NzTrExpandDirective, NzTrMeasureComponent };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,YAAY,EAAEC,UAAU,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,YAAY,EAAEC,YAAY,EAAEC,eAAe,EAAEC,QAAQ,QAAQ,eAAe;AACzO,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,OAAO,EAAEC,aAAa,EAAEC,eAAe,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AAC1G,SAASC,SAAS,EAAEC,GAAG,EAAEC,oBAAoB,EAAEC,YAAY,EAAEC,IAAI,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,gBAAgB;AACxI,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,UAAU,QAAQ,2BAA2B;AACtD,OAAO,KAAKC,EAAE,MAAM,6BAA6B;AACjD,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,YAAY,EAAEC,WAAW,EAAEC,KAAK,EAAEC,gBAAgB,QAAQ,yBAAyB;AAC5F,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,mBAAmB,EAAEC,gBAAgB,QAAQ,wBAAwB;AAC9E,SAASC,gBAAgB,EAAEC,IAAI,EAAEC,OAAO,EAAEC,SAAS,EAAEC,OAAO,QAAQ,iBAAiB;AACrF,OAAO,KAAKC,EAAE,MAAM,gBAAgB;AACpC,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,gBAAgB,QAAQ,qBAAqB;AACtD,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,OAAO,KAAKC,IAAI,MAAM,wBAAwB;AAC9C,SAASC,wBAAwB,EAAEC,eAAe,QAAQ,wBAAwB;AAClF,OAAO,KAAKC,IAAI,MAAM,qBAAqB;AAC3C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,OAAO,KAAKC,IAAI,MAAM,mCAAmC;AACzD,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,OAAO,KAAKC,IAAI,MAAM,0BAA0B;AAChD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,eAAe,QAAQ,oBAAoB;AACpD,OAAO,KAAKC,IAAI,MAAM,2BAA2B;AACjD,SAASC,cAAc,QAAQ,2BAA2B;AAC1D,OAAO,KAAKC,IAAI,MAAM,mBAAmB;AAAC,MAAAC,GAAA;AAAA,SAAAC,8CAAAC,EAAA,EAAAC,GAAA;AAAA,SAAAC,4DAAAF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAG,GAAA,GAoC0DnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,eA0LA,CAAC;IA1LHrF,EAAE,CAAAsF,UAAA,2BAAAC,2FAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAM,IAAA,GAAFzF,EAAE,CAAA0F,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA0LTD,MAAA,CAAAE,KAAA,CAAAL,IAAO,CAAC;IAAA,EAAC;IA1LFzF,EAAE,CAAA+F,YAAA,CA0LQ,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAS,IAAA,GA1LXzF,EAAE,CAAA0F,aAAA,GAAAC,SAAA;IAAF3F,EAAE,CAAAgG,UAAA,YAAAP,IAAA,CAAAQ,OA0L5B,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAAlB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAmB,GAAA,GA1LyBnG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,eA2LE,CAAC;IA3LLrF,EAAE,CAAAsF,UAAA,2BAAAc,2FAAA;MAAFpG,EAAE,CAAAwF,aAAA,CAAAW,GAAA;MAAA,MAAAV,IAAA,GAAFzF,EAAE,CAAA0F,aAAA,GAAAC,SAAA;MAAA,MAAAC,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA2LPD,MAAA,CAAAE,KAAA,CAAAL,IAAO,CAAC;IAAA,EAAC;IA3LJzF,EAAE,CAAA+F,YAAA,CA2LU,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAS,IAAA,GA3LbzF,EAAE,CAAA0F,aAAA,GAAAC,SAAA;IAAF3F,EAAE,CAAAgG,UAAA,YAAAP,IAAA,CAAAQ,OA2L1B,CAAC;EAAA;AAAA;AAAA,SAAAI,oDAAArB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsB,GAAA,GA3LuBtG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,YAyL1F,CAAC;IAzLuFrF,EAAE,CAAAsF,UAAA,mBAAAiB,wEAAA;MAAA,MAAAd,IAAA,GAAFzF,EAAE,CAAAwF,aAAA,CAAAc,GAAA,EAAAX,SAAA;MAAA,MAAAC,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAwL/ED,MAAA,CAAAE,KAAA,CAAAL,IAAO,CAAC;IAAA,EAAC;IAxLoEzF,EAAE,CAAAwG,UAAA,IAAAtB,2DAAA,mBA0LA,CAAC,IAAAgB,2DAAA,mBACC,CAAC;IA3LLlG,EAAE,CAAAqF,cAAA,UA4LnF,CAAC;IA5LgFrF,EAAE,CAAAyG,MAAA,EA4LvE,CAAC;IA5LoEzG,EAAE,CAAA+F,YAAA,CA4LhE,CAAC,CACvB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAS,IAAA,GAAAR,GAAA,CAAAU,SAAA;IAAA,MAAAC,MAAA,GA7LmF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,eAAAP,IAAA,CAAAQ,OAsLjE,CAAC;IAtL8DjG,EAAE,CAAA0G,SAAA,CA0LnD,CAAC;IA1LgD1G,EAAE,CAAAgG,UAAA,UAAAJ,MAAA,CAAAe,cA0LnD,CAAC;IA1LgD3G,EAAE,CAAA0G,SAAA,CA2LjD,CAAC;IA3L8C1G,EAAE,CAAAgG,UAAA,SAAAJ,MAAA,CAAAe,cA2LjD,CAAC;IA3L8C3G,EAAE,CAAA0G,SAAA,EA4LvE,CAAC;IA5LoE1G,EAAE,CAAA4G,iBAAA,CAAAnB,IAAA,CAAAoB,IA4LvE,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAA9B,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GA5LoE/G,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAgH,uBAAA,EAwK3C,CAAC;IAxKwChH,EAAE,CAAAqF,cAAA,0BA8KhG,CAAC;IA9K6FrF,EAAE,CAAAsF,UAAA,6BAAA2B,4FAAAC,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA6K3ED,MAAA,CAAAuB,eAAA,CAAAD,MAAsB,CAAC;IAAA,EAAC;IA7KiDlH,EAAE,CAAAoH,SAAA,aA+K3C,CAAC;IA/KwCpH,EAAE,CAAA+F,YAAA,CAgL7E,CAAC;IAhL0E/F,EAAE,CAAAqF,cAAA,+BAiLlD,CAAC,YACP,CAAC,WAC1B,CAAC;IAnL8ErF,EAAE,CAAAwG,UAAA,IAAAH,mDAAA,eAyL1F,CAAC;IAzLuFrG,EAAE,CAAA+F,YAAA,CA8LxF,CAAC;IA9LqF/F,EAAE,CAAAqF,cAAA,YA+LjD,CAAC,gBAC8C,CAAC;IAhMDrF,EAAE,CAAAsF,UAAA,mBAAA+B,uEAAA;MAAFrH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAgMlCD,MAAA,CAAA0B,KAAA,CAAM,CAAC;IAAA,EAAC;IAhMwBtH,EAAE,CAAAyG,MAAA,GAkM3F,CAAC;IAlMwFzG,EAAE,CAAA+F,YAAA,CAkMlF,CAAC;IAlM+E/F,EAAE,CAAAqF,cAAA,iBAmMrB,CAAC;IAnMkBrF,EAAE,CAAAsF,UAAA,mBAAAiC,wEAAA;MAAFvH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAmM/BD,MAAA,CAAA4B,OAAA,CAAQ,CAAC;IAAA,EAAC;IAnMmBxH,EAAE,CAAAyG,MAAA,GAmMK,CAAC;IAnMRzG,EAAE,CAAA+F,YAAA,CAmMc,CAAC,CACtG,CAAC,CACH,CAAC,CACU,CAAC;IAtM2E/F,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAA0C,aAAA,GAAF1H,EAAE,CAAA2H,WAAA;IAAA,MAAA/B,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CA0KxE,CAAC;IA1KqE1G,EAAE,CAAAgG,UAAA,cAAAJ,MAAA,CAAAgC,SA0KxE,CAAC,aAAAhC,MAAA,CAAAiC,SACF,CAAC,mBAAAH,aACM,CAAC;IA5K+D1H,EAAE,CAAA0G,SAAA,EAuLpD,CAAC;IAvLiD1G,EAAE,CAAAgG,UAAA,YAAAJ,MAAA,CAAAkC,kBAuLpD,CAAC,iBAAAlC,MAAA,CAAAmC,YAAoB,CAAC;IAvL4B/H,EAAE,CAAA0G,SAAA,EAgMH,CAAC;IAhMA1G,EAAE,CAAAgG,UAAA,cAAAJ,MAAA,CAAAiC,SAgMH,CAAC;IAhMA7H,EAAE,CAAA0G,SAAA,CAkM3F,CAAC;IAlMwF1G,EAAE,CAAAgI,kBAAA,MAAApC,MAAA,CAAAqC,MAAA,CAAAC,WAAA,KAkM3F,CAAC;IAlMwFlI,EAAE,CAAA0G,SAAA,EAmMK,CAAC;IAnMR1G,EAAE,CAAA4G,iBAAA,CAAAhB,MAAA,CAAAqC,MAAA,CAAAE,aAmMK,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAnMR/G,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,cAmXlG,CAAC;IAnX+FrF,EAAE,CAAAsF,UAAA,2BAAA+C,0EAAAnB,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAkX/ED,MAAA,CAAA0C,eAAA,CAAApB,MAAsB,CAAC;IAAA,EAAC;IAlXqDlH,EAAE,CAAA+F,YAAA,CAmX1F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GAnXuF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAuI,WAAA,0CAAA3C,MAAA,CAAA4C,gBA6WjC,CAAC;IA7W8BxI,EAAE,CAAAgG,UAAA,YAAAJ,MAAA,CAAAK,OA8W9E,CAAC,eAAAL,MAAA,CAAA6C,QACG,CAAC,oBAAA7C,MAAA,CAAA8C,aACS,CAAC;IAhX6D1I,EAAE,CAAA2I,WAAA,eAAA/C,MAAA,CAAAgD,KAAA;EAAA;AAAA;AAAA,SAAAC,8CAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsB,GAAA,GAAFtG,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,WA0XF,CAAC;IA1XDrF,EAAE,CAAAsF,UAAA,mBAAAwD,kEAAA;MAAA,MAAAC,YAAA,GAAF/I,EAAE,CAAAwF,aAAA,CAAAc,GAAA,EAAAX,SAAA;MAAA,OAAF3F,EAAE,CAAA6F,WAAA,CA0XvBkD,YAAA,CAAAC,QAAA,CAAmB,CAAC;IAAA,EAAC;IA1XAhJ,EAAE,CAAAyG,MAAA,EA4X7F,CAAC;IA5X0FzG,EAAE,CAAA+F,YAAA,CA4XxF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAA+D,YAAA,GAAA9D,GAAA,CAAAU,SAAA;IA5XqF3F,EAAE,CAAA0G,SAAA,CA4X7F,CAAC;IA5X0F1G,EAAE,CAAAgI,kBAAA,MAAAe,YAAA,CAAAlC,IAAA,KA4X7F,CAAC;EAAA;AAAA;AAAA,SAAAoC,yCAAAjE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5X0FhF,EAAE,CAAAqF,cAAA,YAoXnC,CAAC,aAC8C,CAAC;IArXfrF,EAAE,CAAAoH,SAAA,aAsX5D,CAAC;IAtXyDpH,EAAE,CAAA+F,YAAA,CAuX1F,CAAC;IAvXuF/F,EAAE,CAAAqF,cAAA,+BAwX/C,CAAC,WACJ,CAAC;IAzX+CrF,EAAE,CAAAwG,UAAA,IAAAqC,6CAAA,eA0XF,CAAC;IA1XD7I,EAAE,CAAA+F,YAAA,CA6X1F,CAAC,CACW,CAAC,CAChB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAkE,gBAAA,GA/X0FlJ,EAAE,CAAA2H,WAAA;IAAA,MAAA/B,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAqXW,CAAC;IArXd1G,EAAE,CAAAgG,UAAA,mBAAAkD,gBAqXW,CAAC;IArXdlJ,EAAE,CAAA0G,SAAA,EA0XnC,CAAC;IA1XgC1G,EAAE,CAAAgG,UAAA,YAAAJ,MAAA,CAAAuD,gBA0XnC,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAApE,EAAA,EAAAC,GAAA;AAAA,SAAAoE,wCAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA1XgChF,EAAE,CAAAoH,SAAA,aAqdvF,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAsE,MAAA,GArdoFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAuI,WAAA,WAAAe,MAAA,CAAAC,SAAA,aAodtD,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAxE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IApdmDhF,EAAE,CAAAoH,SAAA,aA4dvF,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAsE,MAAA,GA5doFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAuI,WAAA,WAAAe,MAAA,CAAAC,SAAA,cA2drD,CAAC;EAAA;AAAA;AAAA,MAAAE,GAAA;AAAA,SAAAC,yDAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GA3dkD/G,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,eAg6B9F,CAAC;IAh6B2FrF,EAAE,CAAAsF,UAAA,0BAAAqE,wFAAAzC,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA85B5ED,MAAA,CAAAgE,cAAA,CAAA1C,MAAqB,CAAC;IAAA,EAAC;IA95BmDlH,EAAE,CAAA+F,YAAA,CAg6BrF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GAh6BkF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,WAAAJ,MAAA,CAAAiE,QA65B1E,CAAC,eAAAjE,MAAA,CAAAkE,YAEO,CAAC;EAAA;AAAA;AAAA,SAAAC,wEAAA/E,EAAA,EAAAC,GAAA;AAAA,SAAA+E,0DAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/5B+DhF,EAAE,CAAAgH,uBAAA,EAk6B9C,CAAC;IAl6B2ChH,EAAE,CAAAwG,UAAA,IAAAuD,uEAAA,wBAm6BhD,CAAC;IAn6B6C/J,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAY,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAm6BjD,CAAC;IAn6B8C1G,EAAE,CAAAgG,UAAA,qBAAAJ,MAAA,CAAAqE,YAm6BjD,CAAC;EAAA;AAAA;AAAA,SAAAC,2CAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn6B8ChF,EAAE,CAAAgH,uBAAA,EAw5B5C,CAAC;IAx5ByChH,EAAE,CAAAoH,SAAA,sBAy5BtC,CAAC;IAz5BmCpH,EAAE,CAAAwG,UAAA,IAAAkD,wDAAA,gCAAF1J,EAAE,CAAAmK,sBA05BzE,CAAC,IAAAH,yDAAA,yBAQ0B,CAAC;IAl6B2ChK,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAoF,YAAA,GAAFpK,EAAE,CAAA2H,WAAA;IAAA,MAAA/B,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAy5BvD,CAAC;IAz5BoD1G,EAAE,CAAAgG,UAAA,eAAAJ,MAAA,CAAAyE,YAy5BvD,CAAC;IAz5BoDrK,EAAE,CAAA0G,SAAA,EAk6B9D,CAAC;IAl6B2D1G,EAAE,CAAAgG,UAAA,SAAAJ,MAAA,CAAAqE,YAk6B9D,CAAC,aAAAG,YAAa,CAAC;EAAA;AAAA;AAAA,SAAAE,oCAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuF,GAAA,GAl6B6CvK,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,cA86BlG,CAAC;IA96B+FrF,EAAE,CAAAsF,UAAA,2BAAAkF,mEAAAtD,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAA+E,GAAA;MAAA,MAAA3E,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA66B/ED,MAAA,CAAA0C,eAAA,CAAApB,MAAsB,CAAC;IAAA,EAAC;IA76BqDlH,EAAE,CAAA+F,YAAA,CA86B1F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GA96BuF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,eAAAJ,MAAA,CAAA6E,UAy6BxE,CAAC,YAAA7E,MAAA,CAAA8E,SACL,CAAC,oBAAA9E,MAAA,CAAA+E,eACa,CAAC;IA36B2D3K,EAAE,CAAA2I,WAAA,eAAA/C,MAAA,CAAAgF,OAAA;EAAA;AAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,8CAAAhG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAAF/G,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,wBA6mClG,CAAC;IA7mC+FrF,EAAE,CAAAsF,UAAA,0BAAA2F,sFAAA/D,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CA4mChFD,MAAA,CAAAsF,mBAAA,CAAAhE,MAA0B,CAAC;IAAA,EAAC;IA5mCkDlH,EAAE,CAAA+F,YAAA,CA6mChF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GA7mC6E5F,EAAE,CAAA0F,aAAA;IAAA,MAAAyF,oBAAA,GAAFnL,EAAE,CAAA2H,WAAA;IAAA,MAAAyD,gBAAA,GAAFpL,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAgG,UAAA,oBAAAmF,oBAumC5D,CAAC,kBAAAC,gBACP,CAAC,iBAAAxF,MAAA,CAAAyF,cACD,CAAC,mBAAAzF,MAAA,CAAA0F,gBACG,CAAC,iBAAA1F,MAAA,CAAA2F,SACV,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAAxG,EAAA,EAAAC,GAAA;AAAA,SAAAwG,0CAAAzG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA3mCoEhF,EAAE,CAAAwG,UAAA,IAAAgF,uDAAA,wBA+mCnB,CAAC;EAAA;EAAA,IAAAxG,EAAA;IAAA,MAAAY,MAAA,GA/mCgB5F,EAAE,CAAA0F,aAAA;IAAA,MAAAgG,eAAA,GAAF1L,EAAE,CAAA2H,WAAA;IAAA,MAAAgE,kBAAA,GAAF3L,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAgG,UAAA,qBAAAJ,MAAA,CAAAgG,UAAA,GAAAF,eAAA,GAAAC,kBA+mCpB,CAAC;EAAA;AAAA;AAAA,SAAAE,0CAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/mCiBhF,EAAE,CAAA8L,YAAA,EAknCjD,CAAC;IAlnC8C9L,EAAE,CAAA8L,YAAA,KAmnC7C,CAAC;EAAA;AAAA;AAAA,SAAAC,0CAAA/G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnnC0ChF,EAAE,CAAAoH,SAAA,yBA0nC7E,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAY,MAAA,GA1nC0E5F,EAAE,CAAA0F,aAAA;IAAA,MAAAiG,kBAAA,GAAF3L,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAgG,UAAA,cAAAJ,MAAA,CAAA2D,SAunCxE,CAAC,mBAAA3D,MAAA,CAAAoG,cACS,CAAC,oBAAAL,kBACC,CAAC;EAAA;AAAA;AAAA,SAAAM,0CAAAjH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAznCyDhF,EAAE,CAAA8L,YAAA,KA6nCxE,CAAC;EAAA;AAAA;AAAA,MAAAI,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAApH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7nCqEhF,EAAE,CAAAoH,SAAA,SAm8CR,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAqH,QAAA,GAAApH,GAAA,CAAAU,SAAA;IAn8CK3F,EAAE,CAAAsM,WAAA,UAAAD,QAm8CzE,CAAC,cAAAA,QAAwB,CAAC;EAAA;AAAA;AAAA,SAAAE,uDAAAvH,EAAA,EAAAC,GAAA;AAAA,SAAAuH,yCAAAxH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAn8C6ChF,EAAE,CAAAqF,cAAA,cAo8C9C,CAAC;IAp8C2CrF,EAAE,CAAAwG,UAAA,IAAA+F,sDAAA,wBAq8CjD,CAAC;IAr8C8CvM,EAAE,CAAA+F,YAAA,CAs8C3F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GAt8CwF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAq8ClD,CAAC;IAr8C+C1G,EAAE,CAAAgG,UAAA,qBAAAJ,MAAA,CAAA6G,aAq8ClD,CAAC;EAAA;AAAA;AAAA,SAAAC,+CAAA1H,EAAA,EAAAC,GAAA;AAAA,MAAA0H,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,sDAAA7H,EAAA,EAAAC,GAAA;AAAA,SAAA6H,wCAAA9H,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAr8C+ChF,EAAE,CAAAqF,cAAA,YAkhDhG,CAAC;IAlhD6FrF,EAAE,CAAA+M,MAAA;IAAF/M,EAAE,CAAAwG,UAAA,IAAAqG,qDAAA,wBAmhD7C,CAAC;IAnhD0C7M,EAAE,CAAA+F,YAAA,CAohD3F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsE,MAAA,GAphDwFtJ,EAAE,CAAA0F,aAAA;IAAA,MAAAsH,kBAAA,GAAFhN,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAsM,WAAA,UAAFtM,EAAE,CAAAiN,WAAA,OAAA3D,MAAA,CAAA4D,UAAA,OAihD1D,CAAC;IAjhDuDlN,EAAE,CAAA0G,SAAA,EAmhD9C,CAAC;IAnhD2C1G,EAAE,CAAAgG,UAAA,qBAAAgH,kBAmhD9C,CAAC;EAAA;AAAA;AAAA,SAAAG,gDAAAnI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAnhD2ChF,EAAE,CAAA8L,YAAA,EAuhDxE,CAAC;EAAA;AAAA;AAAA,MAAAsB,GAAA;AAAA,SAAAC,mCAAArI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvhDqEhF,EAAE,CAAAoH,SAAA,cAgqD7F,CAAC;EAAA;AAAA;AAAA,SAAAkG,8CAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA+B,GAAA,GAhqD0F/G,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,WAmuDhG,CAAC;IAnuD6FrF,EAAE,CAAAsF,UAAA,6BAAAiI,4EAAArG,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAuB,GAAA;MAAA,MAAAnB,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAkuD3ED,MAAA,CAAA4H,uBAAA,CAAAtG,MAA8B,CAAC;IAAA,EAAC;IAluDyClH,EAAE,CAAA+F,YAAA,CAmuD3F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAyI,sBAAA,GAnuDwFzN,EAAE,CAAA0F,aAAA,GAAAgI,IAAA;IAAF1N,EAAE,CAAAgG,UAAA,wBAAAyH,sBAiuDpD,CAAC;EAAA;AAAA;AAAA,SAAAE,yCAAA3I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjuDiDhF,EAAE,CAAAgH,uBAAA,EA6tDzB,CAAC;IA7tDsBhH,EAAE,CAAAwG,UAAA,IAAA8G,6CAAA,eAmuDhG,CAAC;IAnuD6FtN,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAyI,sBAAA,GAAAxI,GAAA,CAAAyI,IAAA;IAAA,MAAA9H,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAguD7C,CAAC;IAhuD0C1G,EAAE,CAAAgG,UAAA,SAAAJ,MAAA,CAAAgI,aAAA,IAAAH,sBAAA,CAAAI,MAguD7C,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAA9I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhuD0ChF,EAAE,CAAAqF,cAAA,WAsuDnB,CAAC;IAtuDgBrF,EAAE,CAAAoH,SAAA,uBAuuDC,CAAC;IAvuDJpH,EAAE,CAAA+M,MAAA;IAAF/M,EAAE,CAAA+F,YAAA,CAwuD9F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GAxuD2F5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAuuDjB,CAAC;IAvuDc1G,EAAE,CAAAgG,UAAA,oBAAFhG,EAAE,CAAAiN,WAAA,OAAArH,MAAA,CAAAmI,SAAA,CAuuDjB,CAAC;EAAA;AAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA;AAAA,MAAAC,IAAA,GAAAA,CAAAC,EAAA,EAAAC,EAAA;EAAAzI,SAAA,EAAAwI,EAAA;EAAAE,KAAA,EAAAD;AAAA;AAAA,SAAAE,0DAAAtJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvuDchF,EAAE,CAAAqF,cAAA,eA82DD,CAAC;IA92DFrF,EAAE,CAAAoH,SAAA,cAq3DtF,CAAC;IAr3DmFpH,EAAE,CAAA+F,YAAA,CAs3D3F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsE,MAAA,GAt3DwFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAiF,YA82DF,CAAC;IA92DDvO,EAAE,CAAA0G,SAAA,EAk3D1E,CAAC;IAl3DuE1G,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAkF,OAk3D1E,CAAC,mBAAAlF,MAAA,CAAAmF,cACa,CAAC,oBAAAnF,MAAA,CAAAoF,eACC,CAAC;EAAA;AAAA;AAAA,SAAAC,+GAAA3J,EAAA,EAAAC,GAAA;AAAA,SAAA2J,iGAAA5J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp3DuDhF,EAAE,CAAAgH,uBAAA,EAi4DA,CAAC;IAj4DHhH,EAAE,CAAAwG,UAAA,IAAAmI,8GAAA,yBAq4DxF,CAAC;IAr4DqF3O,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAA6J,OAAA,GAAA5J,GAAA,CAAAU,SAAA;IAAA,MAAAmJ,IAAA,GAAA7J,GAAA,CAAAoJ,KAAA;IAAA,MAAA/E,MAAA,GAAFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAm4DnD,CAAC;IAn4DgD1G,EAAE,CAAAgG,UAAA,qBAAAsD,MAAA,CAAAyF,eAm4DnD,CAAC,4BAn4DgD/O,EAAE,CAAAgP,eAAA,IAAAd,IAAA,EAAAW,OAAA,EAAAC,IAAA,CAo4D9B,CAAC;EAAA;AAAA;AAAA,SAAAG,kFAAAjK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp4D2BhF,EAAE,CAAAqF,cAAA,wCA83DhG,CAAC,eACkG,CAAC,WAC1F,CAAC;IAh4DmFrF,EAAE,CAAAwG,UAAA,IAAAoI,gGAAA,0BAi4DA,CAAC;IAj4DH5O,EAAE,CAAA+F,YAAA,CAu4DrF,CAAC,CACH,CAAC,CACmB,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsE,MAAA,GAz4DgEtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAsM,WAAA,WAAAhD,MAAA,CAAA4F,IAAA,CAAArB,MAAA,GAAAvE,MAAA,CAAA6F,OAAA,GAAA7F,MAAA,CAAA8F,mBA63DnC,CAAC;IA73DgCpP,EAAE,CAAAgG,UAAA,aAAAsD,MAAA,CAAA+F,eA03DnE,CAAC,gBAAA/F,MAAA,CAAAgG,kBACK,CAAC,gBAAAhG,MAAA,CAAAiG,kBACD,CAAC;IA53D0DvP,EAAE,CAAA0G,SAAA,EA+3DhC,CAAC;IA/3D6B1G,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAkF,OA+3DhC,CAAC,mBAAAlF,MAAA,CAAAmF,cAAiC,CAAC;IA/3DLzO,EAAE,CAAA0G,SAAA,EAi4D3C,CAAC;IAj4DwC1G,EAAE,CAAAgG,UAAA,oBAAAsD,MAAA,CAAA4F,IAi4D3C,CAAC,yBAAA5F,MAAA,CAAAkG,iBAAwC,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAAzK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAj4DDhF,EAAE,CAAAgH,uBAAA,EAo2DrE,CAAC;IAp2DkEhH,EAAE,CAAAqF,cAAA,eAq2DI,CAAC;IAr2DPrF,EAAE,CAAAoH,SAAA,cA42DtF,CAAC;IA52DmFpH,EAAE,CAAA+F,YAAA,CA62D3F,CAAC;IA72DwF/F,EAAE,CAAAwG,UAAA,IAAA8H,yDAAA,gBA82DD,CAAC,IAAAW,iFAAA,wCAgBhG,CAAC;IA93D6FjP,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAsE,MAAA,GAAFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CAq2D9C,CAAC;IAr2D2C1G,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAoG,cAq2D9C,CAAC;IAr2D2C1P,EAAE,CAAA0G,SAAA,EAy2D1E,CAAC;IAz2DuE1G,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAkF,OAy2D1E,CAAC,mBAAAlF,MAAA,CAAAmF,cACa,CAAC,kBAAAnF,MAAA,CAAAmD,aACH,CAAC;IA32D2DzM,EAAE,CAAA0G,SAAA,CA82DnD,CAAC;IA92DgD1G,EAAE,CAAAgG,UAAA,UAAAsD,MAAA,CAAAyF,eA82DnD,CAAC;IA92DgD/O,EAAE,CAAA0G,SAAA,CAy3DzE,CAAC;IAz3DsE1G,EAAE,CAAAgG,UAAA,SAAAsD,MAAA,CAAAyF,eAy3DzE,CAAC;EAAA;AAAA;AAAA,SAAAY,2CAAA3K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAz3DsEhF,EAAE,CAAAqF,cAAA,gBA24DR,CAAC;IA34DKrF,EAAE,CAAAoH,SAAA,eAm5DxF,CAAC;IAn5DqFpH,EAAE,CAAA+F,YAAA,CAo5D7F,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAsE,MAAA,GAp5D0FtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAiF,YA24DT,CAAC;IA34DMvO,EAAE,CAAA0G,SAAA,EA+4D5E,CAAC;IA/4DyE1G,EAAE,CAAAgG,UAAA,YAAAsD,MAAA,CAAAkF,OA+4D5E,CAAC,mBAAAlF,MAAA,CAAAmF,cACa,CAAC,kBAAAnF,MAAA,CAAAmD,aACH,CAAC,oBAAAnD,MAAA,CAAAoF,eACG,CAAC;EAAA;AAAA;AAAA,SAAAkB,oDAAA5K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAl5DyDhF,EAAE,CAAAgH,uBAAA,EA6hErD,CAAC;IA7hEkDhH,EAAE,CAAAyG,MAAA,EA6hE1C,CAAC;IA7hEuCzG,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAsE,MAAA,GAAFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CA6hE1C,CAAC;IA7hEuC1G,EAAE,CAAA4G,iBAAA,CAAA0C,MAAA,CAAAuG,KA6hE1C,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAA9K,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA7hEuChF,EAAE,CAAAgH,uBAAA,EA8hEpD,CAAC;IA9hEiDhH,EAAE,CAAAyG,MAAA,EA8hExC,CAAC;IA9hEqCzG,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAsE,MAAA,GAAFtJ,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAA0G,SAAA,CA8hExC,CAAC;IA9hEqC1G,EAAE,CAAA4G,iBAAA,CAAA0C,MAAA,CAAAyG,MA8hExC,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAAhL,EAAA,EAAAC,GAAA;AAAA,SAAAgL,yCAAAjL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9hEqChF,EAAE,CAAAgH,uBAAA,EAswET,CAAC;IAtwEMhH,EAAE,CAAAwG,UAAA,IAAAwJ,sDAAA,yBAuwE1C,CAAC;IAvwEuChQ,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAFhF,EAAE,CAAA0F,aAAA;IAAA,MAAAwK,qBAAA,GAAFlQ,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAA0G,SAAA,CAuwE3C,CAAC;IAvwEwC1G,EAAE,CAAAgG,UAAA,qBAAAkK,qBAuwE3C,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAAnL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvwEwChF,EAAE,CAAAoH,SAAA,+BAsxEd,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAY,MAAA,GAtxEW5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,UAAAJ,MAAA,CAAAwK,OAsxEvD,CAAC;EAAA;AAAA;AAAA,SAAAC,kDAAArL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAtxEoDhF,EAAE,CAAAoH,SAAA,+BAsyEtE,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAY,MAAA,GAtyEmE5F,EAAE,CAAA0F,aAAA;IAAA,MAAA4K,mBAAA,GAAFtQ,EAAE,CAAA2H,WAAA;IAAA,MAAA4I,kBAAA,GAAFvQ,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAgG,UAAA,SAAAJ,MAAA,CAAAsJ,IAyxEhF,CAAC,YAAAtJ,MAAA,CAAA4I,OACK,CAAC,YAAA5I,MAAA,CAAAuJ,OACD,CAAC,oBAAAoB,kBACe,CAAC,mBAAA3K,MAAA,CAAA4K,kBACC,CAAC,kBAAA5K,MAAA,CAAA6G,aACP,CAAC,2BAAA7G,MAAA,CAAA6K,sBACiB,CAAC,oBAAA7K,MAAA,CAAA8K,wBAAA,GAAA9K,MAAA,CAAA8K,wBAAA,CAAAC,WAAA,OACwC,CAAC,oBAAA/K,MAAA,CAAAgL,iBACtD,CAAC,uBAAAhL,MAAA,CAAAiL,oBACK,CAAC,uBAAAjL,MAAA,CAAAkL,oBACD,CAAC,qBAAAR,mBACP,CAAC,sBAAA1K,MAAA,CAAAmL,mBACG,CAAC;EAAA;AAAA;AAAA,SAAAC,wCAAAhM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAryEiDhF,EAAE,CAAAoH,SAAA,gCA6yEnE,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAY,MAAA,GA7yEgE5F,EAAE,CAAA0F,aAAA;IAAA,MAAA6K,kBAAA,GAAFvQ,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAAgG,UAAA,gBAAAJ,MAAA,CAAAqL,aAyyE9D,CAAC,mBAAArL,MAAA,CAAAsL,oBACS,CAAC,kBAAAtL,MAAA,CAAA6G,aACT,CAAC,oBAAA8D,kBACG,CAAC;EAAA;AAAA;AAAA,SAAAY,kDAAAnM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA5yEqDhF,EAAE,CAAAoH,SAAA,+BA+yEX,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAY,MAAA,GA/yEQ5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,WAAAJ,MAAA,CAAAwL,QA+yErD,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAArM,EAAA,EAAAC,GAAA;AAAA,SAAAqM,yCAAAtM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/yEkDhF,EAAE,CAAAgH,uBAAA,EAizEN,CAAC;IAjzEGhH,EAAE,CAAAwG,UAAA,IAAA6K,sDAAA,yBAkzE1C,CAAC;IAlzEuCrR,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAFhF,EAAE,CAAA0F,aAAA;IAAA,MAAAwK,qBAAA,GAAFlQ,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAA0G,SAAA,CAkzE3C,CAAC;IAlzEwC1G,EAAE,CAAAgG,UAAA,qBAAAkK,qBAkzE3C,CAAC;EAAA;AAAA;AAAA,SAAAqB,yDAAAvM,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAG,GAAA,GAlzEwCnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAqF,cAAA,uBAu0EhG,CAAC;IAv0E6FrF,EAAE,CAAAsF,UAAA,8BAAAkM,mGAAAtK,MAAA;MAAFlH,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAS,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAq0E1ED,MAAA,CAAA6L,gBAAA,CAAAvK,MAAuB,CAAC;IAAA,EAAC,+BAAAwK,oGAAAxK,MAAA;MAr0E+ClH,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAS,MAAA,GAAF5F,EAAE,CAAA0F,aAAA;MAAA,OAAF1F,EAAE,CAAA6F,WAAA,CAs0EzED,MAAA,CAAA+L,iBAAA,CAAAzK,MAAwB,CAAC;IAAA,EAAC;IAt0E6ClH,EAAE,CAAA+F,YAAA,CAu0EhF,CAAC;EAAA;EAAA,IAAAf,EAAA;IAAA,MAAAY,MAAA,GAv0E6E5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,YAAAJ,MAAA,CAAAgM,cAwzErE,CAAC,sBAAAhM,MAAA,CAAAiM,iBAEY,CAAC,sBAAAjM,MAAA,CAAAkM,iBACD,CAAC,iBAAAlM,MAAA,CAAAmM,YACV,CAAC,sBAAAnM,MAAA,CAAAoM,iBACQ,CAAC,uBAAApM,MAAA,CAAAqM,kBACC,CAAC,gBAAArM,MAAA,CAAAsM,WACf,CAAC,WAAAtM,MAAA,CAAAuM,gBAAA,yBAAAvM,MAAA,CAAAwM,MAAA,oCACkE,CAAC,eAAAxM,MAAA,CAAAyM,UACtE,CAAC,YAAAzM,MAAA,CAAA0M,OACP,CAAC,aAAA1M,MAAA,CAAA2M,QACC,CAAC,gBAAA3M,MAAA,CAAA4M,WACK,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAAzN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAp0EiEhF,EAAE,CAAAwG,UAAA,IAAA+K,wDAAA,4BAu0EhG,CAAC;EAAA;EAAA,IAAAvM,EAAA;IAAA,MAAAY,MAAA,GAv0E6F5F,EAAE,CAAA0F,aAAA;IAAF1F,EAAE,CAAAgG,UAAA,SAAAJ,MAAA,CAAA8M,gBAAA,IAAA9M,MAAA,CAAAsJ,IAAA,CAAArB,MAuzEzD,CAAC;EAAA;AAAA;AAAA,SAAA8E,yCAAA3N,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAvzEsDhF,EAAE,CAAA8L,YAAA,EA00ExE,CAAC;EAAA;AAAA;AAAA,MAAA8G,IAAA;AAAA,SAAAC,wCAAA7N,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA10EqEhF,EAAE,CAAA8L,YAAA,EAytFxE,CAAC;EAAA;AAAA;AAAA,SAAAgH,uDAAA9N,EAAA,EAAAC,GAAA;AAAA,SAAA8N,yCAAA/N,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAztFqEhF,EAAE,CAAAgH,uBAAA,EA2tF9D,CAAC;IA3tF2DhH,EAAE,CAAAwG,UAAA,IAAAsM,sDAAA,wBA4tF/C,CAAC;IA5tF4C9S,EAAE,CAAAyH,qBAAA;EAAA;EAAA,IAAAzC,EAAA;IAAFhF,EAAE,CAAA0F,aAAA;IAAA,MAAAsN,kBAAA,GAAFhT,EAAE,CAAA2H,WAAA;IAAF3H,EAAE,CAAA0G,SAAA,CA4tFhD,CAAC;IA5tF6C1G,EAAE,CAAAgG,UAAA,qBAAAgN,kBA4tFhD,CAAC;EAAA;AAAA;AA9vFvD,MAAMC,uBAAuB,GAAG,eAAe;AAC/C,MAAMC,wBAAwB,CAAC;EAC3B/L,eAAeA,CAACgM,OAAO,EAAE;IACrB,IAAI,CAACC,SAAS,GAAGD,OAAO;IACxB,IAAI,CAACE,eAAe,CAACC,IAAI,CAACH,OAAO,CAAC;EACtC;EACAI,IAAIA,CAAA,EAAG;IACH,IAAI,CAACH,SAAS,GAAG,KAAK;IACtB,IAAI,CAACI,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,CAACN,SAAS,GAAG,IAAI;IACrB,IAAI,CAACI,GAAG,CAACC,YAAY,CAAC,CAAC;EAC3B;EACAE,WAAWA,CAACC,eAAe,EAAEC,MAAM,EAAEL,GAAG,EAAEM,QAAQ,EAAE;IAChD,IAAI,CAACF,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACL,GAAG,GAAGA,GAAG;IACd,IAAI,CAACM,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGd,uBAAuB;IAC5C,IAAI,CAACe,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACZ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACa,UAAU,GAAG,KAAK;IACvB,IAAI,CAACZ,eAAe,GAAG,IAAIpT,YAAY,CAAC,CAAC;EAC7C;EACAiU,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,MAAM,CAACM,iBAAiB,CAAC,MAAM;MAChCjT,SAAS,CAAC,IAAI,CAACkT,UAAU,CAACC,aAAa,EAAE,OAAO,CAAC,CAC5CC,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAACC,KAAK,IAAI;QACpBA,KAAK,CAACC,eAAe,CAAC,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,iCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwF1B,wBAAwB,EAAlClT,EAAE,CAAA6U,iBAAA,CAAkDzS,EAAE,CAAC0S,eAAe,GAAtE9U,EAAE,CAAA6U,iBAAA,CAAiF7U,EAAE,CAACY,MAAM,GAA5FZ,EAAE,CAAA6U,iBAAA,CAAuG7U,EAAE,CAAC+U,iBAAiB,GAA7H/U,EAAE,CAAA6U,iBAAA,CAAwIvS,EAAE,CAACC,gBAAgB;IAAA,CAA4C;EAAE;EAC3S;IAAS,IAAI,CAACyS,IAAI,kBAD8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EACJhC,wBAAwB;MAAAiC,SAAA;MAAAC,SAAA,WAAAC,+BAAArQ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADtBhF,EAAE,CAAAsV,WAAA,CACmVzS,mBAAmB,KAA2B3C,UAAU;QAAA;QAAA,IAAA8E,EAAA;UAAA,IAAAuQ,EAAA;UAD7YvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAmP,UAAA,GAAAmB,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,MAAA;QAAA3B,QAAA;QAAA4B,cAAA;QAAAxC,SAAA;QAAAa,UAAA;MAAA;MAAA4B,OAAA;QAAAxC,eAAA;MAAA;MAAAyC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFhW,EAAE,CAAAiW,kBAAA,CAC2P,CAAC1T,gBAAgB,CAAC,GAD/QvC,EAAE,CAAAkW,mBAAA;MAAAC,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAxR,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAqF,cAAA,aAclG,CAAC;UAd+FrF,EAAE,CAAAsF,UAAA,6BAAAoR,kEAAAxP,MAAA;YAAA,OAa7EjC,GAAA,CAAAkC,eAAA,CAAAD,MAAsB,CAAC;UAAA,EAAC;UAbmDlH,EAAE,CAAA8L,YAAA,EAexE,CAAC;UAfqE9L,EAAE,CAAA+F,YAAA,CAgB5F,CAAC;QAAA;QAAA,IAAAf,EAAA;UAhByFhF,EAAE,CAAAuI,WAAA,WAAAtD,GAAA,CAAA+O,QAUxE,CAAC,0BAAA/O,GAAA,CAAAmO,SACe,CAAC;UAXqDpT,EAAE,CAAAgG,UAAA,eAAAf,GAAA,CAAAgP,UAOxE,CAAC,qBACL,CAAC,mBAAAhP,GAAA,CAAA2Q,cACW,CAAC,cAAA3Q,GAAA,CAAAmO,SAGX,CAAC;QAAA;MAAA;MAAAuD,YAAA,GAKiC7T,gBAAgB,EAA+BF,EAAE,CAACC,mBAAmB;MAAA+T,aAAA;MAAAC,eAAA;IAAA,EAAsW;EAAE;AAC3e;AACA5V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAE0Q,wBAAwB,CAAC4D,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AAC5D;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvBoG/W,EAAE,CAAAgX,iBAAA,CAuBX9D,wBAAwB,EAAc,CAAC;IACtHgC,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,mBAAmB;MAC7BpB,QAAQ,EAAG,iBAAgB;MAC3Be,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CC,mBAAmB,EAAE,KAAK;MAC1BR,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBe,SAAS,EAAE,CAAC/U,gBAAgB,CAAC;MAC7BgV,OAAO,EAAE,CAACzU,gBAAgB,CAAC;MAC3BiT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE9S,EAAE,CAAC0S;EAAgB,CAAC,EAAE;IAAEI,IAAI,EAAElV,EAAE,CAACY;EAAO,CAAC,EAAE;IAAEsU,IAAI,EAAElV,EAAE,CAAC+U;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAE5S,EAAE,CAACC;EAAiB,CAAC,CAAC,EAAkB;IAAEyR,QAAQ,EAAE,CAAC;MACnKkB,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsV,cAAc,EAAE,CAAC;MACjBV,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8S,SAAS,EAAE,CAAC;MACZ8B,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2T,UAAU,EAAE,CAAC;MACbiB,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+S,eAAe,EAAE,CAAC;MAClB6B,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE6T,UAAU,EAAE,CAAC;MACbc,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAACpU,mBAAmB,EAAE;QAAE2U,MAAM,EAAE,IAAI;QAAEC,IAAI,EAAEvX;MAAW,CAAC;IAClE,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMwX,sBAAsB,CAAC;EACzB3P,YAAYA,CAAC4P,CAAC,EAAEC,IAAI,EAAE;IAClB,OAAOA,IAAI,CAACC,KAAK;EACrB;EACA/R,KAAKA,CAAC/D,MAAM,EAAE;IACV,IAAI,IAAI,CAAC4E,cAAc,EAAE;MACrB,IAAI,CAACmB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACnG,GAAG,CAACiW,IAAI,IAAI;QAC1D,IAAIA,IAAI,KAAK7V,MAAM,EAAE;UACjB,OAAO;YAAE,GAAG6V,IAAI;YAAE3R,OAAO,EAAE,CAAClE,MAAM,CAACkE;UAAQ,CAAC;QAChD,CAAC,MACI;UACD,OAAO2R,IAAI;QACf;MACJ,CAAC,CAAC;MACF7V,MAAM,CAACkE,OAAO,GAAG,CAAClE,MAAM,CAACkE,OAAO;IACpC,CAAC,MACI;MACD,IAAI,CAAC6B,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACnG,GAAG,CAACiW,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3R,OAAO,EAAE2R,IAAI,KAAK7V;MAAO,CAAC,CAAC,CAAC;IAC1G;IACA,IAAI,CAAC8F,SAAS,GAAG,IAAI,CAACiQ,gBAAgB,CAAC,IAAI,CAAChQ,kBAAkB,CAAC;EACnE;EACAN,OAAOA,CAAA,EAAG;IACN,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB,IAAI,CAACmQ,cAAc,CAAC,CAAC;EACzB;EACAzQ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACM,SAAS,GAAG,KAAK;IACtB,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACkQ,iBAAiB,CAAC,IAAI,CAACC,YAAY,EAAE,IAAI,CAAC;IACzE,IAAI,CAACpQ,SAAS,GAAG,IAAI,CAACiQ,gBAAgB,CAAC,IAAI,CAAChQ,kBAAkB,CAAC;IAC/D,IAAI,CAACiQ,cAAc,CAAC,CAAC;EACzB;EACA5Q,eAAeA,CAAC0Q,KAAK,EAAE;IACnB,IAAI,CAACjQ,SAAS,GAAGiQ,KAAK;IACtB,IAAI,CAACA,KAAK,EAAE;MACR,IAAI,CAACE,cAAc,CAAC,CAAC;IACzB,CAAC,MACI;MACD,IAAI,CAACG,aAAa,GAAG,IAAI,CAACpQ,kBAAkB,CAAC/F,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAAC3R,OAAO,CAAC,CAACtE,GAAG,CAACiW,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IACrG;EACJ;EACAE,cAAcA,CAAA,EAAG;IACb,MAAMG,aAAa,GAAG,IAAI,CAACpQ,kBAAkB,CAAC/F,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAAC3R,OAAO,CAAC,CAACtE,GAAG,CAACiW,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;IAClG,IAAI,CAACpV,WAAW,CAAC,IAAI,CAACyV,aAAa,EAAEA,aAAa,CAAC,EAAE;MACjD,IAAI,IAAI,CAACvR,cAAc,EAAE;QACrB,IAAI,CAACwR,YAAY,CAACC,IAAI,CAACF,aAAa,CAAC;MACzC,CAAC,MACI;QACD,IAAI,CAACC,YAAY,CAACC,IAAI,CAACF,aAAa,CAACrK,MAAM,GAAG,CAAC,GAAGqK,aAAa,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;MAC9E;IACJ;EACJ;EACAF,iBAAiBA,CAACC,YAAY,EAAE3Q,KAAK,EAAE;IACnC,OAAO2Q,YAAY,CAACtW,GAAG,CAACiW,IAAI,IAAI;MAC5B,MAAM3R,OAAO,GAAGqB,KAAK,GAAG,KAAK,GAAG,CAAC,CAACsQ,IAAI,CAACS,SAAS;MAChD,OAAO;QAAExR,IAAI,EAAE+Q,IAAI,CAAC/Q,IAAI;QAAEgR,KAAK,EAAED,IAAI,CAACC,KAAK;QAAE5R;MAAQ,CAAC;IAC1D,CAAC,CAAC;EACN;EACA6R,gBAAgBA,CAAChQ,kBAAkB,EAAE;IACjC,OAAOA,kBAAkB,CAACwQ,IAAI,CAACV,IAAI,IAAIA,IAAI,CAAC3R,OAAO,CAAC;EACxD;EACA0N,WAAWA,CAACH,GAAG,EAAE+E,IAAI,EAAE;IACnB,IAAI,CAAC/E,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC+E,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC7J,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC8J,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC9R,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACsR,YAAY,GAAG,EAAE;IACtB,IAAI,CAACE,YAAY,GAAG,IAAIlY,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC6T,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC0G,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,SAAS,GAAG,KAAK;IACtB,IAAI,CAACE,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACoQ,aAAa,GAAG,EAAE;EAC3B;EACAhE,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqE,IAAI,CAACG,YAAY,CAACpE,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,MAAM;MAClE,IAAI,CAACtM,MAAM,GAAG,IAAI,CAACsQ,IAAI,CAACI,aAAa,CAAC,OAAO,CAAC;MAC9C,IAAI,CAACnF,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAmF,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEZ;IAAa,CAAC,GAAGY,OAAO;IAChC,IAAIZ,YAAY,IAAI,IAAI,CAACA,YAAY,IAAI,IAAI,CAACA,YAAY,CAACpK,MAAM,EAAE;MAC/D,IAAI,CAAC/F,kBAAkB,GAAG,IAAI,CAACkQ,iBAAiB,CAAC,IAAI,CAACC,YAAY,CAAC;MACnE,IAAI,CAACpQ,SAAS,GAAG,IAAI,CAACiQ,gBAAgB,CAAC,IAAI,CAAChQ,kBAAkB,CAAC;IACnE;EACJ;EACAgR,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAsE,+BAAApE,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,sBAAsB,EAnKhC1X,EAAE,CAAA6U,iBAAA,CAmKgD7U,EAAE,CAAC+U,iBAAiB,GAnKtE/U,EAAE,CAAA6U,iBAAA,CAmKiFhR,IAAI,CAACoV,aAAa;IAAA,CAA4C;EAAE;EACnP;IAAS,IAAI,CAACjE,IAAI,kBApK8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAoKJwC,sBAAsB;MAAAvC,SAAA;MAAA+D,SAAA;MAAAvD,MAAA;QAAAjH,eAAA;QAAA8J,YAAA;QAAAC,aAAA;QAAA9R,cAAA;QAAAsR,YAAA;MAAA;MAAApC,OAAA;QAAAsC,YAAA;MAAA;MAAApC,UAAA;MAAAC,QAAA,GApKpBhW,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6C,gCAAApU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAqF,cAAA,aAqK9D,CAAC;UArK2DrF,EAAE,CAAAwG,UAAA,IAAAzB,6CAAA,wBAsK/C,CAAC;UAtK4C/E,EAAE,CAAA+F,YAAA,CAuK5F,CAAC;UAvKyF/F,EAAE,CAAAwG,UAAA,IAAAM,8CAAA,0BAwK3C,CAAC;QAAA;QAAA,IAAA9B,EAAA;UAxKwChF,EAAE,CAAA0G,SAAA,CAsKhD,CAAC;UAtK6C1G,EAAE,CAAAgG,UAAA,qBAAAf,GAAA,CAAAyJ,eAsKhD,CAAC;UAtK6C1O,EAAE,CAAA0G,SAAA,CAwK/D,CAAC;UAxK4D1G,EAAE,CAAAgG,UAAA,UAAAf,GAAA,CAAAuT,YAwK/D,CAAC,aAAAvT,GAAA,CAAAwT,aAAiB,CAAC;QAAA;MAAA;MAAA9B,YAAA,GAgCK5T,gBAAgB,EAAoJC,IAAI,EAA6FkQ,wBAAwB,EAA6LvP,YAAY,EAA+BD,IAAI,CAAC2V,eAAe,EAAgKvW,gBAAgB,EAA+BgB,EAAE,CAACwV,eAAe,EAAwLxV,EAAE,CAACyV,mBAAmB,EAA+L3W,EAAE,CAAC4W,uBAAuB,EAA2FvW,OAAO,EAAmHW,gBAAgB,EAAoKH,gBAAgB,EAA+BD,EAAE,CAACiW,mBAAmB,EAA2MpW,WAAW,EAA+BD,EAAE,CAACsW,eAAe,EAAsFtW,EAAE,CAACuW,OAAO,EAA6MpW,cAAc,EAA+BD,EAAE,CAACsW,iBAAiB,EAAgO7V,EAAE,CAAC8V,2BAA2B,EAAoM7V,EAAE,CAAC8V,eAAe;MAAAlD,aAAA;MAAAC,eAAA;IAAA,EAA8O;EAAE;AACv+F;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KA1MoG/W,EAAE,CAAAgX,iBAAA,CA0MXU,sBAAsB,EAAc,CAAC;IACpHxC,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BE,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAA0B,CAAC;MAC1CzC,OAAO,EAAE,CACLxU,gBAAgB,EAChBC,IAAI,EACJkQ,wBAAwB,EACxBvP,YAAY,EACZb,gBAAgB,EAChBG,OAAO,EACPW,gBAAgB,EAChBH,gBAAgB,EAChBJ,WAAW,EACXE,cAAc,CACjB;MACDwS,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAAC+U;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAErR,IAAI,CAACoV;EAAc,CAAC,CAAC,EAAkB;IAAEvK,eAAe,EAAE,CAAC;MACtHwG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkY,YAAY,EAAE,CAAC;MACftD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmY,aAAa,EAAE,CAAC;MAChBvD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEqG,cAAc,EAAE,CAAC;MACjBuO,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2X,YAAY,EAAE,CAAC;MACf/C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6X,YAAY,EAAE,CAAC;MACfjD,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM0Z,0BAA0B,CAAC;EAC7BtG,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuG,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,YAAY,GAAG,IAAIna,YAAY,CAAC,CAAC;EAC1C;EACAoa,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE;MACjB,IAAI,CAACD,MAAM,GAAG,CAAC,IAAI,CAACA,MAAM;MAC1B,IAAI,CAACE,YAAY,CAAC9G,IAAI,CAAC,IAAI,CAAC4G,MAAM,CAAC;IACvC;EACJ;EACA;IAAS,IAAI,CAACxF,IAAI,YAAA4F,mCAAA1F,CAAA;MAAA,YAAAA,CAAA,IAAwFqF,0BAA0B;IAAA,CAAmD;EAAE;EACzL;IAAS,IAAI,CAACM,IAAI,kBApS8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAoSJ+E,0BAA0B;MAAA9E,SAAA;MAAA+D,SAAA;MAAAuB,QAAA;MAAAC,YAAA,WAAAC,wCAAA3V,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApSxBhF,EAAE,CAAAsF,UAAA,mBAAAsV,oDAAA;YAAA,OAoSJ3V,GAAA,CAAAoV,WAAA,CAAY,CAAC;UAAA,CAAY,CAAC;QAAA;QAAA,IAAArV,EAAA;UApSxBhF,EAAE,CAAA6a,cAAA,SAoSJ,QAAyB,CAAC;UApSxB7a,EAAE,CAAAuI,WAAA,wCAAAtD,GAAA,CAAAkV,SAAA,IAAAlV,GAAA,CAAAiV,MAAA,KAoSqB,KAAC,yCAAAjV,GAAA,CAAAkV,SAAA,IAAAlV,GAAA,CAAAiV,MAAA,KAAD,MAAC,qCAAAjV,GAAA,CAAAkV,SAAD,CAAC;QAAA;MAAA;MAAAxE,MAAA;QAAAuE,MAAA;QAAAC,SAAA;MAAA;MAAAtE,OAAA;QAAAuE,YAAA;MAAA;MAAArE,UAAA;IAAA,EAAugB;EAAE;AACroB;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAtSoG/W,EAAE,CAAAgX,iBAAA,CAsSXiD,0BAA0B,EAAc,CAAC;IACxH/E,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,8BAA8B;MACxC6C,IAAI,EAAE;QACFC,KAAK,EAAE,2BAA2B;QAClC,QAAQ,EAAG,UAAS;QACpB,4CAA4C,EAAG,+BAA8B;QAC7E,6CAA6C,EAAG,gCAA+B;QAC/E,0CAA0C,EAAE,WAAW;QACvD,SAAS,EAAE;MACf,CAAC;MACDjE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEmE,MAAM,EAAE,CAAC;MACjDhF,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6Z,SAAS,EAAE,CAAC;MACZjF,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8Z,YAAY,EAAE,CAAC;MACflF,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMua,oBAAoB,CAAC;EACvBnH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoH,UAAU,GAAG,CAAC;EACvB;EACA;IAAS,IAAI,CAACrG,IAAI,YAAAsG,6BAAApG,CAAA;MAAA,YAAAA,CAAA,IAAwFkG,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACP,IAAI,kBArU8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAqUJ4F,oBAAoB;MAAA3F,SAAA;MAAA+D,SAAA;MAAAuB,QAAA;MAAAC,YAAA,WAAAO,kCAAAjW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArUlBhF,EAAE,CAAAsM,WAAA,iBAAArH,GAAA,CAAA8V,UAAA,MAqUe,CAAC;QAAA;MAAA;MAAApF,MAAA;QAAAoF,UAAA;MAAA;MAAAhF,UAAA;IAAA,EAA+M;EAAE;AACvU;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAvUoG/W,EAAE,CAAAgX,iBAAA,CAuUX8D,oBAAoB,EAAc,CAAC;IAClH5F,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,eAAe;MACzB6C,IAAI,EAAE;QACFC,KAAK,EAAE,sBAAsB;QAC7B,yBAAyB,EAAE;MAC/B,CAAC;MACDjE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgF,UAAU,EAAE,CAAC;MACrD7F,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM4a,yBAAyB,CAAC;EAC5BvH,WAAWA,CAAA,EAAG;IACV,IAAI,CAACxK,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAAClD,OAAO,GAAG,KAAK;IACpB,IAAI,CAACwC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACE,KAAK,GAAG,IAAI;IACjB,IAAI,CAACuS,YAAY,GAAG,KAAK;IACzB,IAAI,CAAC3S,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAAC4S,aAAa,GAAG,IAAInb,YAAY,CAAC,CAAC;EAC3C;EACAqI,eAAeA,CAACrC,OAAO,EAAE;IACrB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACmV,aAAa,CAAChD,IAAI,CAACnS,OAAO,CAAC;EACpC;EACA;IAAS,IAAI,CAACyO,IAAI,YAAA2G,kCAAAzG,CAAA;MAAA,YAAAA,CAAA,IAAwFsG,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAAClG,IAAI,kBAzW8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAyWJgG,yBAAyB;MAAA/F,SAAA;MAAA+D,SAAA;MAAAvD,MAAA;QAAAxM,gBAAA;QAAAlD,OAAA;QAAAwC,QAAA;QAAAC,aAAA;QAAAE,KAAA;QAAAuS,YAAA;QAAA3S,gBAAA;MAAA;MAAAqN,OAAA;QAAAuF,aAAA;MAAA;MAAArF,UAAA;MAAAC,QAAA,GAzWvBhW,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA+E,mCAAAtW,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAwG,UAAA,IAAA4B,0CAAA,kBAmXlG,CAAC,IAAAa,wCAAA,gBAC8D,CAAC;QAAA;QAAA,IAAAjE,EAAA;UApXgChF,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAkW,YA2W9E,CAAC;UA3W2Enb,EAAE,CAAA0G,SAAA,CAoXrC,CAAC;UApXkC1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAuD,gBAoXrC,CAAC;QAAA;MAAA;MAAAmO,YAAA,GAYH3T,IAAI,EAA4FK,WAAW,EAA+BD,EAAE,CAACsW,eAAe,EAAsFtW,EAAE,CAACuW,OAAO,EAA6MlW,gBAAgB,EAA+BD,EAAE,CAACiW,mBAAmB,EAA2M3W,gBAAgB,EAA+BgB,EAAE,CAACwV,eAAe,EAAwLxV,EAAE,CAACyV,mBAAmB,EAA+L3W,EAAE,CAACC,mBAAmB,EAAiSD,EAAE,CAAC4W,uBAAuB,EAA0F7V,YAAY,EAA+BD,IAAI,CAAC2V,eAAe,EAAiKpW,OAAO;MAAA2T,aAAA;MAAAC,eAAA;IAAA,EAAwL;EAAE;AACljE;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAlYoG/W,EAAE,CAAAgX,iBAAA,CAkYXkE,yBAAyB,EAAc,CAAC;IACvHhG,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BE,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAsB,CAAC;MACtCzC,OAAO,EAAE,CAACvU,IAAI,EAAEK,WAAW,EAAEI,gBAAgB,EAAEX,gBAAgB,EAAEa,YAAY,EAAEV,OAAO,CAAC;MACvF8S,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE5M,gBAAgB,EAAE,CAAC;MAC3D+L,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2F,OAAO,EAAE,CAAC;MACViP,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmI,QAAQ,EAAE,CAAC;MACXyM,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoI,aAAa,EAAE,CAAC;MAChBwM,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsI,KAAK,EAAE,CAAC;MACRsM,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6a,YAAY,EAAE,CAAC;MACfjG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkI,gBAAgB,EAAE,CAAC;MACnB0M,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8a,aAAa,EAAE,CAAC;MAChBlG,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMgb,uBAAuB,CAAC;EAC1B5H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC3H,cAAc,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;IACjD,IAAI,CAACzC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmF,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC8M,IAAI,GAAG,KAAK;IACjB,IAAI,CAACC,MAAM,GAAG,KAAK;EACvB;EACA7C,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE7M;IAAe,CAAC,GAAG6M,OAAO;IAClC,IAAI7M,cAAc,EAAE;MAChB,IAAI,CAACwP,IAAI,GAAG,IAAI,CAACxP,cAAc,CAAC0P,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;MACxD,IAAI,CAACD,MAAM,GAAG,IAAI,CAACzP,cAAc,CAAC0P,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;IAC/D;EACJ;EACA;IAAS,IAAI,CAAChH,IAAI,YAAAiH,gCAAA/G,CAAA;MAAA,YAAAA,CAAA,IAAwF2G,uBAAuB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACvG,IAAI,kBA3c8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EA2cJqG,uBAAuB;MAAApG,SAAA;MAAA+D,SAAA;MAAAvD,MAAA;QAAA3J,cAAA;QAAAzC,SAAA;QAAAmF,eAAA;MAAA;MAAAqH,UAAA;MAAAC,QAAA,GA3crBhW,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqF,iCAAA5W,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAqF,cAAA,aA4c9D,CAAC;UA5c2DrF,EAAE,CAAAwG,UAAA,IAAA4C,8CAAA,wBA4cZ,CAAC;UA5cSpJ,EAAE,CAAA+F,YAAA,CA4cS,CAAC;UA5cZ/F,EAAE,CAAAqF,cAAA,aA6cP,CAAC,aAC/C,CAAC;UA9ckDrF,EAAE,CAAAwG,UAAA,IAAA6C,uCAAA,iBAqd9F,CAAC,IAAAG,uCAAA,iBAOD,CAAC;UA5d2FxJ,EAAE,CAAA+F,YAAA,CA6d1F,CAAC,CACH,CAAC;QAAA;QAAA,IAAAf,EAAA;UA9dyFhF,EAAE,CAAA0G,SAAA,CA4cb,CAAC;UA5cU1G,EAAE,CAAAgG,UAAA,qBAAAf,GAAA,CAAAyJ,eA4cb,CAAC;UA5cU1O,EAAE,CAAA0G,SAAA,CA6cR,CAAC;UA7cK1G,EAAE,CAAAuI,WAAA,iCAAAtD,GAAA,CAAAwW,MAAA,IAAAxW,GAAA,CAAAuW,IA6cR,CAAC;UA7cKxb,EAAE,CAAA0G,SAAA,EAkdlF,CAAC;UAld+E1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAuW,IAkdlF,CAAC;UAld+Exb,EAAE,CAAA0G,SAAA,CAydhF,CAAC;UAzd6E1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAwW,MAydhF,CAAC;QAAA;MAAA;MAAA9E,YAAA,GAMuChT,YAAY,EAA+BD,IAAI,CAAC2V,eAAe,EAAiKtW,gBAAgB,EAAoJC,IAAI;MAAA4T,aAAA;MAAAC,eAAA;IAAA,EAAkK;EAAE;AAC1mB;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAjeoG/W,EAAE,CAAAgX,iBAAA,CAieXuE,uBAAuB,EAAc,CAAC;IACrHrG,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BE,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAA2B,CAAC;MAC3CzC,OAAO,EAAE,CAAC5T,YAAY,EAAEZ,gBAAgB,EAAEC,IAAI,CAAC;MAC/C+S,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE/J,cAAc,EAAE,CAAC;MACzDkJ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEiJ,SAAS,EAAE,CAAC;MACZ2L,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoO,eAAe,EAAE,CAAC;MAClBwG,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMub,oBAAoB,CAAC;EACvBC,gBAAgBA,CAACC,QAAQ,EAAE;IACvB,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC7H,aAAa,EAAE,MAAM,EAAE0H,QAAQ,CAAC;EAC3E;EACAI,iBAAiBA,CAACC,SAAS,EAAE;IACzB,IAAI,CAACJ,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,UAAU,CAAC7H,aAAa,EAAE,OAAO,EAAE+H,SAAS,CAAC;EAC7E;EACAC,eAAeA,CAACC,YAAY,EAAE;IAC1B,IAAI,CAACC,WAAW,CAACD,YAAY,EAAE,gCAAgC,CAAC;EACpE;EACAE,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACF,WAAW,CAACE,UAAU,EAAE,8BAA8B,CAAC;EAChE;EACAF,WAAWA,CAACG,IAAI,EAAEC,SAAS,EAAE;IACzB;IACA,IAAI,CAACX,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACV,UAAU,CAAC7H,aAAa,EAAEsI,SAAS,CAAC;IACnE,IAAID,IAAI,EAAE;MACN,IAAI,CAACV,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACX,UAAU,CAAC7H,aAAa,EAAEsI,SAAS,CAAC;IACpE;EACJ;EACAhJ,WAAWA,CAACqI,QAAQ,EAAEE,UAAU,EAAE;IAC9B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACY,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,QAAQ,GAAG,IAAI/b,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACgc,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA3E,WAAWA,CAAA,EAAG;IACV,IAAI,CAACyD,eAAe,CAAC,KAAK,CAAC;IAC3B,IAAI,CAACG,aAAa,CAAC,KAAK,CAAC;IACzB,IAAI,CAACW,UAAU,GAAG,IAAI,CAACJ,MAAM,KAAK,EAAE,IAAI,IAAI,CAACA,MAAM,KAAK,IAAI;IAC5D,IAAI,CAACK,WAAW,GAAG,IAAI,CAACN,OAAO,KAAK,EAAE,IAAI,IAAI,CAACA,OAAO,KAAK,IAAI;IAC/D,IAAI,CAACO,WAAW,GAAG,IAAI,CAACN,MAAM,KAAK,KAAK;IACxC,IAAI,CAACO,YAAY,GAAG,IAAI,CAACR,OAAO,KAAK,KAAK;IAC1C,IAAI,CAACS,OAAO,GAAG,IAAI,CAACF,WAAW,IAAI,IAAI,CAACC,YAAY;IACpD,MAAME,UAAU,GAAI3F,KAAK,IAAK;MAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;QAC3C,OAAOA,KAAK;MAChB,CAAC,MACI;QACD,OAAO,IAAI;MACf;IACJ,CAAC;IACD,IAAI,CAACiE,gBAAgB,CAAC0B,UAAU,CAAC,IAAI,CAACT,MAAM,CAAC,CAAC;IAC9C,IAAI,CAACZ,iBAAiB,CAACqB,UAAU,CAAC,IAAI,CAACV,OAAO,CAAC,CAAC;IAChD,IAAI,CAACI,QAAQ,CAAC5J,IAAI,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAACoB,IAAI,YAAA+I,6BAAA7I,CAAA;MAAA,YAAAA,CAAA,IAAwFiH,oBAAoB,EAnkB9B7b,EAAE,CAAA6U,iBAAA,CAmkB8C7U,EAAE,CAAC0d,SAAS,GAnkB5D1d,EAAE,CAAA6U,iBAAA,CAmkBuE7U,EAAE,CAACE,UAAU;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAACqa,IAAI,kBApkB8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAokBJ2G,oBAAoB;MAAA1G,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAiD,kCAAA3Y,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApkBlBhF,EAAE,CAAAsM,WAAA,aAAArH,GAAA,CAAAsY,OAAA,GAokBK,QAAQ,GAAG,IAAD,CAAC;UApkBlBvd,EAAE,CAAAuI,WAAA,6BAAAtD,GAAA,CAAAqY,YAokBe,CAAC,4BAAArY,GAAA,CAAAoY,WAAD,CAAC;QAAA;MAAA;MAAA1H,MAAA;QAAAmH,OAAA;QAAAC,MAAA;QAAAC,OAAA;QAAAC,OAAA;MAAA;MAAAlH,UAAA;MAAAC,QAAA,GApkBlBhW,EAAE,CAAAmZ,oBAAA;IAAA,EAokByY;EAAE;AACjf;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KAtkBoG/W,EAAE,CAAAgX,iBAAA,CAskBX6E,oBAAoB,EAAc,CAAC;IAClH3G,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+CAA+C;MACzD6C,IAAI,EAAE;QACF,kCAAkC,EAAG,cAAa;QAClD,iCAAiC,EAAG,aAAY;QAChD,kBAAkB,EAAG;MACzB,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,EAAE;IAAExI,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,CAAC,EAAkB;IAAE4c,OAAO,EAAE,CAAC;MACjG5H,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyc,MAAM,EAAE,CAAC;MACT7H,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE0c,OAAO,EAAE,CAAC;MACV9H,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2c,OAAO,EAAE,CAAC;MACV/H,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMsd,mBAAmB,CAAC;EACtBC,gBAAgBA,CAACtH,QAAQ,EAAE;IACvB,IAAI,CAACuH,cAAc,CAACxK,IAAI,CAACiD,QAAQ,CAAC;EACtC;EACAwH,aAAaA,CAACC,UAAU,EAAE;IACtB,IAAI,CAACC,WAAW,CAAC3K,IAAI,CAAC0K,UAAU,CAAC;EACrC;EACAE,cAAcA,CAACC,WAAW,EAAE;IACxB,IAAI,CAACC,YAAY,CAAC9K,IAAI,CAAC6K,WAAW,CAAC;EACvC;EACAE,mBAAmBA,CAACC,WAAW,EAAE;IAC7B,IAAI,CAACC,mBAAmB,CAACjL,IAAI,CAACgL,WAAW,CAAC;EAC9C;EACAE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAIC,WAAW,GAAG,CAAC;IACnBD,QAAQ,CAACE,OAAO,CAACC,EAAE,IAAI;MACnBF,WAAW,IAAKE,EAAE,CAAC5B,OAAO,IAAI,CAAC4B,EAAE,CAAC5B,OAAO,IAAM4B,EAAE,CAAC3B,OAAO,IAAI,CAAC2B,EAAE,CAAC3B,OAAQ,IAAI,CAAC;IAClF,CAAC,CAAC;IACF,MAAM4B,UAAU,GAAGJ,QAAQ,CAAC9c,GAAG,CAACiW,IAAI,IAAIA,IAAI,CAACkH,OAAO,CAAC;IACrD,IAAI,CAACC,YAAY,CAACzL,IAAI,CAACoL,WAAW,CAAC;IACnC,IAAI,CAACM,sBAAsB,CAAC1L,IAAI,CAACuL,UAAU,CAAC;EAChD;EACAI,sBAAsBA,CAACR,QAAQ,EAAE;IAC7B,MAAMS,UAAU,GAAG,EAAE;IACrBT,QAAQ,CAACE,OAAO,CAACC,EAAE,IAAI;MACnB,MAAM/Q,MAAM,GAAI+Q,EAAE,CAAC5B,OAAO,IAAI,CAAC4B,EAAE,CAAC5B,OAAO,IAAM4B,EAAE,CAAC3B,OAAO,IAAI,CAAC2B,EAAE,CAAC3B,OAAQ,IAAI,CAAC;MAC9E,KAAK,IAAIkC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGtR,MAAM,EAAEsR,CAAC,EAAE,EAAE;QAC7BD,UAAU,CAACE,IAAI,CAAE,eAAcD,CAAE,EAAC,CAAC;MACvC;IACJ,CAAC,CAAC;IACF,IAAI,CAACE,oBAAoB,CAAC/L,IAAI,CAAC4L,UAAU,CAAC;EAC9C;EACAI,kBAAkBA,CAACC,eAAe,EAAE;IAChC,IAAI,CAACC,kBAAkB,CAAClM,IAAI,CAACiM,eAAe,CAAC5d,GAAG,CAAC8d,KAAK,IAAK,GAAEA,KAAM,IAAG,CAAC,CAAC;EAC5E;EACAC,YAAYA,CAACC,SAAS,EAAE;IACpB,IAAI,CAACC,UAAU,CAACtM,IAAI,CAACqM,SAAS,CAAC;EACnC;EACAE,WAAWA,CAACC,QAAQ,EAAE;IAClB,IAAI,CAAC/R,SAAS,CAACuF,IAAI,CAACwM,QAAQ,CAAC;EACjC;EACAC,SAASA,CAACvR,OAAO,EAAEW,OAAO,EAAE;IACxB,MAAM6Q,iBAAiB,GAAG,CAAC,EAAExR,OAAO,IAAIW,OAAO,CAAC;IAChD,IAAI,CAAC6Q,iBAAiB,EAAE;MACpB,IAAI,CAACV,kBAAkB,CAAC,EAAE,CAAC;IAC/B;IACA,IAAI,CAACW,kBAAkB,CAAC3M,IAAI,CAAC0M,iBAAiB,CAAC;EACnD;EACArM,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmK,cAAc,GAAG,IAAI1c,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC6c,WAAW,GAAG,IAAI7c,aAAa,CAAC,CAAC,CAAC;IACvC,IAAI,CAACgd,YAAY,GAAG,IAAIhd,aAAa,CAAC,CAAC,CAAC;IACxC,IAAI,CAAC8L,UAAU,GAAG,IAAI9L,aAAa,CAAC,CAAC,CAAC;IACtC,IAAI,CAAC2d,YAAY,GAAG,IAAI3d,aAAa,CAAC,CAAC,CAAC;IACxC,IAAI,CAACwe,UAAU,GAAG,IAAIxe,aAAa,CAAC,CAAC,CAAC;IACtC,IAAI,CAAC2M,SAAS,GAAG,IAAI3M,aAAa,CAAC,CAAC,CAAC;IACrC,IAAI,CAAC4d,sBAAsB,GAAG,IAAI3d,eAAe,CAAC,EAAE,CAAC;IACrD,IAAI,CAACkd,mBAAmB,GAAG,IAAIld,eAAe,CAAC,EAAE,CAAC;IAClD,IAAI,CAAC6e,oBAAoB,GAAG5e,aAAa,CAAC,CAAC,IAAI,CAACid,mBAAmB,EAAE,IAAI,CAACS,sBAAsB,CAAC,CAAC,CAAC1K,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAAC2c,WAAW,EAAE6B,WAAW,CAAC,KAAM7B,WAAW,CAACzQ,MAAM,GAAGyQ,WAAW,GAAG6B,WAAY,CAAC,CAAC;IAC9L,IAAI,CAACX,kBAAkB,GAAG,IAAIpe,aAAa,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACgf,sBAAsB,GAAG7e,KAAK,EACnC;IACA,IAAI,CAAC2e,oBAAoB,EAAE5e,aAAa,CAAC,CAAC,IAAI,CAACke,kBAAkB,EAAE,IAAI,CAACU,oBAAoB,CAAC,CAAC,CAAC5L,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAAC0e,SAAS,EAAEC,WAAW,CAAC,KAAK;MAClI;MACA,IAAID,SAAS,CAACxS,MAAM,KAAKyS,WAAW,CAACzS,MAAM,EAAE;QACzC,OAAOwS,SAAS,CAAC1e,GAAG,CAAC,CAAC8d,KAAK,EAAEpR,KAAK,KAAK;UACnC,IAAIoR,KAAK,KAAK,KAAK,EAAE;YACjB,OAAOa,WAAW,CAACjS,KAAK,CAAC,IAAI,IAAI;UACrC,CAAC,MACI;YACD,OAAOiS,WAAW,CAACjS,KAAK,CAAC,IAAIoR,KAAK;UACtC;QACJ,CAAC,CAAC;MACN,CAAC,MACI;QACD,OAAOa,WAAW;MACtB;IACJ,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACjB,oBAAoB,GAAG,IAAIje,aAAa,CAAC,CAAC,CAAC;IAChD,IAAI,CAACmf,oBAAoB,GAAG,IAAI,CAACf,kBAAkB,CAAClL,IAAI,CAAC3S,GAAG,CAAC6e,IAAI,IAAIA,IAAI,CAAC7e,GAAG,CAAC8d,KAAK,IAAIgB,QAAQ,CAAChB,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7G,IAAI,CAACQ,kBAAkB,GAAG,IAAI7e,aAAa,CAAC,CAAC,CAAC;EAClD;EACA;IAAS,IAAI,CAACsT,IAAI,YAAAgM,4BAAA9L,CAAA;MAAA,YAAAA,CAAA,IAAwFgJ,mBAAmB;IAAA,CAAoD;EAAE;EACnL;IAAS,IAAI,CAAC+C,KAAK,kBAlrB6E3gB,EAAE,CAAA4gB,kBAAA;MAAAC,KAAA,EAkrBYjD,mBAAmB;MAAAkD,OAAA,EAAnBlD,mBAAmB,CAAAlJ;IAAA,EAAG;EAAE;AAC1I;AACA;EAAA,QAAAqC,SAAA,oBAAAA,SAAA,KAprBoG/W,EAAE,CAAAgX,iBAAA,CAorBX4G,mBAAmB,EAAc,CAAC;IACjH1I,IAAI,EAAExU;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAMqgB,oBAAoB,CAAC;EACvBpN,WAAWA,CAACqN,mBAAmB,EAAE;IAC7B,IAAI,CAACpT,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACA,aAAa,GAAG,CAAC,CAACoT,mBAAmB;EAC9C;EACA;IAAS,IAAI,CAACtM,IAAI,YAAAuM,6BAAArM,CAAA;MAAA,YAAAA,CAAA,IAAwFmM,oBAAoB,EAjsB9B/gB,EAAE,CAAA6U,iBAAA,CAisB8C+I,mBAAmB;IAAA,CAA4D;EAAE;EACjO;IAAS,IAAI,CAACrD,IAAI,kBAlsB8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAksBJ6L,oBAAoB;MAAA5L,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAwG,kCAAAlc,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAlsBlBhF,EAAE,CAAAuI,WAAA,mBAAAtD,GAAA,CAAA2I,aAksBe,CAAC;QAAA;MAAA;MAAAmI,UAAA;IAAA,EAAoM;EAAE;AAC5T;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KApsBoG/W,EAAE,CAAAgX,iBAAA,CAosBX+J,oBAAoB,EAAc,CAAC;IAClH7L,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,gFAAgF;MAC1F6C,IAAI,EAAE;QACF,wBAAwB,EAAE;MAC9B,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE0I,mBAAmB;IAAEuD,UAAU,EAAE,CAAC;MACzDjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA,MAAMygB,kBAAkB,CAAC;EACrBC,cAAcA,CAACC,IAAI,EAAE;IACjB,IAAI,CAACC,SAAS,CAACjO,IAAI,CAACgO,IAAI,CAAC;EAC7B;EACAE,qBAAqBA,CAACC,UAAU,EAAE;IAC9B,IAAI,CAACC,gBAAgB,CAACpO,IAAI,CAACmO,UAAU,CAAC;EAC1C;EACAE,eAAeA,CAACtT,KAAK,EAAE;IACnB,IAAI,CAACuT,UAAU,CAACtO,IAAI,CAACjF,KAAK,CAAC;EAC/B;EACAwT,gBAAgBA,CAACrB,IAAI,EAAE;IACnB,IAAI,CAACsB,WAAW,CAACxO,IAAI,CAACkN,IAAI,CAAC;EAC/B;EACAuB,wBAAwBA,CAACvB,IAAI,EAAE;IAC3B,IAAI,CAACwB,mBAAmB,CAAC1O,IAAI,CAACkN,IAAI,CAAC;EACvC;EACA7M,WAAWA,CAAA,EAAG;IACV,IAAI,CAACG,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACygB,UAAU,GAAG,IAAIvgB,eAAe,CAAC,CAAC,CAAC;IACxC,IAAI,CAACqgB,gBAAgB,GAAG,IAAIrgB,eAAe,CAAC,IAAI,CAAC;IACjD,IAAI,CAACkgB,SAAS,GAAG,IAAIlgB,eAAe,CAAC,EAAE,CAAC;IACxC,IAAI,CAACygB,WAAW,GAAG,IAAIzgB,eAAe,CAAC,EAAE,CAAC;IAC1C,IAAI,CAAC2gB,mBAAmB,GAAG,IAAI3gB,eAAe,CAAC,EAAE,CAAC;IAClD,IAAI,CAAC4gB,kBAAkB,GAAG,IAAI,CAACL,UAAU,CAACtN,IAAI,CAAC1S,oBAAoB,CAAC,CAAC,CAAC;IACtE,IAAI,CAACsgB,iBAAiB,GAAG,IAAI,CAACX,SAAS,CAACjN,IAAI,CAAC1S,oBAAoB,CAAC,CAAC,CAAC;IACpE,IAAI,CAACugB,mBAAmB,GAAG,IAAI9gB,eAAe,CAAC,EAAE,CAAC;IAClD,IAAI,CAAC+gB,YAAY,GAAG9gB,aAAa,CAAC,CAC9B,IAAI,CAAC2gB,kBAAkB,EACvB,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACC,mBAAmB,CAC3B,CAAC,CAAC7N,IAAI,CAACzS,YAAY,CAAC,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC,CAAC,EAAEH,GAAG,CAAC,CAAC,CAAC0gB,SAAS,EAAEC,QAAQ,EAAEC,UAAU,CAAC,MAAM;MAC1EF,SAAS;MACTC,QAAQ;MACRE,IAAI,EAAED,UAAU,CACXxgB,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAAC6K,MAAM,CAAC,CAC3B9gB,GAAG,CAACiW,IAAI,KAAK;QACd8K,GAAG,EAAE9K,IAAI,CAAC8K,GAAG;QACb7K,KAAK,EAAED,IAAI,CAACrO;MAChB,CAAC,CAAC,CAAC;MACHxH,MAAM,EAAEwgB,UAAU,CACbxgB,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAAC+K,QAAQ,CAAC,CAC7BhhB,GAAG,CAACiW,IAAI,KAAK;QACd8K,GAAG,EAAE9K,IAAI,CAAC8K,GAAG;QACb7K,KAAK,EAAED,IAAI,CAACgL;MAChB,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC;IACJ,IAAI,CAACC,oBAAoB,GAAGvhB,aAAa,CAAC,CAAC,IAAI,CAACwgB,WAAW,EAAE,IAAI,CAACK,mBAAmB,CAAC,CAAC,CAAC7N,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAACmhB,UAAU,EAAEC,kBAAkB,CAAC,KAAK;MACnI,IAAIC,mBAAmB,GAAG,CAAC,GAAGF,UAAU,CAAC;MACzC,MAAMG,oBAAoB,GAAGF,kBAAkB,CAAChhB,MAAM,CAAC6V,IAAI,IAAI;QAC3D,MAAM;UAAEgL,WAAW;UAAED;QAAS,CAAC,GAAG/K,IAAI;QACtC,MAAMsL,OAAO,GAAGN,WAAW,KAAK,IAAI,IAChCA,WAAW,KAAKO,SAAS,IACxBC,KAAK,CAACC,OAAO,CAACT,WAAW,CAAC,IAAIA,WAAW,CAAC/U,MAAM,KAAK,CAAE;QAC5D,OAAO,CAACqV,OAAO,IAAI,OAAOP,QAAQ,KAAK,UAAU;MACrD,CAAC,CAAC;MACF,KAAK,MAAM/K,IAAI,IAAIqL,oBAAoB,EAAE;QACrC,MAAM;UAAEN,QAAQ;UAAEC;QAAY,CAAC,GAAGhL,IAAI;QACtCoL,mBAAmB,GAAGA,mBAAmB,CAACjhB,MAAM,CAACmN,IAAI,IAAIyT,QAAQ,CAACC,WAAW,EAAE1T,IAAI,CAAC,CAAC;MACzF;MACA,MAAMoU,kBAAkB,GAAGP,kBAAkB,CACxChhB,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAACrO,SAAS,KAAK,IAAI,IAAI,OAAOqO,IAAI,CAAC6K,MAAM,KAAK,UAAU,CAAC,CAC5ED,IAAI,CAAC,CAACe,CAAC,EAAEC,CAAC,KAAK,CAACA,CAAC,CAACC,YAAY,GAAG,CAACF,CAAC,CAACE,YAAY,CAAC;MACtD,IAAIV,kBAAkB,CAAClV,MAAM,EAAE;QAC3BmV,mBAAmB,CAACR,IAAI,CAAC,CAACkB,OAAO,EAAEC,OAAO,KAAK;UAC3C,KAAK,MAAM/L,IAAI,IAAI0L,kBAAkB,EAAE;YACnC,MAAM;cAAEb,MAAM;cAAElZ;YAAU,CAAC,GAAGqO,IAAI;YAClC,IAAI6K,MAAM,IAAIlZ,SAAS,EAAE;cACrB,MAAMqa,aAAa,GAAGnB,MAAM,CAACiB,OAAO,EAAEC,OAAO,EAAEpa,SAAS,CAAC;cACzD,IAAIqa,aAAa,KAAK,CAAC,EAAE;gBACrB,OAAOra,SAAS,KAAK,QAAQ,GAAGqa,aAAa,GAAG,CAACA,aAAa;cAClE;YACJ;UACJ;UACA,OAAO,CAAC;QACZ,CAAC,CAAC;MACN;MACA,OAAOZ,mBAAmB;IAC9B,CAAC,CAAC,CAAC;IACH,IAAI,CAACa,8BAA8B,GAAGviB,aAAa,CAAC,CAChD,IAAI,CAAC2gB,kBAAkB,EACvB,IAAI,CAACC,iBAAiB,EACtB,IAAI,CAACW,oBAAoB,CAC5B,CAAC,CAACvO,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,EAAE/R,MAAM,CAAC8V,KAAK,IAAI;MAC9C,MAAM,CAACwK,SAAS,EAAEC,QAAQ,EAAEQ,UAAU,CAAC,GAAGjL,KAAK;MAC/C,MAAMiM,YAAY,GAAGC,IAAI,CAACC,IAAI,CAAClB,UAAU,CAACjV,MAAM,GAAGyU,QAAQ,CAAC,IAAI,CAAC;MACjE,OAAOD,SAAS,IAAIyB,YAAY;IACpC,CAAC,CAAC,EAAEniB,GAAG,CAAC,CAAC,CAAC0gB,SAAS,EAAEC,QAAQ,EAAEQ,UAAU,CAAC,KAAKA,UAAU,CAACmB,KAAK,CAAC,CAAC5B,SAAS,GAAG,CAAC,IAAIC,QAAQ,EAAED,SAAS,GAAGC,QAAQ,CAAC,CAAC,CAAC;IACnH,IAAI,CAAC4B,sBAAsB,GAAG,IAAI,CAACxC,gBAAgB,CAACpN,IAAI,CAACtS,SAAS,CAACyf,UAAU,IAAKA,UAAU,GAAG,IAAI,CAACoC,8BAA8B,GAAG,IAAI,CAAChB,oBAAqB,CAAC,CAAC;IACjK,IAAI,CAACsB,MAAM,GAAG,IAAI,CAACzC,gBAAgB,CAACpN,IAAI,CAACtS,SAAS,CAACyf,UAAU,IAAKA,UAAU,GAAG,IAAI,CAACoB,oBAAoB,GAAG,IAAI,CAACf,WAAY,CAAC,EAAEngB,GAAG,CAAC6e,IAAI,IAAIA,IAAI,CAAC3S,MAAM,CAAC,EAAEjM,oBAAoB,CAAC,CAAC,CAAC;EACpL;EACAkX,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAA0P,2BAAAxP,CAAA;MAAA,YAAAA,CAAA,IAAwFwM,kBAAkB;IAAA,CAAoD;EAAE;EAClL;IAAS,IAAI,CAACT,KAAK,kBApzB6E3gB,EAAE,CAAA4gB,kBAAA;MAAAC,KAAA,EAozBYO,kBAAkB;MAAAN,OAAA,EAAlBM,kBAAkB,CAAA1M;IAAA,EAAG;EAAE;AACzI;AACA;EAAA,QAAAqC,SAAA,oBAAAA,SAAA,KAtzBoG/W,EAAE,CAAAgX,iBAAA,CAszBXoK,kBAAkB,EAAc,CAAC;IAChHlM,IAAI,EAAExU;EACV,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAM2jB,uBAAuB,CAAC;EAC1B1Q,WAAWA,CAAC2Q,EAAE,EAAEtI,QAAQ,EAAEuI,kBAAkB,EAAE;IAC1C,IAAI,CAACD,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACtI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuI,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC1Q,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;EACjC;EACA+S,QAAQA,CAAA,EAAG;IACP,IAAI,CAACqQ,kBAAkB,CAACvC,mBAAmB,CAAC1N,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACqD,IAAI,IAAI;MACzF,IAAIA,IAAI,CAAC/J,MAAM,EAAE;QACb+J,IAAI,CAAC+G,OAAO,CAAC,CAAC8F,CAAC,EAAEtF,CAAC,KAAK;UACnB,IAAIsF,CAAC,CAAC5M,KAAK,KAAK,IAAI,CAAC2M,aAAa,EAAE;YAChC,IAAI,CAACC,CAAC,CAACC,OAAO,EAAE;cACZ,IAAI,CAAC1I,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACqI,EAAE,CAACjQ,aAAa,EAAE,SAAS,EAAE,MAAM,CAAC;YACpE,CAAC,MACI;cACD,IAAI,CAAC2H,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACqI,EAAE,CAACjQ,aAAa,EAAE,SAAS,EAAE,OAAO,CAAC;YACrE;YACA,IAAI,CAAC2H,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACqI,EAAE,CAACjQ,aAAa,EAAE,OAAO,EAAE8K,CAAC,CAAC;YACzD,IAAI,CAACsF,CAAC,EAAEE,QAAQ,EAAE;cACd,IAAI,CAAC3I,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACqI,EAAE,CAACjQ,aAAa,EAAE,MAAM,EAAG,OAAMoQ,CAAC,CAAChF,KAAM,IAAG,CAAC;YAC7E,CAAC,MACI;cACD,IAAI,CAACzD,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACqI,EAAE,CAACjQ,aAAa,EAAE,MAAM,EAAG,OAAMoQ,CAAC,CAAChF,KAAM,IAAG,CAAC;YAC7E;UACJ;QACJ,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA3G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAkQ,gCAAAhQ,CAAA;MAAA,YAAAA,CAAA,IAAwFyP,uBAAuB,EAj2BjCrkB,EAAE,CAAA6U,iBAAA,CAi2BiD7U,EAAE,CAACE,UAAU,GAj2BhEF,EAAE,CAAA6U,iBAAA,CAi2B2E7U,EAAE,CAAC0d,SAAS,GAj2BzF1d,EAAE,CAAA6U,iBAAA,CAi2BoGuM,kBAAkB;IAAA,CAA4C;EAAE;EACtQ;IAAS,IAAI,CAAC7G,IAAI,kBAl2B8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAk2BJmP,uBAAuB;MAAAlP,SAAA;MAAAQ,MAAA;QAAA6O,aAAA;MAAA;MAAAzO,UAAA;IAAA,EAAkI;EAAE;AAC7P;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAp2BoG/W,EAAE,CAAAgX,iBAAA,CAo2BXqN,uBAAuB,EAAc,CAAC;IACrHnP,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qCAAqC;MAC/CnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,EAAE;IAAEgV,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,EAAE;IAAExI,IAAI,EAAEkM;EAAmB,CAAC,CAAC,EAAkB;IAAEoD,aAAa,EAAE,CAAC;MACrItP,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMukB,kBAAkB,CAAC;EACrBlR,WAAWA,CAAA,EAAG;IACV,IAAI,CAACjJ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACP,YAAY,GAAG,CAAC;IACrB,IAAI,CAACP,YAAY,GAAG,KAAK;IACzB,IAAI,CAACgb,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACjb,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACI,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC8a,eAAe,GAAG,IAAI9kB,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC+kB,cAAc,GAAG,IAAI/kB,YAAY,CAAC,CAAC;IACxC,IAAI,CAACglB,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACxC;EACA5c,eAAeA,CAACrC,OAAO,EAAE;IACrB,IAAI,CAACyE,SAAS,GAAGzE,OAAO;IACxB,IAAI,CAAC8e,eAAe,CAAC3M,IAAI,CAACnS,OAAO,CAAC;EACtC;EACA2D,cAAcA,CAACsQ,MAAM,EAAE;IACnB,IAAI,CAACrQ,QAAQ,GAAGqQ,MAAM;IACtB,IAAI,CAAC8K,cAAc,CAAC5M,IAAI,CAAC8B,MAAM,CAAC;EACpC;EACAtB,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMsM,aAAa,GAAItN,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACuN,WAAW,IAAIvN,KAAK,CAACwN,YAAY,KAAKlC,SAAS;IAC/F,MAAM;MAAEtZ,QAAQ;MAAEa,SAAS;MAAEZ,YAAY;MAAEgb;IAAe,CAAC,GAAGjM,OAAO;IACrE,IAAI/O,YAAY,EAAE;MACd,IAAI,CAACmb,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACI,uBAAuB,GAAG,IAAI;IACvC;IACA,IAAIC,aAAa,CAACtb,QAAQ,CAAC,IAAI,CAAC,IAAI,CAACob,qBAAqB,EAAE;MACxD,IAAI,CAACnb,YAAY,GAAG,IAAI;IAC5B;IACA,IAAIqb,aAAa,CAACza,SAAS,CAAC,IAAI,CAAC,IAAI,CAACwa,uBAAuB,EAAE;MAC3D,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;EACJ;EACA;IAAS,IAAI,CAACpQ,IAAI,YAAA4Q,2BAAA1Q,CAAA;MAAA,YAAAA,CAAA,IAAwFiQ,kBAAkB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAAC7P,IAAI,kBAv5B8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAu5BJ2P,kBAAkB;MAAA1P,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAA6K,gCAAAvgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAv5BhBhF,EAAE,CAAAuI,WAAA,+BAAAtD,GAAA,CAAA6E,YAAA,IAAA7E,GAAA,CAAAoF,YAAA,GAu5B2B,CAAd,CAAC,+BAAApF,GAAA,CAAA6f,cAAD,CAAC;QAAA;MAAA;MAAAnP,MAAA;QAAAjL,SAAA;QAAAD,UAAA;QAAAE,eAAA;QAAAC,OAAA;QAAAP,YAAA;QAAAP,YAAA;QAAAgb,cAAA;QAAAjb,QAAA;QAAAI,YAAA;MAAA;MAAA4L,OAAA;QAAAkP,eAAA;QAAAC,cAAA;MAAA;MAAAjP,UAAA;MAAAC,QAAA,GAv5BhBhW,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAA/b,GAAA;MAAA0M,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAkP,4BAAAzgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAwG,UAAA,IAAA0D,0CAAA,yBAw5B5C,CAAC,IAAAI,mCAAA,kBAsBvD,CAAC;UA96B+FtK,EAAE,CAAA8L,YAAA,EA+6B1E,CAAC;QAAA;QAAA,IAAA9G,EAAA;UA/6BuEhF,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAA6E,YAAA,IAAA7E,GAAA,CAAAoF,YAAA,IAw5B9C,CAAC;UAx5B2CrK,EAAE,CAAA0G,SAAA,CAw6B5E,CAAC;UAx6ByE1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAA6f,cAw6B5E,CAAC;QAAA;MAAA;MAAAnO,YAAA,GAQoCmE,oBAAoB,EAAkFb,0BAA0B,EAAqIjX,IAAI,EAA6FD,gBAAgB,EAAmJU,gBAAgB,EAA+BD,EAAE,CAACiW,mBAAmB,EAA2MpW,WAAW,EAA+BD,EAAE,CAACsW,eAAe,EAAsFtW,EAAE,CAACuW,OAAO;MAAA/C,aAAA;MAAAC,eAAA;IAAA,EAAmR;EAAE;AACzwC;AACA5V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEqiB,kBAAkB,CAAC/N,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AACxD7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEqiB,kBAAkB,CAAC/N,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC1D7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEqiB,kBAAkB,CAAC/N,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AACpD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA37BoG/W,EAAE,CAAAgX,iBAAA,CA27BX6N,kBAAkB,EAAc,CAAC;IAChH3P,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0HAA0H;MACpIL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CC,mBAAmB,EAAE,KAAK;MAC1BR,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,oCAAoC,EAAG,kCAAiC;QACxE,oCAAoC,EAAG;MAC3C,CAAC;MACDxC,OAAO,EAAE,CAACuD,oBAAoB,EAAEb,0BAA0B,EAAEjX,IAAI,EAAED,gBAAgB,EAAEU,gBAAgB,EAAEJ,WAAW,CAAC;MAClH0S,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAErL,SAAS,EAAE,CAAC;MAC1BwK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmK,UAAU,EAAE,CAAC;MACbyK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEqK,eAAe,EAAE,CAAC;MAClBuK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsK,OAAO,EAAE,CAAC;MACVsK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+J,YAAY,EAAE,CAAC;MACf6K,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwJ,YAAY,EAAE,CAAC;MACfoL,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwkB,cAAc,EAAE,CAAC;MACjB5P,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEuJ,QAAQ,EAAE,CAAC;MACXqL,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2J,YAAY,EAAE,CAAC;MACfiL,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEykB,eAAe,EAAE,CAAC;MAClB7P,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAEykB,cAAc,EAAE,CAAC;MACjB9P,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMmlB,kBAAkB,CAAC;EACrBC,oBAAoBA,CAAC3Z,cAAc,EAAE4Z,OAAO,EAAE;IAC1C,MAAMvX,KAAK,GAAGrC,cAAc,CAAC0P,OAAO,CAACkK,OAAO,CAAC;IAC7C,IAAIvX,KAAK,KAAKrC,cAAc,CAAC6B,MAAM,GAAG,CAAC,EAAE;MACrC,OAAO7B,cAAc,CAAC,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,OAAOA,cAAc,CAACqC,KAAK,GAAG,CAAC,CAAC;IACpC;EACJ;EACAwX,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAACC,gBAAgB,CAACzS,IAAI,CAACwS,KAAK,CAAC;EACrC;EACAE,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACzc,SAAS,KAAK,IAAI,EAAE;MACzB,IAAI,CAACsc,YAAY,CAAC,IAAI,CAAC;IAC3B;EACJ;EACA3a,mBAAmBA,CAAC2M,KAAK,EAAE;IACvB,IAAI,CAACoO,cAAc,CAAC7N,IAAI,CAACP,KAAK,CAAC;IAC/B,IAAI,CAACqO,aAAa,GAAGrO,KAAK;IAC1B,IAAI,CAACsO,kBAAkB,CAAC,CAAC;EAC7B;EACAA,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,mBAAmB,CAAC9S,IAAI,CAAC,CAAC;EACnC;EACAK,WAAWA,CAACoG,IAAI,EAAEvG,GAAG,EAAEK,MAAM,EAAEC,QAAQ,EAAE;IACrC,IAAI,CAACiG,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACvG,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACuS,iBAAiB,GAAG,IAAIllB,OAAO,CAAC,CAAC;IACtC,IAAI,CAACilB,mBAAmB,GAAG,IAAIjlB,OAAO,CAAC,CAAC;IACxC,IAAI,CAAC+kB,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC3c,SAAS,GAAG,IAAI;IACrB,IAAI,CAACyC,cAAc,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;IACjD,IAAI,CAAC+Z,gBAAgB,GAAG,IAAI5kB,OAAO,CAAC,CAAC;IACrC,IAAI,CAACmlB,mBAAmB,GAAG,KAAK;IAChC,IAAI,CAACC,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACjb,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACkb,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,gBAAgB,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC;IACnD,IAAI,CAACnb,SAAS,GAAG,EAAE;IACnB,IAAI,CAACob,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAAChb,UAAU,GAAG,KAAK;IACvB,IAAI,CAACib,YAAY,GAAG,KAAK;IACzB,IAAI,CAACxb,cAAc,GAAG,KAAK;IAC3B,IAAI,CAAC0Z,eAAe,GAAG,IAAI9kB,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC6mB,iBAAiB,GAAG,IAAI7mB,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACgmB,cAAc,GAAG,IAAIhmB,YAAY,CAAC,CAAC;EAC5C;EACAiU,QAAQA,CAAA,EAAG;IACP,IAAI,CAACL,MAAM,CAACM,iBAAiB,CAAC,MAAMjT,SAAS,CAAC,IAAI,CAAC6Y,IAAI,CAAC1F,aAAa,EAAE,OAAO,CAAC,CAC1EC,IAAI,CAACvS,MAAM,CAAC,MAAM,IAAI,CAAC6J,UAAU,CAAC,EAAElK,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC7DS,SAAS,CAAC,MAAM;MACjB,MAAMwS,SAAS,GAAG,IAAI,CAACpB,oBAAoB,CAAC,IAAI,CAAC3Z,cAAc,EAAE,IAAI,CAACzC,SAAS,CAAC;MAChF,IAAI,CAACsK,MAAM,CAACmT,GAAG,CAAC,MAAM;QAClB,IAAI,CAACnB,YAAY,CAACkB,SAAS,CAAC;QAC5B,IAAI,CAACV,iBAAiB,CAAC/S,IAAI,CAAC,IAAI,CAAC;MACrC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC;IACH,IAAI,CAACyS,gBAAgB,CAACzR,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACuR,KAAK,IAAI;MACpE,IAAI,IAAI,CAACvc,SAAS,KAAKuc,KAAK,EAAE;QAC1B,IAAI,CAACvc,SAAS,GAAGuc,KAAK;QACtB,IAAI,CAACgB,iBAAiB,CAAC1O,IAAI,CAAC0N,KAAK,CAAC;MACtC;MACA,IAAI,CAACK,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAAC3S,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAmF,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE6N,gBAAgB;MAAEnb,SAAS;MAAEib,WAAW;MAAEG,QAAQ;MAAEC,UAAU;MAAEH,cAAc;MAAEnb,gBAAgB;MAAEM,UAAU;MAAEib;IAAa,CAAC,GAAGhO,OAAO;IAC9I,IAAI6N,gBAAgB,EAAE;MAClB,IAAI,IAAI,CAACA,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC7Y,MAAM,EAAE;QACvD,IAAI,CAAC7B,cAAc,GAAG,IAAI,CAAC0a,gBAAgB;MAC/C;IACJ;IACA,IAAIF,WAAW,EAAE;MACb,IAAI,CAACjd,SAAS,GAAG,IAAI,CAACid,WAAW;MACjC,IAAI,CAACX,YAAY,CAAC,IAAI,CAACW,WAAW,CAAC;IACvC;IACA,IAAI5a,UAAU,EAAE;MACZ,IAAI,CAAC0a,mBAAmB,GAAG,IAAI;IACnC;IACA,IAAIO,YAAY,EAAE;MACd,IAAI,CAACN,qBAAqB,GAAG,IAAI;IACrC;IACA,MAAMpB,aAAa,GAAItN,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACuN,WAAW,IAAIvN,KAAK,CAACwN,YAAY,KAAKlC,SAAS;IAC/F,IAAI,CAACgC,aAAa,CAACqB,WAAW,CAAC,IAAIrB,aAAa,CAACwB,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACL,mBAAmB,EAAE;MACtF,IAAI,CAAC1a,UAAU,GAAG,IAAI;IAC1B;IACA,IAAIuZ,aAAa,CAAC5Z,SAAS,CAAC,IAAI,CAAC,IAAI,CAACgb,qBAAqB,EAAE;MACzD,IAAI,CAACM,YAAY,GAAG,IAAI;IAC5B;IACA,IAAI,CAACtb,SAAS,IAAID,gBAAgB,KAAK,IAAI,CAACub,YAAY,EAAE;MACtD,MAAMI,WAAW,GAAG,IAAI,CAAC1b,SAAS,CAACxJ,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAACS,SAAS,CAAC,CAAC1W,GAAG,CAACiW,IAAI,IAAIA,IAAI,CAACC,KAAK,CAAC;MACzF,IAAI,CAACqO,aAAa,GAAG,IAAI,CAAC5a,gBAAgB,GAAG2b,WAAW,GAAGA,WAAW,CAAC,CAAC,CAAC,IAAI,IAAI;IACrF;IACA,IAAIN,QAAQ,IAAIC,UAAU,IAAIH,cAAc,IAAIlb,SAAS,EAAE;MACvD,IAAI,CAAC4a,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;IAAS,IAAI,CAACzR,IAAI,YAAAwS,2BAAAtS,CAAA;MAAA,YAAAA,CAAA,IAAwF8Q,kBAAkB,EAnmC5B1lB,EAAE,CAAA6U,iBAAA,CAmmC4C7U,EAAE,CAACE,UAAU,GAnmC3DF,EAAE,CAAA6U,iBAAA,CAmmCsE7U,EAAE,CAAC+U,iBAAiB,GAnmC5F/U,EAAE,CAAA6U,iBAAA,CAmmCuG7U,EAAE,CAACY,MAAM,GAnmClHZ,EAAE,CAAA6U,iBAAA,CAmmC6HvS,EAAE,CAACC,gBAAgB;IAAA,CAA4C;EAAE;EAChS;IAAS,IAAI,CAACyS,IAAI,kBApmC8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAomCJwQ,kBAAkB;MAAAvQ,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAyM,gCAAAniB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApmChBhF,EAAE,CAAAuI,WAAA,iCAAAtD,GAAA,CAAA2G,UAomCa,CAAC,0BAAA3G,GAAA,CAAAsE,SAAA,KAAJ,SAAS,IAAAtE,GAAA,CAAAsE,SAAA,KAAkB,QAAxB,CAAC;QAAA;MAAA;MAAAoM,MAAA;QAAAyR,WAAA;QAAA9b,gBAAA;QAAAkb,WAAA;QAAAC,cAAA;QAAAC,gBAAA;QAAAnb,SAAA;QAAAob,QAAA;QAAAC,UAAA;QAAAhb,UAAA;QAAAib,YAAA;QAAAxb,cAAA;MAAA;MAAAwK,OAAA;QAAAkP,eAAA;QAAA+B,iBAAA;QAAAb,cAAA;MAAA;MAAAlQ,UAAA;MAAAC,QAAA,GApmChBhW,EAAE,CAAAiW,kBAAA,CAomCiyB,CAAC1T,gBAAgB,CAAC,GApmCrzBvC,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAA3a,GAAA;MAAAsL,kBAAA,EAAApL,GAAA;MAAAqL,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8Q,4BAAAriB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA,CAAA3L,GAAA;UAAF9K,EAAE,CAAAwG,UAAA,IAAAwE,6CAAA,4BA6mClG,CAAC,IAAAS,yCAAA,gCA7mC+FzL,EAAE,CAAAmK,sBA8mCnE,CAAC,IAAA0B,yCAAA,gCA9mCgE7L,EAAE,CAAAmK,sBAinCvE,CAAC,IAAA4B,yCAAA,gCAjnCoE/L,EAAE,CAAAmK,sBAqnCxE,CAAC,IAAA8B,yCAAA,gCArnCqEjM,EAAE,CAAAmK,sBA4nCrE,CAAC;QAAA;QAAA,IAAAnF,EAAA;UAAA,MAAAmG,oBAAA,GA5nCkEnL,EAAE,CAAA2H,WAAA;UAAF3H,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAA4hB,YAAA,IAAA5hB,GAAA,CAAAoG,cAsmC1D,CAAC,aAAAF,oBAAqB,CAAC;QAAA;MAAA;MAAAwL,YAAA,GAyBJe,sBAAsB,EAAuL1U,IAAI,EAA6FD,gBAAgB,EAAoJwY,uBAAuB;MAAA3E,aAAA;MAAAC,eAAA;IAAA,EAA8L;EAAE;AACxuB;AACA5V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEkjB,kBAAkB,CAAC5O,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACtD7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEkjB,kBAAkB,CAAC5O,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AACxD7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEkjB,kBAAkB,CAAC5O,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC1D;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA1oCoG/W,EAAE,CAAAgX,iBAAA,CA0oCX0O,kBAAkB,EAAc,CAAC;IAChHxQ,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qHAAqH;MAC/HE,mBAAmB,EAAE,KAAK;MAC1BR,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCR,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,sCAAsC,EAAE,YAAY;QACpD,+BAA+B,EAAG;MACtC,CAAC;MACDzC,SAAS,EAAE,CAAC/U,gBAAgB,CAAC;MAC7BgV,OAAO,EAAE,CAACG,sBAAsB,EAAE1U,IAAI,EAAED,gBAAgB,EAAEwY,uBAAuB,CAAC;MAClFxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,EAAE;IAAEgV,IAAI,EAAElV,EAAE,CAAC+U;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAElV,EAAE,CAACY;EAAO,CAAC,EAAE;IAAEsU,IAAI,EAAE5S,EAAE,CAACC;EAAiB,CAAC,CAAC,EAAkB;IAAE6kB,WAAW,EAAE,CAAC;MACjKlS,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEgL,gBAAgB,EAAE,CAAC;MACnB4J,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkmB,WAAW,EAAE,CAAC;MACdtR,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmmB,cAAc,EAAE,CAAC;MACjBvR,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEomB,gBAAgB,EAAE,CAAC;MACnBxR,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEiL,SAAS,EAAE,CAAC;MACZ2J,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEqmB,QAAQ,EAAE,CAAC;MACXzR,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsmB,UAAU,EAAE,CAAC;MACb1R,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsL,UAAU,EAAE,CAAC;MACbsJ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEumB,YAAY,EAAE,CAAC;MACf3R,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+K,cAAc,EAAE,CAAC;MACjB6J,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEykB,eAAe,EAAE,CAAC;MAClB7P,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAEumB,iBAAiB,EAAE,CAAC;MACpB5R,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE0lB,cAAc,EAAE,CAAC;MACjB/Q,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM+mB,oBAAoB,CAAC;EACvB3T,WAAWA,CAACqI,QAAQ,EAAEE,UAAU,EAAE;IAC9B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACgB,QAAQ,GAAG,IAAI/b,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC2d,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC9B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACsK,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACA5O,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEiG,OAAO;MAAE9B,OAAO;MAAEuK,OAAO;MAAEtK,OAAO;MAAEuK;IAAQ,CAAC,GAAG3O,OAAO;IAC/D,IAAImE,OAAO,IAAIC,OAAO,EAAE;MACpB,MAAMwK,GAAG,GAAG,IAAI,CAACzK,OAAO,IAAI,IAAI,CAACC,OAAO;MACxC,IAAI,CAACva,KAAK,CAAC+kB,GAAG,CAAC,EAAE;QACb,IAAI,CAACzL,QAAQ,CAAC0L,YAAY,CAAC,IAAI,CAACxL,UAAU,CAAC7H,aAAa,EAAE,SAAS,EAAG,GAAEoT,GAAI,EAAC,CAAC;MAClF,CAAC,MACI;QACD,IAAI,CAACzL,QAAQ,CAAC2L,eAAe,CAAC,IAAI,CAACzL,UAAU,CAAC7H,aAAa,EAAE,SAAS,CAAC;MAC3E;IACJ;IACA,IAAIkT,OAAO,IAAIC,OAAO,EAAE;MACpB,MAAMI,GAAG,GAAG,IAAI,CAACL,OAAO,IAAI,IAAI,CAACC,OAAO;MACxC,IAAI,CAAC9kB,KAAK,CAACklB,GAAG,CAAC,EAAE;QACb,IAAI,CAAC5L,QAAQ,CAAC0L,YAAY,CAAC,IAAI,CAACxL,UAAU,CAAC7H,aAAa,EAAE,SAAS,EAAG,GAAEuT,GAAI,EAAC,CAAC;MAClF,CAAC,MACI;QACD,IAAI,CAAC5L,QAAQ,CAAC2L,eAAe,CAAC,IAAI,CAACzL,UAAU,CAAC7H,aAAa,EAAE,SAAS,CAAC;MAC3E;IACJ;IACA,IAAIyK,OAAO,IAAI9B,OAAO,EAAE;MACpB,IAAI,CAACE,QAAQ,CAAC5J,IAAI,CAAC,CAAC;IACxB;EACJ;EACA;IAAS,IAAI,CAACoB,IAAI,YAAAmT,6BAAAjT,CAAA;MAAA,YAAAA,CAAA,IAAwF0S,oBAAoB,EA1vC9BtnB,EAAE,CAAA6U,iBAAA,CA0vC8C7U,EAAE,CAAC0d,SAAS,GA1vC5D1d,EAAE,CAAA6U,iBAAA,CA0vCuE7U,EAAE,CAACE,UAAU;IAAA,CAA4C;EAAE;EACpO;IAAS,IAAI,CAACqa,IAAI,kBA3vC8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EA2vCJoS,oBAAoB;MAAAnS,SAAA;MAAAQ,MAAA;QAAAmJ,OAAA;QAAA9B,OAAA;QAAAC,OAAA;QAAAsK,OAAA;QAAAC,OAAA;MAAA;MAAAzR,UAAA;MAAAC,QAAA,GA3vClBhW,EAAE,CAAAmZ,oBAAA;IAAA,EA2vC0M;EAAE;AAClT;AACA;EAAA,QAAApC,SAAA,oBAAAA,SAAA,KA7vCoG/W,EAAE,CAAAgX,iBAAA,CA6vCXsQ,oBAAoB,EAAc,CAAC;IAClHpS,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,IAAI;MACdnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,EAAE;IAAExI,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,CAAC,EAAkB;IAAE4e,OAAO,EAAE,CAAC;MACjG5J,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE0c,OAAO,EAAE,CAAC;MACV9H,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2c,OAAO,EAAE,CAAC;MACV/H,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEinB,OAAO,EAAE,CAAC;MACVrS,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEknB,OAAO,EAAE,CAAC;MACVtS,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwnB,sBAAsB,CAAC;EACzBnU,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoU,YAAY,GAAG,EAAE;IACtB,IAAI,CAACrd,SAAS,GAAG,KAAK;IACtB,IAAI,CAACD,UAAU,GAAG,KAAK;IACvB,IAAI,CAACE,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACka,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACkD,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACjD,eAAe,GAAG,IAAI9kB,YAAY,CAAC,CAAC;IACzC,IAAI,CAACglB,qBAAqB,GAAG,KAAK;IAClC,IAAI,CAACC,uBAAuB,GAAG,KAAK;EACxC;EACA5c,eAAeA,CAACrC,OAAO,EAAE;IACrB,IAAI,CAACyE,SAAS,GAAGzE,OAAO;IACxB,IAAI,CAAC8e,eAAe,CAAC3M,IAAI,CAACnS,OAAO,CAAC;EACtC;EACA2S,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAMsM,aAAa,GAAItN,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACuN,WAAW,IAAIvN,KAAK,CAACwN,YAAY,KAAKlC,SAAS;IAC/F,MAAM;MAAEzY,SAAS;MAAEqd,YAAY;MAAEje,YAAY;MAAEgb;IAAe,CAAC,GAAGjM,OAAO;IACzE,IAAI/O,YAAY,EAAE;MACd,IAAI,CAACmb,qBAAqB,GAAG,IAAI;IACrC;IACA,IAAIH,cAAc,EAAE;MAChB,IAAI,CAACI,uBAAuB,GAAG,IAAI;IACvC;IACA,IAAIC,aAAa,CAAC4C,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC9C,qBAAqB,EAAE;MAC5D,IAAI,CAAC+C,kBAAkB,GAAG,IAAI;IAClC;IACA,IAAI7C,aAAa,CAACza,SAAS,CAAC,IAAI,CAAC,IAAI,CAACwa,uBAAuB,EAAE;MAC3D,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC9B;EACJ;EACA;IAAS,IAAI,CAACpQ,IAAI,YAAAuT,+BAAArT,CAAA;MAAA,YAAAA,CAAA,IAAwFkT,sBAAsB;IAAA,CAAmD;EAAE;EACrL;IAAS,IAAI,CAAC9S,IAAI,kBAjzC8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAizCJ4S,sBAAsB;MAAA3S,SAAA;MAAA+D,SAAA;MAAAvD,MAAA;QAAAoS,YAAA;QAAArd,SAAA;QAAAD,UAAA;QAAAE,eAAA;QAAAC,OAAA;QAAAka,cAAA;QAAAkD,kBAAA;MAAA;MAAAnS,OAAA;QAAAkP,eAAA;MAAA;MAAAhP,UAAA;MAAAC,QAAA,GAjzCpBhW,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAAtZ,GAAA;MAAAiK,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2R,gCAAAljB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAqF,cAAA,2BA2zClG,CAAC;UA3zC+FrF,EAAE,CAAAsF,UAAA,2BAAA6iB,4EAAAjhB,MAAA;YAAA,OA0zC/EjC,GAAA,CAAAqD,eAAA,CAAApB,MAAsB,CAAC;UAAA,EAAC;UA1zCqDlH,EAAE,CAAA+F,YAAA,CA2zC7E,CAAC;UA3zC0E/F,EAAE,CAAA8L,YAAA,EA4zC1E,CAAC;QAAA;QAAA,IAAA9G,EAAA;UA5zCuEhF,EAAE,CAAAgG,UAAA,YAAAf,GAAA,CAAAyF,SAmzC5E,CAAC,aAAAzF,GAAA,CAAAwF,UACC,CAAC,kBAAAxF,GAAA,CAAA0F,eACS,CAAC,UAAA1F,GAAA,CAAA2F,OACjB,CAAC,qBAAA3F,GAAA,CAAA8iB,YACe,CAAC,iBAAA9iB,GAAA,CAAA6f,cACH,CAAC,qBAAA7f,GAAA,CAAA+iB,kBACO,CAAC;QAAA;MAAA;MAAArR,YAAA,GAIkBuE,yBAAyB;MAAAtE,aAAA;MAAAC,eAAA;IAAA,EAAmR;EAAE;AAC7W;AACA5V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEslB,sBAAsB,CAAChR,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AAC9D7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEslB,sBAAsB,CAAChR,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;AAClE;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAr0CoG/W,EAAE,CAAAgX,iBAAA,CAq0CX8Q,sBAAsB,EAAc,CAAC;IACpH5S,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0EAA0E;MACpFE,mBAAmB,EAAE,KAAK;MAC1BR,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCR,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CZ,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAA6B,CAAC;MAC7CzC,OAAO,EAAE,CAAC2D,yBAAyB,CAAC;MACpCnF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgS,YAAY,EAAE,CAAC;MACvD7S,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoK,SAAS,EAAE,CAAC;MACZwK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmK,UAAU,EAAE,CAAC;MACbyK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEqK,eAAe,EAAE,CAAC;MAClBuK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsK,OAAO,EAAE,CAAC;MACVsK,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwkB,cAAc,EAAE,CAAC;MACjB5P,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE0nB,kBAAkB,EAAE,CAAC;MACrB9S,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEykB,eAAe,EAAE,CAAC;MAClB7P,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM6nB,oBAAoB,CAAC;EACvBzU,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC0U,OAAO,GAAG,IAAI;EACvB;EACA;IAAS,IAAI,CAAC3T,IAAI,YAAA4T,6BAAA1T,CAAA;MAAA,YAAAA,CAAA,IAAwFwT,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAAC7N,IAAI,kBAx3C8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAw3CJkT,oBAAoB;MAAAjT,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAA6N,kCAAAvjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAx3ClBhF,EAAE,CAAAsM,WAAA,eAAArH,GAAA,CAAAojB,OAw3Ce,CAAC;QAAA;MAAA;MAAA1S,MAAA;QAAA0S,OAAA;MAAA;MAAAtS,UAAA;IAAA,EAAmK;EAAE;AAC3R;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KA13CoG/W,EAAE,CAAAgX,iBAAA,CA03CXoR,oBAAoB,EAAc,CAAC;IAClHlT,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnC6C,IAAI,EAAE;QACF,oBAAoB,EAAE;MAC1B,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEsS,OAAO,EAAE,CAAC;MACxBnT,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMkoB,uBAAuB,CAAC;EAC1B7U,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8U,UAAU,GAAG,IAAI;EAC1B;EACA;IAAS,IAAI,CAAC/T,IAAI,YAAAgU,gCAAA9T,CAAA;MAAA,YAAAA,CAAA,IAAwF4T,uBAAuB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAACjO,IAAI,kBA54C8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EA44CJsT,uBAAuB;MAAArT,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAiO,qCAAA3jB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA54CrBhF,EAAE,CAAAuI,WAAA,4BAAAtD,GAAA,CAAAwjB,UA44CkB,CAAC;QAAA;MAAA;MAAA9S,MAAA;QAAA8S,UAAA;MAAA;MAAA1S,UAAA;IAAA,EAA+L;EAAE;AAC1T;AACA9U,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEgmB,uBAAuB,CAAC1R,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AAC3D;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAj5CoG/W,EAAE,CAAAgX,iBAAA,CAi5CXwR,uBAAuB,EAAc,CAAC;IACrHtT,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+BAA+B;MACzC6C,IAAI,EAAE;QACF,iCAAiC,EAAE;MACvC,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE0S,UAAU,EAAE,CAAC;MAC3BvT,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMsoB,wBAAwB,CAAC;EAC3BjV,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkV,WAAW,GAAG,IAAI;EAC3B;EACA;IAAS,IAAI,CAACnU,IAAI,YAAAoU,iCAAAlU,CAAA;MAAA,YAAAA,CAAA,IAAwFgU,wBAAwB;IAAA,CAAmD;EAAE;EACvL;IAAS,IAAI,CAACrO,IAAI,kBAn6C8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAm6CJ0T,wBAAwB;MAAAzT,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAqO,sCAAA/jB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAn6CtBhF,EAAE,CAAAsM,WAAA,eAAArH,GAAA,CAAA4jB,WAAA,GAm6CU,WAAW,GAAG,EAAL,CAAC;QAAA;MAAA;MAAAlT,MAAA;QAAAkT,WAAA;MAAA;MAAA9S,UAAA;IAAA,EAA0M;EAAE;AACtU;AACA9U,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEomB,wBAAwB,CAAC9R,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AAC7D;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAx6CoG/W,EAAE,CAAAgX,iBAAA,CAw6CX4R,wBAAwB,EAAc,CAAC;IACtH1T,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iCAAiC;MAC3C6C,IAAI,EAAE;QACF,oBAAoB,EAAG;MAC3B,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAE8S,WAAW,EAAE,CAAC;MAC5B3T,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM0oB,uBAAuB,CAAC;EAC1BrV,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsV,WAAW,GAAG,MAAM;IACzB,IAAI,CAACxc,aAAa,GAAG,IAAI;IACzB,IAAI,CAACiC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACD,cAAc,GAAG,EAAE;IACxB,IAAI,CAACD,OAAO,GAAG,IAAI;EACvB;EACA;IAAS,IAAI,CAACkG,IAAI,YAAAwU,gCAAAtU,CAAA;MAAA,YAAAA,CAAA,IAAwFoU,uBAAuB;IAAA,CAAmD;EAAE;EACtL;IAAS,IAAI,CAAChU,IAAI,kBAl8C8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAk8CJ8T,uBAAuB;MAAA7T,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAyO,qCAAAnkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAl8CrBhF,EAAE,CAAAsM,WAAA,iBAAArH,GAAA,CAAAgkB,WAk8CkB,CAAC,UAAAhkB,GAAA,CAAAuJ,OAAD,CAAC,cAAAvJ,GAAA,CAAAuJ,OAAA,GAAb,MAAM,GAAG,IAAG,CAAC;UAl8CrBxO,EAAE,CAAAuI,WAAA,oBAAAtD,GAAA,CAAAuJ,OAk8CkB,CAAC;QAAA;MAAA;MAAAmH,MAAA;QAAAsT,WAAA;QAAAxc,aAAA;QAAAiC,eAAA;QAAAD,cAAA;QAAAD,OAAA;MAAA;MAAAuH,UAAA;MAAAC,QAAA,GAl8CrBhW,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAArZ,GAAA;MAAAgK,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA6S,iCAAApkB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAwG,UAAA,IAAA4F,sCAAA,gBAm8CR,CAAC,IAAAI,wCAAA,kBACvC,CAAC,IAAAE,8CAAA,wBAGJ,CAAC;UAv8C8C1M,EAAE,CAAA8L,YAAA,EAw8C1E,CAAC;QAAA;QAAA,IAAA9G,EAAA;UAx8CuEhF,EAAE,CAAAgG,UAAA,YAAAf,GAAA,CAAAwJ,cAm8CZ,CAAC;UAn8CSzO,EAAE,CAAA0G,SAAA,CAo8ChD,CAAC;UAp8C6C1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAwH,aAo8ChD,CAAC;UAp8C6CzM,EAAE,CAAA0G,SAAA,CAu8ClD,CAAC;UAv8C+C1G,EAAE,CAAAgG,UAAA,qBAAAf,GAAA,CAAAyJ,eAu8ClD,CAAC;QAAA;MAAA;MAAAiI,YAAA,GAEU5T,gBAAgB,EAAoJC,IAAI,EAA6FC,OAAO;MAAA2T,aAAA;MAAAC,eAAA;IAAA,EAAwL;EAAE;AACrgB;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KA38CoG/W,EAAE,CAAAgX,iBAAA,CA28CXgS,uBAAuB,EAAc,CAAC;IACrH9T,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,yBAAyB;MACnCL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,sBAAsB,EAAE,aAAa;QACrC,yBAAyB,EAAE,SAAS;QACpC,eAAe,EAAE,SAAS;QAC1B,mBAAmB,EAAG;MAC1B,CAAC;MACDxC,OAAO,EAAE,CAACxU,gBAAgB,EAAEC,IAAI,EAAEC,OAAO,CAAC;MAC1C8S,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEkT,WAAW,EAAE,CAAC;MAC5B/T,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChByI,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoO,eAAe,EAAE,CAAC;MAClBwG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmO,cAAc,EAAE,CAAC;MACjByG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkO,OAAO,EAAE,CAAC;MACV0G,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM+oB,wBAAwB,CAAC;EAC3B1V,WAAWA,CAACqN,mBAAmB,EAAEhF,QAAQ,EAAE;IACvC,IAAI,CAACgF,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAChF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAC9O,UAAU,GAAG,IAAI7L,eAAe,CAAC,IAAI,CAAC;IAC3C,IAAI,CAAC4e,kBAAkB,GAAG,IAAI5e,eAAe,CAAC,KAAK,CAAC;IACpD,IAAI,CAACyS,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;EACjC;EACA+S,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC8M,mBAAmB,EAAE;MAC1B,MAAM;QAAEf,kBAAkB;QAAE/S;MAAW,CAAC,GAAG,IAAI,CAAC8T,mBAAmB;MACnEf,kBAAkB,CAAC3L,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAAC0L,kBAAkB,CAAC;MACpF/S,UAAU,CAACoH,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAACrH,UAAU,CAAC;IACxE;EACJ;EACAoc,eAAeA,CAAA,EAAG;IACd,IAAI,CAACtI,mBAAmB,CAACjC,YAAY,CAACzK,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACgV,KAAK,IAAI;MACpF,IAAI,CAACvN,QAAQ,CAAC0L,YAAY,CAAC,IAAI,CAAC8B,SAAS,CAACnV,aAAa,EAAE,SAAS,EAAG,GAAEkV,KAAM,EAAC,CAAC;IACnF,CAAC,CAAC;EACN;EACAzQ,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAA+U,iCAAA7U,CAAA;MAAA,YAAAA,CAAA,IAAwFyU,wBAAwB,EA1gDlCrpB,EAAE,CAAA6U,iBAAA,CA0gDkD+I,mBAAmB,GA1gDvE5d,EAAE,CAAA6U,iBAAA,CA0gDkF7U,EAAE,CAAC0d,SAAS;IAAA,CAA4C;EAAE;EAC9O;IAAS,IAAI,CAAC1I,IAAI,kBA3gD8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EA2gDJmU,wBAAwB;MAAAlU,SAAA;MAAAC,SAAA,WAAAsU,+BAAA1kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3gDtBhF,EAAE,CAAAsV,WAAA,CAAA3I,GAAA;QAAA;QAAA,IAAA3H,EAAA;UAAA,IAAAuQ,EAAA;UAAFvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAukB,SAAA,GAAAjU,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAK,UAAA;MAAAC,QAAA,GAAFhW,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAA5Y,GAAA;MAAAuJ,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoT,kCAAA3kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAqF,cAAA,cA4gD/C,CAAC;UA5gD4CrF,EAAE,CAAAwG,UAAA,IAAAsG,uCAAA,gBAkhDhG,CAAC;UAlhD6F9M,EAAE,CAAA+M,MAAA;UAAF/M,EAAE,CAAA+F,YAAA,CAqhD9F,CAAC;UArhD2F/F,EAAE,CAAAwG,UAAA,IAAA2G,+CAAA,gCAAFnN,EAAE,CAAAmK,sBAshDrE,CAAC;QAAA;QAAA,IAAAnF,EAAA;UAAA,MAAAgI,kBAAA,GAthDkEhN,EAAE,CAAA2H,WAAA;UAAF3H,EAAE,CAAA0G,SAAA,EA+gD5D,CAAC;UA/gDyD1G,EAAE,CAAAgG,UAAA,SAAFhG,EAAE,CAAAiN,WAAA,OAAAhI,GAAA,CAAAgb,kBAAA,CA+gD5D,CAAC,aAAAjT,kBAAmB,CAAC;QAAA;MAAA;MAAA2J,YAAA,GAUA3T,IAAI,EAAwFE,SAAS,EAA8CH,gBAAgB;MAAA6T,aAAA;MAAAC,eAAA;IAAA,EAAyN;EAAE;AAC7b;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KA3hDoG/W,EAAE,CAAAgX,iBAAA,CA2hDXqS,wBAAwB,EAAc,CAAC;IACtHnU,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sCAAsC;MAChDL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBgB,OAAO,EAAE,CAACvU,IAAI,EAAEE,SAAS,EAAEH,gBAAgB,CAAC;MAC5CgT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE0I;EAAoB,CAAC,EAAE;IAAE1I,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,CAAC,EAAkB;IAAE8L,SAAS,EAAE,CAAC;MACzGtU,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAAC,WAAW,EAAE;QAAEO,MAAM,EAAE;MAAK,CAAC;IACxC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMoS,4BAA4B,CAAC;EAC/BjW,WAAWA,CAAA,EAAG;IACV,IAAI,CAACsV,WAAW,GAAG,MAAM;IACzB,IAAI,CAACxa,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACiC,eAAe,GAAG,IAAI;EAC/B;EACA;IAAS,IAAI,CAACgG,IAAI,YAAAmV,qCAAAjV,CAAA;MAAA,YAAAA,CAAA,IAAwFgV,4BAA4B;IAAA,CAAmD;EAAE;EAC3L;IAAS,IAAI,CAAC5U,IAAI,kBApkD8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAokDJ0U,4BAA4B;MAAAzU,SAAA;MAAA+D,SAAA;MAAAvD,MAAA;QAAAsT,WAAA;QAAAxa,cAAA;QAAAhC,aAAA;QAAAiC,eAAA;MAAA;MAAAqH,UAAA;MAAAC,QAAA,GApkD1BhW,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuT,sCAAA9kB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAqF,cAAA,YAqkDpE,CAAC;UArkDiErF,EAAE,CAAAoH,SAAA,cA4kDxF,CAAC;UA5kDqFpH,EAAE,CAAA+F,YAAA,CA6kD7F,CAAC;QAAA;QAAA,IAAAf,EAAA;UA7kD0FhF,EAAE,CAAA0G,SAAA,CAwkD5D,CAAC;UAxkDyD1G,EAAE,CAAAgG,UAAA,oBAAAf,GAAA,CAAAyJ,eAwkD5D,CAAC,gBAAAzJ,GAAA,CAAAgkB,WACT,CAAC,mBAAAhkB,GAAA,CAAAwJ,cACK,CAAC,kBAAAxJ,GAAA,CAAAwH,aACH,CAAC;QAAA;MAAA;MAAAkK,YAAA,GAGwBqS,uBAAuB;MAAApS,aAAA;MAAAC,eAAA;IAAA,EAAmO;EAAE;AAC3T;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAhlDoG/W,EAAE,CAAAgX,iBAAA,CAglDX4S,4BAA4B,EAAc,CAAC;IAC1H1U,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAsB,CAAC;MACtCzC,OAAO,EAAE,CAACyR,uBAAuB,CAAC;MAClCjT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEkT,WAAW,EAAE,CAAC;MACtD/T,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmO,cAAc,EAAE,CAAC;MACjByG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChByI,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoO,eAAe,EAAE,CAAC;MAClBwG,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMypB,oBAAoB,CAAC;EACvBpW,WAAWA,CAACqW,gBAAgB,EAAEnW,MAAM,EAAE;IAClC,IAAI,CAACmW,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACnW,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACoW,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC1K,eAAe,GAAG,IAAItf,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC6T,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;EACjC;EACA+oB,WAAWA,CAACvS,CAAC,EAAE+K,GAAG,EAAE;IAChB,OAAOA,GAAG;EACd;EACA4G,eAAeA,CAAA,EAAG;IACd,IAAI,CAACa,eAAe,CAACtR,OAAO,CACvBvE,IAAI,CAACrS,SAAS,CAAC,IAAI,CAACkoB,eAAe,CAAC,CAAC,CACrC7V,IAAI,CAACtS,SAAS,CAACwe,IAAI,IAAIlf,aAAa,CAACkf,IAAI,CAAC4J,OAAO,CAAC,CAAC,CAACzoB,GAAG,CAAEiW,IAAI,IAAK,IAAI,CAACoS,gBAAgB,CAACK,OAAO,CAACzS,IAAI,CAAC,CAACtD,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAAC2oB,KAAK,CAAC,KAAK;MAC7H,MAAM;QAAE7K;MAAM,CAAC,GAAG6K,KAAK,CAACC,MAAM,CAACC,qBAAqB,CAAC,CAAC;MACtD,OAAOzG,IAAI,CAAC0G,KAAK,CAAChL,KAAK,CAAC;IAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE5d,YAAY,CAAC,EAAE,CAAC,EAAEH,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9CS,SAAS,CAACrF,IAAI,IAAI;MACnB;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC2E,MAAM,YAAYjT,MAAM,IAAIA,MAAM,CAAC8pB,eAAe,CAAC,CAAC,EAAE;QAC3D,IAAI,CAACnL,eAAe,CAACjM,IAAI,CAACpE,IAAI,CAAC;MACnC,CAAC,MACI;QACD,IAAI,CAAC2E,MAAM,CAACmT,GAAG,CAAC,MAAM,IAAI,CAACzH,eAAe,CAACjM,IAAI,CAACpE,IAAI,CAAC,CAAC;MAC1D;IACJ,CAAC,CAAC;EACN;EACA4J,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,IAAI,CAAC;IACxB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAiW,6BAAA/V,CAAA;MAAA,YAAAA,CAAA,IAAwFmV,oBAAoB,EAzpD9B/pB,EAAE,CAAA6U,iBAAA,CAypD8CvQ,IAAI,CAACsmB,gBAAgB,GAzpDrE5qB,EAAE,CAAA6U,iBAAA,CAypDgF7U,EAAE,CAACY,MAAM;IAAA,CAA4C;EAAE;EACzO;IAAS,IAAI,CAACoU,IAAI,kBA1pD8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EA0pDJ6U,oBAAoB;MAAA5U,SAAA;MAAAC,SAAA,WAAAyV,2BAAA7lB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA1pDlBhF,EAAE,CAAAsV,WAAA,CAAA3I,GAAA;QAAA;QAAA,IAAA3H,EAAA;UAAA,IAAAuQ,EAAA;UAAFvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAklB,eAAA,GAAA5U,EAAA;QAAA;MAAA;MAAA2D,SAAA;MAAAvD,MAAA;QAAAsU,mBAAA;MAAA;MAAApU,OAAA;QAAA0J,eAAA;MAAA;MAAAxJ,UAAA;MAAAC,QAAA,GAAFhW,EAAE,CAAAkW,mBAAA;MAAAsP,KAAA,EAAApY,GAAA;MAAAgJ,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAuU,8BAAA9lB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAwG,UAAA,IAAA6G,kCAAA,eAgqDlG,CAAC;QAAA;QAAA,IAAArI,EAAA;UAhqD+FhF,EAAE,CAAAgG,UAAA,YAAAf,GAAA,CAAAglB,mBA+pD1D,CAAC,iBAAAhlB,GAAA,CAAAilB,WAAmB,CAAC;QAAA;MAAA;MAAAvT,YAAA,GAEF1T,OAAO;MAAA2T,aAAA;MAAAC,eAAA;IAAA,EAAwL;EAAE;AAChQ;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAnqDoG/W,EAAE,CAAAgX,iBAAA,CAmqDX+S,oBAAoB,EAAc,CAAC;IAClH7U,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0BAA0B;MACpCE,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAwB,CAAC;MACxCzC,OAAO,EAAE,CAACtU,OAAO,CAAC;MAClB8S,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE5Q,IAAI,CAACsmB;EAAiB,CAAC,EAAE;IAAE1V,IAAI,EAAElV,EAAE,CAACY;EAAO,CAAC,CAAC,EAAkB;IAAEqpB,mBAAmB,EAAE,CAAC;MAClH/U,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEif,eAAe,EAAE,CAAC;MAClBrK,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE4pB,eAAe,EAAE,CAAC;MAClBjV,IAAI,EAAErU,YAAY;MAClBoW,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAM8T,gBAAgB,CAAC;EACnBpX,WAAWA,CAACqN,mBAAmB,EAAE;IAC7B,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACpT,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACgS,UAAU,GAAG,IAAIve,eAAe,CAAC,KAAK,CAAC;IAC5C,IAAI,CAAC0M,SAAS,GAAG,IAAI1M,eAAe,CAAC8hB,SAAS,CAAC;IAC/C,IAAI,CAAC9D,oBAAoB,GAAG,IAAIhe,eAAe,CAAC,EAAE,CAAC;IACnD,IAAI,CAACyS,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACyM,aAAa,GAAG,CAAC,CAAC,IAAI,CAACoT,mBAAmB;IAC/C,IAAI,IAAI,CAACA,mBAAmB,EAAE;MAC1B,MAAM;QAAEpB,UAAU;QAAE7R,SAAS;QAAEsR;MAAqB,CAAC,GAAG,IAAI,CAAC2B,mBAAmB;MAChFjT,SAAS,CAACuG,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAACxG,SAAS,CAAC;MAClEsR,oBAAoB,CAAC/K,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAAC8K,oBAAoB,CAAC;MACxFO,UAAU,CAACtL,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAACqL,UAAU,CAAC;IACxE;EACJ;EACApS,uBAAuBA,CAAC+R,eAAe,EAAE;IACrC,IAAI,CAACyB,mBAAmB,CAAC1B,kBAAkB,CAACC,eAAe,CAAC;EAChE;EACAzG,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAsW,yBAAApW,CAAA;MAAA,YAAAA,CAAA,IAAwFmW,gBAAgB,EA3tD1B/qB,EAAE,CAAA6U,iBAAA,CA2tD0C+I,mBAAmB;IAAA,CAA4D;EAAE;EAC7N;IAAS,IAAI,CAAC5I,IAAI,kBA5tD8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EA4tDJ6V,gBAAgB;MAAA5V,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAuQ,8BAAAjmB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5tDdhF,EAAE,CAAAuI,WAAA,oBAAAtD,GAAA,CAAA2I,aA4tDW,CAAC;QAAA;MAAA;MAAAmI,UAAA;MAAAC,QAAA,GA5tDdhW,EAAE,CAAAkW,mBAAA;MAAAC,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2U,0BAAAlmB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAwG,UAAA,IAAAmH,wCAAA,yBA6tDzB,CAAC;UA7tDsB3N,EAAE,CAAA+M,MAAA;UAAF/M,EAAE,CAAA8L,YAAA,EAquD1E,CAAC;UAruDuE9L,EAAE,CAAAwG,UAAA,IAAAsH,8BAAA,eAsuDnB,CAAC;UAtuDgB9N,EAAE,CAAA+M,MAAA;QAAA;QAAA,IAAA/H,EAAA;UAAFhF,EAAE,CAAAgG,UAAA,SAAFhG,EAAE,CAAAiN,WAAA,OAAAhI,GAAA,CAAAoa,oBAAA,CA6tDjD,CAAC;UA7tD8Crf,EAAE,CAAA0G,SAAA,EAsuDrB,CAAC;UAtuDkB1G,EAAE,CAAAgG,UAAA,SAAFhG,EAAE,CAAAiN,WAAA,OAAAhI,GAAA,CAAA2a,UAAA,CAsuDrB,CAAC;QAAA;MAAA;MAAAjJ,YAAA,GAGnB3T,IAAI,EAAwFE,SAAS,EAA8C6mB,oBAAoB,EAAoIV,wBAAwB,EAAgFhlB,aAAa,EAA+BD,IAAI,CAAC+mB,qBAAqB;MAAAvU,aAAA;MAAAC,eAAA;IAAA,EAA4M;EAAE;AACtuB;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KA3uDoG/W,EAAE,CAAAgX,iBAAA,CA2uDX+T,gBAAgB,EAAc,CAAC;IAC9G7V,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,OAAO;MACjBE,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,yBAAyB,EAAE;MAC/B,CAAC;MACDxC,OAAO,EAAE,CAACvU,IAAI,EAAEE,SAAS,EAAE6mB,oBAAoB,EAAEV,wBAAwB,EAAEhlB,aAAa,CAAC;MACzF0R,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE0I,mBAAmB;IAAEuD,UAAU,EAAE,CAAC;MACzDjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMyqB,2BAA2B,CAAC;EAC9BC,0BAA0BA,CAACC,KAAK,GAAG,KAAK,EAAE;IACtC,MAAM;MAAEC,WAAW;MAAEC,UAAU;MAAEC;IAAY,CAAC,GAAG,IAAI,CAACC,gBAAgB,CAACrX,aAAa;IACpF,MAAMsX,aAAa,GAAG,qBAAqB;IAC3C,MAAMC,cAAc,GAAG,sBAAsB;IAC7C,IAAKL,WAAW,KAAKE,WAAW,IAAIF,WAAW,KAAK,CAAC,IAAKD,KAAK,EAAE;MAC7D,IAAI,CAACtP,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACiP,gBAAgB,EAAEF,aAAa,CAAC;MAC/D,IAAI,CAAC3P,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACiP,gBAAgB,EAAED,cAAc,CAAC;IACpE,CAAC,MACI,IAAIJ,UAAU,KAAK,CAAC,EAAE;MACvB,IAAI,CAACxP,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACiP,gBAAgB,EAAEF,aAAa,CAAC;MAC/D,IAAI,CAAC3P,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACgP,gBAAgB,EAAED,cAAc,CAAC;IACjE,CAAC,MACI,IAAIL,WAAW,KAAKC,UAAU,GAAGC,WAAW,EAAE;MAC/C,IAAI,CAACzP,QAAQ,CAACY,WAAW,CAAC,IAAI,CAACiP,gBAAgB,EAAED,cAAc,CAAC;MAChE,IAAI,CAAC5P,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACgP,gBAAgB,EAAEF,aAAa,CAAC;IAChE,CAAC,MACI;MACD,IAAI,CAAC3P,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACgP,gBAAgB,EAAEF,aAAa,CAAC;MAC5D,IAAI,CAAC3P,QAAQ,CAACa,QAAQ,CAAC,IAAI,CAACgP,gBAAgB,EAAED,cAAc,CAAC;IACjE;EACJ;EACAjY,WAAWA,CAACqI,QAAQ,EAAEnI,MAAM,EAAEiY,QAAQ,EAAEC,aAAa,EAAE;IACnD,IAAI,CAAC/P,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACnI,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACiY,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC7c,IAAI,GAAG,EAAE;IACd,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACW,OAAO,GAAG,IAAI;IACnB,IAAI,CAACT,eAAe,GAAG,IAAI;IAC3B,IAAI,CAAC4P,WAAW,GAAG,EAAE;IACrB,IAAI,CAAC7P,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACsC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACM,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,kBAAkB,GAAG,GAAG;IAC7B,IAAI,CAACC,kBAAkB,GAAG,GAAG;IAC7B,IAAI,CAACC,iBAAiB,GAAGnB,KAAK,IAAIA,KAAK;IACvC,IAAI,CAACqB,cAAc,GAAG,CAAC,CAAC;IACxB,IAAI,CAACnB,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACkC,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACrB,mBAAmB,GAAG,OAAO;IAClC,IAAI,CAAC4c,KAAK,GAAG,IAAI7qB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC8qB,OAAO,GAAG,IAAI9qB,OAAO,CAAC,CAAC;IAC5B,IAAI,CAAC2S,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;EACjC;EACAyX,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAErK,OAAO;MAAEW,OAAO;MAAED;IAAK,CAAC,GAAG2J,OAAO;IAC1C,IAAIrK,OAAO,IAAIW,OAAO,EAAE;MACpB,MAAM+c,oBAAoB,GAAG,IAAI,CAACzb,sBAAsB,KAAK,CAAC;MAC9D,IAAI,CAACf,cAAc,GAAG;QAClByc,SAAS,EAAE,QAAQ;QACnBC,SAAS,EAAE,IAAI,CAACjd,OAAO,IAAI+c,oBAAoB,GAAG,QAAQ,GAAG;MACjE,CAAC;MACD,IAAI,CAAC3d,YAAY,GAAG;QAChB6d,SAAS,EAAE,IAAI,CAACjd,OAAO,GAAG,QAAQ,GAAG,QAAQ;QAC7Cgd,SAAS,EAAE,IAAI,CAAC3d,OAAO,GAAG,MAAM,GAAG,IAAI;QACvC6d,SAAS,EAAE,IAAI,CAACld;MACpB,CAAC;MACD;MACA;MACA,IAAI,CAAC0E,MAAM,CAACM,iBAAiB,CAAC,MAAM,IAAI,CAAC8X,OAAO,CAAC3Y,IAAI,CAAC,CAAC,CAAC;IAC5D;IACA,IAAIpE,IAAI,EAAE;MACN;MACA,IAAI,CAAC2E,MAAM,CAACM,iBAAiB,CAAC,MAAM,IAAI,CAAC6X,KAAK,CAAC1Y,IAAI,CAAC,CAAC,CAAC;IAC1D;EACJ;EACAgW,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACwC,QAAQ,CAACQ,SAAS,EAAE;MACzB,IAAI,CAACzY,MAAM,CAACM,iBAAiB,CAAC,MAAM;QAChC,MAAMoY,YAAY,GAAG,IAAI,CAACN,OAAO,CAAC3X,IAAI,CAACrS,SAAS,CAAC,IAAI,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,EAAEF,SAAS,CAAC,MAAMd,SAAS,CAAC,IAAI,CAACwqB,gBAAgB,CAACrX,aAAa,EAAE,QAAQ,CAAC,CAACC,IAAI,CAACrS,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAEP,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;QAC5L,MAAM0Y,OAAO,GAAG,IAAI,CAACT,aAAa,CAACxX,SAAS,CAAC,CAAC,CAACD,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;QAC7E,MAAMkY,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC1X,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;QACvD,MAAM2Y,aAAa,GAAGlrB,KAAK,CAACgrB,YAAY,EAAEC,OAAO,EAAER,KAAK,EAAE,IAAI,CAACC,OAAO,CAAC,CAAC3X,IAAI,CAACrS,SAAS,CAAC,IAAI,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,EAAER,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;QACjI2Y,aAAa,CAAClY,SAAS,CAAC,MAAM,IAAI,CAAC8W,0BAA0B,CAAC,CAAC,CAAC;QAChEkB,YAAY,CACPjY,IAAI,CAACvS,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAACoN,OAAO,CAAC,CAAC,CAClCoF,SAAS,CAAC,MAAO,IAAI,CAACmY,kBAAkB,CAACrY,aAAa,CAACmX,UAAU,GAAG,IAAI,CAACE,gBAAgB,CAACrX,aAAa,CAACmX,UAAW,CAAC;MAC7H,CAAC,CAAC;IACN;EACJ;EACA1S,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuS,0BAA0B,CAAC,IAAI,CAAC;IACrC,IAAI,CAACvX,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAiY,oCAAA/X,CAAA;MAAA,YAAAA,CAAA,IAAwFwW,2BAA2B,EAl2DrCprB,EAAE,CAAA6U,iBAAA,CAk2DqD7U,EAAE,CAAC0d,SAAS,GAl2DnE1d,EAAE,CAAA6U,iBAAA,CAk2D8E7U,EAAE,CAACY,MAAM,GAl2DzFZ,EAAE,CAAA6U,iBAAA,CAk2DoGtQ,IAAI,CAACqoB,QAAQ,GAl2DnH5sB,EAAE,CAAA6U,iBAAA,CAk2D8HvS,EAAE,CAACuqB,eAAe;IAAA,CAA4C;EAAE;EAChS;IAAS,IAAI,CAAC7X,IAAI,kBAn2D8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAm2DJkW,2BAA2B;MAAAjW,SAAA;MAAAC,SAAA,WAAA0X,kCAAA9nB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAn2DzBhF,EAAE,CAAAsV,WAAA,CAAAtH,IAAA,KAm2D8tB9N,UAAU;UAn2D1uBF,EAAE,CAAAsV,WAAA,CAAArH,IAAA,KAm2Du1B/N,UAAU;UAn2Dn2BF,EAAE,CAAAsV,WAAA,CAm2Dy6BpR,wBAAwB,KAA2BA,wBAAwB;QAAA;QAAA,IAAAc,EAAA;UAAA,IAAAuQ,EAAA;UAn2Dt/BvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAynB,kBAAA,GAAAnX,EAAA,CAAAG,KAAA;UAAF1V,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAymB,gBAAA,GAAAnW,EAAA,CAAAG,KAAA;UAAF1V,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAA8nB,wBAAA,GAAAxX,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAwD,SAAA;MAAAvD,MAAA;QAAAzG,IAAA;QAAAV,OAAA;QAAAW,OAAA;QAAAT,eAAA;QAAA4P,WAAA;QAAA7P,cAAA;QAAAhC,aAAA;QAAAsC,eAAA;QAAAM,eAAA;QAAAC,kBAAA;QAAAC,kBAAA;QAAAsc,gBAAA;QAAArc,iBAAA;QAAAiB,sBAAA;MAAA;MAAAsF,UAAA;MAAAC,QAAA,GAAFhW,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAyW,qCAAAhoB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAwG,UAAA,IAAAiJ,mDAAA,yBAo2DrE,CAAC,IAAAE,0CAAA,gBAuC4D,CAAC;QAAA;QAAA,IAAA3K,EAAA;UA34DKhF,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAkK,OAo2DvE,CAAC;UAp2DoEnP,EAAE,CAAA0G,SAAA,CA24DnC,CAAC;UA34DgC1G,EAAE,CAAAgG,UAAA,UAAAf,GAAA,CAAAkK,OA24DnC,CAAC;QAAA;MAAA;MAAAwH,YAAA,GAULqS,uBAAuB,EAA8JhmB,IAAI,EAA6FG,OAAO,EAA0EgB,eAAe,EAA+BF,IAAI,CAACgpB,yBAAyB,EAAsIhpB,IAAI,CAACipB,eAAe,EAA6LjpB,IAAI,CAACC,wBAAwB,EAAiJnB,gBAAgB,EAAoJgoB,gBAAgB;MAAAnU,aAAA;MAAAC,eAAA;IAAA,EAAuH;EAAE;AACpyC;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAv5DoG/W,EAAE,CAAAgX,iBAAA,CAu5DXoU,2BAA2B,EAAc,CAAC;IACzHlW,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QAAEC,KAAK,EAAE;MAAsB,CAAC;MACtCzC,OAAO,EAAE,CAACyR,uBAAuB,EAAEhmB,IAAI,EAAEG,OAAO,EAAEgB,eAAe,EAAEpB,gBAAgB,EAAEgoB,gBAAgB,CAAC;MACtGhV,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,EAAE;IAAExI,IAAI,EAAElV,EAAE,CAACY;EAAO,CAAC,EAAE;IAAEsU,IAAI,EAAE3Q,IAAI,CAACqoB;EAAS,CAAC,EAAE;IAAE1X,IAAI,EAAE5S,EAAE,CAACuqB;EAAgB,CAAC,CAAC,EAAkB;IAAE3d,IAAI,EAAE,CAAC;MACjJgG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkO,OAAO,EAAE,CAAC;MACV0G,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6O,OAAO,EAAE,CAAC;MACV+F,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoO,eAAe,EAAE,CAAC;MAClBwG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEge,WAAW,EAAE,CAAC;MACdpJ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmO,cAAc,EAAE,CAAC;MACjByG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEmM,aAAa,EAAE,CAAC;MAChByI,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyO,eAAe,EAAE,CAAC;MAClBmG,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+O,eAAe,EAAE,CAAC;MAClB6F,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEgP,kBAAkB,EAAE,CAAC;MACrB4F,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEiP,kBAAkB,EAAE,CAAC;MACrB2F,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEurB,gBAAgB,EAAE,CAAC;MACnB3W,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkP,iBAAiB,EAAE,CAAC;MACpB0F,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEosB,kBAAkB,EAAE,CAAC;MACrBxX,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAAC,oBAAoB,EAAE;QAAEQ,IAAI,EAAEvX;MAAW,CAAC;IACrD,CAAC,CAAC;IAAEwrB,gBAAgB,EAAE,CAAC;MACnBxW,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAAC,kBAAkB,EAAE;QAAEQ,IAAI,EAAEvX;MAAW,CAAC;IACnD,CAAC,CAAC;IAAE6sB,wBAAwB,EAAE,CAAC;MAC3B7X,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAAC/S,wBAAwB,EAAE;QAAEuT,IAAI,EAAEvT;MAAyB,CAAC;IACvE,CAAC,CAAC;IAAEuM,sBAAsB,EAAE,CAAC;MACzByE,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM6sB,6BAA6B,CAAC;EAChCxZ,WAAWA,CAAChD,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;EAClC;EACA,OAAOyc,sBAAsBA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACtC,OAAO,IAAI;EACf;EACA;IAAS,IAAI,CAAC5Y,IAAI,YAAA6Y,sCAAA3Y,CAAA;MAAA,YAAAA,CAAA,IAAwFuY,6BAA6B,EAtgEvCntB,EAAE,CAAA6U,iBAAA,CAsgEuD7U,EAAE,CAACwtB,WAAW;IAAA,CAA4C;EAAE;EACrN;IAAS,IAAI,CAACjT,IAAI,kBAvgE8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAugEJiY,6BAA6B;MAAAhY,SAAA;MAAAW,QAAA;MAAAC,UAAA;IAAA,EAAqG;EAAE;AACtO;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KAzgEoG/W,EAAE,CAAAgX,iBAAA,CAygEXmW,6BAA6B,EAAc,CAAC;IAC3HjY,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BpB,QAAQ,EAAE,iBAAiB;MAC3BC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAACwtB;EAAY,CAAC,CAAC;AAAA;;AAE5D;AACA;AACA;AACA;AACA,MAAMC,2BAA2B,CAAC;EAC9B9Z,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9D,KAAK,GAAG,IAAI;IACjB,IAAI,CAACE,MAAM,GAAG,IAAI;EACtB;EACA;IAAS,IAAI,CAAC2E,IAAI,YAAAgZ,oCAAA9Y,CAAA;MAAA,YAAAA,CAAA,IAAwF6Y,2BAA2B;IAAA,CAAmD;EAAE;EAC1L;IAAS,IAAI,CAACzY,IAAI,kBA5hE8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EA4hEJuY,2BAA2B;MAAAtY,SAAA;MAAAsF,QAAA;MAAAC,YAAA,WAAAiT,yCAAA3oB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5hEzBhF,EAAE,CAAAuI,WAAA,oBAAAtD,GAAA,CAAA4K,KAAA,KA4hEM,IAAgB,CAAC,qBAAA5K,GAAA,CAAA8K,MAAA,KAAhB,IAAe,CAAC;QAAA;MAAA;MAAA4F,MAAA;QAAA9F,KAAA;QAAAE,MAAA;MAAA;MAAAgG,UAAA;MAAAC,QAAA,GA5hEzBhW,EAAE,CAAAkW,mBAAA;MAAAE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAqX,qCAAA5oB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAwG,UAAA,IAAAoJ,mDAAA,yBA6hErD,CAAC,IAAAE,mDAAA,yBACA,CAAC;QAAA;QAAA,IAAA9K,EAAA;UA9hEiDhF,EAAE,CAAAgG,UAAA,2BAAAf,GAAA,CAAA4K,KA6hEvD,CAAC;UA7hEoD7P,EAAE,CAAA0G,SAAA,CA8hEtD,CAAC;UA9hEmD1G,EAAE,CAAAgG,UAAA,2BAAAf,GAAA,CAAA8K,MA8hEtD,CAAC;QAAA;MAAA;MAAA4G,YAAA,GACa/R,cAAc,EAA+BD,IAAI,CAACkpB,+BAA+B;MAAAjX,aAAA;MAAAC,eAAA;IAAA,EAAqP;EAAE;AACtY;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAjiEoG/W,EAAE,CAAAgX,iBAAA,CAiiEXyW,2BAA2B,EAAc,CAAC;IACzHvY,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,uBAAuB;MACjCL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACF,yBAAyB,EAAG,gBAAe;QAC3C,0BAA0B,EAAG;MACjC,CAAC;MACDxC,OAAO,EAAE,CAAC3S,cAAc,CAAC;MACzBmR,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAElG,KAAK,EAAE,CAAC;MACtBqF,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyP,MAAM,EAAE,CAAC;MACTmF,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMwtB,qBAAqB,GAAG,OAAO;AACrC,MAAMC,gBAAgB,CAAC;EACnBtc,gBAAgBA,CAAC6P,IAAI,EAAE;IACnB,IAAI,CAACiD,kBAAkB,CAAClD,cAAc,CAACC,IAAI,CAAC;EAChD;EACA3P,iBAAiBA,CAACtD,KAAK,EAAE;IACrB,IAAI,CAACkW,kBAAkB,CAAC5C,eAAe,CAACtT,KAAK,CAAC;EAClD;EACAsF,WAAWA,CAACuI,UAAU,EAAE8N,gBAAgB,EAAEpW,eAAe,EAAEJ,GAAG,EAAEwN,mBAAmB,EAAEuD,kBAAkB,EAAEyJ,cAAc,EAAE;IACrH,IAAI,CAAC9R,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAC8N,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACpW,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACJ,GAAG,GAAGA,GAAG;IACd,IAAI,CAACwN,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACyJ,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACja,aAAa,GAAG+Z,qBAAqB;IAC1C,IAAI,CAAC7c,aAAa,GAAG,MAAM;IAC3B,IAAI,CAACiB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACH,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC3B,OAAO,GAAG,IAAI;IACnB,IAAI,CAACgB,QAAQ,GAAG,IAAI;IACpB,IAAI,CAAC6c,UAAU,GAAG9K,SAAS;IAC3B,IAAI,CAACrR,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IAC7C,IAAI,CAAClB,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,oBAAoB,GAAG,GAAG;IAC/B,IAAI,CAACC,oBAAoB,GAAG,GAAG;IAC/B,IAAI,CAACC,mBAAmB,GAAG1C,KAAK,IAAIA,KAAK;IACzC,IAAI,CAAC6f,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC1b,WAAW,GAAG,CAAC;IACpB,IAAI,CAACH,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAAC6b,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,oBAAoB,GAAG,QAAQ;IACpC,IAAI,CAACC,QAAQ,GAAG;MAAEC,CAAC,EAAE,IAAI;MAAEC,CAAC,EAAE;IAAK,CAAC;IACpC,IAAI,CAACtc,gBAAgB,GAAG,SAAS;IACjC,IAAI,CAACuc,iBAAiB,GAAG,IAAI;IAC7B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACjc,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACkc,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC3c,MAAM,GAAG,SAAS;IACvB,IAAI,CAACP,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACI,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACD,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACO,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACyc,gBAAgB,GAAG,IAAI/uB,YAAY,CAAC,CAAC;IAC1C,IAAI,CAACgvB,iBAAiB,GAAG,IAAIhvB,YAAY,CAAC,CAAC;IAC3C,IAAI,CAACivB,aAAa,GAAG,IAAIjvB,YAAY,CAAC,CAAC;IACvC,IAAI,CAACkvB,uBAAuB,GAAG,IAAIlvB,YAAY,CAAC,CAAC;IACjD,IAAI,CAACmvB,oBAAoB,GAAG,IAAInvB,YAAY,CAAC,CAAC;IAC9C;IACA,IAAI,CAACiP,IAAI,GAAG,EAAE;IACd,IAAI,CAACV,OAAO,GAAG,IAAI;IACnB,IAAI,CAACW,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC1C,aAAa,GAAG,IAAI;IACzB,IAAI,CAAC+D,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACU,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAAC8M,UAAU,GAAG,KAAK;IACvB,IAAI,CAACG,WAAW,GAAG,KAAK;IACxB,IAAI,CAACvM,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACkC,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACkuB,aAAa,GAAG,IAAIhuB,eAAe,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACiuB,GAAG,GAAG,KAAK;IAChB,IAAI,CAAC7e,sBAAsB,GAAG,CAAC;IAC/B,IAAI,CAACmD,eAAe,CACf2b,gCAAgC,CAACzB,qBAAqB,CAAC,CACvDxZ,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAC,MAAM;MACjB,IAAI,CAACf,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAS,QAAQA,CAAA,EAAG;IACP,MAAM;MAAE+N,kBAAkB;MAAEC,iBAAiB;MAAEgC,sBAAsB;MAAEC,MAAM;MAAE/B,YAAY;MAAEJ;IAAoB,CAAC,GAAG,IAAI,CAACuC,kBAAkB;IAC5I,MAAM;MAAEzG,cAAc;MAAEG,WAAW;MAAEG;IAAa,CAAC,GAAG,IAAI,CAAC4C,mBAAmB;IAC9E,IAAI,CAACsO,GAAG,GAAG,IAAI,CAACtB,cAAc,CAACnW,KAAK;IACpC,IAAI,CAACmW,cAAc,CAACwB,MAAM,EAAElb,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAEkb,SAAS,IAAK;MAChF,IAAI,CAACH,GAAG,GAAGG,SAAS;MACpB,IAAI,CAACjc,GAAG,CAACkc,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACFtN,YAAY,CAAC9N,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC,IAAI,CAAC2a,aAAa,CAAC;IACzEjN,kBAAkB,CAAC3N,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC8N,SAAS,IAAI;MACrE,IAAIA,SAAS,KAAK,IAAI,CAAC7P,WAAW,EAAE;QAChC,IAAI,CAACA,WAAW,GAAG6P,SAAS;QAC5B,IAAI,CAAC4M,iBAAiB,CAAC3b,IAAI,CAAC+O,SAAS,CAAC;MAC1C;IACJ,CAAC,CAAC;IACFH,iBAAiB,CAAC5N,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC+N,QAAQ,IAAI;MACnE,IAAIA,QAAQ,KAAK,IAAI,CAACjQ,UAAU,EAAE;QAC9B,IAAI,CAACA,UAAU,GAAGiQ,QAAQ;QAC1B,IAAI,CAAC0M,gBAAgB,CAAC1b,IAAI,CAACgP,QAAQ,CAAC;MACxC;IACJ,CAAC,CAAC;IACF6B,MAAM,CACD7P,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,EAAE/R,MAAM,CAAC,MAAM,IAAI,CAAC2sB,iBAAiB,CAAC,CAAC,CACpEna,SAAS,CAACob,KAAK,IAAI;MACpB,IAAIA,KAAK,KAAK,IAAI,CAACrd,OAAO,EAAE;QACxB,IAAI,CAACA,OAAO,GAAGqd,KAAK;QACpB,IAAI,CAACnc,GAAG,CAACC,YAAY,CAAC,CAAC;MAC3B;IACJ,CAAC,CAAC;IACFyQ,sBAAsB,CAAC5P,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACrF,IAAI,IAAI;MACpE,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACigB,uBAAuB,CAAC7b,IAAI,CAACpE,IAAI,CAAC;MACvC,IAAI,CAACsE,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACFuO,mBAAmB,CAAC1N,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACrF,IAAI,IAAI;MACjE,IAAI,CAACmf,cAAc,GAAGnf,IAAI;MAC1B,IAAI,CAACkgB,oBAAoB,CAAC9b,IAAI,CAACpE,IAAI,CAAC;MACpC,IAAI,CAACsE,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACFqK,cAAc,CAACxJ,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC9H,aAAa,IAAI;MACrE,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,IAAI,CAAC+G,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACFwK,WAAW,CAAC3J,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAACyJ,UAAU,IAAI;MAC/D,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC5B,IAAI,CAACxK,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF2K,YAAY,CAAC9J,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC4J,WAAW,IAAI;MACjE,IAAI,CAACA,WAAW,GAAGA,WAAW;MAC9B,IAAI,CAAC3K,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACFnS,aAAa,CAAC,CAAC6iB,MAAM,EAAE,IAAI,CAACkL,aAAa,CAAC,CAAC,CACtC/a,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAACguB,KAAK,EAAEC,YAAY,CAAC,KAAKD,KAAK,KAAK,CAAC,IAAI,CAACC,YAAY,CAAC,EAAEluB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC5FS,SAAS,CAACsb,KAAK,IAAI;MACpB,IAAI,CAAC7O,mBAAmB,CAACtB,YAAY,CAACmQ,KAAK,CAAC;IAChD,CAAC,CAAC;IACF,IAAI,CAACpf,sBAAsB,GAAG9N,gBAAgB,CAAC,UAAU,CAAC;IAC1D,IAAI,CAACqe,mBAAmB,CAACZ,sBAAsB,CAAC9L,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC4L,WAAW,IAAI;MACpG,IAAI,CAAC3P,kBAAkB,GAAG2P,WAAW;MACrC,IAAI,CAAC3M,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;IACF,IAAI,CAACuN,mBAAmB,CAACd,oBAAoB,CAAC5L,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAACS,SAAS,CAAC4L,WAAW,IAAI;MAClG,IAAI,CAACjP,oBAAoB,GAAGiP,WAAW;MACvC,IAAI,CAAC3M,GAAG,CAACC,YAAY,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACAmF,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAE0V,QAAQ;MAAE/b,WAAW;MAAEH,UAAU;MAAEqc,iBAAiB;MAAEN,MAAM;MAAEC,cAAc;MAAEF,aAAa;MAAEF,UAAU;MAAEU;IAAe,CAAC,GAAG9V,OAAO;IAC3I,IAAIrG,WAAW,EAAE;MACb,IAAI,CAAC+R,kBAAkB,CAAC5C,eAAe,CAAC,IAAI,CAACnP,WAAW,CAAC;IAC7D;IACA,IAAIH,UAAU,EAAE;MACZ,IAAI,CAACkS,kBAAkB,CAAClD,cAAc,CAAC,IAAI,CAAChP,UAAU,CAAC;IAC3D;IACA,IAAI+b,MAAM,EAAE;MACR,IAAI,CAACA,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,EAAE;MAC/B,IAAI,CAAC7J,kBAAkB,CAAC1C,gBAAgB,CAAC,IAAI,CAACuM,MAAM,CAAC;IACzD;IACA,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACA,cAAc,GAAG,IAAI,CAACA,cAAc,IAAI,EAAE;MAC/C,IAAI,CAAC9J,kBAAkB,CAACxC,wBAAwB,CAAC,IAAI,CAACsM,cAAc,CAAC;IACzE;IACA,IAAIK,iBAAiB,EAAE;MACnB,IAAI,CAACnK,kBAAkB,CAAC/C,qBAAqB,CAAC,IAAI,CAACkN,iBAAiB,CAAC;IACzE;IACA,IAAIH,QAAQ,EAAE;MACV,IAAI,CAACuB,kBAAkB,CAAC,CAAC;IAC7B;IACA,IAAI3B,aAAa,EAAE;MACf,IAAI,CAACnN,mBAAmB,CAAC3C,mBAAmB,CAAC,IAAI,CAAC8P,aAAa,CAAC;IACpE;IACA,IAAIQ,cAAc,EAAE;MAChB,IAAI,CAACU,aAAa,CAAC/b,IAAI,CAAC,IAAI,CAACqb,cAAc,CAAC;IAChD;IACA,IAAIV,UAAU,EAAE;MACZ,IAAI,CAACjN,mBAAmB,CAACnB,WAAW,CAAC,IAAI,CAACoO,UAAU,CAAC;IACzD;IACA,IAAI,CAAC8B,oBAAoB,CAAC,CAAC;EAC/B;EACAzG,eAAeA,CAAA,EAAG;IACd,IAAI,CAACU,gBAAgB,CAChBK,OAAO,CAAC,IAAI,CAACnO,UAAU,CAAC,CACxB5H,IAAI,CAAC3S,GAAG,CAAC,CAAC,CAAC2oB,KAAK,CAAC,KAAK;MACvB,MAAM;QAAE7K;MAAM,CAAC,GAAG6K,KAAK,CAACC,MAAM,CAACC,qBAAqB,CAAC,CAAC;MACtD,MAAMwF,cAAc,GAAG,IAAI,CAAC7gB,OAAO,GAAG,IAAI,CAACsB,sBAAsB,GAAG,CAAC;MACrE,OAAOsT,IAAI,CAAC0G,KAAK,CAAChL,KAAK,GAAGuQ,cAAc,CAAC;IAC7C,CAAC,CAAC,EAAEtuB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CACxBS,SAAS,CAAC,IAAI,CAACyM,mBAAmB,CAAC9T,UAAU,CAAC;IACnD,IAAI,IAAI,CAAC+iB,2BAA2B,IAAI,IAAI,CAACA,2BAA2B,CAAClD,wBAAwB,EAAE;MAC/F,IAAI,CAACA,wBAAwB,GAAG,IAAI,CAACkD,2BAA2B,CAAClD,wBAAwB;IAC7F;EACJ;EACAjU,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA+W,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACthB,OAAO,GAAI,IAAI,CAAC+f,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,CAAC,IAAK,IAAI;IACzD,IAAI,CAACrf,OAAO,GAAI,IAAI,CAACof,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACE,CAAC,IAAK,IAAI;IACzD,IAAI,CAACzN,mBAAmB,CAACjB,SAAS,CAAC,IAAI,CAACvR,OAAO,EAAE,IAAI,CAACW,OAAO,CAAC;EAClE;EACA4gB,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACne,cAAc,GACd,IAAI,CAACK,kBAAkB,IAAI,IAAI,CAACmc,MAAM,CAACvgB,MAAM,GAAG,IAAI,CAACwE,UAAU,IAC3D,IAAI,CAAC+b,MAAM,CAACvgB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAACoE,kBAAmB,IACnD,CAAC,IAAI,CAACyc,iBAAiB,IAAI,IAAI,CAACpc,OAAO,GAAG,IAAI,CAACD,UAAW;EACvE;EACA;IAAS,IAAI,CAACqC,IAAI,YAAAwb,yBAAAtb,CAAA;MAAA,YAAAA,CAAA,IAAwFmZ,gBAAgB,EAnwE1B/tB,EAAE,CAAA6U,iBAAA,CAmwE0C7U,EAAE,CAACE,UAAU,GAnwEzDF,EAAE,CAAA6U,iBAAA,CAmwEoEvQ,IAAI,CAACsmB,gBAAgB,GAnwE3F5qB,EAAE,CAAA6U,iBAAA,CAmwEsGzS,EAAE,CAAC0S,eAAe,GAnwE1H9U,EAAE,CAAA6U,iBAAA,CAmwEqI7U,EAAE,CAAC+U,iBAAiB,GAnwE3J/U,EAAE,CAAA6U,iBAAA,CAmwEsK+I,mBAAmB,GAnwE3L5d,EAAE,CAAA6U,iBAAA,CAmwEsMuM,kBAAkB,GAnwE1NphB,EAAE,CAAA6U,iBAAA,CAmwEqOhQ,IAAI,CAACsrB,cAAc;IAAA,CAA4D;EAAE;EACxZ;IAAS,IAAI,CAACnb,IAAI,kBApwE8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAowEJ6Y,gBAAgB;MAAA5Y,SAAA;MAAAib,cAAA,WAAAC,gCAAArrB,EAAA,EAAAC,GAAA,EAAAqrB,QAAA;QAAA,IAAAtrB,EAAA;UApwEdhF,EAAE,CAAAuwB,cAAA,CAAAD,QAAA,EAowEkoDnD,6BAA6B;QAAA;QAAA,IAAAnoB,EAAA;UAAA,IAAAuQ,EAAA;UApwEjqDvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAyL,wBAAA,GAAA6E,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAN,SAAA,WAAAob,uBAAAxrB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAsV,WAAA,CAowE4wD8V,2BAA2B;QAAA;QAAA,IAAApmB,EAAA;UAAA,IAAAuQ,EAAA;UApwEzyDvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAgrB,2BAAA,GAAA1a,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAwD,SAAA;MAAAuB,QAAA;MAAAC,YAAA,WAAA+V,8BAAAzrB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAuI,WAAA,0BAAAtD,GAAA,CAAAqqB,GAAA,KAowEI,KAAO,CAAC,4BAAArqB,GAAA,CAAAopB,cAAA,CAAAxgB,MAAD,CAAC;QAAA;MAAA;MAAA8H,MAAA;QAAA1E,aAAA;QAAAiB,WAAA;QAAAH,YAAA;QAAA3B,OAAA;QAAAgB,QAAA;QAAA6c,UAAA;QAAAnc,iBAAA;QAAAlB,iBAAA;QAAAC,oBAAA;QAAAC,oBAAA;QAAAC,mBAAA;QAAAmd,cAAA;QAAA1b,WAAA;QAAAH,UAAA;QAAAC,OAAA;QAAA6b,aAAA;QAAAC,MAAA;QAAAC,cAAA;QAAAC,oBAAA;QAAAC,QAAA;QAAApc,gBAAA;QAAAuc,iBAAA;QAAAC,cAAA;QAAAjc,gBAAA;QAAAkc,SAAA;QAAAC,eAAA;QAAAC,kBAAA;QAAAC,UAAA;QAAA3c,MAAA;QAAAP,iBAAA;QAAAI,kBAAA;QAAAD,iBAAA;QAAAO,QAAA;MAAA;MAAAsD,OAAA;QAAAmZ,gBAAA;QAAAC,iBAAA;QAAAC,aAAA;QAAAC,uBAAA;QAAAC,oBAAA;MAAA;MAAAtZ,QAAA;MAAAC,UAAA;MAAAC,QAAA,GApwEdhW,EAAE,CAAAiW,kBAAA,CAowEygD,CAAC2H,mBAAmB,EAAEwD,kBAAkB,CAAC,GApwEpjDphB,EAAE,CAAAmZ,oBAAA,EAAFnZ,EAAE,CAAAkW,mBAAA;MAAAC,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAma,0BAAA1rB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAqF,cAAA,gBAqwEH,CAAC;UArwEArF,EAAE,CAAAwG,UAAA,IAAAyJ,wCAAA,yBAswET,CAAC;UAtwEMjQ,EAAE,CAAAqF,cAAA,eAqxEhG,CAAC;UArxE6FrF,EAAE,CAAAwG,UAAA,IAAA2J,iDAAA,kCAsxEtC,CAAC,IAAAE,iDAAA,mCAgBzD,CAAC,IAAAW,uCAAA,gCAtyE2FhR,EAAE,CAAAmK,sBAuyEjE,CAAC,IAAAgH,iDAAA,kCAQ6B,CAAC;UA/yEgCnR,EAAE,CAAA+F,YAAA,CAgzE3F,CAAC;UAhzEwF/F,EAAE,CAAAwG,UAAA,IAAA8K,wCAAA,yBAizEN,CAAC;UAjzEGtR,EAAE,CAAA+F,YAAA,CAozEzF,CAAC;UApzEsF/F,EAAE,CAAAwG,UAAA,KAAAiM,wCAAA,gCAAFzS,EAAE,CAAAmK,sBAqzElE,CAAC,KAAAwI,wCAAA,gCArzE+D3S,EAAE,CAAAmK,sBAy0ErE,CAAC;QAAA;QAAA,IAAAnF,EAAA;UAAA,MAAA2rB,kBAAA,GAz0EkE3wB,EAAE,CAAA2H,WAAA;UAAF3H,EAAE,CAAAgG,UAAA,YAAAf,GAAA,CAAAipB,cAqwEhE,CAAC,eAAAjpB,GAAA,CAAA2pB,SAAwB,CAAC,gBAAA3pB,GAAA,CAAA6pB,kBAAkC,CAAC;UArwEC9uB,EAAE,CAAA0G,SAAA,CAswEX,CAAC;UAtwEQ1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAqpB,oBAAA,eAAArpB,GAAA,CAAAqpB,oBAAA,UAswEX,CAAC;UAtwEQtuB,EAAE,CAAA0G,SAAA,CA4wE1D,CAAC;UA5wEuD1G,EAAE,CAAAuI,WAAA,kBAAAtD,GAAA,CAAAqqB,GAAA,UA4wE1D,CAAC,2BAAArqB,GAAA,CAAAmpB,MAAA,CAAAvgB,MAAA,IAAA5I,GAAA,CAAAkK,OACmB,CAAC,2BAAAlK,GAAA,CAAAuJ,OAClB,CAAC,2BAAAvJ,GAAA,CAAA+Y,UACE,CAAC,4BAAA/Y,GAAA,CAAAkZ,WACC,CAAC,uBAAAlZ,GAAA,CAAA8pB,UACP,CAAC,0BAAA9pB,GAAA,CAAA4pB,eAAA,KAAA5pB,GAAA,CAAA8pB,UACsB,CAAC,qBAAA9pB,GAAA,CAAAmN,MAAA,aACjB,CAAC,oBAAAnN,GAAA,CAAAmN,MAAA,YACH,CAAC;UApxEgDpS,EAAE,CAAA0G,SAAA,EAsxExC,CAAC;UAtxEqC1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAmL,OAsxExC,CAAC;UAtxEqCpQ,EAAE,CAAA0G,SAAA,CAwxElE,CAAC;UAxxE+D1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAkK,OAAA,IAAAlK,GAAA,CAAAuJ,OAwxElE,CAAC,aAAAmiB,kBAAmB,CAAC;UAxxE2C3wB,EAAE,CAAA0G,SAAA,EA+yErC,CAAC;UA/yEkC1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAmM,QA+yErC,CAAC;UA/yEkCpR,EAAE,CAAA0G,SAAA,CAizER,CAAC;UAjzEK1G,EAAE,CAAAgG,UAAA,SAAAf,GAAA,CAAAqpB,oBAAA,eAAArpB,GAAA,CAAAqpB,oBAAA,aAizER,CAAC;QAAA;MAAA;MAAA3X,YAAA,GA2BhCjS,eAAe,EAA2J1B,IAAI,EAA6FD,gBAAgB,EAAoJ0qB,2BAA2B,EAA+FrC,2BAA2B,EAAoUxB,4BAA4B,EAAiJnlB,kBAAkB,EAA+BD,IAAI,CAACosB,qBAAqB;MAAAha,aAAA;MAAAC,eAAA;IAAA,EAAya;EAAE;AAC1mD;AACA5V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC3D7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACxD7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,kBAAkB,EAAE,KAAK,CAAC,CAAC;AAC1D7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;AACnD7V,UAAU,CAAC,CACPuB,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC;AACzD7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,CACf,EAAE0rB,gBAAgB,CAACjX,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;AAC5D7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACpD7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,CACf,EAAE0rB,gBAAgB,CAACjX,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AAChD7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC3D7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,oBAAoB,EAAE,KAAK,CAAC,CAAC;AAC5D7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC3D7V,UAAU,CAAC,CACPoB,UAAU,CAAC,CAAC,EACZG,YAAY,CAAC,CAAC,CACjB,EAAEurB,gBAAgB,CAACjX,SAAS,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;AAClD;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAv3EoG/W,EAAE,CAAAgX,iBAAA,CAu3EX+W,gBAAgB,EAAc,CAAC;IAC9G7Y,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,UAAU;MACpBpB,QAAQ,EAAE,SAAS;MACnBwB,SAAS,EAAE,CAACsG,mBAAmB,EAAEwD,kBAAkB,CAAC;MACpDhK,mBAAmB,EAAE,KAAK;MAC1BP,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBwD,IAAI,EAAE;QACFC,KAAK,EAAE,mBAAmB;QAC1B,+BAA+B,EAAE,eAAe;QAChD,iCAAiC,EAAG;MACxC,CAAC;MACDzC,OAAO,EAAE,CACL7S,eAAe,EACf1B,IAAI,EACJD,gBAAgB,EAChB0qB,2BAA2B,EAC3BrC,2BAA2B,EAC3BxB,4BAA4B,EAC5BnlB,kBAAkB,CACrB;MACDsR,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,EAAE;IAAEgV,IAAI,EAAE5Q,IAAI,CAACsmB;EAAiB,CAAC,EAAE;IAAE1V,IAAI,EAAE9S,EAAE,CAAC0S;EAAgB,CAAC,EAAE;IAAEI,IAAI,EAAElV,EAAE,CAAC+U;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAE0I;EAAoB,CAAC,EAAE;IAAE1I,IAAI,EAAEkM;EAAmB,CAAC,EAAE;IAAElM,IAAI,EAAErQ,IAAI,CAACsrB,cAAc;IAAEhP,UAAU,EAAE,CAAC;MAC9OjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEsQ,aAAa,EAAE,CAAC;MACzCiE,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE4R,WAAW,EAAE,CAAC;MACdgD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyR,YAAY,EAAE,CAAC;MACfmD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8P,OAAO,EAAE,CAAC;MACV8E,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8Q,QAAQ,EAAE,CAAC;MACX8D,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2tB,UAAU,EAAE,CAAC;MACb/Y,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwR,iBAAiB,EAAE,CAAC;MACpBoD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsQ,iBAAiB,EAAE,CAAC;MACpBsE,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEuQ,oBAAoB,EAAE,CAAC;MACvBqE,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwQ,oBAAoB,EAAE,CAAC;MACvBoE,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyQ,mBAAmB,EAAE,CAAC;MACtBmE,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE4tB,cAAc,EAAE,CAAC;MACjBhZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEkS,WAAW,EAAE,CAAC;MACd0C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+R,UAAU,EAAE,CAAC;MACb6C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEgS,OAAO,EAAE,CAAC;MACV4C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6tB,aAAa,EAAE,CAAC;MAChBjZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8tB,MAAM,EAAE,CAAC;MACTlZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE+tB,cAAc,EAAE,CAAC;MACjBnZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEguB,oBAAoB,EAAE,CAAC;MACvBpZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEiuB,QAAQ,EAAE,CAAC;MACXrZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE6R,gBAAgB,EAAE,CAAC;MACnB+C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEouB,iBAAiB,EAAE,CAAC;MACpBxZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEquB,cAAc,EAAE,CAAC;MACjBzZ,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEoS,gBAAgB,EAAE,CAAC;MACnBwC,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEsuB,SAAS,EAAE,CAAC;MACZ1Z,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEuuB,eAAe,EAAE,CAAC;MAClB3Z,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEwuB,kBAAkB,EAAE,CAAC;MACrB5Z,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEyuB,UAAU,EAAE,CAAC;MACb7Z,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE8R,MAAM,EAAE,CAAC;MACT8C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEuR,iBAAiB,EAAE,CAAC;MACpBqD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE2R,kBAAkB,EAAE,CAAC;MACrBiD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE0R,iBAAiB,EAAE,CAAC;MACpBkD,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAEiS,QAAQ,EAAE,CAAC;MACX2C,IAAI,EAAE5U;IACV,CAAC,CAAC;IAAE0uB,gBAAgB,EAAE,CAAC;MACnB9Z,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE0uB,iBAAiB,EAAE,CAAC;MACpB/Z,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE2uB,aAAa,EAAE,CAAC;MAChBha,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE4uB,uBAAuB,EAAE,CAAC;MAC1Bja,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAE6uB,oBAAoB,EAAE,CAAC;MACvBla,IAAI,EAAE3U;IACV,CAAC,CAAC;IAAEmQ,wBAAwB,EAAE,CAAC;MAC3BwE,IAAI,EAAEpU,YAAY;MAClBmW,IAAI,EAAE,CAACkW,6BAA6B,EAAE;QAAE3V,MAAM,EAAE;MAAM,CAAC;IAC3D,CAAC,CAAC;IAAEyY,2BAA2B,EAAE,CAAC;MAC9B/a,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAACmU,2BAA2B;IACtC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMyF,aAAa,CAAC;EAChBld,WAAWA,CAACqN,mBAAmB,EAAE;IAC7B,IAAI,CAACA,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAAClN,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC2vB,mBAAmB,GAAG,IAAI1vB,aAAa,CAAC,CAAC,CAAC;IAC/C,IAAI,CAAC2vB,cAAc,GAAG,IAAI3vB,aAAa,CAAC,CAAC,CAAC;IAC1C,IAAI,CAAC4vB,0BAA0B,GAAG,IAAI,CAACF,mBAAmB,CAACxc,IAAI,CAACtS,SAAS,CAACwe,IAAI,IAAIjf,KAAK,CAAC,GAAG,CAAC,IAAI,CAACuvB,mBAAmB,EAAE,GAAGtQ,IAAI,CAAC7e,GAAG,CAAEsvB,CAAC,IAAKA,CAAC,CAAC/T,QAAQ,CAAC,CAAC,CAAC,CAAC5I,IAAI,CAACnS,QAAQ,CAAC,MAAM,IAAI,CAAC2uB,mBAAmB,CAAC,CAAC,CAAC,EAAEpvB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;IACjO,IAAI,CAACod,6BAA6B,GAAG,IAAI,CAACF,0BAA0B,CAAC1c,IAAI,CAAC3S,GAAG,CAAC6e,IAAI,IAAIA,IAAI,CAACze,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAACmF,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC;IAClI,IAAI,CAACoU,8BAA8B,GAAG,IAAI,CAACH,0BAA0B,CAAC1c,IAAI,CAAC3S,GAAG,CAAC6e,IAAI,IAAIA,IAAI,CAACze,MAAM,CAAC6V,IAAI,IAAIA,IAAI,CAACkF,OAAO,KAAK,KAAK,CAAC,CAAC,CAAC;IACpI,IAAI,CAACsU,qBAAqB,GAAG,IAAI,CAACL,cAAc,CAACzc,IAAI,CAACtS,SAAS,CAACwe,IAAI,IAAIjf,KAAK,CAAC,GAAG,CAAC,IAAI,CAACwvB,cAAc,EAAE,GAAGvQ,IAAI,CAAC7e,GAAG,CAAEsvB,CAAC,IAAKA,CAAC,CAAC/T,QAAQ,CAAC,CAAC,CAAC,CAAC5I,IAAI,CAACnS,QAAQ,CAAC,MAAM,IAAI,CAAC4uB,cAAc,CAAC,CAAC,CAAC,EAAErvB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;IAC7M,IAAI,CAAClG,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACA,aAAa,GAAG,CAAC,CAACoT,mBAAmB;EAC9C;EACAqQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACrQ,mBAAmB,EAAE;MAC1B,IAAI,CAACsQ,wBAAwB,CAACzY,OAAO,CAChCvE,IAAI,CAACrS,SAAS,CAAC,IAAI,CAACqvB,wBAAwB,CAAC,EAAE5vB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CACxES,SAAS,CAAC,IAAI,CAACuc,mBAAmB,CAAC;MACxC,IAAI,CAACS,mBAAmB,CAAC1Y,OAAO,CAC3BvE,IAAI,CAACrS,SAAS,CAAC,IAAI,CAACsvB,mBAAmB,CAAC,EAAE7vB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CACnES,SAAS,CAAC,IAAI,CAACwc,cAAc,CAAC;MACnC;MACA,IAAI,CAACG,6BAA6B,CAAC3c,SAAS,CAACid,eAAe,IAAI;QAC5DA,eAAe,CAAC7S,OAAO,CAAC8S,IAAI,IAAIA,IAAI,CAACjV,aAAa,CAACiV,IAAI,KAAKD,eAAe,CAACA,eAAe,CAAC3jB,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC;MAC7G,CAAC,CAAC;MACF,IAAI,CAACsjB,8BAA8B,CAAC5c,SAAS,CAACmd,gBAAgB,IAAI;QAC9DA,gBAAgB,CAAC/S,OAAO,CAAC8S,IAAI,IAAIA,IAAI,CAACpV,eAAe,CAACoV,IAAI,KAAKC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,CAAC;MACF;MACApwB,aAAa,CAAC,CAAC,IAAI,CAAC0f,mBAAmB,CAACT,oBAAoB,EAAE,IAAI,CAAC2Q,6BAA6B,CAAC,CAAC,CAC7F5c,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAC,CAAC,CAACgL,eAAe,EAAEoS,cAAc,CAAC,KAAK;QAClDA,cAAc,CAAChT,OAAO,CAAC,CAAC8S,IAAI,EAAEpjB,KAAK,KAAK;UACpC,IAAIojB,IAAI,CAACtU,UAAU,EAAE;YACjB,MAAMyU,YAAY,GAAGD,cAAc,CAAC1N,KAAK,CAAC,CAAC,EAAE5V,KAAK,CAAC;YACnD,MAAMkb,KAAK,GAAGqI,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAAC/U,OAAO,IAAI+U,GAAG,CAAC9U,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3F,MAAMwC,KAAK,GAAGF,eAAe,CAAC0E,KAAK,CAAC,CAAC,EAAEsF,KAAK,CAAC,CAACsI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,EAAE,CAAC,CAAC;YAChFN,IAAI,CAAC3V,gBAAgB,CAAE,GAAE2D,KAAM,IAAG,CAAC;UACvC;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;MACFne,aAAa,CAAC,CAAC,IAAI,CAAC0f,mBAAmB,CAACT,oBAAoB,EAAE,IAAI,CAAC4Q,8BAA8B,CAAC,CAAC,CAC9F7c,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAAC,CAAC,CAACgL,eAAe,EAAEyS,eAAe,CAAC,KAAK;QACnDA,eAAe,CAACrT,OAAO,CAAC,CAAChH,CAAC,EAAEtJ,KAAK,KAAK;UAClC,MAAMojB,IAAI,GAAGO,eAAe,CAACA,eAAe,CAACnkB,MAAM,GAAGQ,KAAK,GAAG,CAAC,CAAC;UAChE,IAAIojB,IAAI,CAACrU,WAAW,EAAE;YAClB,MAAMwU,YAAY,GAAGI,eAAe,CAAC/N,KAAK,CAAC+N,eAAe,CAACnkB,MAAM,GAAGQ,KAAK,EAAE2jB,eAAe,CAACnkB,MAAM,CAAC;YAClG,MAAM0b,KAAK,GAAGqI,YAAY,CAACC,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,IAAIC,GAAG,CAAC/U,OAAO,IAAI+U,GAAG,CAAC9U,OAAO,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;YAC3F,MAAMwC,KAAK,GAAGF,eAAe,CACxB0E,KAAK,CAAC1E,eAAe,CAAC1R,MAAM,GAAG0b,KAAK,EAAEhK,eAAe,CAAC1R,MAAM,CAAC,CAC7DgkB,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,GAAGC,GAAG,EAAE,CAAC,CAAC;YACvCN,IAAI,CAACtV,iBAAiB,CAAE,GAAEsD,KAAM,IAAG,CAAC;UACxC;QACJ,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA3G,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAud,sBAAArd,CAAA;MAAA,YAAAA,CAAA,IAAwFic,aAAa,EAjnFvB7wB,EAAE,CAAA6U,iBAAA,CAinFuC+I,mBAAmB;IAAA,CAA4D;EAAE;EAC1N;IAAS,IAAI,CAACrD,IAAI,kBAlnF8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EAknFJ2b,aAAa;MAAA1b,SAAA;MAAAib,cAAA,WAAA8B,6BAAAltB,EAAA,EAAAC,GAAA,EAAAqrB,QAAA;QAAA,IAAAtrB,EAAA;UAlnFXhF,EAAE,CAAAuwB,cAAA,CAAAD,QAAA,EAknFwRhJ,oBAAoB;UAlnF9StnB,EAAE,CAAAuwB,cAAA,CAAAD,QAAA,EAknFuWzU,oBAAoB;QAAA;QAAA,IAAA7W,EAAA;UAAA,IAAAuQ,EAAA;UAlnF7XvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAssB,mBAAA,GAAAhc,EAAA;UAAFvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAqsB,wBAAA,GAAA/b,EAAA;QAAA;MAAA;MAAAkF,QAAA;MAAAC,YAAA,WAAAyX,2BAAAntB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAuI,WAAA,kBAAAtD,GAAA,CAAA2I,aAknFQ,CAAC;QAAA;MAAA;MAAAmI,UAAA;IAAA,EAAsY;EAAE;AACvf;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KApnFoG/W,EAAE,CAAAgX,iBAAA,CAonFX6Z,aAAa,EAAc,CAAC;IAC3G3b,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+GAA+G;MACzH6C,IAAI,EAAE;QACF,uBAAuB,EAAE;MAC7B,CAAC;MACDhE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAE0I,mBAAmB;IAAEuD,UAAU,EAAE,CAAC;MACzDjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE4wB,mBAAmB,EAAE,CAAC;MAC/Crc,IAAI,EAAEnU,eAAe;MACrBkW,IAAI,EAAE,CAACqQ,oBAAoB;IAC/B,CAAC,CAAC;IAAEgK,wBAAwB,EAAE,CAAC;MAC3Bpc,IAAI,EAAEnU,eAAe;MACrBkW,IAAI,EAAE,CAAC4E,oBAAoB;IAC/B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMuW,gBAAgB,CAAC;EACnBze,WAAWA,CAACuI,UAAU,EAAEF,QAAQ,EAAEgF,mBAAmB,EAAEuD,kBAAkB,EAAE;IACvE,IAAI,CAACrI,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgF,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACuD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACzQ,QAAQ,GAAG,IAAI3S,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACyM,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACkZ,iBAAiB,GAAG,IAAI7mB,YAAY,CAAC,CAAC;IAC3C,IAAI,CAAC2N,aAAa,GAAG,CAAC,CAAC,IAAI,CAACoT,mBAAmB;EACnD;EACA9M,QAAQA,CAAA,EAAG;IACP,IAAI,IAAI,CAAC8M,mBAAmB,EAAE;MAC1B,IAAI,CAACA,mBAAmB,CAACnD,gBAAgB,CAAC,IAAI,CAAClN,WAAW,CAAC;IAC/D;EACJ;EACA0gB,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACrQ,mBAAmB,EAAE;MAC1B,MAAMqR,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAACzZ,OAAO,CAACvE,IAAI,CAACrS,SAAS,CAAC,IAAI,CAACqwB,mBAAmB,CAAC,EAAE3wB,GAAG,CAACiW,IAAI,IAAIA,IAAI,IAAIA,IAAI,CAAClC,KAAK,CAAC,CAAC;MAClI,MAAM0b,qBAAqB,GAAGiB,cAAc,CAAC/d,IAAI,CAACtS,SAAS,CAACuwB,aAAa,IAAKA,aAAa,GAAGA,aAAa,CAACnB,qBAAqB,GAAG5vB,KAAM,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;MACtKsd,qBAAqB,CAAC7c,SAAS,CAACrF,IAAI,IAAI,IAAI,CAAC8R,mBAAmB,CAACxC,WAAW,CAACtP,IAAI,CAAC,CAAC;MACnF;MACA,IAAI,CAAC8R,mBAAmB,CAACf,kBAAkB,CACtC3L,IAAI,CAACtS,SAAS,CAACwwB,MAAM,IAAKA,MAAM,GAAGpB,qBAAqB,GAAG3vB,EAAE,CAAC,EAAE,CAAE,CAAC,CAAC,CACpE6S,IAAI,CAAC5S,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC,CAC9BS,SAAS,CAACrF,IAAI,IAAI,IAAI,CAAC8R,mBAAmB,CAAC/B,sBAAsB,CAAC/P,IAAI,CAAC,CAAC;MAC7E,MAAMgiB,6BAA6B,GAAGmB,cAAc,CAAC/d,IAAI,CAACtS,SAAS,CAACywB,OAAO,IAAKA,OAAO,GAAGA,OAAO,CAACvB,6BAA6B,GAAG1vB,KAAM,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;MACpK,MAAMqd,8BAA8B,GAAGkB,cAAc,CAAC/d,IAAI,CAACtS,SAAS,CAACywB,OAAO,IAAKA,OAAO,GAAGA,OAAO,CAACtB,8BAA8B,GAAG3vB,KAAM,CAAC,EAAEE,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;MACtKod,6BAA6B,CAAC3c,SAAS,CAACme,qBAAqB,IAAI;QAC7D,IAAI,CAAC1R,mBAAmB,CAACjD,aAAa,CAAC2U,qBAAqB,CAAC7kB,MAAM,KAAK,CAAC,CAAC;MAC9E,CAAC,CAAC;MACFsjB,8BAA8B,CAAC5c,SAAS,CAACoe,sBAAsB,IAAI;QAC/D,IAAI,CAAC3R,mBAAmB,CAAC9C,cAAc,CAACyU,sBAAsB,CAAC9kB,MAAM,KAAK,CAAC,CAAC;MAChF,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAAC0W,kBAAkB,EAAE;MACzB,MAAMqO,aAAa,GAAG,IAAI,CAACC,wBAAwB,CAACha,OAAO,CAACvE,IAAI,CAACrS,SAAS,CAAC,IAAI,CAAC4wB,wBAAwB,CAAC,CAAC;MAC1G,MAAMC,WAAW,GAAGF,aAAa,CAACte,IAAI,CAACtS,SAAS,CAAC,MAAMT,KAAK,CAAC,GAAG,IAAI,CAACsxB,wBAAwB,CAAClxB,GAAG,CAACid,EAAE,IAAIA,EAAE,CAACyH,iBAAiB,CAAC,CAAC,CAAC,EAAE3kB,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;MAC1Jgf,WAAW,CAACve,SAAS,CAAErF,IAAI,IAAK;QAC5B,MAAM6jB,SAAS,GAAG;UAAErQ,GAAG,EAAExT,IAAI,CAACkY,WAAW;UAAEvP,KAAK,EAAE3I,IAAI,CAAC3F;QAAU,CAAC;QAClE,IAAI,CAACud,iBAAiB,CAAC1O,IAAI,CAAC2a,SAAS,CAAC;QACtC,IAAI7jB,IAAI,CAACyX,QAAQ,IAAIzX,IAAI,CAACuX,cAAc,KAAK,KAAK,EAAE;UAChD,IAAI,CAACoM,wBAAwB,CAAC9wB,MAAM,CAAC6c,EAAE,IAAIA,EAAE,KAAK1P,IAAI,CAAC,CAACyP,OAAO,CAACC,EAAE,IAAIA,EAAE,CAACoH,cAAc,CAAC,CAAC,CAAC;QAC9F;MACJ,CAAC,CAAC;MACF,MAAM7D,mBAAmB,GAAGyQ,aAAa,CAACte,IAAI,CAACtS,SAAS,CAACwe,IAAI,IAAIjf,KAAK,CAAC,GAAG,CAACqxB,aAAa,EAAE,GAAGpS,IAAI,CAAC7e,GAAG,CAAEsvB,CAAC,IAAKA,CAAC,CAAC7K,mBAAmB,CAAC,CAAC,CAAC,CAAC9R,IAAI,CAACnS,QAAQ,CAAC,MAAMywB,aAAa,CAAC,CAAC,CAAC,EAAEjxB,GAAG,CAAC6e,IAAI,IAAIA,IAAI,CACvLze,MAAM,CAAC6V,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC+O,QAAQ,IAAI,CAAC,CAAC/O,IAAI,CAACgP,UAAU,CAAC,CACpDjlB,GAAG,CAACiW,IAAI,IAAI;QACb,MAAM;UAAE+O,QAAQ;UAAEpd,SAAS;UAAEqd,UAAU;UAAEV,aAAa;UAAEO,cAAc;UAAEW;QAAY,CAAC,GAAGxP,IAAI;QAC5F,OAAO;UACH8K,GAAG,EAAE0E,WAAW;UAChB3E,MAAM,EAAEkE,QAAQ;UAChBlD,YAAY,EAAEgD,cAAc;UAC5Bld,SAAS,EAAEA,SAAS;UACpBoZ,QAAQ,EAAEiE,UAAU;UACpBhE,WAAW,EAAEsD;QACjB,CAAC;MACL,CAAC,CAAC,CAAC;MACH;MACAhkB,KAAK,CAAC,CAAC,CAAC,EAAER,SAAS,CAAC,IAAI,CAACoS,QAAQ,CAAC,CAAC;MACnCqO,mBAAmB,CAAC5N,SAAS,CAACiM,IAAI,IAAI;QAClC,IAAI,CAAC+D,kBAAkB,CAACpC,mBAAmB,CAAC7O,IAAI,CAACkN,IAAI,CAAC;MAC1D,CAAC,CAAC;IACN;EACJ;EACA8I,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACtI,mBAAmB,EAAE;MAC1B,IAAI,CAAChF,QAAQ,CAACgX,WAAW,CAAC,IAAI,CAAChX,QAAQ,CAACiX,UAAU,CAAC,IAAI,CAAC/W,UAAU,CAAC7H,aAAa,CAAC,EAAE,IAAI,CAAC6H,UAAU,CAAC7H,aAAa,CAAC;IACrH;EACJ;EACAyE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChF,QAAQ,CAACR,IAAI,CAAC,CAAC;IACpB,IAAI,CAACQ,QAAQ,CAACiF,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrE,IAAI,YAAAwe,yBAAAte,CAAA;MAAA,YAAAA,CAAA,IAAwFwd,gBAAgB,EAttF1BpyB,EAAE,CAAA6U,iBAAA,CAstF0C7U,EAAE,CAACE,UAAU,GAttFzDF,EAAE,CAAA6U,iBAAA,CAstFoE7U,EAAE,CAAC0d,SAAS,GAttFlF1d,EAAE,CAAA6U,iBAAA,CAstF6F+I,mBAAmB,MAttFlH5d,EAAE,CAAA6U,iBAAA,CAstF6IuM,kBAAkB;IAAA,CAA4D;EAAE;EAC/T;IAAS,IAAI,CAACpM,IAAI,kBAvtF8EhV,EAAE,CAAAiV,iBAAA;MAAAC,IAAA,EAutFJkd,gBAAgB;MAAAjd,SAAA;MAAAib,cAAA,WAAA+C,gCAAAnuB,EAAA,EAAAC,GAAA,EAAAqrB,QAAA;QAAA,IAAAtrB,EAAA;UAvtFdhF,EAAE,CAAAuwB,cAAA,CAAAD,QAAA,EAutF4LO,aAAa;UAvtF3M7wB,EAAE,CAAAuwB,cAAA,CAAAD,QAAA,EAutFuR5K,kBAAkB;QAAA;QAAA,IAAA1gB,EAAA;UAAA,IAAAuQ,EAAA;UAvtF3SvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAAqtB,mBAAA,GAAA/c,EAAA;UAAFvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAA4tB,wBAAA,GAAAtd,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAAge,uBAAApuB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAsV,WAAA,CAAA1C,IAAA;QAAA;QAAA,IAAA5N,EAAA;UAAA,IAAAuQ,EAAA;UAAFvV,EAAE,CAAAwV,cAAA,CAAAD,EAAA,GAAFvV,EAAE,CAAAyV,WAAA,QAAAxQ,GAAA,CAAA0L,WAAA,GAAA4E,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAG,OAAA;QAAAiR,iBAAA;MAAA;MAAA/Q,UAAA;MAAAC,QAAA,GAAFhW,EAAE,CAAAkW,mBAAA;MAAAC,kBAAA,EAAArR,GAAA;MAAAsR,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8c,0BAAAruB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFhF,EAAE,CAAAyW,eAAA;UAAFzW,EAAE,CAAAwG,UAAA,IAAAqM,uCAAA,gCAAF7S,EAAE,CAAAmK,sBAwtFrE,CAAC,IAAA4I,wCAAA,yBAGM,CAAC;QAAA;QAAA,IAAA/N,EAAA;UA3tF2DhF,EAAE,CAAA0G,SAAA,EA2tFhE,CAAC;UA3tF6D1G,EAAE,CAAAgG,UAAA,UAAAf,GAAA,CAAA2I,aA2tFhE,CAAC;QAAA;MAAA;MAAA+I,YAAA,GAGwB3T,IAAI,EAA6FD,gBAAgB;MAAA6T,aAAA;MAAAC,eAAA;IAAA,EAAyN;EAAE;AAC3Y;AACA;EAAA,QAAAE,SAAA,oBAAAA,SAAA,KAhuFoG/W,EAAE,CAAAgX,iBAAA,CAguFXob,gBAAgB,EAAc,CAAC;IAC9Gld,IAAI,EAAE/U,SAAS;IACf8W,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,6BAA6B;MACvCL,eAAe,EAAEzW,uBAAuB,CAAC+W,MAAM;MAC/CP,aAAa,EAAEvW,iBAAiB,CAACgX,IAAI;MACrCd,QAAQ,EAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;MACiBgB,OAAO,EAAE,CAACvU,IAAI,EAAED,gBAAgB,CAAC;MACjCgT,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEb,IAAI,EAAElV,EAAE,CAACE;EAAW,CAAC,EAAE;IAAEgV,IAAI,EAAElV,EAAE,CAAC0d;EAAU,CAAC,EAAE;IAAExI,IAAI,EAAE0I,mBAAmB;IAAEuD,UAAU,EAAE,CAAC;MAC1GjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,EAAE;IAAEuU,IAAI,EAAEkM,kBAAkB;IAAED,UAAU,EAAE,CAAC;MAC3CjM,IAAI,EAAEvU;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEgQ,WAAW,EAAE,CAAC;MACvCuE,IAAI,EAAE1U,SAAS;MACfyW,IAAI,EAAE,CAAC,iBAAiB,EAAE;QAAEO,MAAM,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAE8a,mBAAmB,EAAE,CAAC;MACtBpd,IAAI,EAAEnU,eAAe;MACrBkW,IAAI,EAAE,CAAC4Z,aAAa,EAAE;QAAEyC,WAAW,EAAE;MAAK,CAAC;IAC/C,CAAC,CAAC;IAAET,wBAAwB,EAAE,CAAC;MAC3B3d,IAAI,EAAEnU,eAAe;MACrBkW,IAAI,EAAE,CAACyO,kBAAkB,EAAE;QAAE4N,WAAW,EAAE;MAAK,CAAC;IACpD,CAAC,CAAC;IAAExM,iBAAiB,EAAE,CAAC;MACpB5R,IAAI,EAAE3U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMgzB,mBAAmB,CAAC;EACtB5f,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9J,QAAQ,GAAG,IAAI;EACxB;EACA;IAAS,IAAI,CAAC6K,IAAI,YAAA8e,4BAAA5e,CAAA;MAAA,YAAAA,CAAA,IAAwF2e,mBAAmB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAAChZ,IAAI,kBA3wF8Eva,EAAE,CAAAwa,iBAAA;MAAAtF,IAAA,EA2wFJqe,mBAAmB;MAAApe,SAAA;MAAA+D,SAAA;MAAAuB,QAAA;MAAAC,YAAA,WAAA+Y,iCAAAzuB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3wFjBhF,EAAE,CAAA6a,cAAA,YAAA5V,GAAA,CAAA4E,QA2wFc,CAAC;QAAA;MAAA;MAAA8L,MAAA;QAAA9L,QAAA;MAAA;MAAAkM,UAAA;IAAA,EAA4L;EAAE;AACnT;AACA;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KA7wFoG/W,EAAE,CAAAgX,iBAAA,CA6wFXuc,mBAAmB,EAAc,CAAC;IACjHre,IAAI,EAAEzU,SAAS;IACfwW,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,cAAc;MACxB6C,IAAI,EAAE;QACFC,KAAK,EAAE,wBAAwB;QAC/B,UAAU,EAAG;MACjB,CAAC;MACDjE,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElM,QAAQ,EAAE,CAAC;MACnDqL,IAAI,EAAE5U;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMozB,aAAa,CAAC;EAChB;IAAS,IAAI,CAAChf,IAAI,YAAAif,sBAAA/e,CAAA;MAAA,YAAAA,CAAA,IAAwF8e,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBAjyF8E5zB,EAAE,CAAA6zB,gBAAA;MAAA3e,IAAA,EAiyFSwe,aAAa;MAAAnc,OAAA,GAAYwW,gBAAgB,EAC5IrI,kBAAkB,EAClB3E,oBAAoB,EACpBuG,oBAAoB,EACpBzC,kBAAkB,EAClBuN,gBAAgB,EAChBrH,gBAAgB,EAChB8F,aAAa,EACb0C,mBAAmB,EACnBpG,6BAA6B,EAC7BtR,oBAAoB,EACpBwI,uBAAuB,EACvB2E,uBAAuB,EACvByE,2BAA2B,EAC3B7D,4BAA4B,EAC5BwB,2BAA2B,EAC3BrB,oBAAoB,EACpBjP,oBAAoB,EACpBb,0BAA0B,EAC1B2O,wBAAwB,EACxBR,oBAAoB,EACpB7M,uBAAuB,EACvB7D,sBAAsB,EACtBwD,yBAAyB,EACzBsN,uBAAuB,EACvBtV,wBAAwB,EACxBmW,wBAAwB,EACxBvB,sBAAsB;MAAAgM,OAAA,GAAa/F,gBAAgB,EACnDrI,kBAAkB,EAClB3E,oBAAoB,EACpBuG,oBAAoB,EACpBzC,kBAAkB,EAClBuN,gBAAgB,EAChBrH,gBAAgB,EAChB8F,aAAa,EACb1D,6BAA6B,EAC7BtR,oBAAoB,EACpBwI,uBAAuB,EACvBnR,wBAAwB,EACxBqgB,mBAAmB,EACnB3K,wBAAwB,EACxBR,oBAAoB,EACpBI,uBAAuB,EACvBa,wBAAwB,EACxBvB,sBAAsB;IAAA,EAAI;EAAE;EACpC;IAAS,IAAI,CAACiM,IAAI,kBA90F8E/zB,EAAE,CAAAg0B,gBAAA;MAAAzc,OAAA,GA80FkCwW,gBAAgB,EAC5IrI,kBAAkB,EAClBb,kBAAkB,EAClBkG,gBAAgB,EAChB0C,2BAA2B,EAC3BrC,2BAA2B,EAC3B7P,uBAAuB,EACvB7D,sBAAsB,EACtBwD,yBAAyB,EACzBhI,wBAAwB,EACxB4U,sBAAsB;IAAA,EAAI;EAAE;AACxC;AACA;EAAA,QAAA/Q,SAAA,oBAAAA,SAAA,KA11FoG/W,EAAE,CAAAgX,iBAAA,CA01FX0c,aAAa,EAAc,CAAC;IAC3Gxe,IAAI,EAAElU,QAAQ;IACdiW,IAAI,EAAE,CAAC;MACCM,OAAO,EAAE,CACLwW,gBAAgB,EAChBrI,kBAAkB,EAClB3E,oBAAoB,EACpBuG,oBAAoB,EACpBzC,kBAAkB,EAClBuN,gBAAgB,EAChBrH,gBAAgB,EAChB8F,aAAa,EACb0C,mBAAmB,EACnBpG,6BAA6B,EAC7BtR,oBAAoB,EACpBwI,uBAAuB,EACvB2E,uBAAuB,EACvByE,2BAA2B,EAC3B7D,4BAA4B,EAC5BwB,2BAA2B,EAC3BrB,oBAAoB,EACpBjP,oBAAoB,EACpBb,0BAA0B,EAC1B2O,wBAAwB,EACxBR,oBAAoB,EACpB7M,uBAAuB,EACvB7D,sBAAsB,EACtBwD,yBAAyB,EACzBsN,uBAAuB,EACvBtV,wBAAwB,EACxBmW,wBAAwB,EACxBvB,sBAAsB,CACzB;MACDgM,OAAO,EAAE,CACL/F,gBAAgB,EAChBrI,kBAAkB,EAClB3E,oBAAoB,EACpBuG,oBAAoB,EACpBzC,kBAAkB,EAClBuN,gBAAgB,EAChBrH,gBAAgB,EAChB8F,aAAa,EACb1D,6BAA6B,EAC7BtR,oBAAoB,EACpBwI,uBAAuB,EACvBnR,wBAAwB,EACxBqgB,mBAAmB,EACnB3K,wBAAwB,EACxBR,oBAAoB,EACpBI,uBAAuB,EACvBa,wBAAwB,EACxBvB,sBAAsB;IAE9B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASM,oBAAoB,EAAEQ,wBAAwB,EAAEJ,uBAAuB,EAAE3M,oBAAoB,EAAEwI,uBAAuB,EAAEnR,wBAAwB,EAAE+G,0BAA0B,EAAEa,oBAAoB,EAAEiG,oBAAoB,EAAEgN,gBAAgB,EAAE/E,uBAAuB,EAAE5H,kBAAkB,EAAE1J,sBAAsB,EAAE2R,wBAAwB,EAAEO,4BAA4B,EAAEwB,2BAA2B,EAAEsI,aAAa,EAAExY,yBAAyB,EAAEK,uBAAuB,EAAEqC,mBAAmB,EAAE6P,2BAA2B,EAAEN,6BAA6B,EAAEpC,gBAAgB,EAAElG,kBAAkB,EAAEa,kBAAkB,EAAE4B,oBAAoB,EAAEQ,sBAAsB,EAAEsK,gBAAgB,EAAEvB,aAAa,EAAE0C,mBAAmB,EAAExJ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}