{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\nimport { LogLevel } from \"./ILogger\";\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\nexport class FetchHttpClient extends HttpClient {\n  constructor(logger) {\n    super();\n    this._logger = logger;\n    // Node added a fetch implementation to the global scope starting in v18.\n    // We need to add a cookie jar in node to be able to share cookies with WebSocket\n    if (typeof fetch === \"undefined\" || Platform.isNode) {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: T<PERSON> doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\n      this._jar = new (requireFunc(\"tough-cookie\").CookieJar)();\n      if (typeof fetch === \"undefined\") {\n        this._fetchType = requireFunc(\"node-fetch\");\n      } else {\n        // Use fetch from Node if available\n        this._fetchType = fetch;\n      }\n      // node-fetch doesn't have a nice API for getting and setting cookies\n      // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\n      this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\n    } else {\n      this._fetchType = fetch.bind(getGlobalThis());\n    }\n    if (typeof AbortController === \"undefined\") {\n      // In order to ignore the dynamic require in webpack builds we need to do this magic\n      // @ts-ignore: TS doesn't know about these names\n      const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\n      // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\n      this._abortControllerType = requireFunc(\"abort-controller\");\n    } else {\n      this._abortControllerType = AbortController;\n    }\n  }\n  /** @inheritDoc */\n  send(request) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Check that abort was not signaled before calling send\n      if (request.abortSignal && request.abortSignal.aborted) {\n        throw new AbortError();\n      }\n      if (!request.method) {\n        throw new Error(\"No method defined.\");\n      }\n      if (!request.url) {\n        throw new Error(\"No url defined.\");\n      }\n      const abortController = new _this._abortControllerType();\n      let error;\n      // Hook our abortSignal into the abort controller\n      if (request.abortSignal) {\n        request.abortSignal.onabort = () => {\n          abortController.abort();\n          error = new AbortError();\n        };\n      }\n      // If a timeout has been passed in, setup a timeout to call abort\n      // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\n      let timeoutId = null;\n      if (request.timeout) {\n        const msTimeout = request.timeout;\n        timeoutId = setTimeout(() => {\n          abortController.abort();\n          _this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\n          error = new TimeoutError();\n        }, msTimeout);\n      }\n      if (request.content === \"\") {\n        request.content = undefined;\n      }\n      if (request.content) {\n        // Explicitly setting the Content-Type header for React Native on Android platform.\n        request.headers = request.headers || {};\n        if (isArrayBuffer(request.content)) {\n          request.headers[\"Content-Type\"] = \"application/octet-stream\";\n        } else {\n          request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\n        }\n      }\n      let response;\n      try {\n        response = yield _this._fetchType(request.url, {\n          body: request.content,\n          cache: \"no-cache\",\n          credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\n          headers: {\n            \"X-Requested-With\": \"XMLHttpRequest\",\n            ...request.headers\n          },\n          method: request.method,\n          mode: \"cors\",\n          redirect: \"follow\",\n          signal: abortController.signal\n        });\n      } catch (e) {\n        if (error) {\n          throw error;\n        }\n        _this._logger.log(LogLevel.Warning, `Error from HTTP request. ${e}.`);\n        throw e;\n      } finally {\n        if (timeoutId) {\n          clearTimeout(timeoutId);\n        }\n        if (request.abortSignal) {\n          request.abortSignal.onabort = null;\n        }\n      }\n      if (!response.ok) {\n        const errorMessage = yield deserializeContent(response, \"text\");\n        throw new HttpError(errorMessage || response.statusText, response.status);\n      }\n      const content = deserializeContent(response, request.responseType);\n      const payload = yield content;\n      return new HttpResponse(response.status, response.statusText, payload);\n    })();\n  }\n  getCookieString(url) {\n    let cookies = \"\";\n    if (Platform.isNode && this._jar) {\n      // @ts-ignore: unused variable\n      this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\n    }\n    return cookies;\n  }\n}\nfunction deserializeContent(response, responseType) {\n  let content;\n  switch (responseType) {\n    case \"arraybuffer\":\n      content = response.arrayBuffer();\n      break;\n    case \"text\":\n      content = response.text();\n      break;\n    case \"blob\":\n    case \"document\":\n    case \"json\":\n      throw new Error(`${responseType} is not supported.`);\n    default:\n      content = response.text();\n      break;\n  }\n  return content;\n}", "map": {"version": 3, "names": ["AbortError", "HttpError", "TimeoutError", "HttpClient", "HttpResponse", "LogLevel", "Platform", "getGlobalThis", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FetchHttpClient", "constructor", "logger", "_logger", "fetch", "isNode", "requireFunc", "__webpack_require__", "__non_webpack_require__", "require", "_jar", "<PERSON><PERSON><PERSON><PERSON>", "_fetchType", "bind", "AbortController", "_abortControllerType", "send", "request", "_this", "_asyncToGenerator", "abortSignal", "aborted", "method", "Error", "url", "abortController", "error", "<PERSON>ab<PERSON>", "abort", "timeoutId", "timeout", "msTimeout", "setTimeout", "log", "Warning", "content", "undefined", "headers", "response", "body", "cache", "credentials", "withCredentials", "mode", "redirect", "signal", "e", "clearTimeout", "ok", "errorMessage", "deserializeContent", "statusText", "status", "responseType", "payload", "getCookieString", "cookies", "getCookies", "c", "join", "arrayBuffer", "text"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/FetchHttpClient.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpResponse } from \"./HttpClient\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\r\nexport class FetchHttpClient extends HttpClient {\r\n    constructor(logger) {\r\n        super();\r\n        this._logger = logger;\r\n        // Node added a fetch implementation to the global scope starting in v18.\r\n        // We need to add a cookie jar in node to be able to share cookies with WebSocket\r\n        if (typeof fetch === \"undefined\" || Platform.isNode) {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            if (typeof fetch === \"undefined\") {\r\n                this._fetchType = requireFunc(\"node-fetch\");\r\n            }\r\n            else {\r\n                // Use fetch from Node if available\r\n                this._fetchType = fetch;\r\n            }\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        }\r\n        else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        }\r\n        else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n    /** @inheritDoc */\r\n    async send(request) {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n        const abortController = new this._abortControllerType();\r\n        let error;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if (isArrayBuffer(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            }\r\n            else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n        let response;\r\n        try {\r\n            response = await this._fetchType(request.url, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        }\r\n        catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(LogLevel.Warning, `Error from HTTP request. ${e}.`);\r\n            throw e;\r\n        }\r\n        finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\");\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n        return new HttpResponse(response.status, response.statusText, payload);\r\n    }\r\n    getCookieString(url) {\r\n        let cookies = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\nfunction deserializeContent(response, responseType) {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n    return content;\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,UAAU,EAAEC,SAAS,EAAEC,YAAY,QAAQ,UAAU;AAC9D,SAASC,UAAU,EAAEC,YAAY,QAAQ,cAAc;AACvD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,QAAQ,EAAEC,aAAa,EAAEC,aAAa,QAAQ,SAAS;AAChE,OAAO,MAAMC,eAAe,SAASN,UAAU,CAAC;EAC5CO,WAAWA,CAACC,MAAM,EAAE;IAChB,KAAK,CAAC,CAAC;IACP,IAAI,CAACC,OAAO,GAAGD,MAAM;IACrB;IACA;IACA,IAAI,OAAOE,KAAK,KAAK,WAAW,IAAIP,QAAQ,CAACQ,MAAM,EAAE;MACjD;MACA;MACA,MAAMC,WAAW,GAAG,OAAOC,mBAAmB,KAAK,UAAU,GAAGC,uBAAuB,GAAGC,OAAO;MACjG;MACA,IAAI,CAACC,IAAI,GAAG,KAAKJ,WAAW,CAAC,cAAc,CAAC,CAAEK,SAAS,EAAC,CAAC;MACzD,IAAI,OAAOP,KAAK,KAAK,WAAW,EAAE;QAC9B,IAAI,CAACQ,UAAU,GAAGN,WAAW,CAAC,YAAY,CAAC;MAC/C,CAAC,MACI;QACD;QACA,IAAI,CAACM,UAAU,GAAGR,KAAK;MAC3B;MACA;MACA;MACA,IAAI,CAACQ,UAAU,GAAGN,WAAW,CAAC,cAAc,CAAC,CAAC,IAAI,CAACM,UAAU,EAAE,IAAI,CAACF,IAAI,CAAC;IAC7E,CAAC,MACI;MACD,IAAI,CAACE,UAAU,GAAGR,KAAK,CAACS,IAAI,CAACf,aAAa,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,OAAOgB,eAAe,KAAK,WAAW,EAAE;MACxC;MACA;MACA,MAAMR,WAAW,GAAG,OAAOC,mBAAmB,KAAK,UAAU,GAAGC,uBAAuB,GAAGC,OAAO;MACjG;MACA,IAAI,CAACM,oBAAoB,GAAGT,WAAW,CAAC,kBAAkB,CAAC;IAC/D,CAAC,MACI;MACD,IAAI,CAACS,oBAAoB,GAAGD,eAAe;IAC/C;EACJ;EACA;EACME,IAAIA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChB;MACA,IAAIF,OAAO,CAACG,WAAW,IAAIH,OAAO,CAACG,WAAW,CAACC,OAAO,EAAE;QACpD,MAAM,IAAI9B,UAAU,CAAC,CAAC;MAC1B;MACA,IAAI,CAAC0B,OAAO,CAACK,MAAM,EAAE;QACjB,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;MACzC;MACA,IAAI,CAACN,OAAO,CAACO,GAAG,EAAE;QACd,MAAM,IAAID,KAAK,CAAC,iBAAiB,CAAC;MACtC;MACA,MAAME,eAAe,GAAG,IAAIP,KAAI,CAACH,oBAAoB,CAAC,CAAC;MACvD,IAAIW,KAAK;MACT;MACA,IAAIT,OAAO,CAACG,WAAW,EAAE;QACrBH,OAAO,CAACG,WAAW,CAACO,OAAO,GAAG,MAAM;UAChCF,eAAe,CAACG,KAAK,CAAC,CAAC;UACvBF,KAAK,GAAG,IAAInC,UAAU,CAAC,CAAC;QAC5B,CAAC;MACL;MACA;MACA;MACA,IAAIsC,SAAS,GAAG,IAAI;MACpB,IAAIZ,OAAO,CAACa,OAAO,EAAE;QACjB,MAAMC,SAAS,GAAGd,OAAO,CAACa,OAAO;QACjCD,SAAS,GAAGG,UAAU,CAAC,MAAM;UACzBP,eAAe,CAACG,KAAK,CAAC,CAAC;UACvBV,KAAI,CAACf,OAAO,CAAC8B,GAAG,CAACrC,QAAQ,CAACsC,OAAO,EAAG,4BAA2B,CAAC;UAChER,KAAK,GAAG,IAAIjC,YAAY,CAAC,CAAC;QAC9B,CAAC,EAAEsC,SAAS,CAAC;MACjB;MACA,IAAId,OAAO,CAACkB,OAAO,KAAK,EAAE,EAAE;QACxBlB,OAAO,CAACkB,OAAO,GAAGC,SAAS;MAC/B;MACA,IAAInB,OAAO,CAACkB,OAAO,EAAE;QACjB;QACAlB,OAAO,CAACoB,OAAO,GAAGpB,OAAO,CAACoB,OAAO,IAAI,CAAC,CAAC;QACvC,IAAItC,aAAa,CAACkB,OAAO,CAACkB,OAAO,CAAC,EAAE;UAChClB,OAAO,CAACoB,OAAO,CAAC,cAAc,CAAC,GAAG,0BAA0B;QAChE,CAAC,MACI;UACDpB,OAAO,CAACoB,OAAO,CAAC,cAAc,CAAC,GAAG,0BAA0B;QAChE;MACJ;MACA,IAAIC,QAAQ;MACZ,IAAI;QACAA,QAAQ,SAASpB,KAAI,CAACN,UAAU,CAACK,OAAO,CAACO,GAAG,EAAE;UAC1Ce,IAAI,EAAEtB,OAAO,CAACkB,OAAO;UACrBK,KAAK,EAAE,UAAU;UACjBC,WAAW,EAAExB,OAAO,CAACyB,eAAe,KAAK,IAAI,GAAG,SAAS,GAAG,aAAa;UACzEL,OAAO,EAAE;YACL,kBAAkB,EAAE,gBAAgB;YACpC,GAAGpB,OAAO,CAACoB;UACf,CAAC;UACDf,MAAM,EAAEL,OAAO,CAACK,MAAM;UACtBqB,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE,QAAQ;UAClBC,MAAM,EAAEpB,eAAe,CAACoB;QAC5B,CAAC,CAAC;MACN,CAAC,CACD,OAAOC,CAAC,EAAE;QACN,IAAIpB,KAAK,EAAE;UACP,MAAMA,KAAK;QACf;QACAR,KAAI,CAACf,OAAO,CAAC8B,GAAG,CAACrC,QAAQ,CAACsC,OAAO,EAAG,4BAA2BY,CAAE,GAAE,CAAC;QACpE,MAAMA,CAAC;MACX,CAAC,SACO;QACJ,IAAIjB,SAAS,EAAE;UACXkB,YAAY,CAAClB,SAAS,CAAC;QAC3B;QACA,IAAIZ,OAAO,CAACG,WAAW,EAAE;UACrBH,OAAO,CAACG,WAAW,CAACO,OAAO,GAAG,IAAI;QACtC;MACJ;MACA,IAAI,CAACW,QAAQ,CAACU,EAAE,EAAE;QACd,MAAMC,YAAY,SAASC,kBAAkB,CAACZ,QAAQ,EAAE,MAAM,CAAC;QAC/D,MAAM,IAAI9C,SAAS,CAACyD,YAAY,IAAIX,QAAQ,CAACa,UAAU,EAAEb,QAAQ,CAACc,MAAM,CAAC;MAC7E;MACA,MAAMjB,OAAO,GAAGe,kBAAkB,CAACZ,QAAQ,EAAErB,OAAO,CAACoC,YAAY,CAAC;MAClE,MAAMC,OAAO,SAASnB,OAAO;MAC7B,OAAO,IAAIxC,YAAY,CAAC2C,QAAQ,CAACc,MAAM,EAAEd,QAAQ,CAACa,UAAU,EAAEG,OAAO,CAAC;IAAC;EAC3E;EACAC,eAAeA,CAAC/B,GAAG,EAAE;IACjB,IAAIgC,OAAO,GAAG,EAAE;IAChB,IAAI3D,QAAQ,CAACQ,MAAM,IAAI,IAAI,CAACK,IAAI,EAAE;MAC9B;MACA,IAAI,CAACA,IAAI,CAAC+C,UAAU,CAACjC,GAAG,EAAE,CAACsB,CAAC,EAAEY,CAAC,KAAKF,OAAO,GAAGE,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC/D;IACA,OAAOH,OAAO;EAClB;AACJ;AACA,SAASN,kBAAkBA,CAACZ,QAAQ,EAAEe,YAAY,EAAE;EAChD,IAAIlB,OAAO;EACX,QAAQkB,YAAY;IAChB,KAAK,aAAa;MACdlB,OAAO,GAAGG,QAAQ,CAACsB,WAAW,CAAC,CAAC;MAChC;IACJ,KAAK,MAAM;MACPzB,OAAO,GAAGG,QAAQ,CAACuB,IAAI,CAAC,CAAC;MACzB;IACJ,KAAK,MAAM;IACX,KAAK,UAAU;IACf,KAAK,MAAM;MACP,MAAM,IAAItC,KAAK,CAAE,GAAE8B,YAAa,oBAAmB,CAAC;IACxD;MACIlB,OAAO,GAAGG,QAAQ,CAACuB,IAAI,CAAC,CAAC;MACzB;EACR;EACA,OAAO1B,OAAO;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}