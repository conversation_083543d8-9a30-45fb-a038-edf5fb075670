{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\n/** @private */\nexport class ServerSentEventsTransport {\n  constructor(httpClient, accessToken, logger, options) {\n    this._httpClient = httpClient;\n    this._accessToken = accessToken;\n    this._logger = logger;\n    this._options = options;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  connect(url, transferFormat) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      Arg.isRequired(url, \"url\");\n      Arg.isRequired(transferFormat, \"transferFormat\");\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n      _this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\n      // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\n      _this._url = url;\n      if (_this._accessToken) {\n        url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(_this._accessToken)}`;\n      }\n      return new Promise((resolve, reject) => {\n        let opened = false;\n        if (transferFormat !== TransferFormat.Text) {\n          reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\n          return;\n        }\n        let eventSource;\n        if (Platform.isBrowser || Platform.isWebWorker) {\n          eventSource = new _this._options.EventSource(url, {\n            withCredentials: _this._options.withCredentials\n          });\n        } else {\n          // Non-browser passes cookies via the dictionary\n          const cookies = _this._httpClient.getCookieString(url);\n          const headers = {};\n          headers.Cookie = cookies;\n          const [name, value] = getUserAgentHeader();\n          headers[name] = value;\n          eventSource = new _this._options.EventSource(url, {\n            withCredentials: _this._options.withCredentials,\n            headers: {\n              ...headers,\n              ..._this._options.headers\n            }\n          });\n        }\n        try {\n          eventSource.onmessage = e => {\n            if (_this.onreceive) {\n              try {\n                _this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, _this._options.logMessageContent)}.`);\n                _this.onreceive(e.data);\n              } catch (error) {\n                _this._close(error);\n                return;\n              }\n            }\n          };\n          // @ts-ignore: not using event on purpose\n          eventSource.onerror = e => {\n            // EventSource doesn't give any useful information about server side closes.\n            if (opened) {\n              _this._close();\n            } else {\n              reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\" + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\" + \" If you have multiple servers check that sticky sessions are enabled.\"));\n            }\n          };\n          eventSource.onopen = () => {\n            _this._logger.log(LogLevel.Information, `SSE connected to ${_this._url}`);\n            _this._eventSource = eventSource;\n            opened = true;\n            resolve();\n          };\n        } catch (e) {\n          reject(e);\n          return;\n        }\n      });\n    })();\n  }\n  send(data) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2._eventSource) {\n        return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n      }\n      return sendMessage(_this2._logger, \"SSE\", _this2._httpClient, _this2._url, data, _this2._options);\n    })();\n  }\n  stop() {\n    this._close();\n    return Promise.resolve();\n  }\n  _close(e) {\n    if (this._eventSource) {\n      this._eventSource.close();\n      this._eventSource = undefined;\n      if (this.onclose) {\n        this.onclose(e);\n      }\n    }\n  }\n}", "map": {"version": 3, "names": ["LogLevel", "TransferFormat", "Arg", "getDataDetail", "getUserAgentHeader", "Platform", "sendMessage", "ServerSentEventsTransport", "constructor", "httpClient", "accessToken", "logger", "options", "_httpClient", "_accessToken", "_logger", "_options", "onreceive", "onclose", "connect", "url", "transferFormat", "_this", "_asyncToGenerator", "isRequired", "isIn", "log", "Trace", "_url", "indexOf", "encodeURIComponent", "Promise", "resolve", "reject", "opened", "Text", "Error", "eventSource", "<PERSON><PERSON><PERSON><PERSON>", "isWebWorker", "EventSource", "withCredentials", "cookies", "getCookieString", "headers", "<PERSON><PERSON>", "name", "value", "onmessage", "e", "data", "logMessageContent", "error", "_close", "onerror", "onopen", "Information", "_eventSource", "send", "_this2", "stop", "close", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/ServerSentEventsTransport.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\n/** @private */\r\nexport class ServerSentEventsTransport {\r\n    constructor(httpClient, accessToken, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n        return new Promise((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n            let eventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials });\r\n            }\r\n            else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                eventSource = new this._options.EventSource(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers } });\r\n            }\r\n            try {\r\n                eventSource.onmessage = (e) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent)}.`);\r\n                            this.onreceive(e.data);\r\n                        }\r\n                        catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    }\r\n                    else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                            + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                            + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            }\r\n            catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n    async send(data) {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    stop() {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n    _close(e) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,SAAS;AACvF;AACA,OAAO,MAAMC,yBAAyB,CAAC;EACnCC,WAAWA,CAACC,UAAU,EAAEC,WAAW,EAAEC,MAAM,EAAEC,OAAO,EAAE;IAClD,IAAI,CAACC,WAAW,GAAGJ,UAAU;IAC7B,IAAI,CAACK,YAAY,GAAGJ,WAAW;IAC/B,IAAI,CAACK,OAAO,GAAGJ,MAAM;IACrB,IAAI,CAACK,QAAQ,GAAGJ,OAAO;IACvB,IAAI,CAACK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACMC,OAAOA,CAACC,GAAG,EAAEC,cAAc,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/BrB,GAAG,CAACsB,UAAU,CAACJ,GAAG,EAAE,KAAK,CAAC;MAC1BlB,GAAG,CAACsB,UAAU,CAACH,cAAc,EAAE,gBAAgB,CAAC;MAChDnB,GAAG,CAACuB,IAAI,CAACJ,cAAc,EAAEpB,cAAc,EAAE,gBAAgB,CAAC;MAC1DqB,KAAI,CAACP,OAAO,CAACW,GAAG,CAAC1B,QAAQ,CAAC2B,KAAK,EAAE,6BAA6B,CAAC;MAC/D;MACAL,KAAI,CAACM,IAAI,GAAGR,GAAG;MACf,IAAIE,KAAI,CAACR,YAAY,EAAE;QACnBM,GAAG,IAAI,CAACA,GAAG,CAACS,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,IAAK,gBAAeC,kBAAkB,CAACR,KAAI,CAACR,YAAY,CAAE,EAAC;MACvG;MACA,OAAO,IAAIiB,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;QACpC,IAAIC,MAAM,GAAG,KAAK;QAClB,IAAIb,cAAc,KAAKpB,cAAc,CAACkC,IAAI,EAAE;UACxCF,MAAM,CAAC,IAAIG,KAAK,CAAC,2EAA2E,CAAC,CAAC;UAC9F;QACJ;QACA,IAAIC,WAAW;QACf,IAAIhC,QAAQ,CAACiC,SAAS,IAAIjC,QAAQ,CAACkC,WAAW,EAAE;UAC5CF,WAAW,GAAG,IAAIf,KAAI,CAACN,QAAQ,CAACwB,WAAW,CAACpB,GAAG,EAAE;YAAEqB,eAAe,EAAEnB,KAAI,CAACN,QAAQ,CAACyB;UAAgB,CAAC,CAAC;QACxG,CAAC,MACI;UACD;UACA,MAAMC,OAAO,GAAGpB,KAAI,CAACT,WAAW,CAAC8B,eAAe,CAACvB,GAAG,CAAC;UACrD,MAAMwB,OAAO,GAAG,CAAC,CAAC;UAClBA,OAAO,CAACC,MAAM,GAAGH,OAAO;UACxB,MAAM,CAACI,IAAI,EAAEC,KAAK,CAAC,GAAG3C,kBAAkB,CAAC,CAAC;UAC1CwC,OAAO,CAACE,IAAI,CAAC,GAAGC,KAAK;UACrBV,WAAW,GAAG,IAAIf,KAAI,CAACN,QAAQ,CAACwB,WAAW,CAACpB,GAAG,EAAE;YAAEqB,eAAe,EAAEnB,KAAI,CAACN,QAAQ,CAACyB,eAAe;YAAEG,OAAO,EAAE;cAAE,GAAGA,OAAO;cAAE,GAAGtB,KAAI,CAACN,QAAQ,CAAC4B;YAAQ;UAAE,CAAC,CAAC;QAC3J;QACA,IAAI;UACAP,WAAW,CAACW,SAAS,GAAIC,CAAC,IAAK;YAC3B,IAAI3B,KAAI,CAACL,SAAS,EAAE;cAChB,IAAI;gBACAK,KAAI,CAACP,OAAO,CAACW,GAAG,CAAC1B,QAAQ,CAAC2B,KAAK,EAAG,kCAAiCxB,aAAa,CAAC8C,CAAC,CAACC,IAAI,EAAE5B,KAAI,CAACN,QAAQ,CAACmC,iBAAiB,CAAE,GAAE,CAAC;gBAC7H7B,KAAI,CAACL,SAAS,CAACgC,CAAC,CAACC,IAAI,CAAC;cAC1B,CAAC,CACD,OAAOE,KAAK,EAAE;gBACV9B,KAAI,CAAC+B,MAAM,CAACD,KAAK,CAAC;gBAClB;cACJ;YACJ;UACJ,CAAC;UACD;UACAf,WAAW,CAACiB,OAAO,GAAIL,CAAC,IAAK;YACzB;YACA,IAAIf,MAAM,EAAE;cACRZ,KAAI,CAAC+B,MAAM,CAAC,CAAC;YACjB,CAAC,MACI;cACDpB,MAAM,CAAC,IAAIG,KAAK,CAAC,iFAAiF,GAC5F,0GAA0G,GAC1G,uEAAuE,CAAC,CAAC;YACnF;UACJ,CAAC;UACDC,WAAW,CAACkB,MAAM,GAAG,MAAM;YACvBjC,KAAI,CAACP,OAAO,CAACW,GAAG,CAAC1B,QAAQ,CAACwD,WAAW,EAAG,oBAAmBlC,KAAI,CAACM,IAAK,EAAC,CAAC;YACvEN,KAAI,CAACmC,YAAY,GAAGpB,WAAW;YAC/BH,MAAM,GAAG,IAAI;YACbF,OAAO,CAAC,CAAC;UACb,CAAC;QACL,CAAC,CACD,OAAOiB,CAAC,EAAE;UACNhB,MAAM,CAACgB,CAAC,CAAC;UACT;QACJ;MACJ,CAAC,CAAC;IAAC;EACP;EACMS,IAAIA,CAACR,IAAI,EAAE;IAAA,IAAAS,MAAA;IAAA,OAAApC,iBAAA;MACb,IAAI,CAACoC,MAAI,CAACF,YAAY,EAAE;QACpB,OAAO1B,OAAO,CAACE,MAAM,CAAC,IAAIG,KAAK,CAAC,8CAA8C,CAAC,CAAC;MACpF;MACA,OAAO9B,WAAW,CAACqD,MAAI,CAAC5C,OAAO,EAAE,KAAK,EAAE4C,MAAI,CAAC9C,WAAW,EAAE8C,MAAI,CAAC/B,IAAI,EAAEsB,IAAI,EAAES,MAAI,CAAC3C,QAAQ,CAAC;IAAC;EAC9F;EACA4C,IAAIA,CAAA,EAAG;IACH,IAAI,CAACP,MAAM,CAAC,CAAC;IACb,OAAOtB,OAAO,CAACC,OAAO,CAAC,CAAC;EAC5B;EACAqB,MAAMA,CAACJ,CAAC,EAAE;IACN,IAAI,IAAI,CAACQ,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACI,KAAK,CAAC,CAAC;MACzB,IAAI,CAACJ,YAAY,GAAGK,SAAS;MAC7B,IAAI,IAAI,CAAC5C,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC+B,CAAC,CAAC;MACnB;IACJ;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}