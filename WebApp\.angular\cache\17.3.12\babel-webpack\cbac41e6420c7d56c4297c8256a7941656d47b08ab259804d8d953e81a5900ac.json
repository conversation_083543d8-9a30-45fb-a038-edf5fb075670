{"ast": null, "code": "import createPadding from './_createPadding.js';\nimport stringSize from './_stringSize.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n  nativeFloor = Math.floor;\n\n/**\n * Pads `string` on the left and right sides if it's shorter than `length`.\n * Padding characters are truncated if they can't be evenly divided by `length`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.pad('abc', 8);\n * // => '  abc   '\n *\n * _.pad('abc', 8, '_-');\n * // => '_-abc_-_'\n *\n * _.pad('abc', 3);\n * // => 'abc'\n */\nfunction pad(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n  var strLength = length ? stringSize(string) : 0;\n  if (!length || strLength >= length) {\n    return string;\n  }\n  var mid = (length - strLength) / 2;\n  return createPadding(nativeFloor(mid), chars) + string + createPadding(nativeCeil(mid), chars);\n}\nexport default pad;", "map": {"version": 3, "names": ["createPadding", "stringSize", "toInteger", "toString", "nativeCeil", "Math", "ceil", "nativeFloor", "floor", "pad", "string", "length", "chars", "str<PERSON><PERSON><PERSON>", "mid"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/pad.js"], "sourcesContent": ["import createPadding from './_createPadding.js';\nimport stringSize from './_stringSize.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeCeil = Math.ceil,\n    nativeFloor = Math.floor;\n\n/**\n * Pads `string` on the left and right sides if it's shorter than `length`.\n * Padding characters are truncated if they can't be evenly divided by `length`.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to pad.\n * @param {number} [length=0] The padding length.\n * @param {string} [chars=' '] The string used as padding.\n * @returns {string} Returns the padded string.\n * @example\n *\n * _.pad('abc', 8);\n * // => '  abc   '\n *\n * _.pad('abc', 8, '_-');\n * // => '_-abc_-_'\n *\n * _.pad('abc', 3);\n * // => 'abc'\n */\nfunction pad(string, length, chars) {\n  string = toString(string);\n  length = toInteger(length);\n\n  var strLength = length ? stringSize(string) : 0;\n  if (!length || strLength >= length) {\n    return string;\n  }\n  var mid = (length - strLength) / 2;\n  return (\n    createPadding(nativeFloor(mid), chars) +\n    string +\n    createPadding(nativeCeil(mid), chars)\n  );\n}\n\nexport default pad;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,UAAU,GAAGC,IAAI,CAACC,IAAI;EACtBC,WAAW,GAAGF,IAAI,CAACG,KAAK;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAClCF,MAAM,GAAGP,QAAQ,CAACO,MAAM,CAAC;EACzBC,MAAM,GAAGT,SAAS,CAACS,MAAM,CAAC;EAE1B,IAAIE,SAAS,GAAGF,MAAM,GAAGV,UAAU,CAACS,MAAM,CAAC,GAAG,CAAC;EAC/C,IAAI,CAACC,MAAM,IAAIE,SAAS,IAAIF,MAAM,EAAE;IAClC,OAAOD,MAAM;EACf;EACA,IAAII,GAAG,GAAG,CAACH,MAAM,GAAGE,SAAS,IAAI,CAAC;EAClC,OACEb,aAAa,CAACO,WAAW,CAACO,GAAG,CAAC,EAAEF,KAAK,CAAC,GACtCF,MAAM,GACNV,aAAa,CAACI,UAAU,CAACU,GAAG,CAAC,EAAEF,KAAK,CAAC;AAEzC;AAEA,eAAeH,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}