{"ast": null, "code": "import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function (object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\nexport default pick;", "map": {"version": 3, "names": ["base<PERSON>ick", "flatRest", "pick", "object", "paths"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/pick.js"], "sourcesContent": ["import basePick from './_basePick.js';\nimport flatRest from './_flatRest.js';\n\n/**\n * Creates an object composed of the picked `object` properties.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The source object.\n * @param {...(string|string[])} [paths] The property paths to pick.\n * @returns {Object} Returns the new object.\n * @example\n *\n * var object = { 'a': 1, 'b': '2', 'c': 3 };\n *\n * _.pick(object, ['a', 'c']);\n * // => { 'a': 1, 'c': 3 }\n */\nvar pick = flatRest(function(object, paths) {\n  return object == null ? {} : basePick(object, paths);\n});\n\nexport default pick;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,IAAI,GAAGD,QAAQ,CAAC,UAASE,MAAM,EAAEC,KAAK,EAAE;EAC1C,OAAOD,MAAM,IAAI,IAAI,GAAG,CAAC,CAAC,GAAGH,QAAQ,CAACG,MAAM,EAAEC,KAAK,CAAC;AACtD,CAAC,CAAC;AAEF,eAAeF,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}