{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar Op;\n(function (Op) {\n  function length(op) {\n    if (typeof op.delete === 'number') {\n      return op.delete;\n    } else if (typeof op.retain === 'number') {\n      return op.retain;\n    } else if (typeof op.retain === 'object' && op.retain !== null) {\n      return 1;\n    } else {\n      return typeof op.insert === 'string' ? op.insert.length : 1;\n    }\n  }\n  Op.length = length;\n})(Op || (Op = {}));\nexports.default = Op;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Op", "length", "op", "delete", "retain", "insert", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill-delta/dist/Op.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nvar Op;\n(function (Op) {\n    function length(op) {\n        if (typeof op.delete === 'number') {\n            return op.delete;\n        }\n        else if (typeof op.retain === 'number') {\n            return op.retain;\n        }\n        else if (typeof op.retain === 'object' && op.retain !== null) {\n            return 1;\n        }\n        else {\n            return typeof op.insert === 'string' ? op.insert.length : 1;\n        }\n    }\n    Op.length = length;\n})(Op || (Op = {}));\nexports.default = Op;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,IAAIC,EAAE;AACN,CAAC,UAAUA,EAAE,EAAE;EACX,SAASC,MAAMA,CAACC,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,CAACC,MAAM,KAAK,QAAQ,EAAE;MAC/B,OAAOD,EAAE,CAACC,MAAM;IACpB,CAAC,MACI,IAAI,OAAOD,EAAE,CAACE,MAAM,KAAK,QAAQ,EAAE;MACpC,OAAOF,EAAE,CAACE,MAAM;IACpB,CAAC,MACI,IAAI,OAAOF,EAAE,CAACE,MAAM,KAAK,QAAQ,IAAIF,EAAE,CAACE,MAAM,KAAK,IAAI,EAAE;MAC1D,OAAO,CAAC;IACZ,CAAC,MACI;MACD,OAAO,OAAOF,EAAE,CAACG,MAAM,KAAK,QAAQ,GAAGH,EAAE,CAACG,MAAM,CAACJ,MAAM,GAAG,CAAC;IAC/D;EACJ;EACAD,EAAE,CAACC,MAAM,GAAGA,MAAM;AACtB,CAAC,EAAED,EAAE,KAAKA,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AACnBF,OAAO,CAACQ,OAAO,GAAGN,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}