{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport eq from './eq.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keysIn from './keysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own and inherited enumerable string keyed properties of source\n * objects to the destination object for all destination properties that\n * resolve to `undefined`. Source objects are applied from left to right.\n * Once a property is set, additional values of the same property are ignored.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaultsDeep\n * @example\n *\n * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar defaults = baseRest(function (object, sources) {\n  object = Object(object);\n  var index = -1;\n  var length = sources.length;\n  var guard = length > 2 ? sources[2] : undefined;\n  if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n    length = 1;\n  }\n  while (++index < length) {\n    var source = sources[index];\n    var props = keysIn(source);\n    var propsIndex = -1;\n    var propsLength = props.length;\n    while (++propsIndex < propsLength) {\n      var key = props[propsIndex];\n      var value = object[key];\n      if (value === undefined || eq(value, objectProto[key]) && !hasOwnProperty.call(object, key)) {\n        object[key] = source[key];\n      }\n    }\n  }\n  return object;\n});\nexport default defaults;", "map": {"version": 3, "names": ["baseRest", "eq", "isIterateeCall", "keysIn", "objectProto", "Object", "prototype", "hasOwnProperty", "defaults", "object", "sources", "index", "length", "guard", "undefined", "source", "props", "propsIndex", "props<PERSON><PERSON>th", "key", "value", "call"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/defaults.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport eq from './eq.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport keysIn from './keysIn.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Assigns own and inherited enumerable string keyed properties of source\n * objects to the destination object for all destination properties that\n * resolve to `undefined`. Source objects are applied from left to right.\n * Once a property is set, additional values of the same property are ignored.\n *\n * **Note:** This method mutates `object`.\n *\n * @static\n * @since 0.1.0\n * @memberOf _\n * @category Object\n * @param {Object} object The destination object.\n * @param {...Object} [sources] The source objects.\n * @returns {Object} Returns `object`.\n * @see _.defaultsDeep\n * @example\n *\n * _.defaults({ 'a': 1 }, { 'b': 2 }, { 'a': 3 });\n * // => { 'a': 1, 'b': 2 }\n */\nvar defaults = baseRest(function(object, sources) {\n  object = Object(object);\n\n  var index = -1;\n  var length = sources.length;\n  var guard = length > 2 ? sources[2] : undefined;\n\n  if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n    length = 1;\n  }\n\n  while (++index < length) {\n    var source = sources[index];\n    var props = keysIn(source);\n    var propsIndex = -1;\n    var propsLength = props.length;\n\n    while (++propsIndex < propsLength) {\n      var key = props[propsIndex];\n      var value = object[key];\n\n      if (value === undefined ||\n          (eq(value, objectProto[key]) && !hasOwnProperty.call(object, key))) {\n        object[key] = source[key];\n      }\n    }\n  }\n\n  return object;\n});\n\nexport default defaults;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,EAAE,MAAM,SAAS;AACxB,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,QAAQ,GAAGR,QAAQ,CAAC,UAASS,MAAM,EAAEC,OAAO,EAAE;EAChDD,MAAM,GAAGJ,MAAM,CAACI,MAAM,CAAC;EAEvB,IAAIE,KAAK,GAAG,CAAC,CAAC;EACd,IAAIC,MAAM,GAAGF,OAAO,CAACE,MAAM;EAC3B,IAAIC,KAAK,GAAGD,MAAM,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGI,SAAS;EAE/C,IAAID,KAAK,IAAIX,cAAc,CAACQ,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEG,KAAK,CAAC,EAAE;IAC1DD,MAAM,GAAG,CAAC;EACZ;EAEA,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,MAAM,GAAGL,OAAO,CAACC,KAAK,CAAC;IAC3B,IAAIK,KAAK,GAAGb,MAAM,CAACY,MAAM,CAAC;IAC1B,IAAIE,UAAU,GAAG,CAAC,CAAC;IACnB,IAAIC,WAAW,GAAGF,KAAK,CAACJ,MAAM;IAE9B,OAAO,EAAEK,UAAU,GAAGC,WAAW,EAAE;MACjC,IAAIC,GAAG,GAAGH,KAAK,CAACC,UAAU,CAAC;MAC3B,IAAIG,KAAK,GAAGX,MAAM,CAACU,GAAG,CAAC;MAEvB,IAAIC,KAAK,KAAKN,SAAS,IAClBb,EAAE,CAACmB,KAAK,EAAEhB,WAAW,CAACe,GAAG,CAAC,CAAC,IAAI,CAACZ,cAAc,CAACc,IAAI,CAACZ,MAAM,EAAEU,GAAG,CAAE,EAAE;QACtEV,MAAM,CAACU,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;MAC3B;IACF;EACF;EAEA,OAAOV,MAAM;AACf,CAAC,CAAC;AAEF,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}