{"ast": null, "code": "import baseForRight from './_baseForRight.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * This method is like `_.forIn` except that it iterates over properties of\n * `object` in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forIn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forInRight(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'c', 'b', then 'a' assuming `_.forIn` logs 'a', 'b', then 'c'.\n */\nfunction forInRight(object, iteratee) {\n  return object == null ? object : baseForRight(object, castFunction(iteratee), keysIn);\n}\nexport default forInRight;", "map": {"version": 3, "names": ["baseForRight", "castFunction", "keysIn", "forInRight", "object", "iteratee"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/forInRight.js"], "sourcesContent": ["import baseForRight from './_baseForRight.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * This method is like `_.forIn` except that it iterates over properties of\n * `object` in the opposite order.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forIn\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forInRight(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'c', 'b', then 'a' assuming `_.forIn` logs 'a', 'b', then 'c'.\n */\nfunction forInRight(object, iteratee) {\n  return object == null\n    ? object\n    : baseForRight(object, castFunction(iteratee), keysIn);\n}\n\nexport default forInRight;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EACpC,OAAOD,MAAM,IAAI,IAAI,GACjBA,MAAM,GACNJ,YAAY,CAACI,MAAM,EAAEH,YAAY,CAACI,QAAQ,CAAC,EAAEH,MAAM,CAAC;AAC1D;AAEA,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}