{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./addor-edit-worksapce.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./addor-edit-worksapce.component.css?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\nimport { FormsModule } from '@angular/forms';\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\nimport { NzSwitchModule } from 'ng-zorro-antd/switch';\nimport { ModelDetailsServiceProxy, WorkspaceServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';\nlet AddorEditWorksapceComponent = class AddorEditWorksapceComponent {\n  constructor(modelDetailsService, data, modelRef, worksapceService) {\n    this.modelDetailsService = modelDetailsService;\n    this.data = data;\n    this.modelRef = modelRef;\n    this.worksapceService = worksapceService;\n    this.workspace = {\n      title: '',\n      description: '',\n      systemInformation: '',\n      modelName: '',\n      isDefault: false,\n      isProjectManagement: false\n    };\n    this.modelSearchQuery = '';\n    this.models = [];\n    this.filteredModels = [...this.models];\n    if (this.data.isUpdating) {\n      this.workspace = this.data.workspace;\n      this.modelSearchQuery = this.data.workspace.modelName;\n      console.log(this.workspace);\n    } else {\n      this.workspace = {\n        title: '',\n        description: '',\n        systemInformation: '',\n        modelName: '',\n        isDefault: false,\n        isProjectManagement: false\n      };\n    }\n    // console.log(this.data.id);\n  }\n  ngOnInit() {\n    this.loadModels();\n  }\n  loadModels() {\n    this.modelDetailsService.getAllActiveModel().subscribe(response => {\n      this.models = response;\n      this.filteredModels = this.models;\n    });\n  }\n  onChange(event) {\n    const query = event.target.value.toLowerCase();\n    this.filteredModels = this.models.filter(option => option.modelName.toLowerCase().includes(query));\n  }\n  updateModel(selectedModel) {\n    this.modelSearchQuery = selectedModel;\n  }\n  updateWorkspace() {}\n  saveWorkspace() {\n    this.workspace.modelName = this.modelSearchQuery;\n    this.workspace.title = this.workspace.title.trim().replace(/[^a-zA-Z0-9 ]/g, ''); // Trim whitespace from title\n    console.log(this.workspace.systemInformation);\n    console.log(this.workspace);\n    this.worksapceService.createOrUpdate(this.workspace).subscribe(response => {\n      if (response) {\n        this.modelRef.close(this.workspace); // Return the added users to the parent component\n        console.log('Workspace saved');\n        this.clearWorkspace();\n      }\n    });\n  }\n  cancel() {\n    this.modelRef.close();\n  }\n  clearWorkspace() {\n    this.workspace = {\n      title: '',\n      description: '',\n      systemInformation: '',\n      modelName: '',\n      isDefault: false,\n      isProjectManagement: false\n    };\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ModelDetailsServiceProxy\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NZ_MODAL_DATA]\n      }]\n    }, {\n      type: NzModalRef\n    }, {\n      type: WorkspaceServiceProxy\n    }];\n  }\n};\nAddorEditWorksapceComponent = __decorate([Component({\n  selector: 'app-addor-edit-worksapce',\n  standalone: true,\n  imports: [CommonModule, NzInputModule, NzIconModule, NzAutocompleteModule, FormsModule, NzBreadCrumbModule, NzSwitchModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddorEditWorksapceComponent);\nexport { AddorEditWorksapceComponent };", "map": {"version": 3, "names": ["Component", "Inject", "CommonModule", "NzInputModule", "NzIconModule", "NzAutocompleteModule", "FormsModule", "NzBreadCrumbModule", "NzSwitchModule", "ModelDetailsServiceProxy", "WorkspaceServiceProxy", "NZ_MODAL_DATA", "NzModalRef", "AddorEditWorksapceComponent", "constructor", "modelDetailsService", "data", "modelRef", "worksapceService", "workspace", "title", "description", "systemInformation", "modelName", "isDefault", "isProjectManagement", "modelSearchQuery", "models", "filteredModels", "isUpdating", "console", "log", "ngOnInit", "loadModels", "getAllActiveModel", "subscribe", "response", "onChange", "event", "query", "target", "value", "toLowerCase", "filter", "option", "includes", "updateModel", "selected<PERSON><PERSON>l", "updateWorkspace", "saveWorkspace", "trim", "replace", "createOrUpdate", "close", "clearWorkspace", "cancel", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\dialogs\\addor-edit-worksapce\\addor-edit-worksapce.component.ts"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NzInputModule } from 'ng-zorro-antd/input';\r\nimport { NzIconModule } from 'ng-zorro-antd/icon';\r\nimport { NzAutocompleteModule } from 'ng-zorro-antd/auto-complete';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';\r\nimport { RouterLink } from '@angular/router';\r\nimport { NzSwitchModule } from 'ng-zorro-antd/switch';\r\nimport {\r\n  ModelDetailsServiceProxy,\r\n  WorkspaceServiceProxy,\r\n} from '../../../shared/service-proxies/service-proxies';\r\nimport { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';\r\n\r\n@Component({\r\n  selector: 'app-addor-edit-worksapce',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    NzInputModule,\r\n    NzIconModule,\r\n    NzAutocompleteModule,\r\n    FormsModule,\r\n    NzBreadCrumbModule,\r\n    NzSwitchModule,\r\n  ],\r\n  templateUrl: './addor-edit-worksapce.component.html',\r\n  styleUrl: './addor-edit-worksapce.component.css',\r\n})\r\nexport class AddorEditWorksapceComponent {\r\n  workspace: any = {\r\n    title: '',\r\n    description: '',\r\n    systemInformation: '',\r\n    modelName: '',\r\n    isDefault: false,\r\n    isProjectManagement: false,\r\n  };\r\n\r\n  modelSearchQuery: string = '';\r\n  models: any[] = [];\r\n  filteredModels = [...this.models];\r\n  constructor(\r\n    private modelDetailsService: ModelDetailsServiceProxy,\r\n    @Inject(NZ_MODAL_DATA)\r\n    public data: { isUpdating: boolean; title: any; workspace: any },\r\n    private modelRef: NzModalRef,\r\n    private worksapceService: WorkspaceServiceProxy\r\n  ) {\r\n    if (this.data.isUpdating) {\r\n      this.workspace = this.data.workspace;\r\n      this.modelSearchQuery = this.data.workspace.modelName;\r\n      console.log(this.workspace);\r\n    } else {\r\n      this.workspace = {\r\n        title: '',\r\n        description: '',\r\n        systemInformation: '',\r\n        modelName: '',\r\n        isDefault: false,\r\n        isProjectManagement: false,\r\n      };\r\n    }\r\n\r\n    // console.log(this.data.id);\r\n  }\r\n  ngOnInit(): void {\r\n    this.loadModels();\r\n  }\r\n  loadModels() {\r\n    this.modelDetailsService.getAllActiveModel().subscribe((response: any) => {\r\n      this.models = response;\r\n      this.filteredModels = this.models;\r\n    });\r\n  }\r\n  onChange(event: Event) {\r\n    const query = (event.target as HTMLInputElement).value.toLowerCase();\r\n    this.filteredModels = this.models.filter((option: any) =>\r\n      option.modelName.toLowerCase().includes(query)\r\n    );\r\n  }\r\n\r\n  updateModel(selectedModel: string) {\r\n    this.modelSearchQuery = selectedModel;\r\n  }\r\n  updateWorkspace() {}\r\n  saveWorkspace() {\r\n    this.workspace.modelName = this.modelSearchQuery;\r\n    this.workspace.title = this.workspace.title\r\n      .trim()\r\n      .replace(/[^a-zA-Z0-9 ]/g, ''); // Trim whitespace from title\r\n    console.log(this.workspace.systemInformation);\r\n    console.log(this.workspace);\r\n    this.worksapceService\r\n      .createOrUpdate(this.workspace)\r\n      .subscribe((response: any) => {\r\n        if (response) {\r\n          this.modelRef.close(this.workspace); // Return the added users to the parent component\r\n          console.log('Workspace saved');\r\n          this.clearWorkspace();\r\n        }\r\n      });\r\n  }\r\n\r\n  cancel() {\r\n    this.modelRef.close();\r\n  }\r\n  clearWorkspace() {\r\n    this.workspace = {\r\n      title: '',\r\n      description: '',\r\n      systemInformation: '',\r\n      modelName: '',\r\n      isDefault: false,\r\n      isProjectManagement: false,\r\n    };\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,YAAY,QAAQ,oBAAoB;AACjD,SAASC,oBAAoB,QAAQ,6BAA6B;AAClE,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,kBAAkB,QAAQ,0BAA0B;AAE7D,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SACEC,wBAAwB,EACxBC,qBAAqB,QAChB,iDAAiD;AACxD,SAASC,aAAa,EAAEC,UAAU,QAAQ,qBAAqB;AAiBxD,IAAMC,2BAA2B,GAAjC,MAAMA,2BAA2B;EAatCC,YACUC,mBAA6C,EAE9CC,IAAyD,EACxDC,QAAoB,EACpBC,gBAAuC;IAJvC,KAAAH,mBAAmB,GAAnBA,mBAAmB;IAEpB,KAAAC,IAAI,GAAJA,IAAI;IACH,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAjB1B,KAAAC,SAAS,GAAQ;MACfC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,KAAK;MAChBC,mBAAmB,EAAE;KACtB;IAED,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,MAAM,GAAU,EAAE;IAClB,KAAAC,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,MAAM,CAAC;IAQ/B,IAAI,IAAI,CAACX,IAAI,CAACa,UAAU,EAAE;MACxB,IAAI,CAACV,SAAS,GAAG,IAAI,CAACH,IAAI,CAACG,SAAS;MACpC,IAAI,CAACO,gBAAgB,GAAG,IAAI,CAACV,IAAI,CAACG,SAAS,CAACI,SAAS;MACrDO,OAAO,CAACC,GAAG,CAAC,IAAI,CAACZ,SAAS,CAAC;KAC5B,MAAM;MACL,IAAI,CAACA,SAAS,GAAG;QACfC,KAAK,EAAE,EAAE;QACTC,WAAW,EAAE,EAAE;QACfC,iBAAiB,EAAE,EAAE;QACrBC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,KAAK;QAChBC,mBAAmB,EAAE;OACtB;;IAGH;EACF;EACAO,QAAQA,CAAA;IACN,IAAI,CAACC,UAAU,EAAE;EACnB;EACAA,UAAUA,CAAA;IACR,IAAI,CAAClB,mBAAmB,CAACmB,iBAAiB,EAAE,CAACC,SAAS,CAAEC,QAAa,IAAI;MACvE,IAAI,CAACT,MAAM,GAAGS,QAAQ;MACtB,IAAI,CAACR,cAAc,GAAG,IAAI,CAACD,MAAM;IACnC,CAAC,CAAC;EACJ;EACAU,QAAQA,CAACC,KAAY;IACnB,MAAMC,KAAK,GAAID,KAAK,CAACE,MAA2B,CAACC,KAAK,CAACC,WAAW,EAAE;IACpE,IAAI,CAACd,cAAc,GAAG,IAAI,CAACD,MAAM,CAACgB,MAAM,CAAEC,MAAW,IACnDA,MAAM,CAACrB,SAAS,CAACmB,WAAW,EAAE,CAACG,QAAQ,CAACN,KAAK,CAAC,CAC/C;EACH;EAEAO,WAAWA,CAACC,aAAqB;IAC/B,IAAI,CAACrB,gBAAgB,GAAGqB,aAAa;EACvC;EACAC,eAAeA,CAAA,GAAI;EACnBC,aAAaA,CAAA;IACX,IAAI,CAAC9B,SAAS,CAACI,SAAS,GAAG,IAAI,CAACG,gBAAgB;IAChD,IAAI,CAACP,SAAS,CAACC,KAAK,GAAG,IAAI,CAACD,SAAS,CAACC,KAAK,CACxC8B,IAAI,EAAE,CACNC,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC,CAAC;IAClCrB,OAAO,CAACC,GAAG,CAAC,IAAI,CAACZ,SAAS,CAACG,iBAAiB,CAAC;IAC7CQ,OAAO,CAACC,GAAG,CAAC,IAAI,CAACZ,SAAS,CAAC;IAC3B,IAAI,CAACD,gBAAgB,CAClBkC,cAAc,CAAC,IAAI,CAACjC,SAAS,CAAC,CAC9BgB,SAAS,CAAEC,QAAa,IAAI;MAC3B,IAAIA,QAAQ,EAAE;QACZ,IAAI,CAACnB,QAAQ,CAACoC,KAAK,CAAC,IAAI,CAAClC,SAAS,CAAC,CAAC,CAAC;QACrCW,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;QAC9B,IAAI,CAACuB,cAAc,EAAE;;IAEzB,CAAC,CAAC;EACN;EAEAC,MAAMA,CAAA;IACJ,IAAI,CAACtC,QAAQ,CAACoC,KAAK,EAAE;EACvB;EACAC,cAAcA,CAAA;IACZ,IAAI,CAACnC,SAAS,GAAG;MACfC,KAAK,EAAE,EAAE;MACTC,WAAW,EAAE,EAAE;MACfC,iBAAiB,EAAE,EAAE;MACrBC,SAAS,EAAE,EAAE;MACbC,SAAS,EAAE,KAAK;MAChBC,mBAAmB,EAAE;KACtB;EACH;;;;;;;cAxEGxB,MAAM;QAAAuD,IAAA,GAAC7C,aAAa;MAAA;IAAA,G;;;;;;;AAfZE,2BAA2B,GAAA4C,UAAA,EAfvCzD,SAAS,CAAC;EACT0D,QAAQ,EAAE,0BAA0B;EACpCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP1D,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,oBAAoB,EACpBC,WAAW,EACXC,kBAAkB,EAClBC,cAAc,CACf;EACDqD,QAAA,EAAAC,oBAAoD;;CAErD,CAAC,C,EACWjD,2BAA2B,CAwFvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}