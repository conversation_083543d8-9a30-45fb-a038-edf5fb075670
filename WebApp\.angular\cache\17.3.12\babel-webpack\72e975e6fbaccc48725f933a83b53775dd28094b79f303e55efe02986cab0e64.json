{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { MessageType } from \"./IHubProtocol\";\nimport { isArrayBuffer } from \"./Utils\";\n/** @private */\nexport class MessageBuffer {\n  constructor(protocol, connection, bufferSize) {\n    this._bufferSize = 100000;\n    this._messages = [];\n    this._totalMessageCount = 0;\n    this._waitForSequenceMessage = false;\n    // Message IDs start at 1 and always increment by 1\n    this._nextReceivingSequenceId = 1;\n    this._latestReceivedSequenceId = 0;\n    this._bufferedByteCount = 0;\n    this._reconnectInProgress = false;\n    this._protocol = protocol;\n    this._connection = connection;\n    this._bufferSize = bufferSize;\n  }\n  _send(message) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const serializedMessage = _this._protocol.writeMessage(message);\n      let backpressurePromise = Promise.resolve();\n      // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\n      if (_this._isInvocationMessage(message)) {\n        _this._totalMessageCount++;\n        let backpressurePromiseResolver = () => {};\n        let backpressurePromiseRejector = () => {};\n        if (isArrayBuffer(serializedMessage)) {\n          _this._bufferedByteCount += serializedMessage.byteLength;\n        } else {\n          _this._bufferedByteCount += serializedMessage.length;\n        }\n        if (_this._bufferedByteCount >= _this._bufferSize) {\n          backpressurePromise = new Promise((resolve, reject) => {\n            backpressurePromiseResolver = resolve;\n            backpressurePromiseRejector = reject;\n          });\n        }\n        _this._messages.push(new BufferedItem(serializedMessage, _this._totalMessageCount, backpressurePromiseResolver, backpressurePromiseRejector));\n      }\n      try {\n        // If this is set it means we are reconnecting or resending\n        // We don't want to send on a disconnected connection\n        // And we don't want to send if resend is running since that would mean sending\n        // this message twice\n        if (!_this._reconnectInProgress) {\n          yield _this._connection.send(serializedMessage);\n        }\n      } catch {\n        _this._disconnected();\n      }\n      yield backpressurePromise;\n    })();\n  }\n  _ack(ackMessage) {\n    let newestAckedMessage = -1;\n    // Find index of newest message being acked\n    for (let index = 0; index < this._messages.length; index++) {\n      const element = this._messages[index];\n      if (element._id <= ackMessage.sequenceId) {\n        newestAckedMessage = index;\n        if (isArrayBuffer(element._message)) {\n          this._bufferedByteCount -= element._message.byteLength;\n        } else {\n          this._bufferedByteCount -= element._message.length;\n        }\n        // resolve items that have already been sent and acked\n        element._resolver();\n      } else if (this._bufferedByteCount < this._bufferSize) {\n        // resolve items that now fall under the buffer limit but haven't been acked\n        element._resolver();\n      } else {\n        break;\n      }\n    }\n    if (newestAckedMessage !== -1) {\n      // We're removing everything including the message pointed to, so add 1\n      this._messages = this._messages.slice(newestAckedMessage + 1);\n    }\n  }\n  _shouldProcessMessage(message) {\n    if (this._waitForSequenceMessage) {\n      if (message.type !== MessageType.Sequence) {\n        return false;\n      } else {\n        this._waitForSequenceMessage = false;\n        return true;\n      }\n    }\n    // No special processing for acks, pings, etc.\n    if (!this._isInvocationMessage(message)) {\n      return true;\n    }\n    const currentId = this._nextReceivingSequenceId;\n    this._nextReceivingSequenceId++;\n    if (currentId <= this._latestReceivedSequenceId) {\n      if (currentId === this._latestReceivedSequenceId) {\n        // Should only hit this if we just reconnected and the server is sending\n        // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\n        this._ackTimer();\n      }\n      // Ignore, this is a duplicate message\n      return false;\n    }\n    this._latestReceivedSequenceId = currentId;\n    // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\n    // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\n    this._ackTimer();\n    return true;\n  }\n  _resetSequence(message) {\n    if (message.sequenceId > this._nextReceivingSequenceId) {\n      // eslint-disable-next-line @typescript-eslint/no-floating-promises\n      this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\n      return;\n    }\n    this._nextReceivingSequenceId = message.sequenceId;\n  }\n  _disconnected() {\n    this._reconnectInProgress = true;\n    this._waitForSequenceMessage = true;\n  }\n  _resend() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const sequenceId = _this2._messages.length !== 0 ? _this2._messages[0]._id : _this2._totalMessageCount + 1;\n      yield _this2._connection.send(_this2._protocol.writeMessage({\n        type: MessageType.Sequence,\n        sequenceId\n      }));\n      // Get a local variable to the _messages, just in case messages are acked while resending\n      // Which would slice the _messages array (which creates a new copy)\n      const messages = _this2._messages;\n      for (const element of messages) {\n        yield _this2._connection.send(element._message);\n      }\n      _this2._reconnectInProgress = false;\n    })();\n  }\n  _dispose(error) {\n    error !== null && error !== void 0 ? error : error = new Error(\"Unable to reconnect to server.\");\n    // Unblock backpressure if any\n    for (const element of this._messages) {\n      element._rejector(error);\n    }\n  }\n  _isInvocationMessage(message) {\n    // There is no way to check if something implements an interface.\n    // So we individually check the messages in a switch statement.\n    // To make sure we don't miss any message types we rely on the compiler\n    // seeing the function returns a value and it will do the\n    // exhaustive check for us on the switch statement, since we don't use 'case default'\n    switch (message.type) {\n      case MessageType.Invocation:\n      case MessageType.StreamItem:\n      case MessageType.Completion:\n      case MessageType.StreamInvocation:\n      case MessageType.CancelInvocation:\n        return true;\n      case MessageType.Close:\n      case MessageType.Sequence:\n      case MessageType.Ping:\n      case MessageType.Ack:\n        return false;\n    }\n  }\n  _ackTimer() {\n    var _this3 = this;\n    if (this._ackTimerHandle === undefined) {\n      this._ackTimerHandle = setTimeout( /*#__PURE__*/_asyncToGenerator(function* () {\n        try {\n          if (!_this3._reconnectInProgress) {\n            yield _this3._connection.send(_this3._protocol.writeMessage({\n              type: MessageType.Ack,\n              sequenceId: _this3._latestReceivedSequenceId\n            }));\n          }\n          // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\n        } catch {}\n        clearTimeout(_this3._ackTimerHandle);\n        _this3._ackTimerHandle = undefined;\n        // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\n      }), 1000);\n    }\n  }\n}\nclass BufferedItem {\n  constructor(message, id, resolver, rejector) {\n    this._message = message;\n    this._id = id;\n    this._resolver = resolver;\n    this._rejector = rejector;\n  }\n}", "map": {"version": 3, "names": ["MessageType", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MessageBuffer", "constructor", "protocol", "connection", "bufferSize", "_bufferSize", "_messages", "_totalMessageCount", "_waitForSequenceMessage", "_nextReceivingSequenceId", "_latestReceivedSequenceId", "_bufferedByteCount", "_reconnectInProgress", "_protocol", "_connection", "_send", "message", "_this", "_asyncToGenerator", "serializedMessage", "writeMessage", "backpressurePromise", "Promise", "resolve", "_isInvocationMessage", "backpressurePromiseResolver", "backpressurePromiseRejector", "byteLength", "length", "reject", "push", "BufferedItem", "send", "_disconnected", "_ack", "ackMessage", "newestAckedMessage", "index", "element", "_id", "sequenceId", "_message", "_resolver", "slice", "_shouldProcessMessage", "type", "Sequence", "currentId", "_ackTimer", "_resetSequence", "stop", "Error", "_resend", "_this2", "messages", "_dispose", "error", "_rejector", "Invocation", "StreamItem", "Completion", "StreamInvocation", "CancelInvocation", "Close", "<PERSON>", "Ack", "_this3", "_ack<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "setTimeout", "clearTimeout", "id", "resolver", "rejector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/MessageBuffer.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { MessageType } from \"./IHubProtocol\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n/** @private */\r\nexport class MessageBuffer {\r\n    constructor(protocol, connection, bufferSize) {\r\n        this._bufferSize = 100000;\r\n        this._messages = [];\r\n        this._totalMessageCount = 0;\r\n        this._waitForSequenceMessage = false;\r\n        // Message IDs start at 1 and always increment by 1\r\n        this._nextReceivingSequenceId = 1;\r\n        this._latestReceivedSequenceId = 0;\r\n        this._bufferedByteCount = 0;\r\n        this._reconnectInProgress = false;\r\n        this._protocol = protocol;\r\n        this._connection = connection;\r\n        this._bufferSize = bufferSize;\r\n    }\r\n    async _send(message) {\r\n        const serializedMessage = this._protocol.writeMessage(message);\r\n        let backpressurePromise = Promise.resolve();\r\n        // Only count invocation messages. Acks, pings, etc. don't need to be resent on reconnect\r\n        if (this._isInvocationMessage(message)) {\r\n            this._totalMessageCount++;\r\n            let backpressurePromiseResolver = () => { };\r\n            let backpressurePromiseRejector = () => { };\r\n            if (isArrayBuffer(serializedMessage)) {\r\n                this._bufferedByteCount += serializedMessage.byteLength;\r\n            }\r\n            else {\r\n                this._bufferedByteCount += serializedMessage.length;\r\n            }\r\n            if (this._bufferedByteCount >= this._bufferSize) {\r\n                backpressurePromise = new Promise((resolve, reject) => {\r\n                    backpressurePromiseResolver = resolve;\r\n                    backpressurePromiseRejector = reject;\r\n                });\r\n            }\r\n            this._messages.push(new BufferedItem(serializedMessage, this._totalMessageCount, backpressurePromiseResolver, backpressurePromiseRejector));\r\n        }\r\n        try {\r\n            // If this is set it means we are reconnecting or resending\r\n            // We don't want to send on a disconnected connection\r\n            // And we don't want to send if resend is running since that would mean sending\r\n            // this message twice\r\n            if (!this._reconnectInProgress) {\r\n                await this._connection.send(serializedMessage);\r\n            }\r\n        }\r\n        catch {\r\n            this._disconnected();\r\n        }\r\n        await backpressurePromise;\r\n    }\r\n    _ack(ackMessage) {\r\n        let newestAckedMessage = -1;\r\n        // Find index of newest message being acked\r\n        for (let index = 0; index < this._messages.length; index++) {\r\n            const element = this._messages[index];\r\n            if (element._id <= ackMessage.sequenceId) {\r\n                newestAckedMessage = index;\r\n                if (isArrayBuffer(element._message)) {\r\n                    this._bufferedByteCount -= element._message.byteLength;\r\n                }\r\n                else {\r\n                    this._bufferedByteCount -= element._message.length;\r\n                }\r\n                // resolve items that have already been sent and acked\r\n                element._resolver();\r\n            }\r\n            else if (this._bufferedByteCount < this._bufferSize) {\r\n                // resolve items that now fall under the buffer limit but haven't been acked\r\n                element._resolver();\r\n            }\r\n            else {\r\n                break;\r\n            }\r\n        }\r\n        if (newestAckedMessage !== -1) {\r\n            // We're removing everything including the message pointed to, so add 1\r\n            this._messages = this._messages.slice(newestAckedMessage + 1);\r\n        }\r\n    }\r\n    _shouldProcessMessage(message) {\r\n        if (this._waitForSequenceMessage) {\r\n            if (message.type !== MessageType.Sequence) {\r\n                return false;\r\n            }\r\n            else {\r\n                this._waitForSequenceMessage = false;\r\n                return true;\r\n            }\r\n        }\r\n        // No special processing for acks, pings, etc.\r\n        if (!this._isInvocationMessage(message)) {\r\n            return true;\r\n        }\r\n        const currentId = this._nextReceivingSequenceId;\r\n        this._nextReceivingSequenceId++;\r\n        if (currentId <= this._latestReceivedSequenceId) {\r\n            if (currentId === this._latestReceivedSequenceId) {\r\n                // Should only hit this if we just reconnected and the server is sending\r\n                // Messages it has buffered, which would mean it hasn't seen an Ack for these messages\r\n                this._ackTimer();\r\n            }\r\n            // Ignore, this is a duplicate message\r\n            return false;\r\n        }\r\n        this._latestReceivedSequenceId = currentId;\r\n        // Only start the timer for sending an Ack message when we have a message to ack. This also conveniently solves\r\n        // timer throttling by not having a recursive timer, and by starting the timer via a network call (recv)\r\n        this._ackTimer();\r\n        return true;\r\n    }\r\n    _resetSequence(message) {\r\n        if (message.sequenceId > this._nextReceivingSequenceId) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._connection.stop(new Error(\"Sequence ID greater than amount of messages we've received.\"));\r\n            return;\r\n        }\r\n        this._nextReceivingSequenceId = message.sequenceId;\r\n    }\r\n    _disconnected() {\r\n        this._reconnectInProgress = true;\r\n        this._waitForSequenceMessage = true;\r\n    }\r\n    async _resend() {\r\n        const sequenceId = this._messages.length !== 0\r\n            ? this._messages[0]._id\r\n            : this._totalMessageCount + 1;\r\n        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Sequence, sequenceId }));\r\n        // Get a local variable to the _messages, just in case messages are acked while resending\r\n        // Which would slice the _messages array (which creates a new copy)\r\n        const messages = this._messages;\r\n        for (const element of messages) {\r\n            await this._connection.send(element._message);\r\n        }\r\n        this._reconnectInProgress = false;\r\n    }\r\n    _dispose(error) {\r\n        error !== null && error !== void 0 ? error : (error = new Error(\"Unable to reconnect to server.\"));\r\n        // Unblock backpressure if any\r\n        for (const element of this._messages) {\r\n            element._rejector(error);\r\n        }\r\n    }\r\n    _isInvocationMessage(message) {\r\n        // There is no way to check if something implements an interface.\r\n        // So we individually check the messages in a switch statement.\r\n        // To make sure we don't miss any message types we rely on the compiler\r\n        // seeing the function returns a value and it will do the\r\n        // exhaustive check for us on the switch statement, since we don't use 'case default'\r\n        switch (message.type) {\r\n            case MessageType.Invocation:\r\n            case MessageType.StreamItem:\r\n            case MessageType.Completion:\r\n            case MessageType.StreamInvocation:\r\n            case MessageType.CancelInvocation:\r\n                return true;\r\n            case MessageType.Close:\r\n            case MessageType.Sequence:\r\n            case MessageType.Ping:\r\n            case MessageType.Ack:\r\n                return false;\r\n        }\r\n    }\r\n    _ackTimer() {\r\n        if (this._ackTimerHandle === undefined) {\r\n            this._ackTimerHandle = setTimeout(async () => {\r\n                try {\r\n                    if (!this._reconnectInProgress) {\r\n                        await this._connection.send(this._protocol.writeMessage({ type: MessageType.Ack, sequenceId: this._latestReceivedSequenceId }));\r\n                    }\r\n                    // Ignore errors, that means the connection is closed and we don't care about the Ack message anymore.\r\n                }\r\n                catch { }\r\n                clearTimeout(this._ackTimerHandle);\r\n                this._ackTimerHandle = undefined;\r\n                // 1 second delay so we don't spam Ack messages if there are many messages being received at once.\r\n            }, 1000);\r\n        }\r\n    }\r\n}\r\nclass BufferedItem {\r\n    constructor(message, id, resolver, rejector) {\r\n        this._message = message;\r\n        this._id = id;\r\n        this._resolver = resolver;\r\n        this._rejector = rejector;\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,QAAQ,SAAS;AACvC;AACA,OAAO,MAAMC,aAAa,CAAC;EACvBC,WAAWA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAE;IAC1C,IAAI,CAACC,WAAW,GAAG,MAAM;IACzB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,uBAAuB,GAAG,KAAK;IACpC;IACA,IAAI,CAACC,wBAAwB,GAAG,CAAC;IACjC,IAAI,CAACC,yBAAyB,GAAG,CAAC;IAClC,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,oBAAoB,GAAG,KAAK;IACjC,IAAI,CAACC,SAAS,GAAGX,QAAQ;IACzB,IAAI,CAACY,WAAW,GAAGX,UAAU;IAC7B,IAAI,CAACE,WAAW,GAAGD,UAAU;EACjC;EACMW,KAAKA,CAACC,OAAO,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACjB,MAAMC,iBAAiB,GAAGF,KAAI,CAACJ,SAAS,CAACO,YAAY,CAACJ,OAAO,CAAC;MAC9D,IAAIK,mBAAmB,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;MAC3C;MACA,IAAIN,KAAI,CAACO,oBAAoB,CAACR,OAAO,CAAC,EAAE;QACpCC,KAAI,CAACV,kBAAkB,EAAE;QACzB,IAAIkB,2BAA2B,GAAGA,CAAA,KAAM,CAAE,CAAC;QAC3C,IAAIC,2BAA2B,GAAGA,CAAA,KAAM,CAAE,CAAC;QAC3C,IAAI3B,aAAa,CAACoB,iBAAiB,CAAC,EAAE;UAClCF,KAAI,CAACN,kBAAkB,IAAIQ,iBAAiB,CAACQ,UAAU;QAC3D,CAAC,MACI;UACDV,KAAI,CAACN,kBAAkB,IAAIQ,iBAAiB,CAACS,MAAM;QACvD;QACA,IAAIX,KAAI,CAACN,kBAAkB,IAAIM,KAAI,CAACZ,WAAW,EAAE;UAC7CgB,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEM,MAAM,KAAK;YACnDJ,2BAA2B,GAAGF,OAAO;YACrCG,2BAA2B,GAAGG,MAAM;UACxC,CAAC,CAAC;QACN;QACAZ,KAAI,CAACX,SAAS,CAACwB,IAAI,CAAC,IAAIC,YAAY,CAACZ,iBAAiB,EAAEF,KAAI,CAACV,kBAAkB,EAAEkB,2BAA2B,EAAEC,2BAA2B,CAAC,CAAC;MAC/I;MACA,IAAI;QACA;QACA;QACA;QACA;QACA,IAAI,CAACT,KAAI,CAACL,oBAAoB,EAAE;UAC5B,MAAMK,KAAI,CAACH,WAAW,CAACkB,IAAI,CAACb,iBAAiB,CAAC;QAClD;MACJ,CAAC,CACD,MAAM;QACFF,KAAI,CAACgB,aAAa,CAAC,CAAC;MACxB;MACA,MAAMZ,mBAAmB;IAAC;EAC9B;EACAa,IAAIA,CAACC,UAAU,EAAE;IACb,IAAIC,kBAAkB,GAAG,CAAC,CAAC;IAC3B;IACA,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC/B,SAAS,CAACsB,MAAM,EAAES,KAAK,EAAE,EAAE;MACxD,MAAMC,OAAO,GAAG,IAAI,CAAChC,SAAS,CAAC+B,KAAK,CAAC;MACrC,IAAIC,OAAO,CAACC,GAAG,IAAIJ,UAAU,CAACK,UAAU,EAAE;QACtCJ,kBAAkB,GAAGC,KAAK;QAC1B,IAAItC,aAAa,CAACuC,OAAO,CAACG,QAAQ,CAAC,EAAE;UACjC,IAAI,CAAC9B,kBAAkB,IAAI2B,OAAO,CAACG,QAAQ,CAACd,UAAU;QAC1D,CAAC,MACI;UACD,IAAI,CAAChB,kBAAkB,IAAI2B,OAAO,CAACG,QAAQ,CAACb,MAAM;QACtD;QACA;QACAU,OAAO,CAACI,SAAS,CAAC,CAAC;MACvB,CAAC,MACI,IAAI,IAAI,CAAC/B,kBAAkB,GAAG,IAAI,CAACN,WAAW,EAAE;QACjD;QACAiC,OAAO,CAACI,SAAS,CAAC,CAAC;MACvB,CAAC,MACI;QACD;MACJ;IACJ;IACA,IAAIN,kBAAkB,KAAK,CAAC,CAAC,EAAE;MAC3B;MACA,IAAI,CAAC9B,SAAS,GAAG,IAAI,CAACA,SAAS,CAACqC,KAAK,CAACP,kBAAkB,GAAG,CAAC,CAAC;IACjE;EACJ;EACAQ,qBAAqBA,CAAC5B,OAAO,EAAE;IAC3B,IAAI,IAAI,CAACR,uBAAuB,EAAE;MAC9B,IAAIQ,OAAO,CAAC6B,IAAI,KAAK/C,WAAW,CAACgD,QAAQ,EAAE;QACvC,OAAO,KAAK;MAChB,CAAC,MACI;QACD,IAAI,CAACtC,uBAAuB,GAAG,KAAK;QACpC,OAAO,IAAI;MACf;IACJ;IACA;IACA,IAAI,CAAC,IAAI,CAACgB,oBAAoB,CAACR,OAAO,CAAC,EAAE;MACrC,OAAO,IAAI;IACf;IACA,MAAM+B,SAAS,GAAG,IAAI,CAACtC,wBAAwB;IAC/C,IAAI,CAACA,wBAAwB,EAAE;IAC/B,IAAIsC,SAAS,IAAI,IAAI,CAACrC,yBAAyB,EAAE;MAC7C,IAAIqC,SAAS,KAAK,IAAI,CAACrC,yBAAyB,EAAE;QAC9C;QACA;QACA,IAAI,CAACsC,SAAS,CAAC,CAAC;MACpB;MACA;MACA,OAAO,KAAK;IAChB;IACA,IAAI,CAACtC,yBAAyB,GAAGqC,SAAS;IAC1C;IACA;IACA,IAAI,CAACC,SAAS,CAAC,CAAC;IAChB,OAAO,IAAI;EACf;EACAC,cAAcA,CAACjC,OAAO,EAAE;IACpB,IAAIA,OAAO,CAACwB,UAAU,GAAG,IAAI,CAAC/B,wBAAwB,EAAE;MACpD;MACA,IAAI,CAACK,WAAW,CAACoC,IAAI,CAAC,IAAIC,KAAK,CAAC,6DAA6D,CAAC,CAAC;MAC/F;IACJ;IACA,IAAI,CAAC1C,wBAAwB,GAAGO,OAAO,CAACwB,UAAU;EACtD;EACAP,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACrB,oBAAoB,GAAG,IAAI;IAChC,IAAI,CAACJ,uBAAuB,GAAG,IAAI;EACvC;EACM4C,OAAOA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAnC,iBAAA;MACZ,MAAMsB,UAAU,GAAGa,MAAI,CAAC/C,SAAS,CAACsB,MAAM,KAAK,CAAC,GACxCyB,MAAI,CAAC/C,SAAS,CAAC,CAAC,CAAC,CAACiC,GAAG,GACrBc,MAAI,CAAC9C,kBAAkB,GAAG,CAAC;MACjC,MAAM8C,MAAI,CAACvC,WAAW,CAACkB,IAAI,CAACqB,MAAI,CAACxC,SAAS,CAACO,YAAY,CAAC;QAAEyB,IAAI,EAAE/C,WAAW,CAACgD,QAAQ;QAAEN;MAAW,CAAC,CAAC,CAAC;MACpG;MACA;MACA,MAAMc,QAAQ,GAAGD,MAAI,CAAC/C,SAAS;MAC/B,KAAK,MAAMgC,OAAO,IAAIgB,QAAQ,EAAE;QAC5B,MAAMD,MAAI,CAACvC,WAAW,CAACkB,IAAI,CAACM,OAAO,CAACG,QAAQ,CAAC;MACjD;MACAY,MAAI,CAACzC,oBAAoB,GAAG,KAAK;IAAC;EACtC;EACA2C,QAAQA,CAACC,KAAK,EAAE;IACZA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAIA,KAAK,GAAG,IAAIL,KAAK,CAAC,gCAAgC,CAAE;IAClG;IACA,KAAK,MAAMb,OAAO,IAAI,IAAI,CAAChC,SAAS,EAAE;MAClCgC,OAAO,CAACmB,SAAS,CAACD,KAAK,CAAC;IAC5B;EACJ;EACAhC,oBAAoBA,CAACR,OAAO,EAAE;IAC1B;IACA;IACA;IACA;IACA;IACA,QAAQA,OAAO,CAAC6B,IAAI;MAChB,KAAK/C,WAAW,CAAC4D,UAAU;MAC3B,KAAK5D,WAAW,CAAC6D,UAAU;MAC3B,KAAK7D,WAAW,CAAC8D,UAAU;MAC3B,KAAK9D,WAAW,CAAC+D,gBAAgB;MACjC,KAAK/D,WAAW,CAACgE,gBAAgB;QAC7B,OAAO,IAAI;MACf,KAAKhE,WAAW,CAACiE,KAAK;MACtB,KAAKjE,WAAW,CAACgD,QAAQ;MACzB,KAAKhD,WAAW,CAACkE,IAAI;MACrB,KAAKlE,WAAW,CAACmE,GAAG;QAChB,OAAO,KAAK;IACpB;EACJ;EACAjB,SAASA,CAAA,EAAG;IAAA,IAAAkB,MAAA;IACR,IAAI,IAAI,CAACC,eAAe,KAAKC,SAAS,EAAE;MACpC,IAAI,CAACD,eAAe,GAAGE,UAAU,eAAAnD,iBAAA,CAAC,aAAY;QAC1C,IAAI;UACA,IAAI,CAACgD,MAAI,CAACtD,oBAAoB,EAAE;YAC5B,MAAMsD,MAAI,CAACpD,WAAW,CAACkB,IAAI,CAACkC,MAAI,CAACrD,SAAS,CAACO,YAAY,CAAC;cAAEyB,IAAI,EAAE/C,WAAW,CAACmE,GAAG;cAAEzB,UAAU,EAAE0B,MAAI,CAACxD;YAA0B,CAAC,CAAC,CAAC;UACnI;UACA;QACJ,CAAC,CACD,MAAM,CAAE;QACR4D,YAAY,CAACJ,MAAI,CAACC,eAAe,CAAC;QAClCD,MAAI,CAACC,eAAe,GAAGC,SAAS;QAChC;MACJ,CAAC,GAAE,IAAI,CAAC;IACZ;EACJ;AACJ;AACA,MAAMrC,YAAY,CAAC;EACf9B,WAAWA,CAACe,OAAO,EAAEuD,EAAE,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IACzC,IAAI,CAAChC,QAAQ,GAAGzB,OAAO;IACvB,IAAI,CAACuB,GAAG,GAAGgC,EAAE;IACb,IAAI,CAAC7B,SAAS,GAAG8B,QAAQ;IACzB,IAAI,CAACf,SAAS,GAAGgB,QAAQ;EAC7B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}