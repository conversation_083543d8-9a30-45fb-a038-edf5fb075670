{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} dayOfYear - the day of the year of the new date\n * @returns {Date} the new date with the day of the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\nexport default function setDayOfYear(dirtyDate, dirtyDayOfYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfYear = toInteger(dirtyDayOfYear);\n  date.setMonth(0);\n  date.setDate(dayOfYear);\n  return date;\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "requiredArgs", "setDayOfYear", "dirtyDate", "dirtyDayOfYear", "arguments", "date", "dayOfYear", "setMonth", "setDate"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/setDayOfYear/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setDayOfYear\n * @category Day Helpers\n * @summary Set the day of the year to the given date.\n *\n * @description\n * Set the day of the year to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} dayOfYear - the day of the year of the new date\n * @returns {Date} the new date with the day of the year set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd day of the year to 2 July 2014:\n * const result = setDayOfYear(new Date(2014, 6, 2), 2)\n * //=> Thu Jan 02 2014 00:00:00\n */\nexport default function setDayOfYear(dirtyDate, dirtyDayOfYear) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var dayOfYear = toInteger(dirtyDayOfYear);\n  date.setMonth(0);\n  date.setDate(dayOfYear);\n  return date;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAEC,cAAc,EAAE;EAC9DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGN,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAII,SAAS,GAAGR,SAAS,CAACK,cAAc,CAAC;EACzCE,IAAI,CAACE,QAAQ,CAAC,CAAC,CAAC;EAChBF,IAAI,CAACG,OAAO,CAACF,SAAS,CAAC;EACvB,OAAOD,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}