{"ast": null, "code": "/**\n * This method invokes `interceptor` and returns `value`. The interceptor\n * is invoked with one argument; (value). The purpose of this method is to\n * \"tap into\" a method chain sequence in order to modify intermediate results.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @param {*} value The value to provide to `interceptor`.\n * @param {Function} interceptor The function to invoke.\n * @returns {*} Returns `value`.\n * @example\n *\n * _([1, 2, 3])\n *  .tap(function(array) {\n *    // Mutate input array.\n *    array.pop();\n *  })\n *  .reverse()\n *  .value();\n * // => [2, 1]\n */\nfunction tap(value, interceptor) {\n  interceptor(value);\n  return value;\n}\nexport default tap;", "map": {"version": 3, "names": ["tap", "value", "interceptor"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/tap.js"], "sourcesContent": ["/**\n * This method invokes `interceptor` and returns `value`. The interceptor\n * is invoked with one argument; (value). The purpose of this method is to\n * \"tap into\" a method chain sequence in order to modify intermediate results.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @param {*} value The value to provide to `interceptor`.\n * @param {Function} interceptor The function to invoke.\n * @returns {*} Returns `value`.\n * @example\n *\n * _([1, 2, 3])\n *  .tap(function(array) {\n *    // Mutate input array.\n *    array.pop();\n *  })\n *  .reverse()\n *  .value();\n * // => [2, 1]\n */\nfunction tap(value, interceptor) {\n  interceptor(value);\n  return value;\n}\n\nexport default tap;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,GAAGA,CAACC,KAAK,EAAEC,WAAW,EAAE;EAC/BA,WAAW,CAACD,KAAK,CAAC;EAClB,OAAOA,KAAK;AACd;AAEA,eAAeD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}