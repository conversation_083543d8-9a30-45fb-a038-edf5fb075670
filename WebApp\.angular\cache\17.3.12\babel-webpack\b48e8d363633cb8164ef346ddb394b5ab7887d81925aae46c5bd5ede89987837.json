{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./add-or-edit-memory.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./add-or-edit-memory.component.css?ngResource\";\nimport { CommonModule } from '@angular/common';\nimport { Component, Inject } from '@angular/core';\nimport { FormsModule } from '@angular/forms';\nimport { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';\nimport { ProjectMemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { Router } from '@angular/router';\nlet AddOrEditMemoryComponent = class AddOrEditMemoryComponent {\n  constructor(\n  // private modelDetailsService: ModelDetailsServiceProxy,\n  data, modelRef, projectMemoryService, router) {\n    this.data = data;\n    this.modelRef = modelRef;\n    this.projectMemoryService = projectMemoryService;\n    this.router = router;\n    this.projectMemory = {\n      projectCategory: '',\n      projectDescription: '',\n      status: '',\n      workspace: ''\n    };\n    if (this.data.isUpdating) {\n      this.projectMemory = this.data.project_memo;\n    } else {\n      this.projectMemory = {\n        projectCategory: '',\n        projectDescription: '',\n        status: '',\n        workspace: ''\n      };\n    }\n    console.log(this.data);\n  }\n  ngOnInit() {\n    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.\n    //Add 'implements OnInit' to the class.\n    let router = this.router.url.split('/');\n    this.workspaceName = router[2];\n    this.projectMemory.workspace = this.workspaceName;\n  }\n  addProjectMemory() {\n    this.projectMemoryService.saveProjectMemory(this.projectMemory).subscribe(result => {\n      console.log(result);\n      this.modelRef.close({\n        ...result\n      });\n    });\n  }\n  closeDialog() {\n    this.modelRef.close();\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NZ_MODAL_DATA]\n      }]\n    }, {\n      type: NzModalRef\n    }, {\n      type: ProjectMemoryServiceProxy\n    }, {\n      type: Router\n    }];\n  }\n};\nAddOrEditMemoryComponent = __decorate([Component({\n  selector: 'app-add-or-edit-memory',\n  standalone: true,\n  imports: [FormsModule, CommonModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], AddOrEditMemoryComponent);\nexport { AddOrEditMemoryComponent };", "map": {"version": 3, "names": ["CommonModule", "Component", "Inject", "FormsModule", "NZ_MODAL_DATA", "NzModalRef", "ProjectMemoryServiceProxy", "Router", "AddOrEditMemoryComponent", "constructor", "data", "modelRef", "projectMemoryService", "router", "projectMemory", "projectCategory", "projectDescription", "status", "workspace", "isUpdating", "project_memo", "console", "log", "ngOnInit", "url", "split", "workspaceName", "addProjectMemory", "saveProjectMemory", "subscribe", "result", "close", "closeDialog", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\dialogs\\add-or-edit-memory\\add-or-edit-memory.component.ts"], "sourcesContent": ["import { CommonModule } from '@angular/common';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { NZ_MODAL_DATA, NzModalRef } from 'ng-zorro-antd/modal';\r\nimport { ProjectMemoryServiceProxy } from '../../../shared/service-proxies/service-proxies';\r\nimport { Router } from '@angular/router';\r\n\r\n@Component({\r\n  selector: 'app-add-or-edit-memory',\r\n  standalone: true,\r\n  imports: [FormsModule, CommonModule],\r\n  templateUrl: './add-or-edit-memory.component.html',\r\n  styleUrl: './add-or-edit-memory.component.css',\r\n})\r\nexport class AddOrEditMemoryComponent {\r\n  projectMemory: any = {\r\n    projectCategory: '',\r\n    projectDescription: '',\r\n    status: '',\r\n    workspace: '',\r\n  };\r\n  workspaceName: any;\r\n  constructor(\r\n    // private modelDetailsService: ModelDetailsServiceProxy,\r\n\r\n    @Inject(NZ_MODAL_DATA)\r\n    public data: { isUpdating: boolean; project_memo: any },\r\n    private modelRef: NzModalRef,\r\n    private projectMemoryService: ProjectMemoryServiceProxy,\r\n    private router:Router\r\n  ) {\r\n    if (this.data.isUpdating) {\r\n      this.projectMemory = this.data.project_memo;\r\n    } else {\r\n      this.projectMemory = {\r\n        projectCategory: '',\r\n        projectDescription: '',\r\n        status: '',\r\n        workspace: '',\r\n      };\r\n    }\r\n    console.log(this.data);\r\n  }\r\n  ngOnInit(): void {\r\n    //Called after the constructor, initializing input properties, and the first call to ngOnChanges.\r\n    //Add 'implements OnInit' to the class.\r\n    let router = this.router.url.split('/');\r\n    this.workspaceName = router[2];\r\n    this.projectMemory.workspace = this.workspaceName;\r\n  }\r\n  addProjectMemory() {\r\n    this.projectMemoryService\r\n      .saveProjectMemory(this.projectMemory)\r\n      .subscribe((result) => {\r\n        console.log(result);\r\n        this.modelRef.close({ ...result });\r\n      });\r\n  }\r\n  closeDialog() {\r\n    this.modelRef.close(); \r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,EAAEC,UAAU,QAAQ,qBAAqB;AAC/D,SAASC,yBAAyB,QAAQ,iDAAiD;AAC3F,SAASC,MAAM,QAAQ,iBAAiB;AASjC,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAQnCC;EACE;EAGOC,IAAgD,EAC/CC,QAAoB,EACpBC,oBAA+C,EAC/CC,MAAa;IAHd,KAAAH,IAAI,GAAJA,IAAI;IACH,KAAAC,QAAQ,GAARA,QAAQ;IACR,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,MAAM,GAANA,MAAM;IAdhB,KAAAC,aAAa,GAAQ;MACnBC,eAAe,EAAE,EAAE;MACnBC,kBAAkB,EAAE,EAAE;MACtBC,MAAM,EAAE,EAAE;MACVC,SAAS,EAAE;KACZ;IAWC,IAAI,IAAI,CAACR,IAAI,CAACS,UAAU,EAAE;MACxB,IAAI,CAACL,aAAa,GAAG,IAAI,CAACJ,IAAI,CAACU,YAAY;KAC5C,MAAM;MACL,IAAI,CAACN,aAAa,GAAG;QACnBC,eAAe,EAAE,EAAE;QACnBC,kBAAkB,EAAE,EAAE;QACtBC,MAAM,EAAE,EAAE;QACVC,SAAS,EAAE;OACZ;;IAEHG,OAAO,CAACC,GAAG,CAAC,IAAI,CAACZ,IAAI,CAAC;EACxB;EACAa,QAAQA,CAAA;IACN;IACA;IACA,IAAIV,MAAM,GAAG,IAAI,CAACA,MAAM,CAACW,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,IAAI,CAACC,aAAa,GAAGb,MAAM,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACC,aAAa,CAACI,SAAS,GAAG,IAAI,CAACQ,aAAa;EACnD;EACAC,gBAAgBA,CAAA;IACd,IAAI,CAACf,oBAAoB,CACtBgB,iBAAiB,CAAC,IAAI,CAACd,aAAa,CAAC,CACrCe,SAAS,CAAEC,MAAM,IAAI;MACpBT,OAAO,CAACC,GAAG,CAACQ,MAAM,CAAC;MACnB,IAAI,CAACnB,QAAQ,CAACoB,KAAK,CAAC;QAAE,GAAGD;MAAM,CAAE,CAAC;IACpC,CAAC,CAAC;EACN;EACAE,WAAWA,CAAA;IACT,IAAI,CAACrB,QAAQ,CAACoB,KAAK,EAAE;EACvB;;;;;cAnCG7B,MAAM;QAAA+B,IAAA,GAAC7B,aAAa;MAAA;IAAA,G;;;;;;;;;AAXZI,wBAAwB,GAAA0B,UAAA,EAPpCjC,SAAS,CAAC;EACTkC,QAAQ,EAAE,wBAAwB;EAClCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAAClC,WAAW,EAAEH,YAAY,CAAC;EACpCsC,QAAA,EAAAC,oBAAkD;;CAEnD,CAAC,C,EACW/B,wBAAwB,CA+CpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}