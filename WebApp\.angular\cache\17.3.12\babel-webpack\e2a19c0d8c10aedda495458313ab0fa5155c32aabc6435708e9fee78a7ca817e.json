{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./workspace-sidebar.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./workspace-sidebar.component.css?ngResource\";\nimport { Component, inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { Router, RouterLink, RouterLinkActive } from '@angular/router';\nimport { NzPopoverModule } from 'ng-zorro-antd/popover';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { ChatServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { AuthService } from '../../../shared/services/auth.service';\nimport { ChatListService } from '../../services/chat-list.service';\nimport { TogglingService } from '../../toggling.service';\nlet WorkspaceSidebarComponent = class WorkspaceSidebarComponent {\n  constructor(_chatService, authService, nzMessageService) {\n    this._chatService = _chatService;\n    this.authService = authService;\n    this.nzMessageService = nzMessageService;\n    this.togglingService = inject(TogglingService);\n    this.chatListService = inject(ChatListService);\n    this.router = inject(Router);\n    this.groupedChats = {\n      History: []\n    };\n    this.filteredGroupedChats = {\n      ...this.groupedChats\n    };\n    this.isAllChatsOpen = true;\n    this.activeTab = 'all';\n    this.hasMoreMessages = true;\n    this.originalChatList = [];\n    this.pinnedChats = [];\n    this.favoriteChats = [];\n    this.archivedChats = [];\n    this.counter = 1;\n    this.originalOrder = (a, b) => {\n      const order = ['History'];\n      return order.indexOf(a.key) - order.indexOf(b.key);\n    };\n    this.tabConfig = {\n      'all': {\n        title: 'All Chats',\n        chats: () => this.groupedChats,\n        isGrouped: true,\n        hasMore: true\n      },\n      'pinned-history': {\n        title: 'Pinned Chats',\n        chats: () => this.pinnedChats,\n        isGrouped: false\n      },\n      'favorite': {\n        title: 'Favorite Chats',\n        chats: () => this.favoriteChats,\n        isGrouped: false\n      },\n      'archive': {\n        title: 'Archive Chats',\n        chats: () => this.archivedChats,\n        isGrouped: false\n      },\n      'history': {\n        title: 'Chat History',\n        chats: () => [],\n        isGrouped: false\n      }\n    };\n    this.workspaceName = '';\n    this.isLoading = false;\n    this.isLoadingMore = false;\n  }\n  ngOnInit() {\n    this.user = this.authService.getUser();\n    let router = this.router.url.split('/');\n    if (router[router.length - 1] === 'chat') {\n      this.workspaceName = router[router.length - 2];\n    } else if (router[router.length - 2] === 'chat') {\n      this.workspaceName = router[router.length - 3];\n    }\n    // Check if we're on the daily insight page (root path)\n    const isDailyInsightMode = this.router.url === '/' || this.router.url === '';\n    // Only load chat lists if we're not in daily insight mode\n    if (!isDailyInsightMode) {\n      console.log('Not in daily insight mode, loading chat lists');\n      // Only load the primary chat list initially\n      this.loadChatList();\n      // Only load data for the active tab\n      if (this.activeTab === 'pinned-history') {\n        this.loadPinnedChats();\n      } else if (this.activeTab === 'favorite') {\n        this.loadFavoriteChats();\n      } else if (this.activeTab === 'archive') {\n        this.loadArchivedChats();\n      }\n    } else {\n      console.log('In daily insight mode, skipping chat list loading');\n    }\n  }\n  loadChatList() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Show loading indicator if we're on the all chats tab\n      if (_this.activeTab === 'all') {\n        _this.isLoading = true;\n      }\n      try {\n        console.log('Loading chat list for workspace:', _this.workspaceName);\n        let res = yield _this._chatService.list(_this.workspaceName, _this.counter, 15).toPromise();\n        _this.chatListService.chatList = res.messages;\n        _this.originalChatList = res.messages;\n        _this.hasMoreMessages = res.hasMoreMessages;\n        _this.chatListService.groupChatsByDate();\n        _this.groupedChats = _this.chatListService.groupedChats;\n        // Only update filteredGroupedChats if we're on the 'all' tab\n        if (_this.activeTab === 'all') {\n          _this.filteredGroupedChats = {\n            ..._this.groupedChats\n          };\n          _this.isLoading = false;\n        } else {\n          // For other tabs, maintain the existing filtered list by refreshing it\n          switch (_this.activeTab) {\n            case 'pinned-history':\n              _this.filteredGroupedChats = _this.groupChatsByDate(_this.pinnedChats);\n              break;\n            case 'favorite':\n              _this.filteredGroupedChats = _this.groupChatsByDate(_this.favoriteChats);\n              break;\n            case 'archive':\n              _this.filteredGroupedChats = _this.groupChatsByDate(_this.archivedChats);\n              break;\n          }\n        }\n        _this.counter++;\n      } catch (err) {\n        console.error('Error loading chat list:', err);\n        _this.chatListService.chatList = [];\n        _this.originalChatList = [];\n        _this.hasMoreMessages = false;\n        _this.chatListService.groupedChats = {};\n        _this.groupedChats = _this.chatListService.groupedChats;\n        if (_this.activeTab === 'all') {\n          _this.filteredGroupedChats = {\n            ..._this.groupedChats\n          };\n          _this.isLoading = false;\n        }\n      }\n    })();\n  }\n  loadMoreChatList() {\n    // Show loading indicator for \"load more\"\n    this.isLoadingMore = true;\n    console.log('Loading more chats for workspace:', this.workspaceName);\n    this._chatService.list(this.workspaceName, this.counter, 15).subscribe({\n      next: res => {\n        this.counter++;\n        if (res) {\n          this.chatListService.chatList.push(...res.messages);\n          this.originalChatList.push(...res.messages);\n          this.hasMoreMessages = res.hasMoreMessages;\n          this.chatListService.groupChatsByDate();\n          this.groupedChats = this.chatListService.groupedChats;\n          // Only update filteredGroupedChats if we're on the 'all' tab\n          if (this.activeTab === 'all') {\n            this.filteredGroupedChats = {\n              ...this.groupedChats\n            };\n          }\n        }\n        this.isLoadingMore = false;\n      },\n      error: err => {\n        console.error('Error loading more chats:', err);\n        this.isLoadingMore = false;\n      }\n    });\n  }\n  loadPinnedChats() {\n    // Show loading indicator if we're on the pinned tab\n    if (this.activeTab === 'pinned-history') {\n      this.isLoading = true;\n    }\n    this._chatService.pinned(this.workspaceName).subscribe({\n      next: res => {\n        this.pinnedChats = res || [];\n        if (this.activeTab === 'pinned-history') {\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n          this.isLoading = false;\n        }\n      },\n      error: err => {\n        console.error('Error loading pinned chats:', err);\n        this.pinnedChats = [];\n        if (this.activeTab === 'pinned-history') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  loadFavoriteChats() {\n    // Show loading indicator if we're on the favorites tab\n    if (this.activeTab === 'favorite') {\n      this.isLoading = true;\n    }\n    this._chatService.favorites(this.workspaceName).subscribe({\n      next: res => {\n        this.favoriteChats = res || [];\n        if (this.activeTab === 'favorite') {\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n          this.isLoading = false;\n        }\n      },\n      error: err => {\n        console.error('Error loading favorite chats:', err);\n        this.favoriteChats = [];\n        if (this.activeTab === 'favorite') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  loadArchivedChats() {\n    // Show loading indicator if we're on the archive tab\n    if (this.activeTab === 'archive') {\n      this.isLoading = true;\n    }\n    this._chatService.archived(this.workspaceName).subscribe({\n      next: res => {\n        this.archivedChats = res || [];\n        if (this.activeTab === 'archive') {\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n          this.isLoading = false;\n        }\n      },\n      error: err => {\n        console.error('Error loading archived chats:', err);\n        this.archivedChats = [];\n        if (this.activeTab === 'archive') {\n          this.filteredGroupedChats = {};\n          this.isLoading = false;\n        }\n      }\n    });\n  }\n  toggleTab(tab) {\n    // If we're already on this tab, don't do anything\n    if (this.activeTab === tab) {\n      return;\n    }\n    // Update the activeTab\n    this.activeTab = tab;\n    this.isAllChatsOpen = true;\n    // Apply changes based on the selected tab\n    switch (tab) {\n      case 'pinned-history':\n        // Check if we already have pinned chats data\n        if (!this.pinnedChats || this.pinnedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadPinnedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\n        }\n        break;\n      case 'favorite':\n        // Check if we already have favorite chats data\n        if (!this.favoriteChats || this.favoriteChats.length === 0) {\n          // Only load if we don't have data\n          this.loadFavoriteChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\n        }\n        break;\n      case 'archive':\n        // Check if we already have archived chats data\n        if (!this.archivedChats || this.archivedChats.length === 0) {\n          // Only load if we don't have data\n          this.loadArchivedChats();\n        } else {\n          // Use existing data\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\n        }\n        break;\n      case 'all':\n      default:\n        this.filteredGroupedChats = this.groupedChats;\n        break;\n    }\n  }\n  ngDoCheck() {\n    if (this.chatListService.groupedChats !== this.groupedChats) {\n      this.groupedChats = this.chatListService.groupedChats;\n      this.filteredGroupedChats = {\n        ...this.groupedChats\n      };\n    }\n  }\n  trackByChatId(index, chat) {\n    return chat.id.toString();\n  }\n  filterChats(event) {\n    event.stopPropagation();\n    const searchTerm = event.target.value.toLowerCase();\n    if (!searchTerm) {\n      this.filteredGroupedChats = {\n        ...this.groupedChats\n      };\n      return;\n    }\n    this.filteredGroupedChats = {};\n    Object.keys(this.groupedChats).forEach(group => {\n      this.filteredGroupedChats[group] = this.groupedChats[group].filter(chat => chat.title.toLowerCase().includes(searchTerm));\n    });\n  }\n  toggleAllChats(event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = !this.isAllChatsOpen;\n  }\n  addNewChats(event) {\n    event.stopPropagation();\n    this.isAllChatsOpen = true;\n    this.router.navigate(['workspaces', this.workspaceName, 'chat']);\n  }\n  toggleChat(event, chat) {\n    this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);\n    event.stopPropagation();\n    chat.isToggled = !chat.isToggled;\n  }\n  addToPinnedChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.pin(this.chatId, !chat.isPinned).subscribe(res => {\n      if (res.isPinned) {\n        this.nzMessageService.success('Chat pinned successfully!');\n        this.pinnedChats = [...this.pinnedChats, res];\n      } else {\n        this.nzMessageService.success('Chat unpinned successfully!');\n        this.pinnedChats = this.pinnedChats.filter(c => c.id !== res.id);\n      }\n      const index = this.chatListService.chatList.findIndex(c => c.id === res.id);\n      if (index !== -1) {\n        this.chatListService.chatList[index] = res;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = {\n          ...this.groupedChats\n        };\n      }\n    });\n  }\n  addToFavChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.favorite(this.chatId, !chat.isFavorite).subscribe(res => {\n      if (res.isFavorite) {\n        this.nzMessageService.success('Chat favorited successfully!');\n        this.favoriteChats = [...this.favoriteChats, res];\n      } else {\n        this.nzMessageService.success('Chat unfavorited successfully!');\n        this.favoriteChats = this.favoriteChats.filter(c => c.id !== res.id);\n      }\n      const index = this.chatListService.chatList.findIndex(c => c.id === res.id);\n      if (index !== -1) {\n        this.chatListService.chatList[index] = res;\n        this.chatListService.groupChatsByDate();\n        this.groupedChats = this.chatListService.groupedChats;\n        this.filteredGroupedChats = {\n          ...this.groupedChats\n        };\n      }\n    });\n  }\n  addToArchiveChat(chat) {\n    this.chatId = chat.id;\n    this._chatService.archive(this.chatId, !chat.isArchived).subscribe(res => {\n      if (res.isArchived) {\n        this.nzMessageService.success('Chat archived successfully!');\n        this.archivedChats = [...this.archivedChats, res];\n        this.chatListService.chatList = this.chatListService.chatList.filter(c => c.id !== res.id);\n      } else {\n        this.nzMessageService.success('Chat unarchived successfully!');\n        this.archivedChats = this.archivedChats.filter(c => c.id !== res.id);\n        this.chatListService.chatList.push(res);\n      }\n      this.chatListService.groupChatsByDate();\n      this.groupedChats = this.chatListService.groupedChats;\n      this.filteredGroupedChats = {\n        ...this.groupedChats\n      };\n    });\n  }\n  getCurrentTabChats() {\n    return this.tabConfig[this.activeTab].chats();\n  }\n  getCurrentTabTitle() {\n    return this.tabConfig[this.activeTab].title;\n  }\n  isCurrentTabGrouped() {\n    return this.tabConfig[this.activeTab].isGrouped;\n  }\n  hasMoreForCurrentTab() {\n    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;\n  }\n  getChatsForTab() {\n    const chats = this.tabConfig[this.activeTab].chats();\n    if (Array.isArray(chats)) {\n      return chats;\n    }\n    return chats || {};\n  }\n  isGroupedForTab() {\n    return this.tabConfig[this.activeTab].isGrouped;\n  }\n  /**\n   * Groups chats by date for display in the sidebar\n   * @param chats The list of chats to group\n   * @returns An object with date groups as keys and arrays of chats as values\n   */\n  groupChatsByDate(chats) {\n    if (!chats || chats.length === 0) {\n      return {};\n    }\n    const groupedChats = {\n      'Today': [],\n      'Yesterday': [],\n      'Last 7 Days': [],\n      'Last 30 Days': [],\n      'Older': []\n    };\n    const today = new Date();\n    today.setHours(0, 0, 0, 0);\n    const yesterday = new Date(today);\n    yesterday.setDate(yesterday.getDate() - 1);\n    const last7Days = new Date(today);\n    last7Days.setDate(last7Days.getDate() - 7);\n    const last30Days = new Date(today);\n    last30Days.setDate(last30Days.getDate() - 30);\n    chats.forEach(chat => {\n      const chatDate = new Date(chat.createdAt);\n      chatDate.setHours(0, 0, 0, 0);\n      if (chatDate.getTime() === today.getTime()) {\n        groupedChats['Today'].push(chat);\n      } else if (chatDate.getTime() === yesterday.getTime()) {\n        groupedChats['Yesterday'].push(chat);\n      } else if (chatDate >= last7Days) {\n        groupedChats['Last 7 Days'].push(chat);\n      } else if (chatDate >= last30Days) {\n        groupedChats['Last 30 Days'].push(chat);\n      } else {\n        groupedChats['Older'].push(chat);\n      }\n    });\n    // Remove empty groups\n    Object.keys(groupedChats).forEach(key => {\n      if (groupedChats[key].length === 0) {\n        delete groupedChats[key];\n      }\n    });\n    return groupedChats;\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: ChatServiceProxy\n    }, {\n      type: AuthService\n    }, {\n      type: NzMessageService\n    }];\n  }\n};\nWorkspaceSidebarComponent = __decorate([Component({\n  selector: 'app-workspace-sidebar',\n  standalone: true,\n  imports: [CommonModule, FormsModule, RouterLink, RouterLinkActive, NzPopoverModule, NzButtonModule, ServiceProxyModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], WorkspaceSidebarComponent);\nexport { WorkspaceSidebarComponent };", "map": {"version": 3, "names": ["Component", "inject", "CommonModule", "FormsModule", "Router", "RouterLink", "RouterLinkActive", "NzPopoverModule", "NzButtonModule", "NzMessageService", "ChatServiceProxy", "ServiceProxyModule", "AuthService", "ChatListService", "TogglingService", "WorkspaceSidebarComponent", "constructor", "_chatService", "authService", "nzMessageService", "togglingService", "chatListService", "router", "groupedChats", "History", "filteredGroupedChats", "isAllChatsOpen", "activeTab", "hasMoreMessages", "originalChatList", "pinnedChats", "favoriteChats", "archivedChats", "counter", "originalOrder", "a", "b", "order", "indexOf", "key", "tabConfig", "title", "chats", "isGrouped", "hasMore", "workspaceName", "isLoading", "isLoadingMore", "ngOnInit", "user", "getUser", "url", "split", "length", "isDailyInsightMode", "console", "log", "loadChatList", "loadPinnedChats", "loadFavoriteChats", "loadArchivedChats", "_this", "_asyncToGenerator", "res", "list", "to<PERSON>romise", "chatList", "messages", "groupChatsByDate", "err", "error", "loadMoreChatList", "subscribe", "next", "push", "pinned", "favorites", "archived", "toggleTab", "tab", "ngDoCheck", "trackByChatId", "index", "chat", "id", "toString", "filterChats", "event", "stopPropagation", "searchTerm", "target", "value", "toLowerCase", "Object", "keys", "for<PERSON>ach", "group", "filter", "includes", "toggleAllChats", "addNewChats", "navigate", "toggleChat", "isToggled", "addToPinnedChat", "chatId", "pin", "isPinned", "success", "c", "findIndex", "addToFavChat", "favorite", "isFavorite", "addToArchiveChat", "archive", "isArchived", "getCurrentTabChats", "getCurrentTabTitle", "isCurrentTabGrouped", "hasMoreForCurrentTab", "getChatsForTab", "Array", "isArray", "isGroupedForTab", "today", "Date", "setHours", "yesterday", "setDate", "getDate", "last7Days", "last30Days", "chatDate", "createdAt", "getTime", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\workspaces\\workspace-sidebar\\workspace-sidebar.component.ts"], "sourcesContent": ["import { Component, inject } from '@angular/core';\r\nimport { KeyValue } from '@angular/common';\r\nimport { CommonModule } from '@angular/common';\r\nimport { FormsModule } from '@angular/forms';\r\nimport { Router, RouterLink, RouterLinkActive } from '@angular/router';\r\nimport { NzPopoverModule } from 'ng-zorro-antd/popover';\r\nimport { NzButtonModule } from 'ng-zorro-antd/button';\r\nimport { NzMessageService } from 'ng-zorro-antd/message';\r\nimport { ChatServiceProxy } from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\nimport { AuthService } from '../../../shared/services/auth.service';\r\nimport { ChatListService } from '../../services/chat-list.service';\r\nimport { TogglingService } from '../../toggling.service';\r\n\r\n// Define the Chat interface\r\ninterface Chat {\r\n  id: number;\r\n  title: string;\r\n  lastMessage?: string;\r\n  isPinned: boolean;\r\n  isFavorite: boolean;\r\n  isArchived: boolean;\r\n  isToggled?: boolean;\r\n  isActive?: boolean;\r\n}\r\n\r\n// Define a union type for chats (can be either grouped or non-grouped)\r\ntype Chats = { [key: string]: Chat[] } | Chat[];\r\n\r\n@Component({\r\n  selector: 'app-workspace-sidebar',\r\n  standalone: true,\r\n  imports: [\r\n    CommonModule,\r\n    FormsModule,\r\n    RouterLink,\r\n    RouterLinkActive,\r\n    NzPopoverModule,\r\n    NzButtonModule,\r\n    ServiceProxyModule\r\n  ],\r\n  templateUrl: './workspace-sidebar.component.html',\r\n  styleUrls: ['./workspace-sidebar.component.css'],\r\n})\r\nexport class WorkspaceSidebarComponent {\r\n  togglingService = inject(TogglingService);\r\n  chatListService = inject(ChatListService);\r\n  router = inject(Router);\r\n\r\n  groupedChats: { [key: string]: Chat[] } = { History: [] };\r\n  chatId: any;\r\n  filteredGroupedChats: { [key: string]: Chat[] } = { ...this.groupedChats };\r\n  isAllChatsOpen = true;\r\n  user: any;\r\n  activeTab: 'all' | 'pinned-history' | 'favorite' | 'archive' | 'history' = 'all';\r\n  hasMoreMessages = true;\r\n  originalChatList: Chat[] = [];\r\n  pinnedChats: Chat[] = [];\r\n  favoriteChats: Chat[] = [];\r\n  archivedChats: Chat[] = [];\r\n  counter = 1;\r\n\r\n  originalOrder = (a: KeyValue<string, Chat[]>, b: KeyValue<string, Chat[]>): number => {\r\n    const order = ['History'];\r\n    return order.indexOf(a.key) - order.indexOf(b.key);\r\n  };\r\n\r\n  tabConfig: { [key: string]: { title: string; chats: () => Chat[] | { [key: string]: Chat[] }; isGrouped: boolean; hasMore?: boolean } } = {\r\n    'all': { title: 'All Chats', chats: () => this.groupedChats, isGrouped: true, hasMore: true },\r\n    'pinned-history': { title: 'Pinned Chats', chats: () => this.pinnedChats, isGrouped: false },\r\n    'favorite': { title: 'Favorite Chats', chats: () => this.favoriteChats, isGrouped: false },\r\n    'archive': { title: 'Archive Chats', chats: () => this.archivedChats, isGrouped: false },\r\n    'history': { title: 'Chat History', chats: () => [], isGrouped: false },\r\n  };\r\n\r\n  constructor(\r\n    private _chatService: ChatServiceProxy,\r\n    public authService: AuthService,\r\n    private nzMessageService: NzMessageService\r\n  ) { }\r\n  workspaceName: string = '';\r\n  isLoading: boolean = false;\r\n  isLoadingMore: boolean = false;\r\n  ngOnInit(): void {\r\n    this.user = this.authService.getUser();\r\n    let router = this.router.url.split('/');\r\n    if (router[router.length - 1] === 'chat') {\r\n      this.workspaceName = router[router.length - 2];\r\n    } else if (router[router.length - 2] === 'chat') {\r\n      this.workspaceName = router[router.length - 3];\r\n    }\r\n\r\n    // Check if we're on the daily insight page (root path)\r\n    const isDailyInsightMode = this.router.url === '/' || this.router.url === '';\r\n\r\n    // Only load chat lists if we're not in daily insight mode\r\n    if (!isDailyInsightMode) {\r\n      console.log('Not in daily insight mode, loading chat lists');\r\n\r\n      // Only load the primary chat list initially\r\n      this.loadChatList();\r\n\r\n      // Only load data for the active tab\r\n      if (this.activeTab === 'pinned-history') {\r\n        this.loadPinnedChats();\r\n      } else if (this.activeTab === 'favorite') {\r\n        this.loadFavoriteChats();\r\n      } else if (this.activeTab === 'archive') {\r\n        this.loadArchivedChats();\r\n      }\r\n    } else {\r\n      console.log('In daily insight mode, skipping chat list loading');\r\n    }\r\n  }\r\n\r\n  async loadChatList() {\r\n    // Show loading indicator if we're on the all chats tab\r\n    if (this.activeTab === 'all') {\r\n      this.isLoading = true;\r\n    }\r\n\r\n    try {\r\n      console.log('Loading chat list for workspace:', this.workspaceName);\r\n\r\n      let res: any = await this._chatService\r\n        .list(this.workspaceName, this.counter, 15)\r\n        .toPromise();\r\n\r\n      this.chatListService.chatList = res.messages;\r\n      this.originalChatList = res.messages;\r\n      this.hasMoreMessages = res.hasMoreMessages;\r\n      this.chatListService.groupChatsByDate();\r\n      this.groupedChats = this.chatListService.groupedChats;\r\n\r\n      // Only update filteredGroupedChats if we're on the 'all' tab\r\n      if (this.activeTab === 'all') {\r\n        this.filteredGroupedChats = { ...this.groupedChats };\r\n        this.isLoading = false;\r\n      } else {\r\n        // For other tabs, maintain the existing filtered list by refreshing it\r\n        switch (this.activeTab) {\r\n          case 'pinned-history':\r\n            this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\r\n            break;\r\n          case 'favorite':\r\n            this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\r\n            break;\r\n          case 'archive':\r\n            this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\r\n            break;\r\n        }\r\n      }\r\n\r\n      this.counter++;\r\n    } catch (err) {\r\n      console.error('Error loading chat list:', err);\r\n      this.chatListService.chatList = [];\r\n      this.originalChatList = [];\r\n      this.hasMoreMessages = false;\r\n      this.chatListService.groupedChats = {};\r\n      this.groupedChats = this.chatListService.groupedChats;\r\n\r\n      if (this.activeTab === 'all') {\r\n        this.filteredGroupedChats = { ...this.groupedChats };\r\n        this.isLoading = false;\r\n      }\r\n    }\r\n  }\r\n\r\n  loadMoreChatList() {\r\n    // Show loading indicator for \"load more\"\r\n    this.isLoadingMore = true;\r\n\r\n    console.log('Loading more chats for workspace:', this.workspaceName);\r\n\r\n    this._chatService\r\n      .list(this.workspaceName, this.counter, 15)\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.counter++;\r\n          if (res) {\r\n            this.chatListService.chatList.push(...res.messages);\r\n            this.originalChatList.push(...res.messages);\r\n            this.hasMoreMessages = res.hasMoreMessages;\r\n            this.chatListService.groupChatsByDate();\r\n            this.groupedChats = this.chatListService.groupedChats;\r\n\r\n            // Only update filteredGroupedChats if we're on the 'all' tab\r\n            if (this.activeTab === 'all') {\r\n              this.filteredGroupedChats = { ...this.groupedChats };\r\n            }\r\n          }\r\n          this.isLoadingMore = false;\r\n        },\r\n        error: (err) => {\r\n          console.error('Error loading more chats:', err);\r\n          this.isLoadingMore = false;\r\n        }\r\n      });\r\n  }\r\n\r\n  loadPinnedChats() {\r\n    // Show loading indicator if we're on the pinned tab\r\n    if (this.activeTab === 'pinned-history') {\r\n      this.isLoading = true;\r\n    }\r\n\r\n    this._chatService.pinned(this.workspaceName).subscribe({\r\n      next: (res: any) => {\r\n        this.pinnedChats = res || [];\r\n        if (this.activeTab === 'pinned-history') {\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\r\n          this.isLoading = false;\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading pinned chats:', err);\r\n        this.pinnedChats = [];\r\n        if (this.activeTab === 'pinned-history') {\r\n          this.filteredGroupedChats = {};\r\n          this.isLoading = false;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  loadFavoriteChats() {\r\n    // Show loading indicator if we're on the favorites tab\r\n    if (this.activeTab === 'favorite') {\r\n      this.isLoading = true;\r\n    }\r\n\r\n    this._chatService.favorites(this.workspaceName).subscribe({\r\n      next: (res: any) => {\r\n        this.favoriteChats = res || [];\r\n        if (this.activeTab === 'favorite') {\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\r\n          this.isLoading = false;\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading favorite chats:', err);\r\n        this.favoriteChats = [];\r\n        if (this.activeTab === 'favorite') {\r\n          this.filteredGroupedChats = {};\r\n          this.isLoading = false;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  loadArchivedChats() {\r\n    // Show loading indicator if we're on the archive tab\r\n    if (this.activeTab === 'archive') {\r\n      this.isLoading = true;\r\n    }\r\n\r\n    this._chatService.archived(this.workspaceName).subscribe({\r\n      next: (res: any) => {\r\n        this.archivedChats = res || [];\r\n        if (this.activeTab === 'archive') {\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\r\n          this.isLoading = false;\r\n        }\r\n      },\r\n      error: (err) => {\r\n        console.error('Error loading archived chats:', err);\r\n        this.archivedChats = [];\r\n        if (this.activeTab === 'archive') {\r\n          this.filteredGroupedChats = {};\r\n          this.isLoading = false;\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  toggleTab(tab: any) {\r\n    // If we're already on this tab, don't do anything\r\n    if (this.activeTab === tab) {\r\n      return;\r\n    }\r\n\r\n    // Update the activeTab\r\n    this.activeTab = tab;\r\n    this.isAllChatsOpen = true;\r\n\r\n    // Apply changes based on the selected tab\r\n    switch (tab) {\r\n      case 'pinned-history':\r\n        // Check if we already have pinned chats data\r\n        if (!this.pinnedChats || this.pinnedChats.length === 0) {\r\n          // Only load if we don't have data\r\n          this.loadPinnedChats();\r\n        } else {\r\n          // Use existing data\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.pinnedChats);\r\n        }\r\n        break;\r\n      case 'favorite':\r\n        // Check if we already have favorite chats data\r\n        if (!this.favoriteChats || this.favoriteChats.length === 0) {\r\n          // Only load if we don't have data\r\n          this.loadFavoriteChats();\r\n        } else {\r\n          // Use existing data\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.favoriteChats);\r\n        }\r\n        break;\r\n      case 'archive':\r\n        // Check if we already have archived chats data\r\n        if (!this.archivedChats || this.archivedChats.length === 0) {\r\n          // Only load if we don't have data\r\n          this.loadArchivedChats();\r\n        } else {\r\n          // Use existing data\r\n          this.filteredGroupedChats = this.groupChatsByDate(this.archivedChats);\r\n        }\r\n        break;\r\n      case 'all':\r\n      default:\r\n        this.filteredGroupedChats = this.groupedChats;\r\n        break;\r\n    }\r\n  }\r\n\r\n  ngDoCheck(): void {\r\n    if (this.chatListService.groupedChats !== this.groupedChats) {\r\n      this.groupedChats = this.chatListService.groupedChats;\r\n      this.filteredGroupedChats = { ...this.groupedChats };\r\n    }\r\n  }\r\n\r\n  trackByChatId(index: number, chat: Chat): string {\r\n    return chat.id.toString();\r\n  }\r\n\r\n  filterChats(event: Event) {\r\n    event.stopPropagation();\r\n    const searchTerm = (event.target as HTMLInputElement).value.toLowerCase();\r\n\r\n    if (!searchTerm) {\r\n      this.filteredGroupedChats = { ...this.groupedChats };\r\n      return;\r\n    }\r\n\r\n    this.filteredGroupedChats = {};\r\n    Object.keys(this.groupedChats).forEach((group) => {\r\n      this.filteredGroupedChats[group] = this.groupedChats[group].filter(\r\n        (chat: Chat) => chat.title.toLowerCase().includes(searchTerm)\r\n      );\r\n    });\r\n  }\r\n\r\n  toggleAllChats(event: Event) {\r\n    event.stopPropagation();\r\n    this.isAllChatsOpen = !this.isAllChatsOpen;\r\n  }\r\n\r\n  addNewChats(event: Event) {\r\n    event.stopPropagation();\r\n    this.isAllChatsOpen = true;\r\n    this.router.navigate(['workspaces', this.workspaceName, 'chat']);\r\n  }\r\n\r\n  toggleChat(event: Event, chat: Chat) {\r\n    this.router.navigate(['workspaces', this.workspaceName, 'chat', chat.id]);\r\n    event.stopPropagation();\r\n    chat.isToggled = !chat.isToggled;\r\n  }\r\n\r\n  addToPinnedChat(chat: Chat) {\r\n    this.chatId = chat.id;\r\n    this._chatService.pin(this.chatId, !chat.isPinned).subscribe((res: any) => {\r\n      if (res.isPinned) {\r\n        this.nzMessageService.success('Chat pinned successfully!');\r\n        this.pinnedChats = [...this.pinnedChats, res];\r\n      } else {\r\n        this.nzMessageService.success('Chat unpinned successfully!');\r\n        this.pinnedChats = this.pinnedChats.filter((c) => c.id !== res.id);\r\n      }\r\n      const index = this.chatListService.chatList.findIndex((c: any) => c.id === res.id);\r\n      if (index !== -1) {\r\n        this.chatListService.chatList[index] = res;\r\n        this.chatListService.groupChatsByDate();\r\n        this.groupedChats = this.chatListService.groupedChats;\r\n        this.filteredGroupedChats = { ...this.groupedChats };\r\n      }\r\n    });\r\n  }\r\n\r\n  addToFavChat(chat: Chat) {\r\n    this.chatId = chat.id;\r\n    this._chatService.favorite(this.chatId, !chat.isFavorite).subscribe((res: any) => {\r\n      if (res.isFavorite) {\r\n        this.nzMessageService.success('Chat favorited successfully!');\r\n        this.favoriteChats = [...this.favoriteChats, res];\r\n      } else {\r\n        this.nzMessageService.success('Chat unfavorited successfully!');\r\n        this.favoriteChats = this.favoriteChats.filter((c) => c.id !== res.id);\r\n      }\r\n      const index = this.chatListService.chatList.findIndex((c: any) => c.id === res.id);\r\n      if (index !== -1) {\r\n        this.chatListService.chatList[index] = res;\r\n        this.chatListService.groupChatsByDate();\r\n        this.groupedChats = this.chatListService.groupedChats;\r\n        this.filteredGroupedChats = { ...this.groupedChats };\r\n      }\r\n    });\r\n  }\r\n\r\n  addToArchiveChat(chat: Chat) {\r\n    this.chatId = chat.id;\r\n    this._chatService.archive(this.chatId, !chat.isArchived).subscribe((res: any) => {\r\n      if (res.isArchived) {\r\n        this.nzMessageService.success('Chat archived successfully!');\r\n        this.archivedChats = [...this.archivedChats, res];\r\n        this.chatListService.chatList = this.chatListService.chatList.filter((c: any) => c.id !== res.id);\r\n      } else {\r\n        this.nzMessageService.success('Chat unarchived successfully!');\r\n        this.archivedChats = this.archivedChats.filter((c) => c.id !== res.id);\r\n        this.chatListService.chatList.push(res);\r\n      }\r\n      this.chatListService.groupChatsByDate();\r\n      this.groupedChats = this.chatListService.groupedChats;\r\n      this.filteredGroupedChats = { ...this.groupedChats };\r\n    });\r\n  }\r\n\r\n  getCurrentTabChats() {\r\n    return this.tabConfig[this.activeTab].chats();\r\n  }\r\n\r\n  getCurrentTabTitle() {\r\n    return this.tabConfig[this.activeTab].title;\r\n  }\r\n\r\n  isCurrentTabGrouped() {\r\n    return this.tabConfig[this.activeTab].isGrouped;\r\n  }\r\n\r\n  hasMoreForCurrentTab() {\r\n    return this.tabConfig[this.activeTab].hasMore && this.hasMoreMessages;\r\n  }\r\n\r\n  getChatsForTab(): Chat[] | { [key: string]: Chat[] } {\r\n    const chats = this.tabConfig[this.activeTab].chats();\r\n    if (Array.isArray(chats)) {\r\n      return chats;\r\n    }\r\n    return chats || {};\r\n  }\r\n\r\n  isGroupedForTab(): boolean {\r\n    return this.tabConfig[this.activeTab].isGrouped;\r\n  }\r\n\r\n  /**\r\n   * Groups chats by date for display in the sidebar\r\n   * @param chats The list of chats to group\r\n   * @returns An object with date groups as keys and arrays of chats as values\r\n   */\r\n  groupChatsByDate(chats: any[]): { [key: string]: any[] } {\r\n    if (!chats || chats.length === 0) {\r\n      return {};\r\n    }\r\n\r\n    const groupedChats: { [key: string]: any[] } = {\r\n      'Today': [],\r\n      'Yesterday': [],\r\n      'Last 7 Days': [],\r\n      'Last 30 Days': [],\r\n      'Older': [],\r\n    };\r\n\r\n    const today = new Date();\r\n    today.setHours(0, 0, 0, 0);\r\n\r\n    const yesterday = new Date(today);\r\n    yesterday.setDate(yesterday.getDate() - 1);\r\n\r\n    const last7Days = new Date(today);\r\n    last7Days.setDate(last7Days.getDate() - 7);\r\n\r\n    const last30Days = new Date(today);\r\n    last30Days.setDate(last30Days.getDate() - 30);\r\n\r\n    chats.forEach((chat) => {\r\n      const chatDate = new Date(chat.createdAt);\r\n      chatDate.setHours(0, 0, 0, 0);\r\n\r\n      if (chatDate.getTime() === today.getTime()) {\r\n        groupedChats['Today'].push(chat);\r\n      } else if (chatDate.getTime() === yesterday.getTime()) {\r\n        groupedChats['Yesterday'].push(chat);\r\n      } else if (chatDate >= last7Days) {\r\n        groupedChats['Last 7 Days'].push(chat);\r\n      } else if (chatDate >= last30Days) {\r\n        groupedChats['Last 30 Days'].push(chat);\r\n      } else {\r\n        groupedChats['Older'].push(chat);\r\n      }\r\n    });\r\n\r\n    // Remove empty groups\r\n    Object.keys(groupedChats).forEach((key) => {\r\n      if (groupedChats[key].length === 0) {\r\n        delete groupedChats[key];\r\n      }\r\n    });\r\n\r\n    return groupedChats;\r\n  }\r\n}\r\n"], "mappings": ";;;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAEjD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,iBAAiB;AACtE,SAASC,eAAe,QAAQ,uBAAuB;AACvD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,gBAAgB,QAAQ,iDAAiD;AAClF,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,WAAW,QAAQ,uCAAuC;AACnE,SAASC,eAAe,QAAQ,kCAAkC;AAClE,SAASC,eAAe,QAAQ,wBAAwB;AAgCjD,IAAMC,yBAAyB,GAA/B,MAAMA,yBAAyB;EA+BpCC,YACUC,YAA8B,EAC/BC,WAAwB,EACvBC,gBAAkC;IAFlC,KAAAF,YAAY,GAAZA,YAAY;IACb,KAAAC,WAAW,GAAXA,WAAW;IACV,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAjC1B,KAAAC,eAAe,GAAGnB,MAAM,CAACa,eAAe,CAAC;IACzC,KAAAO,eAAe,GAAGpB,MAAM,CAACY,eAAe,CAAC;IACzC,KAAAS,MAAM,GAAGrB,MAAM,CAACG,MAAM,CAAC;IAEvB,KAAAmB,YAAY,GAA8B;MAAEC,OAAO,EAAE;IAAE,CAAE;IAEzD,KAAAC,oBAAoB,GAA8B;MAAE,GAAG,IAAI,CAACF;IAAY,CAAE;IAC1E,KAAAG,cAAc,GAAG,IAAI;IAErB,KAAAC,SAAS,GAAkE,KAAK;IAChF,KAAAC,eAAe,GAAG,IAAI;IACtB,KAAAC,gBAAgB,GAAW,EAAE;IAC7B,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,aAAa,GAAW,EAAE;IAC1B,KAAAC,OAAO,GAAG,CAAC;IAEX,KAAAC,aAAa,GAAG,CAACC,CAA2B,EAAEC,CAA2B,KAAY;MACnF,MAAMC,KAAK,GAAG,CAAC,SAAS,CAAC;MACzB,OAAOA,KAAK,CAACC,OAAO,CAACH,CAAC,CAACI,GAAG,CAAC,GAAGF,KAAK,CAACC,OAAO,CAACF,CAAC,CAACG,GAAG,CAAC;IACpD,CAAC;IAED,KAAAC,SAAS,GAAiI;MACxI,KAAK,EAAE;QAAEC,KAAK,EAAE,WAAW;QAAEC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACnB,YAAY;QAAEoB,SAAS,EAAE,IAAI;QAAEC,OAAO,EAAE;MAAI,CAAE;MAC7F,gBAAgB,EAAE;QAAEH,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACZ,WAAW;QAAEa,SAAS,EAAE;MAAK,CAAE;MAC5F,UAAU,EAAE;QAAEF,KAAK,EAAE,gBAAgB;QAAEC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACX,aAAa;QAAEY,SAAS,EAAE;MAAK,CAAE;MAC1F,SAAS,EAAE;QAAEF,KAAK,EAAE,eAAe;QAAEC,KAAK,EAAEA,CAAA,KAAM,IAAI,CAACV,aAAa;QAAEW,SAAS,EAAE;MAAK,CAAE;MACxF,SAAS,EAAE;QAAEF,KAAK,EAAE,cAAc;QAAEC,KAAK,EAAEA,CAAA,KAAM,EAAE;QAAEC,SAAS,EAAE;MAAK;KACtE;IAOD,KAAAE,aAAa,GAAW,EAAE;IAC1B,KAAAC,SAAS,GAAY,KAAK;IAC1B,KAAAC,aAAa,GAAY,KAAK;EAH1B;EAIJC,QAAQA,CAAA;IACN,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC/B,WAAW,CAACgC,OAAO,EAAE;IACtC,IAAI5B,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC6B,GAAG,CAACC,KAAK,CAAC,GAAG,CAAC;IACvC,IAAI9B,MAAM,CAACA,MAAM,CAAC+B,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;MACxC,IAAI,CAACR,aAAa,GAAGvB,MAAM,CAACA,MAAM,CAAC+B,MAAM,GAAG,CAAC,CAAC;KAC/C,MAAM,IAAI/B,MAAM,CAACA,MAAM,CAAC+B,MAAM,GAAG,CAAC,CAAC,KAAK,MAAM,EAAE;MAC/C,IAAI,CAACR,aAAa,GAAGvB,MAAM,CAACA,MAAM,CAAC+B,MAAM,GAAG,CAAC,CAAC;;IAGhD;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAAChC,MAAM,CAAC6B,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC7B,MAAM,CAAC6B,GAAG,KAAK,EAAE;IAE5E;IACA,IAAI,CAACG,kBAAkB,EAAE;MACvBC,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;MAE5D;MACA,IAAI,CAACC,YAAY,EAAE;MAEnB;MACA,IAAI,IAAI,CAAC9B,SAAS,KAAK,gBAAgB,EAAE;QACvC,IAAI,CAAC+B,eAAe,EAAE;OACvB,MAAM,IAAI,IAAI,CAAC/B,SAAS,KAAK,UAAU,EAAE;QACxC,IAAI,CAACgC,iBAAiB,EAAE;OACzB,MAAM,IAAI,IAAI,CAAChC,SAAS,KAAK,SAAS,EAAE;QACvC,IAAI,CAACiC,iBAAiB,EAAE;;KAE3B,MAAM;MACLL,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;;EAEpE;EAEMC,YAAYA,CAAA;IAAA,IAAAI,KAAA;IAAA,OAAAC,iBAAA;MAChB;MACA,IAAID,KAAI,CAAClC,SAAS,KAAK,KAAK,EAAE;QAC5BkC,KAAI,CAACf,SAAS,GAAG,IAAI;;MAGvB,IAAI;QACFS,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEK,KAAI,CAAChB,aAAa,CAAC;QAEnE,IAAIkB,GAAG,SAAcF,KAAI,CAAC5C,YAAY,CACnC+C,IAAI,CAACH,KAAI,CAAChB,aAAa,EAAEgB,KAAI,CAAC5B,OAAO,EAAE,EAAE,CAAC,CAC1CgC,SAAS,EAAE;QAEdJ,KAAI,CAACxC,eAAe,CAAC6C,QAAQ,GAAGH,GAAG,CAACI,QAAQ;QAC5CN,KAAI,CAAChC,gBAAgB,GAAGkC,GAAG,CAACI,QAAQ;QACpCN,KAAI,CAACjC,eAAe,GAAGmC,GAAG,CAACnC,eAAe;QAC1CiC,KAAI,CAACxC,eAAe,CAAC+C,gBAAgB,EAAE;QACvCP,KAAI,CAACtC,YAAY,GAAGsC,KAAI,CAACxC,eAAe,CAACE,YAAY;QAErD;QACA,IAAIsC,KAAI,CAAClC,SAAS,KAAK,KAAK,EAAE;UAC5BkC,KAAI,CAACpC,oBAAoB,GAAG;YAAE,GAAGoC,KAAI,CAACtC;UAAY,CAAE;UACpDsC,KAAI,CAACf,SAAS,GAAG,KAAK;SACvB,MAAM;UACL;UACA,QAAQe,KAAI,CAAClC,SAAS;YACpB,KAAK,gBAAgB;cACnBkC,KAAI,CAACpC,oBAAoB,GAAGoC,KAAI,CAACO,gBAAgB,CAACP,KAAI,CAAC/B,WAAW,CAAC;cACnE;YACF,KAAK,UAAU;cACb+B,KAAI,CAACpC,oBAAoB,GAAGoC,KAAI,CAACO,gBAAgB,CAACP,KAAI,CAAC9B,aAAa,CAAC;cACrE;YACF,KAAK,SAAS;cACZ8B,KAAI,CAACpC,oBAAoB,GAAGoC,KAAI,CAACO,gBAAgB,CAACP,KAAI,CAAC7B,aAAa,CAAC;cACrE;;;QAIN6B,KAAI,CAAC5B,OAAO,EAAE;OACf,CAAC,OAAOoC,GAAG,EAAE;QACZd,OAAO,CAACe,KAAK,CAAC,0BAA0B,EAAED,GAAG,CAAC;QAC9CR,KAAI,CAACxC,eAAe,CAAC6C,QAAQ,GAAG,EAAE;QAClCL,KAAI,CAAChC,gBAAgB,GAAG,EAAE;QAC1BgC,KAAI,CAACjC,eAAe,GAAG,KAAK;QAC5BiC,KAAI,CAACxC,eAAe,CAACE,YAAY,GAAG,EAAE;QACtCsC,KAAI,CAACtC,YAAY,GAAGsC,KAAI,CAACxC,eAAe,CAACE,YAAY;QAErD,IAAIsC,KAAI,CAAClC,SAAS,KAAK,KAAK,EAAE;UAC5BkC,KAAI,CAACpC,oBAAoB,GAAG;YAAE,GAAGoC,KAAI,CAACtC;UAAY,CAAE;UACpDsC,KAAI,CAACf,SAAS,GAAG,KAAK;;;IAEzB;EACH;EAEAyB,gBAAgBA,CAAA;IACd;IACA,IAAI,CAACxB,aAAa,GAAG,IAAI;IAEzBQ,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACX,aAAa,CAAC;IAEpE,IAAI,CAAC5B,YAAY,CACd+C,IAAI,CAAC,IAAI,CAACnB,aAAa,EAAE,IAAI,CAACZ,OAAO,EAAE,EAAE,CAAC,CAC1CuC,SAAS,CAAC;MACTC,IAAI,EAAGV,GAAQ,IAAI;QACjB,IAAI,CAAC9B,OAAO,EAAE;QACd,IAAI8B,GAAG,EAAE;UACP,IAAI,CAAC1C,eAAe,CAAC6C,QAAQ,CAACQ,IAAI,CAAC,GAAGX,GAAG,CAACI,QAAQ,CAAC;UACnD,IAAI,CAACtC,gBAAgB,CAAC6C,IAAI,CAAC,GAAGX,GAAG,CAACI,QAAQ,CAAC;UAC3C,IAAI,CAACvC,eAAe,GAAGmC,GAAG,CAACnC,eAAe;UAC1C,IAAI,CAACP,eAAe,CAAC+C,gBAAgB,EAAE;UACvC,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACF,eAAe,CAACE,YAAY;UAErD;UACA,IAAI,IAAI,CAACI,SAAS,KAAK,KAAK,EAAE;YAC5B,IAAI,CAACF,oBAAoB,GAAG;cAAE,GAAG,IAAI,CAACF;YAAY,CAAE;;;QAGxD,IAAI,CAACwB,aAAa,GAAG,KAAK;MAC5B,CAAC;MACDuB,KAAK,EAAGD,GAAG,IAAI;QACbd,OAAO,CAACe,KAAK,CAAC,2BAA2B,EAAED,GAAG,CAAC;QAC/C,IAAI,CAACtB,aAAa,GAAG,KAAK;MAC5B;KACD,CAAC;EACN;EAEAW,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAAC/B,SAAS,KAAK,gBAAgB,EAAE;MACvC,IAAI,CAACmB,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAAC7B,YAAY,CAAC0D,MAAM,CAAC,IAAI,CAAC9B,aAAa,CAAC,CAAC2B,SAAS,CAAC;MACrDC,IAAI,EAAGV,GAAQ,IAAI;QACjB,IAAI,CAACjC,WAAW,GAAGiC,GAAG,IAAI,EAAE;QAC5B,IAAI,IAAI,CAACpC,SAAS,KAAK,gBAAgB,EAAE;UACvC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACtC,WAAW,CAAC;UACnE,IAAI,CAACgB,SAAS,GAAG,KAAK;;MAE1B,CAAC;MACDwB,KAAK,EAAGD,GAAG,IAAI;QACbd,OAAO,CAACe,KAAK,CAAC,6BAA6B,EAAED,GAAG,CAAC;QACjD,IAAI,CAACvC,WAAW,GAAG,EAAE;QACrB,IAAI,IAAI,CAACH,SAAS,KAAK,gBAAgB,EAAE;UACvC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAACqB,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAa,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAAChC,SAAS,KAAK,UAAU,EAAE;MACjC,IAAI,CAACmB,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAAC7B,YAAY,CAAC2D,SAAS,CAAC,IAAI,CAAC/B,aAAa,CAAC,CAAC2B,SAAS,CAAC;MACxDC,IAAI,EAAGV,GAAQ,IAAI;QACjB,IAAI,CAAChC,aAAa,GAAGgC,GAAG,IAAI,EAAE;QAC9B,IAAI,IAAI,CAACpC,SAAS,KAAK,UAAU,EAAE;UACjC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACrC,aAAa,CAAC;UACrE,IAAI,CAACe,SAAS,GAAG,KAAK;;MAE1B,CAAC;MACDwB,KAAK,EAAGD,GAAG,IAAI;QACbd,OAAO,CAACe,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC;QACnD,IAAI,CAACtC,aAAa,GAAG,EAAE;QACvB,IAAI,IAAI,CAACJ,SAAS,KAAK,UAAU,EAAE;UACjC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAACqB,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAc,iBAAiBA,CAAA;IACf;IACA,IAAI,IAAI,CAACjC,SAAS,KAAK,SAAS,EAAE;MAChC,IAAI,CAACmB,SAAS,GAAG,IAAI;;IAGvB,IAAI,CAAC7B,YAAY,CAAC4D,QAAQ,CAAC,IAAI,CAAChC,aAAa,CAAC,CAAC2B,SAAS,CAAC;MACvDC,IAAI,EAAGV,GAAQ,IAAI;QACjB,IAAI,CAAC/B,aAAa,GAAG+B,GAAG,IAAI,EAAE;QAC9B,IAAI,IAAI,CAACpC,SAAS,KAAK,SAAS,EAAE;UAChC,IAAI,CAACF,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACpC,aAAa,CAAC;UACrE,IAAI,CAACc,SAAS,GAAG,KAAK;;MAE1B,CAAC;MACDwB,KAAK,EAAGD,GAAG,IAAI;QACbd,OAAO,CAACe,KAAK,CAAC,+BAA+B,EAAED,GAAG,CAAC;QACnD,IAAI,CAACrC,aAAa,GAAG,EAAE;QACvB,IAAI,IAAI,CAACL,SAAS,KAAK,SAAS,EAAE;UAChC,IAAI,CAACF,oBAAoB,GAAG,EAAE;UAC9B,IAAI,CAACqB,SAAS,GAAG,KAAK;;MAE1B;KACD,CAAC;EACJ;EAEAgC,SAASA,CAACC,GAAQ;IAChB;IACA,IAAI,IAAI,CAACpD,SAAS,KAAKoD,GAAG,EAAE;MAC1B;;IAGF;IACA,IAAI,CAACpD,SAAS,GAAGoD,GAAG;IACpB,IAAI,CAACrD,cAAc,GAAG,IAAI;IAE1B;IACA,QAAQqD,GAAG;MACT,KAAK,gBAAgB;QACnB;QACA,IAAI,CAAC,IAAI,CAACjD,WAAW,IAAI,IAAI,CAACA,WAAW,CAACuB,MAAM,KAAK,CAAC,EAAE;UACtD;UACA,IAAI,CAACK,eAAe,EAAE;SACvB,MAAM;UACL;UACA,IAAI,CAACjC,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACtC,WAAW,CAAC;;QAErE;MACF,KAAK,UAAU;QACb;QACA,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACsB,MAAM,KAAK,CAAC,EAAE;UAC1D;UACA,IAAI,CAACM,iBAAiB,EAAE;SACzB,MAAM;UACL;UACA,IAAI,CAAClC,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACrC,aAAa,CAAC;;QAEvE;MACF,KAAK,SAAS;QACZ;QACA,IAAI,CAAC,IAAI,CAACC,aAAa,IAAI,IAAI,CAACA,aAAa,CAACqB,MAAM,KAAK,CAAC,EAAE;UAC1D;UACA,IAAI,CAACO,iBAAiB,EAAE;SACzB,MAAM;UACL;UACA,IAAI,CAACnC,oBAAoB,GAAG,IAAI,CAAC2C,gBAAgB,CAAC,IAAI,CAACpC,aAAa,CAAC;;QAEvE;MACF,KAAK,KAAK;MACV;QACE,IAAI,CAACP,oBAAoB,GAAG,IAAI,CAACF,YAAY;QAC7C;;EAEN;EAEAyD,SAASA,CAAA;IACP,IAAI,IAAI,CAAC3D,eAAe,CAACE,YAAY,KAAK,IAAI,CAACA,YAAY,EAAE;MAC3D,IAAI,CAACA,YAAY,GAAG,IAAI,CAACF,eAAe,CAACE,YAAY;MACrD,IAAI,CAACE,oBAAoB,GAAG;QAAE,GAAG,IAAI,CAACF;MAAY,CAAE;;EAExD;EAEA0D,aAAaA,CAACC,KAAa,EAAEC,IAAU;IACrC,OAAOA,IAAI,CAACC,EAAE,CAACC,QAAQ,EAAE;EAC3B;EAEAC,WAAWA,CAACC,KAAY;IACtBA,KAAK,CAACC,eAAe,EAAE;IACvB,MAAMC,UAAU,GAAIF,KAAK,CAACG,MAA2B,CAACC,KAAK,CAACC,WAAW,EAAE;IAEzE,IAAI,CAACH,UAAU,EAAE;MACf,IAAI,CAAChE,oBAAoB,GAAG;QAAE,GAAG,IAAI,CAACF;MAAY,CAAE;MACpD;;IAGF,IAAI,CAACE,oBAAoB,GAAG,EAAE;IAC9BoE,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvE,YAAY,CAAC,CAACwE,OAAO,CAAEC,KAAK,IAAI;MAC/C,IAAI,CAACvE,oBAAoB,CAACuE,KAAK,CAAC,GAAG,IAAI,CAACzE,YAAY,CAACyE,KAAK,CAAC,CAACC,MAAM,CAC/Dd,IAAU,IAAKA,IAAI,CAAC1C,KAAK,CAACmD,WAAW,EAAE,CAACM,QAAQ,CAACT,UAAU,CAAC,CAC9D;IACH,CAAC,CAAC;EACJ;EAEAU,cAAcA,CAACZ,KAAY;IACzBA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC9D,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;EAC5C;EAEA0E,WAAWA,CAACb,KAAY;IACtBA,KAAK,CAACC,eAAe,EAAE;IACvB,IAAI,CAAC9D,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACJ,MAAM,CAAC+E,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACxD,aAAa,EAAE,MAAM,CAAC,CAAC;EAClE;EAEAyD,UAAUA,CAACf,KAAY,EAAEJ,IAAU;IACjC,IAAI,CAAC7D,MAAM,CAAC+E,QAAQ,CAAC,CAAC,YAAY,EAAE,IAAI,CAACxD,aAAa,EAAE,MAAM,EAAEsC,IAAI,CAACC,EAAE,CAAC,CAAC;IACzEG,KAAK,CAACC,eAAe,EAAE;IACvBL,IAAI,CAACoB,SAAS,GAAG,CAACpB,IAAI,CAACoB,SAAS;EAClC;EAEAC,eAAeA,CAACrB,IAAU;IACxB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACC,EAAE;IACrB,IAAI,CAACnE,YAAY,CAACyF,GAAG,CAAC,IAAI,CAACD,MAAM,EAAE,CAACtB,IAAI,CAACwB,QAAQ,CAAC,CAACnC,SAAS,CAAET,GAAQ,IAAI;MACxE,IAAIA,GAAG,CAAC4C,QAAQ,EAAE;QAChB,IAAI,CAACxF,gBAAgB,CAACyF,OAAO,CAAC,2BAA2B,CAAC;QAC1D,IAAI,CAAC9E,WAAW,GAAG,CAAC,GAAG,IAAI,CAACA,WAAW,EAAEiC,GAAG,CAAC;OAC9C,MAAM;QACL,IAAI,CAAC5C,gBAAgB,CAACyF,OAAO,CAAC,6BAA6B,CAAC;QAC5D,IAAI,CAAC9E,WAAW,GAAG,IAAI,CAACA,WAAW,CAACmE,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;;MAEpE,MAAMF,KAAK,GAAG,IAAI,CAAC7D,eAAe,CAAC6C,QAAQ,CAAC4C,SAAS,CAAED,CAAM,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;MAClF,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC7D,eAAe,CAAC6C,QAAQ,CAACgB,KAAK,CAAC,GAAGnB,GAAG;QAC1C,IAAI,CAAC1C,eAAe,CAAC+C,gBAAgB,EAAE;QACvC,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACF,eAAe,CAACE,YAAY;QACrD,IAAI,CAACE,oBAAoB,GAAG;UAAE,GAAG,IAAI,CAACF;QAAY,CAAE;;IAExD,CAAC,CAAC;EACJ;EAEAwF,YAAYA,CAAC5B,IAAU;IACrB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACC,EAAE;IACrB,IAAI,CAACnE,YAAY,CAAC+F,QAAQ,CAAC,IAAI,CAACP,MAAM,EAAE,CAACtB,IAAI,CAAC8B,UAAU,CAAC,CAACzC,SAAS,CAAET,GAAQ,IAAI;MAC/E,IAAIA,GAAG,CAACkD,UAAU,EAAE;QAClB,IAAI,CAAC9F,gBAAgB,CAACyF,OAAO,CAAC,8BAA8B,CAAC;QAC7D,IAAI,CAAC7E,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAEgC,GAAG,CAAC;OAClD,MAAM;QACL,IAAI,CAAC5C,gBAAgB,CAACyF,OAAO,CAAC,gCAAgC,CAAC;QAC/D,IAAI,CAAC7E,aAAa,GAAG,IAAI,CAACA,aAAa,CAACkE,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;;MAExE,MAAMF,KAAK,GAAG,IAAI,CAAC7D,eAAe,CAAC6C,QAAQ,CAAC4C,SAAS,CAAED,CAAM,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;MAClF,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,IAAI,CAAC7D,eAAe,CAAC6C,QAAQ,CAACgB,KAAK,CAAC,GAAGnB,GAAG;QAC1C,IAAI,CAAC1C,eAAe,CAAC+C,gBAAgB,EAAE;QACvC,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACF,eAAe,CAACE,YAAY;QACrD,IAAI,CAACE,oBAAoB,GAAG;UAAE,GAAG,IAAI,CAACF;QAAY,CAAE;;IAExD,CAAC,CAAC;EACJ;EAEA2F,gBAAgBA,CAAC/B,IAAU;IACzB,IAAI,CAACsB,MAAM,GAAGtB,IAAI,CAACC,EAAE;IACrB,IAAI,CAACnE,YAAY,CAACkG,OAAO,CAAC,IAAI,CAACV,MAAM,EAAE,CAACtB,IAAI,CAACiC,UAAU,CAAC,CAAC5C,SAAS,CAAET,GAAQ,IAAI;MAC9E,IAAIA,GAAG,CAACqD,UAAU,EAAE;QAClB,IAAI,CAACjG,gBAAgB,CAACyF,OAAO,CAAC,6BAA6B,CAAC;QAC5D,IAAI,CAAC5E,aAAa,GAAG,CAAC,GAAG,IAAI,CAACA,aAAa,EAAE+B,GAAG,CAAC;QACjD,IAAI,CAAC1C,eAAe,CAAC6C,QAAQ,GAAG,IAAI,CAAC7C,eAAe,CAAC6C,QAAQ,CAAC+B,MAAM,CAAEY,CAAM,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;OAClG,MAAM;QACL,IAAI,CAACjE,gBAAgB,CAACyF,OAAO,CAAC,+BAA+B,CAAC;QAC9D,IAAI,CAAC5E,aAAa,GAAG,IAAI,CAACA,aAAa,CAACiE,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAACzB,EAAE,KAAKrB,GAAG,CAACqB,EAAE,CAAC;QACtE,IAAI,CAAC/D,eAAe,CAAC6C,QAAQ,CAACQ,IAAI,CAACX,GAAG,CAAC;;MAEzC,IAAI,CAAC1C,eAAe,CAAC+C,gBAAgB,EAAE;MACvC,IAAI,CAAC7C,YAAY,GAAG,IAAI,CAACF,eAAe,CAACE,YAAY;MACrD,IAAI,CAACE,oBAAoB,GAAG;QAAE,GAAG,IAAI,CAACF;MAAY,CAAE;IACtD,CAAC,CAAC;EACJ;EAEA8F,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC7E,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACe,KAAK,EAAE;EAC/C;EAEA4E,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAAC9E,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACc,KAAK;EAC7C;EAEA8E,mBAAmBA,CAAA;IACjB,OAAO,IAAI,CAAC/E,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACgB,SAAS;EACjD;EAEA6E,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAAChF,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACiB,OAAO,IAAI,IAAI,CAAChB,eAAe;EACvE;EAEA6F,cAAcA,CAAA;IACZ,MAAM/E,KAAK,GAAG,IAAI,CAACF,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACe,KAAK,EAAE;IACpD,IAAIgF,KAAK,CAACC,OAAO,CAACjF,KAAK,CAAC,EAAE;MACxB,OAAOA,KAAK;;IAEd,OAAOA,KAAK,IAAI,EAAE;EACpB;EAEAkF,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpF,SAAS,CAAC,IAAI,CAACb,SAAS,CAAC,CAACgB,SAAS;EACjD;EAEA;;;;;EAKAyB,gBAAgBA,CAAC1B,KAAY;IAC3B,IAAI,CAACA,KAAK,IAAIA,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO,EAAE;;IAGX,MAAM9B,YAAY,GAA6B;MAC7C,OAAO,EAAE,EAAE;MACX,WAAW,EAAE,EAAE;MACf,aAAa,EAAE,EAAE;MACjB,cAAc,EAAE,EAAE;MAClB,OAAO,EAAE;KACV;IAED,MAAMsG,KAAK,GAAG,IAAIC,IAAI,EAAE;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAE1B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAACD,KAAK,CAAC;IACjCG,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,MAAMC,SAAS,GAAG,IAAIL,IAAI,CAACD,KAAK,CAAC;IACjCM,SAAS,CAACF,OAAO,CAACE,SAAS,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAE1C,MAAME,UAAU,GAAG,IAAIN,IAAI,CAACD,KAAK,CAAC;IAClCO,UAAU,CAACH,OAAO,CAACG,UAAU,CAACF,OAAO,EAAE,GAAG,EAAE,CAAC;IAE7CxF,KAAK,CAACqD,OAAO,CAAEZ,IAAI,IAAI;MACrB,MAAMkD,QAAQ,GAAG,IAAIP,IAAI,CAAC3C,IAAI,CAACmD,SAAS,CAAC;MACzCD,QAAQ,CAACN,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MAE7B,IAAIM,QAAQ,CAACE,OAAO,EAAE,KAAKV,KAAK,CAACU,OAAO,EAAE,EAAE;QAC1ChH,YAAY,CAAC,OAAO,CAAC,CAACmD,IAAI,CAACS,IAAI,CAAC;OACjC,MAAM,IAAIkD,QAAQ,CAACE,OAAO,EAAE,KAAKP,SAAS,CAACO,OAAO,EAAE,EAAE;QACrDhH,YAAY,CAAC,WAAW,CAAC,CAACmD,IAAI,CAACS,IAAI,CAAC;OACrC,MAAM,IAAIkD,QAAQ,IAAIF,SAAS,EAAE;QAChC5G,YAAY,CAAC,aAAa,CAAC,CAACmD,IAAI,CAACS,IAAI,CAAC;OACvC,MAAM,IAAIkD,QAAQ,IAAID,UAAU,EAAE;QACjC7G,YAAY,CAAC,cAAc,CAAC,CAACmD,IAAI,CAACS,IAAI,CAAC;OACxC,MAAM;QACL5D,YAAY,CAAC,OAAO,CAAC,CAACmD,IAAI,CAACS,IAAI,CAAC;;IAEpC,CAAC,CAAC;IAEF;IACAU,MAAM,CAACC,IAAI,CAACvE,YAAY,CAAC,CAACwE,OAAO,CAAExD,GAAG,IAAI;MACxC,IAAIhB,YAAY,CAACgB,GAAG,CAAC,CAACc,MAAM,KAAK,CAAC,EAAE;QAClC,OAAO9B,YAAY,CAACgB,GAAG,CAAC;;IAE5B,CAAC,CAAC;IAEF,OAAOhB,YAAY;EACrB;;;;;;;;;;;AAndWR,yBAAyB,GAAAyH,UAAA,EAfrCxI,SAAS,CAAC;EACTyI,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPzI,YAAY,EACZC,WAAW,EACXE,UAAU,EACVC,gBAAgB,EAChBC,eAAe,EACfC,cAAc,EACdG,kBAAkB,CACnB;EACDiI,QAAA,EAAAC,oBAAiD;;CAElD,CAAC,C,EACW9H,yBAAyB,CAodrC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}