{"ast": null, "code": "export default function requiredArgs(required, args) {\n  if (args.length < required) {\n    throw new TypeError(required + ' argument' + (required > 1 ? 's' : '') + ' required, but only ' + args.length + ' present');\n  }\n}", "map": {"version": 3, "names": ["requiredArgs", "required", "args", "length", "TypeError"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/_lib/requiredArgs/index.js"], "sourcesContent": ["export default function requiredArgs(required, args) {\n  if (args.length < required) {\n    throw new TypeError(required + ' argument' + (required > 1 ? 's' : '') + ' required, but only ' + args.length + ' present');\n  }\n}"], "mappings": "AAAA,eAAe,SAASA,YAAYA,CAACC,QAAQ,EAAEC,IAAI,EAAE;EACnD,IAAIA,IAAI,CAACC,MAAM,GAAGF,QAAQ,EAAE;IAC1B,MAAM,IAAIG,SAAS,CAACH,QAAQ,GAAG,WAAW,IAAIA,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,GAAG,sBAAsB,GAAGC,IAAI,CAACC,MAAM,GAAG,UAAU,CAAC;EAC7H;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}