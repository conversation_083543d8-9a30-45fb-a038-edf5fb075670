{"ast": null, "code": "import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_ARY_FLAG = 128;\n\n/**\n * Creates a function that invokes `func`, with up to `n` arguments,\n * ignoring any additional arguments.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to cap arguments for.\n * @param {number} [n=func.length] The arity cap.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new capped function.\n * @example\n *\n * _.map(['6', '8', '10'], _.ary(parseInt, 1));\n * // => [6, 8, 10]\n */\nfunction ary(func, n, guard) {\n  n = guard ? undefined : n;\n  n = func && n == null ? func.length : n;\n  return createWrap(func, WRAP_ARY_FLAG, undefined, undefined, undefined, undefined, n);\n}\nexport default ary;", "map": {"version": 3, "names": ["createWrap", "WRAP_ARY_FLAG", "ary", "func", "n", "guard", "undefined", "length"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/ary.js"], "sourcesContent": ["import createWrap from './_createWrap.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_ARY_FLAG = 128;\n\n/**\n * Creates a function that invokes `func`, with up to `n` arguments,\n * ignoring any additional arguments.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {Function} func The function to cap arguments for.\n * @param {number} [n=func.length] The arity cap.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Function} Returns the new capped function.\n * @example\n *\n * _.map(['6', '8', '10'], _.ary(parseInt, 1));\n * // => [6, 8, 10]\n */\nfunction ary(func, n, guard) {\n  n = guard ? undefined : n;\n  n = (func && n == null) ? func.length : n;\n  return createWrap(func, WRAP_ARY_FLAG, undefined, undefined, undefined, undefined, n);\n}\n\nexport default ary;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;;AAEzC;AACA,IAAIC,aAAa,GAAG,GAAG;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACC,IAAI,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAC3BD,CAAC,GAAGC,KAAK,GAAGC,SAAS,GAAGF,CAAC;EACzBA,CAAC,GAAID,IAAI,IAAIC,CAAC,IAAI,IAAI,GAAID,IAAI,CAACI,MAAM,GAAGH,CAAC;EACzC,OAAOJ,UAAU,CAACG,IAAI,EAAEF,aAAa,EAAEK,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEA,SAAS,EAAEF,CAAC,CAAC;AACvF;AAEA,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}