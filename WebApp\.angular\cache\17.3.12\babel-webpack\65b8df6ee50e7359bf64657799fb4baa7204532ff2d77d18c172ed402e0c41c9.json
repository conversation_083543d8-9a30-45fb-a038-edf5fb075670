{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport default function getISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfISOWeek(date).getTime() - startOfISOWeekYear(date).getTime();\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}", "map": {"version": 3, "names": ["toDate", "startOfISOWeek", "startOfISOWeekYear", "requiredArgs", "MILLISECONDS_IN_WEEK", "getISOWeek", "dirtyDate", "arguments", "date", "diff", "getTime", "Math", "round"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/getISOWeek/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport startOfISOWeek from \"../startOfISOWeek/index.js\";\nimport startOfISOWeekYear from \"../startOfISOWeekYear/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nvar MILLISECONDS_IN_WEEK = 604800000;\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the ISO week\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport default function getISOWeek(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var diff = startOfISOWeek(date).getTime() - startOfISOWeekYear(date).getTime();\n\n  // Round the number of days to the nearest integer\n  // because the number of milliseconds in a week is not constant\n  // (e.g. it's different in the week of the daylight saving time clock shift)\n  return Math.round(diff / MILLISECONDS_IN_WEEK) + 1;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,IAAIC,oBAAoB,GAAG,SAAS;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAE;EAC5CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGR,MAAM,CAACM,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGR,cAAc,CAACO,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGR,kBAAkB,CAACM,IAAI,CAAC,CAACE,OAAO,CAAC,CAAC;;EAE9E;EACA;EACA;EACA,OAAOC,IAAI,CAACC,KAAK,CAACH,IAAI,GAAGL,oBAAoB,CAAC,GAAG,CAAC;AACpD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}