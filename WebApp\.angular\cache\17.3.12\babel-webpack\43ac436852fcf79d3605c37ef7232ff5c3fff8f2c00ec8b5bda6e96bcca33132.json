{"ast": null, "code": "import { numberInputToObject, rgbaToHex, rgbToCmyk, rgbToHex, rgbToHsl, rgbToHsv } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input.js';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nexport class TinyColor {\n  constructor(color = '', opts = {}) {\n    // If input is already a tinycolor, return itself\n    if (color instanceof TinyColor) {\n      // eslint-disable-next-line no-constructor-return\n      return color;\n    }\n    if (typeof color === 'number') {\n      color = numberInputToObject(color);\n    }\n    this.originalInput = color;\n    const rgb = inputToRGB(color);\n    this.originalInput = color;\n    this.r = rgb.r;\n    this.g = rgb.g;\n    this.b = rgb.b;\n    this.a = rgb.a;\n    this.roundA = Math.round(100 * this.a) / 100;\n    this.format = opts.format ?? rgb.format;\n    this.gradientType = opts.gradientType;\n    // Don't let the range of [0,255] come back in [0,1].\n    // Potentially lose a little bit of precision here, but will fix issues where\n    // .5 gets interpreted as half of the total, instead of half of 1\n    // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n    if (this.r < 1) {\n      this.r = Math.round(this.r);\n    }\n    if (this.g < 1) {\n      this.g = Math.round(this.g);\n    }\n    if (this.b < 1) {\n      this.b = Math.round(this.b);\n    }\n    this.isValid = rgb.ok;\n  }\n  isDark() {\n    return this.getBrightness() < 128;\n  }\n  isLight() {\n    return !this.isDark();\n  }\n  /**\n   * Returns the perceived brightness of the color, from 0-255.\n   */\n  getBrightness() {\n    // http://www.w3.org/TR/AERT#color-contrast\n    const rgb = this.toRgb();\n    return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n  }\n  /**\n   * Returns the perceived luminance of a color, from 0-1.\n   */\n  getLuminance() {\n    // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n    const rgb = this.toRgb();\n    let R;\n    let G;\n    let B;\n    const RsRGB = rgb.r / 255;\n    const GsRGB = rgb.g / 255;\n    const BsRGB = rgb.b / 255;\n    if (RsRGB <= 0.03928) {\n      R = RsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (GsRGB <= 0.03928) {\n      G = GsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n    }\n    if (BsRGB <= 0.03928) {\n      B = BsRGB / 12.92;\n    } else {\n      // eslint-disable-next-line prefer-exponentiation-operator\n      B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n    }\n    return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n  }\n  /**\n   * Returns the alpha value of a color, from 0-1.\n   */\n  getAlpha() {\n    return this.a;\n  }\n  /**\n   * Sets the alpha value on the current color.\n   *\n   * @param alpha - The new alpha value. The accepted range is 0-1.\n   */\n  setAlpha(alpha) {\n    this.a = boundAlpha(alpha);\n    this.roundA = Math.round(100 * this.a) / 100;\n    return this;\n  }\n  /**\n   * Returns whether the color is monochrome.\n   */\n  isMonochrome() {\n    const {\n      s\n    } = this.toHsl();\n    return s === 0;\n  }\n  /**\n   * Returns the object as a HSVA object.\n   */\n  toHsv() {\n    const hsv = rgbToHsv(this.r, this.g, this.b);\n    return {\n      h: hsv.h * 360,\n      s: hsv.s,\n      v: hsv.v,\n      a: this.a\n    };\n  }\n  /**\n   * Returns the hsva values interpolated into a string with the following format:\n   * \"hsva(xxx, xxx, xxx, xx)\".\n   */\n  toHsvString() {\n    const hsv = rgbToHsv(this.r, this.g, this.b);\n    const h = Math.round(hsv.h * 360);\n    const s = Math.round(hsv.s * 100);\n    const v = Math.round(hsv.v * 100);\n    return this.a === 1 ? `hsv(${h}, ${s}%, ${v}%)` : `hsva(${h}, ${s}%, ${v}%, ${this.roundA})`;\n  }\n  /**\n   * Returns the object as a HSLA object.\n   */\n  toHsl() {\n    const hsl = rgbToHsl(this.r, this.g, this.b);\n    return {\n      h: hsl.h * 360,\n      s: hsl.s,\n      l: hsl.l,\n      a: this.a\n    };\n  }\n  /**\n   * Returns the hsla values interpolated into a string with the following format:\n   * \"hsla(xxx, xxx, xxx, xx)\".\n   */\n  toHslString() {\n    const hsl = rgbToHsl(this.r, this.g, this.b);\n    const h = Math.round(hsl.h * 360);\n    const s = Math.round(hsl.s * 100);\n    const l = Math.round(hsl.l * 100);\n    return this.a === 1 ? `hsl(${h}, ${s}%, ${l}%)` : `hsla(${h}, ${s}%, ${l}%, ${this.roundA})`;\n  }\n  /**\n   * Returns the hex value of the color.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  toHex(allow3Char = false) {\n    return rgbToHex(this.r, this.g, this.b, allow3Char);\n  }\n  /**\n   * Returns the hex value of the color -with a # prefixed.\n   * @param allow3Char will shorten hex value to 3 char if possible\n   */\n  toHexString(allow3Char = false) {\n    return '#' + this.toHex(allow3Char);\n  }\n  /**\n   * Returns the hex 8 value of the color.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  toHex8(allow4Char = false) {\n    return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n  }\n  /**\n   * Returns the hex 8 value of the color -with a # prefixed.\n   * @param allow4Char will shorten hex value to 4 char if possible\n   */\n  toHex8String(allow4Char = false) {\n    return '#' + this.toHex8(allow4Char);\n  }\n  /**\n   * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n   * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n   */\n  toHexShortString(allowShortChar = false) {\n    return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n  }\n  /**\n   * Returns the object as a RGBA object.\n   */\n  toRgb() {\n    return {\n      r: Math.round(this.r),\n      g: Math.round(this.g),\n      b: Math.round(this.b),\n      a: this.a\n    };\n  }\n  /**\n   * Returns the RGBA values interpolated into a string with the following format:\n   * \"RGBA(xxx, xxx, xxx, xx)\".\n   */\n  toRgbString() {\n    const r = Math.round(this.r);\n    const g = Math.round(this.g);\n    const b = Math.round(this.b);\n    return this.a === 1 ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${this.roundA})`;\n  }\n  /**\n   * Returns the object as a RGBA object.\n   */\n  toPercentageRgb() {\n    const fmt = x => `${Math.round(bound01(x, 255) * 100)}%`;\n    return {\n      r: fmt(this.r),\n      g: fmt(this.g),\n      b: fmt(this.b),\n      a: this.a\n    };\n  }\n  /**\n   * Returns the RGBA relative values interpolated into a string\n   */\n  toPercentageRgbString() {\n    const rnd = x => Math.round(bound01(x, 255) * 100);\n    return this.a === 1 ? `rgb(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%)` : `rgba(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%, ${this.roundA})`;\n  }\n  toCmyk() {\n    return {\n      ...rgbToCmyk(this.r, this.g, this.b)\n    };\n  }\n  toCmykString() {\n    const {\n      c,\n      m,\n      y,\n      k\n    } = rgbToCmyk(this.r, this.g, this.b);\n    return `cmyk(${c}, ${m}, ${y}, ${k})`;\n  }\n  /**\n   * The 'real' name of the color -if there is one.\n   */\n  toName() {\n    if (this.a === 0) {\n      return 'transparent';\n    }\n    if (this.a < 1) {\n      return false;\n    }\n    const hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n    for (const [key, value] of Object.entries(names)) {\n      if (hex === value) {\n        return key;\n      }\n    }\n    return false;\n  }\n  toString(format) {\n    const formatSet = Boolean(format);\n    format = format ?? this.format;\n    let formattedString = false;\n    const hasAlpha = this.a < 1 && this.a >= 0;\n    const needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n    if (needsAlphaFormat) {\n      // Special case for \"transparent\", all other non-alpha formats\n      // will return rgba when there is transparency.\n      if (format === 'name' && this.a === 0) {\n        return this.toName();\n      }\n      return this.toRgbString();\n    }\n    if (format === 'rgb') {\n      formattedString = this.toRgbString();\n    }\n    if (format === 'prgb') {\n      formattedString = this.toPercentageRgbString();\n    }\n    if (format === 'hex' || format === 'hex6') {\n      formattedString = this.toHexString();\n    }\n    if (format === 'hex3') {\n      formattedString = this.toHexString(true);\n    }\n    if (format === 'hex4') {\n      formattedString = this.toHex8String(true);\n    }\n    if (format === 'hex8') {\n      formattedString = this.toHex8String();\n    }\n    if (format === 'name') {\n      formattedString = this.toName();\n    }\n    if (format === 'hsl') {\n      formattedString = this.toHslString();\n    }\n    if (format === 'hsv') {\n      formattedString = this.toHsvString();\n    }\n    if (format === 'cmyk') {\n      formattedString = this.toCmykString();\n    }\n    return formattedString || this.toHexString();\n  }\n  toNumber() {\n    return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n  }\n  clone() {\n    return new TinyColor(this.toString());\n  }\n  /**\n   * Lighten the color a given amount. Providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  lighten(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.l += amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Brighten the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  brighten(amount = 10) {\n    const rgb = this.toRgb();\n    rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n    rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n    rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n    return new TinyColor(rgb);\n  }\n  /**\n   * Darken the color a given amount, from 0 to 100.\n   * Providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  darken(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.l -= amount / 100;\n    hsl.l = clamp01(hsl.l);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Mix the color with pure white, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return white.\n   * @param amount - valid between 1-100\n   */\n  tint(amount = 10) {\n    return this.mix('white', amount);\n  }\n  /**\n   * Mix the color with pure black, from 0 to 100.\n   * Providing 0 will do nothing, providing 100 will always return black.\n   * @param amount - valid between 1-100\n   */\n  shade(amount = 10) {\n    return this.mix('black', amount);\n  }\n  /**\n   * Desaturate the color a given amount, from 0 to 100.\n   * Providing 100 will is the same as calling greyscale\n   * @param amount - valid between 1-100\n   */\n  desaturate(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.s -= amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Saturate the color a given amount, from 0 to 100.\n   * @param amount - valid between 1-100\n   */\n  saturate(amount = 10) {\n    const hsl = this.toHsl();\n    hsl.s += amount / 100;\n    hsl.s = clamp01(hsl.s);\n    return new TinyColor(hsl);\n  }\n  /**\n   * Completely desaturates a color into greyscale.\n   * Same as calling `desaturate(100)`\n   */\n  greyscale() {\n    return this.desaturate(100);\n  }\n  /**\n   * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n   * Values outside of this range will be wrapped into this range.\n   */\n  spin(amount) {\n    const hsl = this.toHsl();\n    const hue = (hsl.h + amount) % 360;\n    hsl.h = hue < 0 ? 360 + hue : hue;\n    return new TinyColor(hsl);\n  }\n  /**\n   * Mix the current color a given amount with another color, from 0 to 100.\n   * 0 means no mixing (return current color).\n   */\n  mix(color, amount = 50) {\n    const rgb1 = this.toRgb();\n    const rgb2 = new TinyColor(color).toRgb();\n    const p = amount / 100;\n    const rgba = {\n      r: (rgb2.r - rgb1.r) * p + rgb1.r,\n      g: (rgb2.g - rgb1.g) * p + rgb1.g,\n      b: (rgb2.b - rgb1.b) * p + rgb1.b,\n      a: (rgb2.a - rgb1.a) * p + rgb1.a\n    };\n    return new TinyColor(rgba);\n  }\n  analogous(results = 6, slices = 30) {\n    const hsl = this.toHsl();\n    const part = 360 / slices;\n    const ret = [this];\n    for (hsl.h = (hsl.h - (part * results >> 1) + 720) % 360; --results;) {\n      hsl.h = (hsl.h + part) % 360;\n      ret.push(new TinyColor(hsl));\n    }\n    return ret;\n  }\n  /**\n   * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n   */\n  complement() {\n    const hsl = this.toHsl();\n    hsl.h = (hsl.h + 180) % 360;\n    return new TinyColor(hsl);\n  }\n  monochromatic(results = 6) {\n    const hsv = this.toHsv();\n    const {\n      h\n    } = hsv;\n    const {\n      s\n    } = hsv;\n    let {\n      v\n    } = hsv;\n    const res = [];\n    const modification = 1 / results;\n    while (results--) {\n      res.push(new TinyColor({\n        h,\n        s,\n        v\n      }));\n      v = (v + modification) % 1;\n    }\n    return res;\n  }\n  splitcomplement() {\n    const hsl = this.toHsl();\n    const {\n      h\n    } = hsl;\n    return [this, new TinyColor({\n      h: (h + 72) % 360,\n      s: hsl.s,\n      l: hsl.l\n    }), new TinyColor({\n      h: (h + 216) % 360,\n      s: hsl.s,\n      l: hsl.l\n    })];\n  }\n  /**\n   * Compute how the color would appear on a background\n   */\n  onBackground(background) {\n    const fg = this.toRgb();\n    const bg = new TinyColor(background).toRgb();\n    const alpha = fg.a + bg.a * (1 - fg.a);\n    return new TinyColor({\n      r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n      g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n      b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n      a: alpha\n    });\n  }\n  /**\n   * Alias for `polyad(3)`\n   */\n  triad() {\n    return this.polyad(3);\n  }\n  /**\n   * Alias for `polyad(4)`\n   */\n  tetrad() {\n    return this.polyad(4);\n  }\n  /**\n   * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n   * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n   */\n  polyad(n) {\n    const hsl = this.toHsl();\n    const {\n      h\n    } = hsl;\n    const result = [this];\n    const increment = 360 / n;\n    for (let i = 1; i < n; i++) {\n      result.push(new TinyColor({\n        h: (h + i * increment) % 360,\n        s: hsl.s,\n        l: hsl.l\n      }));\n    }\n    return result;\n  }\n  /**\n   * compare color vs current color\n   */\n  equals(color) {\n    const comparedColor = new TinyColor(color);\n    /**\n     * RGB and CMYK do not have the same color gamut, so a CMYK conversion will never be 100%.\n     * This means we need to compare CMYK to CMYK to ensure accuracy of the equals function.\n     */\n    if (this.format === 'cmyk' || comparedColor.format === 'cmyk') {\n      return this.toCmykString() === comparedColor.toCmykString();\n    }\n    return this.toRgbString() === comparedColor.toRgbString();\n  }\n}", "map": {"version": 3, "names": ["numberInputToObject", "rgbaToHex", "rgbToCmyk", "rgbToHex", "rgbToHsl", "rgbToHsv", "names", "inputToRGB", "bound01", "boundAlpha", "clamp01", "TinyColor", "constructor", "color", "opts", "originalInput", "rgb", "r", "g", "b", "a", "roundA", "Math", "round", "format", "gradientType", "<PERSON><PERSON><PERSON><PERSON>", "ok", "isDark", "getBrightness", "isLight", "toRgb", "getLuminance", "R", "G", "B", "RsRGB", "GsRGB", "BsRGB", "pow", "get<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alpha", "isMonochrome", "s", "toHsl", "toHsv", "hsv", "h", "v", "toHsvString", "hsl", "l", "toHslString", "toHex", "allow3Char", "toHexString", "toHex8", "allow4Char", "toHex8String", "toHexShortString", "allowShortChar", "toRgbString", "toPercentageRgb", "fmt", "x", "toPercentageRgbString", "rnd", "toCmyk", "toCmykString", "c", "m", "y", "k", "to<PERSON>ame", "hex", "key", "value", "Object", "entries", "toString", "formatSet", "Boolean", "formattedString", "has<PERSON><PERSON><PERSON>", "needsAlphaFormat", "startsWith", "toNumber", "clone", "lighten", "amount", "brighten", "max", "min", "darken", "tint", "mix", "shade", "desaturate", "saturate", "greyscale", "spin", "hue", "rgb1", "rgb2", "p", "rgba", "analogous", "results", "slices", "part", "ret", "push", "complement", "monochromatic", "res", "modification", "splitcomplement", "onBackground", "background", "fg", "bg", "triad", "polyad", "tetrad", "n", "result", "increment", "i", "equals", "comparedColor"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@ctrl/tinycolor/dist/module/index.js"], "sourcesContent": ["import { numberInputToObject, rgbaToHex, rgbToCmyk, rgbToHex, rgbToHsl, rgbToHsv, } from './conversion.js';\nimport { names } from './css-color-names.js';\nimport { inputToRGB } from './format-input.js';\nimport { bound01, boundAlpha, clamp01 } from './util.js';\nexport class TinyColor {\n    constructor(color = '', opts = {}) {\n        // If input is already a tinycolor, return itself\n        if (color instanceof TinyColor) {\n            // eslint-disable-next-line no-constructor-return\n            return color;\n        }\n        if (typeof color === 'number') {\n            color = numberInputToObject(color);\n        }\n        this.originalInput = color;\n        const rgb = inputToRGB(color);\n        this.originalInput = color;\n        this.r = rgb.r;\n        this.g = rgb.g;\n        this.b = rgb.b;\n        this.a = rgb.a;\n        this.roundA = Math.round(100 * this.a) / 100;\n        this.format = opts.format ?? rgb.format;\n        this.gradientType = opts.gradientType;\n        // Don't let the range of [0,255] come back in [0,1].\n        // Potentially lose a little bit of precision here, but will fix issues where\n        // .5 gets interpreted as half of the total, instead of half of 1\n        // If it was supposed to be 128, this was already taken care of by `inputToRgb`\n        if (this.r < 1) {\n            this.r = Math.round(this.r);\n        }\n        if (this.g < 1) {\n            this.g = Math.round(this.g);\n        }\n        if (this.b < 1) {\n            this.b = Math.round(this.b);\n        }\n        this.isValid = rgb.ok;\n    }\n    isDark() {\n        return this.getBrightness() < 128;\n    }\n    isLight() {\n        return !this.isDark();\n    }\n    /**\n     * Returns the perceived brightness of the color, from 0-255.\n     */\n    getBrightness() {\n        // http://www.w3.org/TR/AERT#color-contrast\n        const rgb = this.toRgb();\n        return (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;\n    }\n    /**\n     * Returns the perceived luminance of a color, from 0-1.\n     */\n    getLuminance() {\n        // http://www.w3.org/TR/2008/REC-WCAG20-20081211/#relativeluminancedef\n        const rgb = this.toRgb();\n        let R;\n        let G;\n        let B;\n        const RsRGB = rgb.r / 255;\n        const GsRGB = rgb.g / 255;\n        const BsRGB = rgb.b / 255;\n        if (RsRGB <= 0.03928) {\n            R = RsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            R = Math.pow((RsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (GsRGB <= 0.03928) {\n            G = GsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            G = Math.pow((GsRGB + 0.055) / 1.055, 2.4);\n        }\n        if (BsRGB <= 0.03928) {\n            B = BsRGB / 12.92;\n        }\n        else {\n            // eslint-disable-next-line prefer-exponentiation-operator\n            B = Math.pow((BsRGB + 0.055) / 1.055, 2.4);\n        }\n        return 0.2126 * R + 0.7152 * G + 0.0722 * B;\n    }\n    /**\n     * Returns the alpha value of a color, from 0-1.\n     */\n    getAlpha() {\n        return this.a;\n    }\n    /**\n     * Sets the alpha value on the current color.\n     *\n     * @param alpha - The new alpha value. The accepted range is 0-1.\n     */\n    setAlpha(alpha) {\n        this.a = boundAlpha(alpha);\n        this.roundA = Math.round(100 * this.a) / 100;\n        return this;\n    }\n    /**\n     * Returns whether the color is monochrome.\n     */\n    isMonochrome() {\n        const { s } = this.toHsl();\n        return s === 0;\n    }\n    /**\n     * Returns the object as a HSVA object.\n     */\n    toHsv() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        return { h: hsv.h * 360, s: hsv.s, v: hsv.v, a: this.a };\n    }\n    /**\n     * Returns the hsva values interpolated into a string with the following format:\n     * \"hsva(xxx, xxx, xxx, xx)\".\n     */\n    toHsvString() {\n        const hsv = rgbToHsv(this.r, this.g, this.b);\n        const h = Math.round(hsv.h * 360);\n        const s = Math.round(hsv.s * 100);\n        const v = Math.round(hsv.v * 100);\n        return this.a === 1 ? `hsv(${h}, ${s}%, ${v}%)` : `hsva(${h}, ${s}%, ${v}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a HSLA object.\n     */\n    toHsl() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        return { h: hsl.h * 360, s: hsl.s, l: hsl.l, a: this.a };\n    }\n    /**\n     * Returns the hsla values interpolated into a string with the following format:\n     * \"hsla(xxx, xxx, xxx, xx)\".\n     */\n    toHslString() {\n        const hsl = rgbToHsl(this.r, this.g, this.b);\n        const h = Math.round(hsl.h * 360);\n        const s = Math.round(hsl.s * 100);\n        const l = Math.round(hsl.l * 100);\n        return this.a === 1 ? `hsl(${h}, ${s}%, ${l}%)` : `hsla(${h}, ${s}%, ${l}%, ${this.roundA})`;\n    }\n    /**\n     * Returns the hex value of the color.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHex(allow3Char = false) {\n        return rgbToHex(this.r, this.g, this.b, allow3Char);\n    }\n    /**\n     * Returns the hex value of the color -with a # prefixed.\n     * @param allow3Char will shorten hex value to 3 char if possible\n     */\n    toHexString(allow3Char = false) {\n        return '#' + this.toHex(allow3Char);\n    }\n    /**\n     * Returns the hex 8 value of the color.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8(allow4Char = false) {\n        return rgbaToHex(this.r, this.g, this.b, this.a, allow4Char);\n    }\n    /**\n     * Returns the hex 8 value of the color -with a # prefixed.\n     * @param allow4Char will shorten hex value to 4 char if possible\n     */\n    toHex8String(allow4Char = false) {\n        return '#' + this.toHex8(allow4Char);\n    }\n    /**\n     * Returns the shorter hex value of the color depends on its alpha -with a # prefixed.\n     * @param allowShortChar will shorten hex value to 3 or 4 char if possible\n     */\n    toHexShortString(allowShortChar = false) {\n        return this.a === 1 ? this.toHexString(allowShortChar) : this.toHex8String(allowShortChar);\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toRgb() {\n        return {\n            r: Math.round(this.r),\n            g: Math.round(this.g),\n            b: Math.round(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA values interpolated into a string with the following format:\n     * \"RGBA(xxx, xxx, xxx, xx)\".\n     */\n    toRgbString() {\n        const r = Math.round(this.r);\n        const g = Math.round(this.g);\n        const b = Math.round(this.b);\n        return this.a === 1 ? `rgb(${r}, ${g}, ${b})` : `rgba(${r}, ${g}, ${b}, ${this.roundA})`;\n    }\n    /**\n     * Returns the object as a RGBA object.\n     */\n    toPercentageRgb() {\n        const fmt = (x) => `${Math.round(bound01(x, 255) * 100)}%`;\n        return {\n            r: fmt(this.r),\n            g: fmt(this.g),\n            b: fmt(this.b),\n            a: this.a,\n        };\n    }\n    /**\n     * Returns the RGBA relative values interpolated into a string\n     */\n    toPercentageRgbString() {\n        const rnd = (x) => Math.round(bound01(x, 255) * 100);\n        return this.a === 1\n            ? `rgb(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%)`\n            : `rgba(${rnd(this.r)}%, ${rnd(this.g)}%, ${rnd(this.b)}%, ${this.roundA})`;\n    }\n    toCmyk() {\n        return {\n            ...rgbToCmyk(this.r, this.g, this.b),\n        };\n    }\n    toCmykString() {\n        const { c, m, y, k } = rgbToCmyk(this.r, this.g, this.b);\n        return `cmyk(${c}, ${m}, ${y}, ${k})`;\n    }\n    /**\n     * The 'real' name of the color -if there is one.\n     */\n    toName() {\n        if (this.a === 0) {\n            return 'transparent';\n        }\n        if (this.a < 1) {\n            return false;\n        }\n        const hex = '#' + rgbToHex(this.r, this.g, this.b, false);\n        for (const [key, value] of Object.entries(names)) {\n            if (hex === value) {\n                return key;\n            }\n        }\n        return false;\n    }\n    toString(format) {\n        const formatSet = Boolean(format);\n        format = format ?? this.format;\n        let formattedString = false;\n        const hasAlpha = this.a < 1 && this.a >= 0;\n        const needsAlphaFormat = !formatSet && hasAlpha && (format.startsWith('hex') || format === 'name');\n        if (needsAlphaFormat) {\n            // Special case for \"transparent\", all other non-alpha formats\n            // will return rgba when there is transparency.\n            if (format === 'name' && this.a === 0) {\n                return this.toName();\n            }\n            return this.toRgbString();\n        }\n        if (format === 'rgb') {\n            formattedString = this.toRgbString();\n        }\n        if (format === 'prgb') {\n            formattedString = this.toPercentageRgbString();\n        }\n        if (format === 'hex' || format === 'hex6') {\n            formattedString = this.toHexString();\n        }\n        if (format === 'hex3') {\n            formattedString = this.toHexString(true);\n        }\n        if (format === 'hex4') {\n            formattedString = this.toHex8String(true);\n        }\n        if (format === 'hex8') {\n            formattedString = this.toHex8String();\n        }\n        if (format === 'name') {\n            formattedString = this.toName();\n        }\n        if (format === 'hsl') {\n            formattedString = this.toHslString();\n        }\n        if (format === 'hsv') {\n            formattedString = this.toHsvString();\n        }\n        if (format === 'cmyk') {\n            formattedString = this.toCmykString();\n        }\n        return formattedString || this.toHexString();\n    }\n    toNumber() {\n        return (Math.round(this.r) << 16) + (Math.round(this.g) << 8) + Math.round(this.b);\n    }\n    clone() {\n        return new TinyColor(this.toString());\n    }\n    /**\n     * Lighten the color a given amount. Providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    lighten(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l += amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Brighten the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    brighten(amount = 10) {\n        const rgb = this.toRgb();\n        rgb.r = Math.max(0, Math.min(255, rgb.r - Math.round(255 * -(amount / 100))));\n        rgb.g = Math.max(0, Math.min(255, rgb.g - Math.round(255 * -(amount / 100))));\n        rgb.b = Math.max(0, Math.min(255, rgb.b - Math.round(255 * -(amount / 100))));\n        return new TinyColor(rgb);\n    }\n    /**\n     * Darken the color a given amount, from 0 to 100.\n     * Providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    darken(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.l -= amount / 100;\n        hsl.l = clamp01(hsl.l);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the color with pure white, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return white.\n     * @param amount - valid between 1-100\n     */\n    tint(amount = 10) {\n        return this.mix('white', amount);\n    }\n    /**\n     * Mix the color with pure black, from 0 to 100.\n     * Providing 0 will do nothing, providing 100 will always return black.\n     * @param amount - valid between 1-100\n     */\n    shade(amount = 10) {\n        return this.mix('black', amount);\n    }\n    /**\n     * Desaturate the color a given amount, from 0 to 100.\n     * Providing 100 will is the same as calling greyscale\n     * @param amount - valid between 1-100\n     */\n    desaturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s -= amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Saturate the color a given amount, from 0 to 100.\n     * @param amount - valid between 1-100\n     */\n    saturate(amount = 10) {\n        const hsl = this.toHsl();\n        hsl.s += amount / 100;\n        hsl.s = clamp01(hsl.s);\n        return new TinyColor(hsl);\n    }\n    /**\n     * Completely desaturates a color into greyscale.\n     * Same as calling `desaturate(100)`\n     */\n    greyscale() {\n        return this.desaturate(100);\n    }\n    /**\n     * Spin takes a positive or negative amount within [-360, 360] indicating the change of hue.\n     * Values outside of this range will be wrapped into this range.\n     */\n    spin(amount) {\n        const hsl = this.toHsl();\n        const hue = (hsl.h + amount) % 360;\n        hsl.h = hue < 0 ? 360 + hue : hue;\n        return new TinyColor(hsl);\n    }\n    /**\n     * Mix the current color a given amount with another color, from 0 to 100.\n     * 0 means no mixing (return current color).\n     */\n    mix(color, amount = 50) {\n        const rgb1 = this.toRgb();\n        const rgb2 = new TinyColor(color).toRgb();\n        const p = amount / 100;\n        const rgba = {\n            r: (rgb2.r - rgb1.r) * p + rgb1.r,\n            g: (rgb2.g - rgb1.g) * p + rgb1.g,\n            b: (rgb2.b - rgb1.b) * p + rgb1.b,\n            a: (rgb2.a - rgb1.a) * p + rgb1.a,\n        };\n        return new TinyColor(rgba);\n    }\n    analogous(results = 6, slices = 30) {\n        const hsl = this.toHsl();\n        const part = 360 / slices;\n        const ret = [this];\n        for (hsl.h = (hsl.h - ((part * results) >> 1) + 720) % 360; --results;) {\n            hsl.h = (hsl.h + part) % 360;\n            ret.push(new TinyColor(hsl));\n        }\n        return ret;\n    }\n    /**\n     * taken from https://github.com/infusion/jQuery-xcolor/blob/master/jquery.xcolor.js\n     */\n    complement() {\n        const hsl = this.toHsl();\n        hsl.h = (hsl.h + 180) % 360;\n        return new TinyColor(hsl);\n    }\n    monochromatic(results = 6) {\n        const hsv = this.toHsv();\n        const { h } = hsv;\n        const { s } = hsv;\n        let { v } = hsv;\n        const res = [];\n        const modification = 1 / results;\n        while (results--) {\n            res.push(new TinyColor({ h, s, v }));\n            v = (v + modification) % 1;\n        }\n        return res;\n    }\n    splitcomplement() {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        return [\n            this,\n            new TinyColor({ h: (h + 72) % 360, s: hsl.s, l: hsl.l }),\n            new TinyColor({ h: (h + 216) % 360, s: hsl.s, l: hsl.l }),\n        ];\n    }\n    /**\n     * Compute how the color would appear on a background\n     */\n    onBackground(background) {\n        const fg = this.toRgb();\n        const bg = new TinyColor(background).toRgb();\n        const alpha = fg.a + bg.a * (1 - fg.a);\n        return new TinyColor({\n            r: (fg.r * fg.a + bg.r * bg.a * (1 - fg.a)) / alpha,\n            g: (fg.g * fg.a + bg.g * bg.a * (1 - fg.a)) / alpha,\n            b: (fg.b * fg.a + bg.b * bg.a * (1 - fg.a)) / alpha,\n            a: alpha,\n        });\n    }\n    /**\n     * Alias for `polyad(3)`\n     */\n    triad() {\n        return this.polyad(3);\n    }\n    /**\n     * Alias for `polyad(4)`\n     */\n    tetrad() {\n        return this.polyad(4);\n    }\n    /**\n     * Get polyad colors, like (for 1, 2, 3, 4, 5, 6, 7, 8, etc...)\n     * monad, dyad, triad, tetrad, pentad, hexad, heptad, octad, etc...\n     */\n    polyad(n) {\n        const hsl = this.toHsl();\n        const { h } = hsl;\n        const result = [this];\n        const increment = 360 / n;\n        for (let i = 1; i < n; i++) {\n            result.push(new TinyColor({ h: (h + i * increment) % 360, s: hsl.s, l: hsl.l }));\n        }\n        return result;\n    }\n    /**\n     * compare color vs current color\n     */\n    equals(color) {\n        const comparedColor = new TinyColor(color);\n        /**\n         * RGB and CMYK do not have the same color gamut, so a CMYK conversion will never be 100%.\n         * This means we need to compare CMYK to CMYK to ensure accuracy of the equals function.\n         */\n        if (this.format === 'cmyk' || comparedColor.format === 'cmyk') {\n            return this.toCmykString() === comparedColor.toCmykString();\n        }\n        return this.toRgbString() === comparedColor.toRgbString();\n    }\n}\n"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAS,iBAAiB;AAC1G,SAASC,KAAK,QAAQ,sBAAsB;AAC5C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,OAAO,EAAEC,UAAU,EAAEC,OAAO,QAAQ,WAAW;AACxD,OAAO,MAAMC,SAAS,CAAC;EACnBC,WAAWA,CAACC,KAAK,GAAG,EAAE,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IAC/B;IACA,IAAID,KAAK,YAAYF,SAAS,EAAE;MAC5B;MACA,OAAOE,KAAK;IAChB;IACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC3BA,KAAK,GAAGb,mBAAmB,CAACa,KAAK,CAAC;IACtC;IACA,IAAI,CAACE,aAAa,GAAGF,KAAK;IAC1B,MAAMG,GAAG,GAAGT,UAAU,CAACM,KAAK,CAAC;IAC7B,IAAI,CAACE,aAAa,GAAGF,KAAK;IAC1B,IAAI,CAACI,CAAC,GAAGD,GAAG,CAACC,CAAC;IACd,IAAI,CAACC,CAAC,GAAGF,GAAG,CAACE,CAAC;IACd,IAAI,CAACC,CAAC,GAAGH,GAAG,CAACG,CAAC;IACd,IAAI,CAACC,CAAC,GAAGJ,GAAG,CAACI,CAAC;IACd,IAAI,CAACC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACH,CAAC,CAAC,GAAG,GAAG;IAC5C,IAAI,CAACI,MAAM,GAAGV,IAAI,CAACU,MAAM,IAAIR,GAAG,CAACQ,MAAM;IACvC,IAAI,CAACC,YAAY,GAAGX,IAAI,CAACW,YAAY;IACrC;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACR,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;IAC/B;IACA,IAAI,IAAI,CAACC,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;IAC/B;IACA,IAAI,IAAI,CAACC,CAAC,GAAG,CAAC,EAAE;MACZ,IAAI,CAACA,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;IAC/B;IACA,IAAI,CAACO,OAAO,GAAGV,GAAG,CAACW,EAAE;EACzB;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACC,aAAa,CAAC,CAAC,GAAG,GAAG;EACrC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,CAAC,IAAI,CAACF,MAAM,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACIC,aAAaA,CAAA,EAAG;IACZ;IACA,MAAMb,GAAG,GAAG,IAAI,CAACe,KAAK,CAAC,CAAC;IACxB,OAAO,CAACf,GAAG,CAACC,CAAC,GAAG,GAAG,GAAGD,GAAG,CAACE,CAAC,GAAG,GAAG,GAAGF,GAAG,CAACG,CAAC,GAAG,GAAG,IAAI,IAAI;EAC3D;EACA;AACJ;AACA;EACIa,YAAYA,CAAA,EAAG;IACX;IACA,MAAMhB,GAAG,GAAG,IAAI,CAACe,KAAK,CAAC,CAAC;IACxB,IAAIE,CAAC;IACL,IAAIC,CAAC;IACL,IAAIC,CAAC;IACL,MAAMC,KAAK,GAAGpB,GAAG,CAACC,CAAC,GAAG,GAAG;IACzB,MAAMoB,KAAK,GAAGrB,GAAG,CAACE,CAAC,GAAG,GAAG;IACzB,MAAMoB,KAAK,GAAGtB,GAAG,CAACG,CAAC,GAAG,GAAG;IACzB,IAAIiB,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGX,IAAI,CAACiB,GAAG,CAAC,CAACH,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,IAAIC,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGZ,IAAI,CAACiB,GAAG,CAAC,CAACF,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,IAAIC,KAAK,IAAI,OAAO,EAAE;MAClBH,CAAC,GAAGG,KAAK,GAAG,KAAK;IACrB,CAAC,MACI;MACD;MACAH,CAAC,GAAGb,IAAI,CAACiB,GAAG,CAAC,CAACD,KAAK,GAAG,KAAK,IAAI,KAAK,EAAE,GAAG,CAAC;IAC9C;IACA,OAAO,MAAM,GAAGL,CAAC,GAAG,MAAM,GAAGC,CAAC,GAAG,MAAM,GAAGC,CAAC;EAC/C;EACA;AACJ;AACA;EACIK,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpB,CAAC;EACjB;EACA;AACJ;AACA;AACA;AACA;EACIqB,QAAQA,CAACC,KAAK,EAAE;IACZ,IAAI,CAACtB,CAAC,GAAGX,UAAU,CAACiC,KAAK,CAAC;IAC1B,IAAI,CAACrB,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,IAAI,CAACH,CAAC,CAAC,GAAG,GAAG;IAC5C,OAAO,IAAI;EACf;EACA;AACJ;AACA;EACIuB,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IAC1B,OAAOD,CAAC,KAAK,CAAC;EAClB;EACA;AACJ;AACA;EACIE,KAAKA,CAAA,EAAG;IACJ,MAAMC,GAAG,GAAG1C,QAAQ,CAAC,IAAI,CAACY,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,OAAO;MAAE6B,CAAC,EAAED,GAAG,CAACC,CAAC,GAAG,GAAG;MAAEJ,CAAC,EAAEG,GAAG,CAACH,CAAC;MAAEK,CAAC,EAAEF,GAAG,CAACE,CAAC;MAAE7B,CAAC,EAAE,IAAI,CAACA;IAAE,CAAC;EAC5D;EACA;AACJ;AACA;AACA;EACI8B,WAAWA,CAAA,EAAG;IACV,MAAMH,GAAG,GAAG1C,QAAQ,CAAC,IAAI,CAACY,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,MAAM6B,CAAC,GAAG1B,IAAI,CAACC,KAAK,CAACwB,GAAG,CAACC,CAAC,GAAG,GAAG,CAAC;IACjC,MAAMJ,CAAC,GAAGtB,IAAI,CAACC,KAAK,CAACwB,GAAG,CAACH,CAAC,GAAG,GAAG,CAAC;IACjC,MAAMK,CAAC,GAAG3B,IAAI,CAACC,KAAK,CAACwB,GAAG,CAACE,CAAC,GAAG,GAAG,CAAC;IACjC,OAAO,IAAI,CAAC7B,CAAC,KAAK,CAAC,GAAI,OAAM4B,CAAE,KAAIJ,CAAE,MAAKK,CAAE,IAAG,GAAI,QAAOD,CAAE,KAAIJ,CAAE,MAAKK,CAAE,MAAK,IAAI,CAAC5B,MAAO,GAAE;EAChG;EACA;AACJ;AACA;EACIwB,KAAKA,CAAA,EAAG;IACJ,MAAMM,GAAG,GAAG/C,QAAQ,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,OAAO;MAAE6B,CAAC,EAAEG,GAAG,CAACH,CAAC,GAAG,GAAG;MAAEJ,CAAC,EAAEO,GAAG,CAACP,CAAC;MAAEQ,CAAC,EAAED,GAAG,CAACC,CAAC;MAAEhC,CAAC,EAAE,IAAI,CAACA;IAAE,CAAC;EAC5D;EACA;AACJ;AACA;AACA;EACIiC,WAAWA,CAAA,EAAG;IACV,MAAMF,GAAG,GAAG/C,QAAQ,CAAC,IAAI,CAACa,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,MAAM6B,CAAC,GAAG1B,IAAI,CAACC,KAAK,CAAC4B,GAAG,CAACH,CAAC,GAAG,GAAG,CAAC;IACjC,MAAMJ,CAAC,GAAGtB,IAAI,CAACC,KAAK,CAAC4B,GAAG,CAACP,CAAC,GAAG,GAAG,CAAC;IACjC,MAAMQ,CAAC,GAAG9B,IAAI,CAACC,KAAK,CAAC4B,GAAG,CAACC,CAAC,GAAG,GAAG,CAAC;IACjC,OAAO,IAAI,CAAChC,CAAC,KAAK,CAAC,GAAI,OAAM4B,CAAE,KAAIJ,CAAE,MAAKQ,CAAE,IAAG,GAAI,QAAOJ,CAAE,KAAIJ,CAAE,MAAKQ,CAAE,MAAK,IAAI,CAAC/B,MAAO,GAAE;EAChG;EACA;AACJ;AACA;AACA;EACIiC,KAAKA,CAACC,UAAU,GAAG,KAAK,EAAE;IACtB,OAAOpD,QAAQ,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEoC,UAAU,CAAC;EACvD;EACA;AACJ;AACA;AACA;EACIC,WAAWA,CAACD,UAAU,GAAG,KAAK,EAAE;IAC5B,OAAO,GAAG,GAAG,IAAI,CAACD,KAAK,CAACC,UAAU,CAAC;EACvC;EACA;AACJ;AACA;AACA;EACIE,MAAMA,CAACC,UAAU,GAAG,KAAK,EAAE;IACvB,OAAOzD,SAAS,CAAC,IAAI,CAACgB,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAEsC,UAAU,CAAC;EAChE;EACA;AACJ;AACA;AACA;EACIC,YAAYA,CAACD,UAAU,GAAG,KAAK,EAAE;IAC7B,OAAO,GAAG,GAAG,IAAI,CAACD,MAAM,CAACC,UAAU,CAAC;EACxC;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAACC,cAAc,GAAG,KAAK,EAAE;IACrC,OAAO,IAAI,CAACzC,CAAC,KAAK,CAAC,GAAG,IAAI,CAACoC,WAAW,CAACK,cAAc,CAAC,GAAG,IAAI,CAACF,YAAY,CAACE,cAAc,CAAC;EAC9F;EACA;AACJ;AACA;EACI9B,KAAKA,CAAA,EAAG;IACJ,OAAO;MACHd,CAAC,EAAEK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;MACrBC,CAAC,EAAEI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;MACrBC,CAAC,EAAEG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;MACrBC,CAAC,EAAE,IAAI,CAACA;IACZ,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI0C,WAAWA,CAAA,EAAG;IACV,MAAM7C,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC;IAC5B,MAAMC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC;IAC5B,MAAMC,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;IAC5B,OAAO,IAAI,CAACC,CAAC,KAAK,CAAC,GAAI,OAAMH,CAAE,KAAIC,CAAE,KAAIC,CAAE,GAAE,GAAI,QAAOF,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAI,IAAI,CAACE,MAAO,GAAE;EAC5F;EACA;AACJ;AACA;EACI0C,eAAeA,CAAA,EAAG;IACd,MAAMC,GAAG,GAAIC,CAAC,IAAM,GAAE3C,IAAI,CAACC,KAAK,CAACf,OAAO,CAACyD,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAE,GAAE;IAC1D,OAAO;MACHhD,CAAC,EAAE+C,GAAG,CAAC,IAAI,CAAC/C,CAAC,CAAC;MACdC,CAAC,EAAE8C,GAAG,CAAC,IAAI,CAAC9C,CAAC,CAAC;MACdC,CAAC,EAAE6C,GAAG,CAAC,IAAI,CAAC7C,CAAC,CAAC;MACdC,CAAC,EAAE,IAAI,CAACA;IACZ,CAAC;EACL;EACA;AACJ;AACA;EACI8C,qBAAqBA,CAAA,EAAG;IACpB,MAAMC,GAAG,GAAIF,CAAC,IAAK3C,IAAI,CAACC,KAAK,CAACf,OAAO,CAACyD,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC;IACpD,OAAO,IAAI,CAAC7C,CAAC,KAAK,CAAC,GACZ,OAAM+C,GAAG,CAAC,IAAI,CAAClD,CAAC,CAAE,MAAKkD,GAAG,CAAC,IAAI,CAACjD,CAAC,CAAE,MAAKiD,GAAG,CAAC,IAAI,CAAChD,CAAC,CAAE,IAAG,GACvD,QAAOgD,GAAG,CAAC,IAAI,CAAClD,CAAC,CAAE,MAAKkD,GAAG,CAAC,IAAI,CAACjD,CAAC,CAAE,MAAKiD,GAAG,CAAC,IAAI,CAAChD,CAAC,CAAE,MAAK,IAAI,CAACE,MAAO,GAAE;EACnF;EACA+C,MAAMA,CAAA,EAAG;IACL,OAAO;MACH,GAAGlE,SAAS,CAAC,IAAI,CAACe,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC;IACvC,CAAC;EACL;EACAkD,YAAYA,CAAA,EAAG;IACX,MAAM;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC;IAAE,CAAC,GAAGvE,SAAS,CAAC,IAAI,CAACe,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IACxD,OAAQ,QAAOmD,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAIC,CAAE,GAAE;EACzC;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACtD,CAAC,KAAK,CAAC,EAAE;MACd,OAAO,aAAa;IACxB;IACA,IAAI,IAAI,CAACA,CAAC,GAAG,CAAC,EAAE;MACZ,OAAO,KAAK;IAChB;IACA,MAAMuD,GAAG,GAAG,GAAG,GAAGxE,QAAQ,CAAC,IAAI,CAACc,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,IAAI,CAACC,CAAC,EAAE,KAAK,CAAC;IACzD,KAAK,MAAM,CAACyD,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACzE,KAAK,CAAC,EAAE;MAC9C,IAAIqE,GAAG,KAAKE,KAAK,EAAE;QACf,OAAOD,GAAG;MACd;IACJ;IACA,OAAO,KAAK;EAChB;EACAI,QAAQA,CAACxD,MAAM,EAAE;IACb,MAAMyD,SAAS,GAAGC,OAAO,CAAC1D,MAAM,CAAC;IACjCA,MAAM,GAAGA,MAAM,IAAI,IAAI,CAACA,MAAM;IAC9B,IAAI2D,eAAe,GAAG,KAAK;IAC3B,MAAMC,QAAQ,GAAG,IAAI,CAAChE,CAAC,GAAG,CAAC,IAAI,IAAI,CAACA,CAAC,IAAI,CAAC;IAC1C,MAAMiE,gBAAgB,GAAG,CAACJ,SAAS,IAAIG,QAAQ,KAAK5D,MAAM,CAAC8D,UAAU,CAAC,KAAK,CAAC,IAAI9D,MAAM,KAAK,MAAM,CAAC;IAClG,IAAI6D,gBAAgB,EAAE;MAClB;MACA;MACA,IAAI7D,MAAM,KAAK,MAAM,IAAI,IAAI,CAACJ,CAAC,KAAK,CAAC,EAAE;QACnC,OAAO,IAAI,CAACsD,MAAM,CAAC,CAAC;MACxB;MACA,OAAO,IAAI,CAACZ,WAAW,CAAC,CAAC;IAC7B;IACA,IAAItC,MAAM,KAAK,KAAK,EAAE;MAClB2D,eAAe,GAAG,IAAI,CAACrB,WAAW,CAAC,CAAC;IACxC;IACA,IAAItC,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAACjB,qBAAqB,CAAC,CAAC;IAClD;IACA,IAAI1C,MAAM,KAAK,KAAK,IAAIA,MAAM,KAAK,MAAM,EAAE;MACvC2D,eAAe,GAAG,IAAI,CAAC3B,WAAW,CAAC,CAAC;IACxC;IACA,IAAIhC,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAAC3B,WAAW,CAAC,IAAI,CAAC;IAC5C;IACA,IAAIhC,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAACxB,YAAY,CAAC,IAAI,CAAC;IAC7C;IACA,IAAInC,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAACxB,YAAY,CAAC,CAAC;IACzC;IACA,IAAInC,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAACT,MAAM,CAAC,CAAC;IACnC;IACA,IAAIlD,MAAM,KAAK,KAAK,EAAE;MAClB2D,eAAe,GAAG,IAAI,CAAC9B,WAAW,CAAC,CAAC;IACxC;IACA,IAAI7B,MAAM,KAAK,KAAK,EAAE;MAClB2D,eAAe,GAAG,IAAI,CAACjC,WAAW,CAAC,CAAC;IACxC;IACA,IAAI1B,MAAM,KAAK,MAAM,EAAE;MACnB2D,eAAe,GAAG,IAAI,CAACd,YAAY,CAAC,CAAC;IACzC;IACA,OAAOc,eAAe,IAAI,IAAI,CAAC3B,WAAW,CAAC,CAAC;EAChD;EACA+B,QAAQA,CAAA,EAAG;IACP,OAAO,CAACjE,IAAI,CAACC,KAAK,CAAC,IAAI,CAACN,CAAC,CAAC,IAAI,EAAE,KAAKK,IAAI,CAACC,KAAK,CAAC,IAAI,CAACL,CAAC,CAAC,IAAI,CAAC,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,CAAC;EACtF;EACAqE,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI7E,SAAS,CAAC,IAAI,CAACqE,QAAQ,CAAC,CAAC,CAAC;EACzC;EACA;AACJ;AACA;AACA;EACIS,OAAOA,CAACC,MAAM,GAAG,EAAE,EAAE;IACjB,MAAMvC,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxBM,GAAG,CAACC,CAAC,IAAIsC,MAAM,GAAG,GAAG;IACrBvC,GAAG,CAACC,CAAC,GAAG1C,OAAO,CAACyC,GAAG,CAACC,CAAC,CAAC;IACtB,OAAO,IAAIzC,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACIwC,QAAQA,CAACD,MAAM,GAAG,EAAE,EAAE;IAClB,MAAM1E,GAAG,GAAG,IAAI,CAACe,KAAK,CAAC,CAAC;IACxBf,GAAG,CAACC,CAAC,GAAGK,IAAI,CAACsE,GAAG,CAAC,CAAC,EAAEtE,IAAI,CAACuE,GAAG,CAAC,GAAG,EAAE7E,GAAG,CAACC,CAAC,GAAGK,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEmE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7E1E,GAAG,CAACE,CAAC,GAAGI,IAAI,CAACsE,GAAG,CAAC,CAAC,EAAEtE,IAAI,CAACuE,GAAG,CAAC,GAAG,EAAE7E,GAAG,CAACE,CAAC,GAAGI,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEmE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7E1E,GAAG,CAACG,CAAC,GAAGG,IAAI,CAACsE,GAAG,CAAC,CAAC,EAAEtE,IAAI,CAACuE,GAAG,CAAC,GAAG,EAAE7E,GAAG,CAACG,CAAC,GAAGG,IAAI,CAACC,KAAK,CAAC,GAAG,GAAG,EAAEmE,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;IAC7E,OAAO,IAAI/E,SAAS,CAACK,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI8E,MAAMA,CAACJ,MAAM,GAAG,EAAE,EAAE;IAChB,MAAMvC,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxBM,GAAG,CAACC,CAAC,IAAIsC,MAAM,GAAG,GAAG;IACrBvC,GAAG,CAACC,CAAC,GAAG1C,OAAO,CAACyC,GAAG,CAACC,CAAC,CAAC;IACtB,OAAO,IAAIzC,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACI4C,IAAIA,CAACL,MAAM,GAAG,EAAE,EAAE;IACd,OAAO,IAAI,CAACM,GAAG,CAAC,OAAO,EAAEN,MAAM,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIO,KAAKA,CAACP,MAAM,GAAG,EAAE,EAAE;IACf,OAAO,IAAI,CAACM,GAAG,CAAC,OAAO,EAAEN,MAAM,CAAC;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIQ,UAAUA,CAACR,MAAM,GAAG,EAAE,EAAE;IACpB,MAAMvC,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxBM,GAAG,CAACP,CAAC,IAAI8C,MAAM,GAAG,GAAG;IACrBvC,GAAG,CAACP,CAAC,GAAGlC,OAAO,CAACyC,GAAG,CAACP,CAAC,CAAC;IACtB,OAAO,IAAIjC,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACIgD,QAAQA,CAACT,MAAM,GAAG,EAAE,EAAE;IAClB,MAAMvC,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxBM,GAAG,CAACP,CAAC,IAAI8C,MAAM,GAAG,GAAG;IACrBvC,GAAG,CAACP,CAAC,GAAGlC,OAAO,CAACyC,GAAG,CAACP,CAAC,CAAC;IACtB,OAAO,IAAIjC,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACIiD,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACF,UAAU,CAAC,GAAG,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIG,IAAIA,CAACX,MAAM,EAAE;IACT,MAAMvC,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxB,MAAMyD,GAAG,GAAG,CAACnD,GAAG,CAACH,CAAC,GAAG0C,MAAM,IAAI,GAAG;IAClCvC,GAAG,CAACH,CAAC,GAAGsD,GAAG,GAAG,CAAC,GAAG,GAAG,GAAGA,GAAG,GAAGA,GAAG;IACjC,OAAO,IAAI3F,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA;AACJ;AACA;AACA;EACI6C,GAAGA,CAACnF,KAAK,EAAE6E,MAAM,GAAG,EAAE,EAAE;IACpB,MAAMa,IAAI,GAAG,IAAI,CAACxE,KAAK,CAAC,CAAC;IACzB,MAAMyE,IAAI,GAAG,IAAI7F,SAAS,CAACE,KAAK,CAAC,CAACkB,KAAK,CAAC,CAAC;IACzC,MAAM0E,CAAC,GAAGf,MAAM,GAAG,GAAG;IACtB,MAAMgB,IAAI,GAAG;MACTzF,CAAC,EAAE,CAACuF,IAAI,CAACvF,CAAC,GAAGsF,IAAI,CAACtF,CAAC,IAAIwF,CAAC,GAAGF,IAAI,CAACtF,CAAC;MACjCC,CAAC,EAAE,CAACsF,IAAI,CAACtF,CAAC,GAAGqF,IAAI,CAACrF,CAAC,IAAIuF,CAAC,GAAGF,IAAI,CAACrF,CAAC;MACjCC,CAAC,EAAE,CAACqF,IAAI,CAACrF,CAAC,GAAGoF,IAAI,CAACpF,CAAC,IAAIsF,CAAC,GAAGF,IAAI,CAACpF,CAAC;MACjCC,CAAC,EAAE,CAACoF,IAAI,CAACpF,CAAC,GAAGmF,IAAI,CAACnF,CAAC,IAAIqF,CAAC,GAAGF,IAAI,CAACnF;IACpC,CAAC;IACD,OAAO,IAAIT,SAAS,CAAC+F,IAAI,CAAC;EAC9B;EACAC,SAASA,CAACC,OAAO,GAAG,CAAC,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,MAAM1D,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxB,MAAMiE,IAAI,GAAG,GAAG,GAAGD,MAAM;IACzB,MAAME,GAAG,GAAG,CAAC,IAAI,CAAC;IAClB,KAAK5D,GAAG,CAACH,CAAC,GAAG,CAACG,GAAG,CAACH,CAAC,IAAK8D,IAAI,GAAGF,OAAO,IAAK,CAAC,CAAC,GAAG,GAAG,IAAI,GAAG,EAAE,EAAEA,OAAO,GAAG;MACpEzD,GAAG,CAACH,CAAC,GAAG,CAACG,GAAG,CAACH,CAAC,GAAG8D,IAAI,IAAI,GAAG;MAC5BC,GAAG,CAACC,IAAI,CAAC,IAAIrG,SAAS,CAACwC,GAAG,CAAC,CAAC;IAChC;IACA,OAAO4D,GAAG;EACd;EACA;AACJ;AACA;EACIE,UAAUA,CAAA,EAAG;IACT,MAAM9D,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxBM,GAAG,CAACH,CAAC,GAAG,CAACG,GAAG,CAACH,CAAC,GAAG,GAAG,IAAI,GAAG;IAC3B,OAAO,IAAIrC,SAAS,CAACwC,GAAG,CAAC;EAC7B;EACA+D,aAAaA,CAACN,OAAO,GAAG,CAAC,EAAE;IACvB,MAAM7D,GAAG,GAAG,IAAI,CAACD,KAAK,CAAC,CAAC;IACxB,MAAM;MAAEE;IAAE,CAAC,GAAGD,GAAG;IACjB,MAAM;MAAEH;IAAE,CAAC,GAAGG,GAAG;IACjB,IAAI;MAAEE;IAAE,CAAC,GAAGF,GAAG;IACf,MAAMoE,GAAG,GAAG,EAAE;IACd,MAAMC,YAAY,GAAG,CAAC,GAAGR,OAAO;IAChC,OAAOA,OAAO,EAAE,EAAE;MACdO,GAAG,CAACH,IAAI,CAAC,IAAIrG,SAAS,CAAC;QAAEqC,CAAC;QAAEJ,CAAC;QAAEK;MAAE,CAAC,CAAC,CAAC;MACpCA,CAAC,GAAG,CAACA,CAAC,GAAGmE,YAAY,IAAI,CAAC;IAC9B;IACA,OAAOD,GAAG;EACd;EACAE,eAAeA,CAAA,EAAG;IACd,MAAMlE,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxB,MAAM;MAAEG;IAAE,CAAC,GAAGG,GAAG;IACjB,OAAO,CACH,IAAI,EACJ,IAAIxC,SAAS,CAAC;MAAEqC,CAAC,EAAE,CAACA,CAAC,GAAG,EAAE,IAAI,GAAG;MAAEJ,CAAC,EAAEO,GAAG,CAACP,CAAC;MAAEQ,CAAC,EAAED,GAAG,CAACC;IAAE,CAAC,CAAC,EACxD,IAAIzC,SAAS,CAAC;MAAEqC,CAAC,EAAE,CAACA,CAAC,GAAG,GAAG,IAAI,GAAG;MAAEJ,CAAC,EAAEO,GAAG,CAACP,CAAC;MAAEQ,CAAC,EAAED,GAAG,CAACC;IAAE,CAAC,CAAC,CAC5D;EACL;EACA;AACJ;AACA;EACIkE,YAAYA,CAACC,UAAU,EAAE;IACrB,MAAMC,EAAE,GAAG,IAAI,CAACzF,KAAK,CAAC,CAAC;IACvB,MAAM0F,EAAE,GAAG,IAAI9G,SAAS,CAAC4G,UAAU,CAAC,CAACxF,KAAK,CAAC,CAAC;IAC5C,MAAMW,KAAK,GAAG8E,EAAE,CAACpG,CAAC,GAAGqG,EAAE,CAACrG,CAAC,IAAI,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC;IACtC,OAAO,IAAIT,SAAS,CAAC;MACjBM,CAAC,EAAE,CAACuG,EAAE,CAACvG,CAAC,GAAGuG,EAAE,CAACpG,CAAC,GAAGqG,EAAE,CAACxG,CAAC,GAAGwG,EAAE,CAACrG,CAAC,IAAI,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC,IAAIsB,KAAK;MACnDxB,CAAC,EAAE,CAACsG,EAAE,CAACtG,CAAC,GAAGsG,EAAE,CAACpG,CAAC,GAAGqG,EAAE,CAACvG,CAAC,GAAGuG,EAAE,CAACrG,CAAC,IAAI,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC,IAAIsB,KAAK;MACnDvB,CAAC,EAAE,CAACqG,EAAE,CAACrG,CAAC,GAAGqG,EAAE,CAACpG,CAAC,GAAGqG,EAAE,CAACtG,CAAC,GAAGsG,EAAE,CAACrG,CAAC,IAAI,CAAC,GAAGoG,EAAE,CAACpG,CAAC,CAAC,IAAIsB,KAAK;MACnDtB,CAAC,EAAEsB;IACP,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACIgF,KAAKA,CAAA,EAAG;IACJ,OAAO,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;EACzB;EACA;AACJ;AACA;EACIC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACD,MAAM,CAAC,CAAC,CAAC;EACzB;EACA;AACJ;AACA;AACA;EACIA,MAAMA,CAACE,CAAC,EAAE;IACN,MAAM1E,GAAG,GAAG,IAAI,CAACN,KAAK,CAAC,CAAC;IACxB,MAAM;MAAEG;IAAE,CAAC,GAAGG,GAAG;IACjB,MAAM2E,MAAM,GAAG,CAAC,IAAI,CAAC;IACrB,MAAMC,SAAS,GAAG,GAAG,GAAGF,CAAC;IACzB,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,EAAEG,CAAC,EAAE,EAAE;MACxBF,MAAM,CAACd,IAAI,CAAC,IAAIrG,SAAS,CAAC;QAAEqC,CAAC,EAAE,CAACA,CAAC,GAAGgF,CAAC,GAAGD,SAAS,IAAI,GAAG;QAAEnF,CAAC,EAAEO,GAAG,CAACP,CAAC;QAAEQ,CAAC,EAAED,GAAG,CAACC;MAAE,CAAC,CAAC,CAAC;IACpF;IACA,OAAO0E,MAAM;EACjB;EACA;AACJ;AACA;EACIG,MAAMA,CAACpH,KAAK,EAAE;IACV,MAAMqH,aAAa,GAAG,IAAIvH,SAAS,CAACE,KAAK,CAAC;IAC1C;AACR;AACA;AACA;IACQ,IAAI,IAAI,CAACW,MAAM,KAAK,MAAM,IAAI0G,aAAa,CAAC1G,MAAM,KAAK,MAAM,EAAE;MAC3D,OAAO,IAAI,CAAC6C,YAAY,CAAC,CAAC,KAAK6D,aAAa,CAAC7D,YAAY,CAAC,CAAC;IAC/D;IACA,OAAO,IAAI,CAACP,WAAW,CAAC,CAAC,KAAKoE,aAAa,CAACpE,WAAW,CAAC,CAAC;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}