{"ast": null, "code": "import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setQuarter\n * @category Quarter Helpers\n * @summary Set the year quarter to the given date.\n *\n * @description\n * Set the year quarter to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} quarter - the quarter of the new date\n * @returns {Date} the new date with the quarter set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd quarter to 2 July 2014:\n * const result = setQuarter(new Date(2014, 6, 2), 2)\n * //=> Wed Apr 02 2014 00:00:00\n */\nexport default function setQuarter(dirtyDate, dirtyQuarter) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = toInteger(dirtyQuarter);\n  var oldQuarter = Math.floor(date.getMonth() / 3) + 1;\n  var diff = quarter - oldQuarter;\n  return setMonth(date, date.getMonth() + diff * 3);\n}", "map": {"version": 3, "names": ["toInteger", "toDate", "setMonth", "requiredArgs", "setQuarter", "dirtyDate", "dirtyQuarter", "arguments", "date", "quarter", "oldQuarter", "Math", "floor", "getMonth", "diff"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/setQuarter/index.js"], "sourcesContent": ["import toInteger from \"../_lib/toInteger/index.js\";\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name setQuarter\n * @category Quarter Helpers\n * @summary Set the year quarter to the given date.\n *\n * @description\n * Set the year quarter to the given date.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Number} quarter - the quarter of the new date\n * @returns {Date} the new date with the quarter set\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Set the 2nd quarter to 2 July 2014:\n * const result = setQuarter(new Date(2014, 6, 2), 2)\n * //=> Wed Apr 02 2014 00:00:00\n */\nexport default function setQuarter(dirtyDate, dirtyQuarter) {\n  requiredArgs(2, arguments);\n  var date = toDate(dirtyDate);\n  var quarter = toInteger(dirtyQuarter);\n  var oldQuarter = Math.floor(date.getMonth() / 3) + 1;\n  var diff = quarter - oldQuarter;\n  return setMonth(date, date.getMonth() + diff * 3);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,4BAA4B;AAClD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,UAAUA,CAACC,SAAS,EAAEC,YAAY,EAAE;EAC1DH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGP,MAAM,CAACI,SAAS,CAAC;EAC5B,IAAII,OAAO,GAAGT,SAAS,CAACM,YAAY,CAAC;EACrC,IAAII,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EACpD,IAAIC,IAAI,GAAGL,OAAO,GAAGC,UAAU;EAC/B,OAAOR,QAAQ,CAACM,IAAI,EAAEA,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAGC,IAAI,GAAG,CAAC,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}