{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nexport class HeaderNames {}\nHeaderNames.Authorization = \"Authorization\";\nHeaderNames.Cookie = \"<PERSON>ie\";", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Authorization", "<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/HeaderNames.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nexport class HeaderNames {\r\n}\r\nHeaderNames.Authorization = \"Authorization\";\r\nHeaderNames.Cookie = \"<PERSON>ie\";\r\n"], "mappings": "AAAA;AACA;AACA,OAAO,MAAMA,WAAW,CAAC;AAEzBA,WAAW,CAACC,aAAa,GAAG,eAAe;AAC3CD,WAAW,CAACE,MAAM,GAAG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}