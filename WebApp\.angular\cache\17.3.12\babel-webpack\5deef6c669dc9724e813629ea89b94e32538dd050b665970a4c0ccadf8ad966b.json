{"ast": null, "code": "import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/**\n * This method is like `_.partial` except that partially applied arguments\n * are appended to the arguments it receives.\n *\n * The `_.partialRight.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of partially\n * applied functions.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Function\n * @param {Function} func The function to partially apply arguments to.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new partially applied function.\n * @example\n *\n * function greet(greeting, name) {\n *   return greeting + ' ' + name;\n * }\n *\n * var greetFred = _.partialRight(greet, 'fred');\n * greetFred('hi');\n * // => 'hi fred'\n *\n * // Partially applied with placeholders.\n * var sayHelloTo = _.partialRight(greet, 'hello', _);\n * sayHelloTo('fred');\n * // => 'hello fred'\n */\nvar partialRight = baseRest(function (func, partials) {\n  var holders = replaceHolders(partials, getHolder(partialRight));\n  return createWrap(func, WRAP_PARTIAL_RIGHT_FLAG, undefined, partials, holders);\n});\n\n// Assign default placeholders.\npartialRight.placeholder = {};\nexport default partialRight;", "map": {"version": 3, "names": ["baseRest", "createWrap", "getHolder", "replaceHolders", "WRAP_PARTIAL_RIGHT_FLAG", "partialRight", "func", "partials", "holders", "undefined", "placeholder"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/partialRight.js"], "sourcesContent": ["import baseRest from './_baseRest.js';\nimport createWrap from './_createWrap.js';\nimport getHolder from './_getHolder.js';\nimport replaceHolders from './_replaceHolders.js';\n\n/** Used to compose bitmasks for function metadata. */\nvar WRAP_PARTIAL_RIGHT_FLAG = 64;\n\n/**\n * This method is like `_.partial` except that partially applied arguments\n * are appended to the arguments it receives.\n *\n * The `_.partialRight.placeholder` value, which defaults to `_` in monolithic\n * builds, may be used as a placeholder for partially applied arguments.\n *\n * **Note:** This method doesn't set the \"length\" property of partially\n * applied functions.\n *\n * @static\n * @memberOf _\n * @since 1.0.0\n * @category Function\n * @param {Function} func The function to partially apply arguments to.\n * @param {...*} [partials] The arguments to be partially applied.\n * @returns {Function} Returns the new partially applied function.\n * @example\n *\n * function greet(greeting, name) {\n *   return greeting + ' ' + name;\n * }\n *\n * var greetFred = _.partialRight(greet, 'fred');\n * greetFred('hi');\n * // => 'hi fred'\n *\n * // Partially applied with placeholders.\n * var sayHelloTo = _.partialRight(greet, 'hello', _);\n * sayHelloTo('fred');\n * // => 'hello fred'\n */\nvar partialRight = baseRest(function(func, partials) {\n  var holders = replaceHolders(partials, getHolder(partialRight));\n  return createWrap(func, WRAP_PARTIAL_RIGHT_FLAG, undefined, partials, holders);\n});\n\n// Assign default placeholders.\npartialRight.placeholder = {};\n\nexport default partialRight;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,cAAc,MAAM,sBAAsB;;AAEjD;AACA,IAAIC,uBAAuB,GAAG,EAAE;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGL,QAAQ,CAAC,UAASM,IAAI,EAAEC,QAAQ,EAAE;EACnD,IAAIC,OAAO,GAAGL,cAAc,CAACI,QAAQ,EAAEL,SAAS,CAACG,YAAY,CAAC,CAAC;EAC/D,OAAOJ,UAAU,CAACK,IAAI,EAAEF,uBAAuB,EAAEK,SAAS,EAAEF,QAAQ,EAAEC,OAAO,CAAC;AAChF,CAAC,CAAC;;AAEF;AACAH,YAAY,CAACK,WAAW,GAAG,CAAC,CAAC;AAE7B,eAAeL,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}