{"ast": null, "code": "/**\n * Converts `set` to its value-value pairs.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the value-value pairs.\n */\nfunction setToPairs(set) {\n  var index = -1,\n    result = Array(set.size);\n  set.forEach(function (value) {\n    result[++index] = [value, value];\n  });\n  return result;\n}\nexport default setToPairs;", "map": {"version": 3, "names": ["setToPairs", "set", "index", "result", "Array", "size", "for<PERSON>ach", "value"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_setToPairs.js"], "sourcesContent": ["/**\n * Converts `set` to its value-value pairs.\n *\n * @private\n * @param {Object} set The set to convert.\n * @returns {Array} Returns the value-value pairs.\n */\nfunction setToPairs(set) {\n  var index = -1,\n      result = Array(set.size);\n\n  set.forEach(function(value) {\n    result[++index] = [value, value];\n  });\n  return result;\n}\n\nexport default setToPairs;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGC,KAAK,CAACH,GAAG,CAACI,IAAI,CAAC;EAE5BJ,GAAG,CAACK,OAAO,CAAC,UAASC,KAAK,EAAE;IAC1BJ,MAAM,CAAC,EAAED,KAAK,CAAC,GAAG,CAACK,KAAK,EAAEA,KAAK,CAAC;EAClC,CAAC,CAAC;EACF,OAAOJ,MAAM;AACf;AAEA,eAAeH,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}