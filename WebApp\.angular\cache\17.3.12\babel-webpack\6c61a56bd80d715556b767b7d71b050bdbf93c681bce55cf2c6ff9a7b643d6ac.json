{"ast": null, "code": "import arrayFilter from './_arrayFilter.js';\nimport stubArray from './stubArray.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function (object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function (symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\nexport default getSymbols;", "map": {"version": 3, "names": ["arrayFilter", "stubArray", "objectProto", "Object", "prototype", "propertyIsEnumerable", "nativeGetSymbols", "getOwnPropertySymbols", "getSymbols", "object", "symbol", "call"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_getSymbols.js"], "sourcesContent": ["import arrayFilter from './_arrayFilter.js';\nimport stubArray from './stubArray.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Built-in value references. */\nvar propertyIsEnumerable = objectProto.propertyIsEnumerable;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeGetSymbols = Object.getOwnPropertySymbols;\n\n/**\n * Creates an array of the own enumerable symbols of `object`.\n *\n * @private\n * @param {Object} object The object to query.\n * @returns {Array} Returns the array of symbols.\n */\nvar getSymbols = !nativeGetSymbols ? stubArray : function(object) {\n  if (object == null) {\n    return [];\n  }\n  object = Object(object);\n  return arrayFilter(nativeGetSymbols(object), function(symbol) {\n    return propertyIsEnumerable.call(object, symbol);\n  });\n};\n\nexport default getSymbols;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,oBAAoB,GAAGH,WAAW,CAACG,oBAAoB;;AAE3D;AACA,IAAIC,gBAAgB,GAAGH,MAAM,CAACI,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAG,CAACF,gBAAgB,GAAGL,SAAS,GAAG,UAASQ,MAAM,EAAE;EAChE,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,EAAE;EACX;EACAA,MAAM,GAAGN,MAAM,CAACM,MAAM,CAAC;EACvB,OAAOT,WAAW,CAACM,gBAAgB,CAACG,MAAM,CAAC,EAAE,UAASC,MAAM,EAAE;IAC5D,OAAOL,oBAAoB,CAACM,IAAI,CAACF,MAAM,EAAEC,MAAM,CAAC;EAClD,CAAC,CAAC;AACJ,CAAC;AAED,eAAeF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}