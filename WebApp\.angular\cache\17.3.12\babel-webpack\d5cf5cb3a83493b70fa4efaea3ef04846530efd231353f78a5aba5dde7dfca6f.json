{"ast": null, "code": "import arrayShuffle from './_arrayShuffle.js';\nimport baseShuffle from './_baseShuffle.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates an array of shuffled values, using a version of the\n * [<PERSON><PERSON><PERSON> shuffle](https://en.wikipedia.org/wiki/<PERSON>-<PERSON>_shuffle).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to shuffle.\n * @returns {Array} Returns the new shuffled array.\n * @example\n *\n * _.shuffle([1, 2, 3, 4]);\n * // => [4, 1, 3, 2]\n */\nfunction shuffle(collection) {\n  var func = isArray(collection) ? arrayShuffle : baseShuffle;\n  return func(collection);\n}\nexport default shuffle;", "map": {"version": 3, "names": ["arrayShuffle", "baseShuffle", "isArray", "shuffle", "collection", "func"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/shuffle.js"], "sourcesContent": ["import arrayShuffle from './_arrayShuffle.js';\nimport baseShuffle from './_baseShuffle.js';\nimport isArray from './isArray.js';\n\n/**\n * Creates an array of shuffled values, using a version of the\n * [<PERSON><PERSON><PERSON> shuffle](https://en.wikipedia.org/wiki/<PERSON>-<PERSON>_shuffle).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object} collection The collection to shuffle.\n * @returns {Array} Returns the new shuffled array.\n * @example\n *\n * _.shuffle([1, 2, 3, 4]);\n * // => [4, 1, 3, 2]\n */\nfunction shuffle(collection) {\n  var func = isArray(collection) ? arrayShuffle : baseShuffle;\n  return func(collection);\n}\n\nexport default shuffle;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,cAAc;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,UAAU,EAAE;EAC3B,IAAIC,IAAI,GAAGH,OAAO,CAACE,UAAU,CAAC,GAAGJ,YAAY,GAAGC,WAAW;EAC3D,OAAOI,IAAI,CAACD,UAAU,CAAC;AACzB;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}