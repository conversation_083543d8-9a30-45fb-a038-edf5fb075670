{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\nimport arrayPush from './_arrayPush.js';\nimport arrayReduce from './_arrayReduce.js';\n\n/**\n * The base implementation of `wrapperValue` which returns the result of\n * performing a sequence of actions on the unwrapped `value`, where each\n * successive action is supplied the return value of the previous.\n *\n * @private\n * @param {*} value The unwrapped value.\n * @param {Array} actions Actions to perform to resolve the unwrapped value.\n * @returns {*} Returns the resolved value.\n */\nfunction baseWrapperValue(value, actions) {\n  var result = value;\n  if (result instanceof LazyWrapper) {\n    result = result.value();\n  }\n  return arrayReduce(actions, function (result, action) {\n    return action.func.apply(action.thisArg, arrayPush([result], action.args));\n  }, result);\n}\nexport default baseWrapperValue;", "map": {"version": 3, "names": ["LazyWrapper", "arrayPush", "arrayReduce", "baseWrapperValue", "value", "actions", "result", "action", "func", "apply", "thisArg", "args"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseWrapperValue.js"], "sourcesContent": ["import LazyWrapper from './_LazyWrapper.js';\nimport arrayPush from './_arrayPush.js';\nimport arrayReduce from './_arrayReduce.js';\n\n/**\n * The base implementation of `wrapperValue` which returns the result of\n * performing a sequence of actions on the unwrapped `value`, where each\n * successive action is supplied the return value of the previous.\n *\n * @private\n * @param {*} value The unwrapped value.\n * @param {Array} actions Actions to perform to resolve the unwrapped value.\n * @returns {*} Returns the resolved value.\n */\nfunction baseWrapperValue(value, actions) {\n  var result = value;\n  if (result instanceof LazyWrapper) {\n    result = result.value();\n  }\n  return arrayReduce(actions, function(result, action) {\n    return action.func.apply(action.thisArg, arrayPush([result], action.args));\n  }, result);\n}\n\nexport default baseWrapperValue;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACxC,IAAIC,MAAM,GAAGF,KAAK;EAClB,IAAIE,MAAM,YAAYN,WAAW,EAAE;IACjCM,MAAM,GAAGA,MAAM,CAACF,KAAK,CAAC,CAAC;EACzB;EACA,OAAOF,WAAW,CAACG,OAAO,EAAE,UAASC,MAAM,EAAEC,MAAM,EAAE;IACnD,OAAOA,MAAM,CAACC,IAAI,CAACC,KAAK,CAACF,MAAM,CAACG,OAAO,EAAET,SAAS,CAAC,CAACK,MAAM,CAAC,EAAEC,MAAM,CAACI,IAAI,CAAC,CAAC;EAC5E,CAAC,EAAEL,MAAM,CAAC;AACZ;AAEA,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}