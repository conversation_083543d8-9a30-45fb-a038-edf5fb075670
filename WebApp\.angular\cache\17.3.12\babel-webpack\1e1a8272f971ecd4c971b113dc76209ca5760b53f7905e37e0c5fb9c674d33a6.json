{"ast": null, "code": "/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\nexport default baseClamp;", "map": {"version": 3, "names": ["baseClamp", "number", "lower", "upper", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/_baseClamp.js"], "sourcesContent": ["/**\n * The base implementation of `_.clamp` which doesn't coerce arguments.\n *\n * @private\n * @param {number} number The number to clamp.\n * @param {number} [lower] The lower bound.\n * @param {number} upper The upper bound.\n * @returns {number} Returns the clamped number.\n */\nfunction baseClamp(number, lower, upper) {\n  if (number === number) {\n    if (upper !== undefined) {\n      number = number <= upper ? number : upper;\n    }\n    if (lower !== undefined) {\n      number = number >= lower ? number : lower;\n    }\n  }\n  return number;\n}\n\nexport default baseClamp;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,EAAE;EACvC,IAAIF,MAAM,KAAKA,MAAM,EAAE;IACrB,IAAIE,KAAK,KAAKC,SAAS,EAAE;MACvBH,MAAM,GAAGA,MAAM,IAAIE,KAAK,GAAGF,MAAM,GAAGE,KAAK;IAC3C;IACA,IAAID,KAAK,KAAKE,SAAS,EAAE;MACvBH,MAAM,GAAGA,MAAM,IAAIC,KAAK,GAAGD,MAAM,GAAGC,KAAK;IAC3C;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}