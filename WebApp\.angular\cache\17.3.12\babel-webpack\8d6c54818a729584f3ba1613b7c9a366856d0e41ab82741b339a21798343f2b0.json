{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst Op_1 = require(\"./Op\");\nclass Iterator {\n  constructor(ops) {\n    this.ops = ops;\n    this.index = 0;\n    this.offset = 0;\n  }\n  hasNext() {\n    return this.peekLength() < Infinity;\n  }\n  next(length) {\n    if (!length) {\n      length = Infinity;\n    }\n    const nextOp = this.ops[this.index];\n    if (nextOp) {\n      const offset = this.offset;\n      const opLength = Op_1.default.length(nextOp);\n      if (length >= opLength - offset) {\n        length = opLength - offset;\n        this.index += 1;\n        this.offset = 0;\n      } else {\n        this.offset += length;\n      }\n      if (typeof nextOp.delete === 'number') {\n        return {\n          delete: length\n        };\n      } else {\n        const retOp = {};\n        if (nextOp.attributes) {\n          retOp.attributes = nextOp.attributes;\n        }\n        if (typeof nextOp.retain === 'number') {\n          retOp.retain = length;\n        } else if (typeof nextOp.retain === 'object' && nextOp.retain !== null) {\n          // offset should === 0, length should === 1\n          retOp.retain = nextOp.retain;\n        } else if (typeof nextOp.insert === 'string') {\n          retOp.insert = nextOp.insert.substr(offset, length);\n        } else {\n          // offset should === 0, length should === 1\n          retOp.insert = nextOp.insert;\n        }\n        return retOp;\n      }\n    } else {\n      return {\n        retain: Infinity\n      };\n    }\n  }\n  peek() {\n    return this.ops[this.index];\n  }\n  peekLength() {\n    if (this.ops[this.index]) {\n      // Should never return 0 if our index is being managed correctly\n      return Op_1.default.length(this.ops[this.index]) - this.offset;\n    } else {\n      return Infinity;\n    }\n  }\n  peekType() {\n    const op = this.ops[this.index];\n    if (op) {\n      if (typeof op.delete === 'number') {\n        return 'delete';\n      } else if (typeof op.retain === 'number' || typeof op.retain === 'object' && op.retain !== null) {\n        return 'retain';\n      } else {\n        return 'insert';\n      }\n    }\n    return 'retain';\n  }\n  rest() {\n    if (!this.hasNext()) {\n      return [];\n    } else if (this.offset === 0) {\n      return this.ops.slice(this.index);\n    } else {\n      const offset = this.offset;\n      const index = this.index;\n      const next = this.next();\n      const rest = this.ops.slice(this.index);\n      this.offset = offset;\n      this.index = index;\n      return [next].concat(rest);\n    }\n  }\n}\nexports.default = Iterator;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "Op_1", "require", "Iterator", "constructor", "ops", "index", "offset", "hasNext", "<PERSON><PERSON><PERSON><PERSON>", "Infinity", "next", "length", "nextOp", "opLength", "default", "delete", "retOp", "attributes", "retain", "insert", "substr", "peek", "peekType", "op", "rest", "slice", "concat"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill-delta/dist/OpIterator.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst Op_1 = require(\"./Op\");\nclass Iterator {\n    constructor(ops) {\n        this.ops = ops;\n        this.index = 0;\n        this.offset = 0;\n    }\n    hasNext() {\n        return this.peekLength() < Infinity;\n    }\n    next(length) {\n        if (!length) {\n            length = Infinity;\n        }\n        const nextOp = this.ops[this.index];\n        if (nextOp) {\n            const offset = this.offset;\n            const opLength = Op_1.default.length(nextOp);\n            if (length >= opLength - offset) {\n                length = opLength - offset;\n                this.index += 1;\n                this.offset = 0;\n            }\n            else {\n                this.offset += length;\n            }\n            if (typeof nextOp.delete === 'number') {\n                return { delete: length };\n            }\n            else {\n                const retOp = {};\n                if (nextOp.attributes) {\n                    retOp.attributes = nextOp.attributes;\n                }\n                if (typeof nextOp.retain === 'number') {\n                    retOp.retain = length;\n                }\n                else if (typeof nextOp.retain === 'object' &&\n                    nextOp.retain !== null) {\n                    // offset should === 0, length should === 1\n                    retOp.retain = nextOp.retain;\n                }\n                else if (typeof nextOp.insert === 'string') {\n                    retOp.insert = nextOp.insert.substr(offset, length);\n                }\n                else {\n                    // offset should === 0, length should === 1\n                    retOp.insert = nextOp.insert;\n                }\n                return retOp;\n            }\n        }\n        else {\n            return { retain: Infinity };\n        }\n    }\n    peek() {\n        return this.ops[this.index];\n    }\n    peekLength() {\n        if (this.ops[this.index]) {\n            // Should never return 0 if our index is being managed correctly\n            return Op_1.default.length(this.ops[this.index]) - this.offset;\n        }\n        else {\n            return Infinity;\n        }\n    }\n    peekType() {\n        const op = this.ops[this.index];\n        if (op) {\n            if (typeof op.delete === 'number') {\n                return 'delete';\n            }\n            else if (typeof op.retain === 'number' ||\n                (typeof op.retain === 'object' && op.retain !== null)) {\n                return 'retain';\n            }\n            else {\n                return 'insert';\n            }\n        }\n        return 'retain';\n    }\n    rest() {\n        if (!this.hasNext()) {\n            return [];\n        }\n        else if (this.offset === 0) {\n            return this.ops.slice(this.index);\n        }\n        else {\n            const offset = this.offset;\n            const index = this.index;\n            const next = this.next();\n            const rest = this.ops.slice(this.index);\n            this.offset = offset;\n            this.index = index;\n            return [next].concat(rest);\n        }\n    }\n}\nexports.default = Iterator;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,IAAI,GAAGC,OAAO,CAAC,MAAM,CAAC;AAC5B,MAAMC,QAAQ,CAAC;EACXC,WAAWA,CAACC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,MAAM,GAAG,CAAC;EACnB;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACC,UAAU,CAAC,CAAC,GAAGC,QAAQ;EACvC;EACAC,IAAIA,CAACC,MAAM,EAAE;IACT,IAAI,CAACA,MAAM,EAAE;MACTA,MAAM,GAAGF,QAAQ;IACrB;IACA,MAAMG,MAAM,GAAG,IAAI,CAACR,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;IACnC,IAAIO,MAAM,EAAE;MACR,MAAMN,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMO,QAAQ,GAAGb,IAAI,CAACc,OAAO,CAACH,MAAM,CAACC,MAAM,CAAC;MAC5C,IAAID,MAAM,IAAIE,QAAQ,GAAGP,MAAM,EAAE;QAC7BK,MAAM,GAAGE,QAAQ,GAAGP,MAAM;QAC1B,IAAI,CAACD,KAAK,IAAI,CAAC;QACf,IAAI,CAACC,MAAM,GAAG,CAAC;MACnB,CAAC,MACI;QACD,IAAI,CAACA,MAAM,IAAIK,MAAM;MACzB;MACA,IAAI,OAAOC,MAAM,CAACG,MAAM,KAAK,QAAQ,EAAE;QACnC,OAAO;UAAEA,MAAM,EAAEJ;QAAO,CAAC;MAC7B,CAAC,MACI;QACD,MAAMK,KAAK,GAAG,CAAC,CAAC;QAChB,IAAIJ,MAAM,CAACK,UAAU,EAAE;UACnBD,KAAK,CAACC,UAAU,GAAGL,MAAM,CAACK,UAAU;QACxC;QACA,IAAI,OAAOL,MAAM,CAACM,MAAM,KAAK,QAAQ,EAAE;UACnCF,KAAK,CAACE,MAAM,GAAGP,MAAM;QACzB,CAAC,MACI,IAAI,OAAOC,MAAM,CAACM,MAAM,KAAK,QAAQ,IACtCN,MAAM,CAACM,MAAM,KAAK,IAAI,EAAE;UACxB;UACAF,KAAK,CAACE,MAAM,GAAGN,MAAM,CAACM,MAAM;QAChC,CAAC,MACI,IAAI,OAAON,MAAM,CAACO,MAAM,KAAK,QAAQ,EAAE;UACxCH,KAAK,CAACG,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACC,MAAM,CAACd,MAAM,EAAEK,MAAM,CAAC;QACvD,CAAC,MACI;UACD;UACAK,KAAK,CAACG,MAAM,GAAGP,MAAM,CAACO,MAAM;QAChC;QACA,OAAOH,KAAK;MAChB;IACJ,CAAC,MACI;MACD,OAAO;QAAEE,MAAM,EAAET;MAAS,CAAC;IAC/B;EACJ;EACAY,IAAIA,CAAA,EAAG;IACH,OAAO,IAAI,CAACjB,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;EAC/B;EACAG,UAAUA,CAAA,EAAG;IACT,IAAI,IAAI,CAACJ,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC,EAAE;MACtB;MACA,OAAOL,IAAI,CAACc,OAAO,CAACH,MAAM,CAAC,IAAI,CAACP,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,GAAG,IAAI,CAACC,MAAM;IAClE,CAAC,MACI;MACD,OAAOG,QAAQ;IACnB;EACJ;EACAa,QAAQA,CAAA,EAAG;IACP,MAAMC,EAAE,GAAG,IAAI,CAACnB,GAAG,CAAC,IAAI,CAACC,KAAK,CAAC;IAC/B,IAAIkB,EAAE,EAAE;MACJ,IAAI,OAAOA,EAAE,CAACR,MAAM,KAAK,QAAQ,EAAE;QAC/B,OAAO,QAAQ;MACnB,CAAC,MACI,IAAI,OAAOQ,EAAE,CAACL,MAAM,KAAK,QAAQ,IACjC,OAAOK,EAAE,CAACL,MAAM,KAAK,QAAQ,IAAIK,EAAE,CAACL,MAAM,KAAK,IAAK,EAAE;QACvD,OAAO,QAAQ;MACnB,CAAC,MACI;QACD,OAAO,QAAQ;MACnB;IACJ;IACA,OAAO,QAAQ;EACnB;EACAM,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAACjB,OAAO,CAAC,CAAC,EAAE;MACjB,OAAO,EAAE;IACb,CAAC,MACI,IAAI,IAAI,CAACD,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO,IAAI,CAACF,GAAG,CAACqB,KAAK,CAAC,IAAI,CAACpB,KAAK,CAAC;IACrC,CAAC,MACI;MACD,MAAMC,MAAM,GAAG,IAAI,CAACA,MAAM;MAC1B,MAAMD,KAAK,GAAG,IAAI,CAACA,KAAK;MACxB,MAAMK,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC,CAAC;MACxB,MAAMc,IAAI,GAAG,IAAI,CAACpB,GAAG,CAACqB,KAAK,CAAC,IAAI,CAACpB,KAAK,CAAC;MACvC,IAAI,CAACC,MAAM,GAAGA,MAAM;MACpB,IAAI,CAACD,KAAK,GAAGA,KAAK;MAClB,OAAO,CAACK,IAAI,CAAC,CAACgB,MAAM,CAACF,IAAI,CAAC;IAC9B;EACJ;AACJ;AACA1B,OAAO,CAACgB,OAAO,GAAGZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}