{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport default function getDaysInMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var monthIndex = date.getMonth();\n  var lastDayOfMonth = new Date(0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "getDaysInMonth", "dirtyDate", "arguments", "date", "year", "getFullYear", "monthIndex", "getMonth", "lastDayOfMonth", "Date", "setFullYear", "setHours", "getDate"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/getDaysInMonth/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date.\n *\n * @param {Date|Number} date - the given date\n * @returns {Number} the number of days in a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport default function getDaysInMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var year = date.getFullYear();\n  var monthIndex = date.getMonth();\n  var lastDayOfMonth = new Date(0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAE;EAChDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,IAAI,GAAGD,IAAI,CAACE,WAAW,CAAC,CAAC;EAC7B,IAAIC,UAAU,GAAGH,IAAI,CAACI,QAAQ,CAAC,CAAC;EAChC,IAAIC,cAAc,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAChCD,cAAc,CAACE,WAAW,CAACN,IAAI,EAAEE,UAAU,GAAG,CAAC,EAAE,CAAC,CAAC;EACnDE,cAAc,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACnC,OAAOH,cAAc,CAACI,OAAO,CAAC,CAAC;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}