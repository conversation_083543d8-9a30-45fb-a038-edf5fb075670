import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of } from 'rxjs';

import { AgentChatComponent } from './agent-chat.component';

describe('AgentChatComponent', () => {
  let component: AgentChatComponent;
  let fixture: ComponentFixture<AgentChatComponent>;

  beforeEach(async () => {
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [AgentChatComponent],
      providers: [
        { provide: Router, useValue: routerSpy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(AgentChatComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Source References Functionality', () => {
    it('should toggle search results sidebar', () => {
      expect(component.showSearchResultsSidebar).toBeFalse();

      component.toggleSearchResultsSidebar();

      expect(component.showSearchResultsSidebar).toBeTrue();
    });

    it('should populate search results when toggling with source descriptions', () => {
      const mockSourceDescriptions = [
        { title: 'Test Source 1', url: 'https://example1.com', description: 'Test description 1' },
        { title: 'Test Source 2', url: 'https://example2.com', description: 'Test description 2' }
      ];
      const sourceName = 'Google';

      component.toggleSearchResultsSidebar(mockSourceDescriptions, sourceName);

      expect(component.showSearchResultsSidebar).toBeTrue();
      expect(component.searchResults.length).toBe(2);
      expect(component.currentSourceName).toBe(sourceName);
      expect(component.searchResults[0].title).toBe('Test Source 1');
    });

    it('should detect if response has source descriptions', () => {
      const responseWithSources = {
        chatSourceDescriptions: [
          { title: 'Test', url: 'https://example.com', description: 'Test desc' }
        ]
      };
      const responseWithoutSources = { chatSourceDescriptions: [] };
      const responseWithNoProperty = {};

      expect(component.hasSourceDescriptions(responseWithSources)).toBeTrue();
      expect(component.hasSourceDescriptions(responseWithoutSources)).toBeFalse();
      expect(component.hasSourceDescriptions(responseWithNoProperty)).toBeFalse();
    });

    it('should get correct source count', () => {
      const responseWithSources = {
        chatSourceDescriptions: [
          { title: 'Test 1' },
          { title: 'Test 2' },
          { title: 'Test 3' }
        ]
      };
      const responseWithoutSources = {};

      expect(component.getSourceCount(responseWithSources)).toBe(3);
      expect(component.getSourceCount(responseWithoutSources)).toBe(0);
    });

    it('should handle source click correctly', () => {
      spyOn(component, 'toggleSearchResultsSidebar');

      const mockResponse = {
        chatSource: 'Google',
        chatSourceDescriptions: [
          { title: 'Test Source', url: 'https://example.com', description: 'Test description' }
        ]
      };

      component.onSourceClick(mockResponse);

      expect(component.toggleSearchResultsSidebar).toHaveBeenCalledWith(
        mockResponse.chatSourceDescriptions,
        'Google'
      );
    });
  });

  describe('Splitter Functionality', () => {
    it('should update split sizes correctly', () => {
      component.showSearchResultsSidebar = true;
      component.rightSidebarWidth = 350;

      // Mock window width
      spyOnProperty(window, 'innerWidth', 'get').and.returnValue(1200);

      // Access private method using bracket notation
      (component as any).updateSplitSizes();

      expect(component.rightSidebarSplitSize).toBeGreaterThan(0);
      expect(component.mainContentSplitSize).toBeLessThan(100);
    });

    it('should save and load sidebar width from localStorage', () => {
      const testWidth = 400;

      component.rightSidebarWidth = testWidth;
      (component as any).saveRightSidebarWidth(testWidth);

      // Reset width
      component.rightSidebarWidth = 350;

      // Load saved width
      (component as any).loadSavedRightSidebarWidth();

      expect(component.rightSidebarWidth).toBe(testWidth);
    });
  });
});
