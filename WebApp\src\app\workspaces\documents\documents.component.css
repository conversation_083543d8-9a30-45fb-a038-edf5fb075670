/* Ensure dialog backdrop blur */
.fixed.inset-0 {
  backdrop-filter: blur(2px);
}

.documents-container {
  display: flex;
  height: 100%;
}

.sidebar {
  width: 250px;
  background-color: #f5f5f5;
  border-right: 1px solid #ddd;
  padding: 15px;
  overflow-y: auto;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 15px;
}

.document-button {
  text-align: left;
  padding: 10px;
  background-color: #1f2937;
  border: 1px solid #1f2937;
  width: 100%;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.document-button:hover {
  background-color: #111827;
}

.document-button.active {
  background-color: #007bff;
  color: white;
  border-color: #0069d9;
}

.add-document-button {
  margin-top: 15px;
  padding: 10px;
  background-color: #28a745;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.add-document-button:hover {
  background-color: #218838;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.document-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-buttons button {
  padding: 6px 12px;
  background-color: #6c757d;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.document-content {
  margin-top: 20px;
}

.no-selection {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  color: #6c757d;
  font-style: italic;
}

/* Add any custom styles not covered by Tailwind here */

/* Fix image size in document view */
.document-content img {
  max-width: 100%;
  height: auto;
}

/* Custom scrollbar for better visibility in dark mode */
:host-context(.dark-theme) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-track {
  background: #1f2937;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

:host-context(.dark-theme) ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* Custom scrollbar for light theme */
:host-context(:not(.dark-theme)) ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

:host-context(:not(.dark-theme)) ::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Styles for the split layout */
as-split {
  --gutter-color: transparent;
  --gutter-hover-color: var(--primary-purple);
}

/* Custom styling for the gutter - completely invisible by default */
.as-split-gutter {
  background-color: transparent !important;
  transition: all 0.2s ease;
  position: relative;
  z-index: 40;
  width: 1px !important; /* Make the gutter very thin */
  opacity: 0;
}

/* Show a subtle gutter only on hover */
.as-split-gutter:hover {
  opacity: 1;
  width: 4px !important;
}

/* Add a subtle visual indicator for the gutter */
.as-split-gutter::after {
  content: "";
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 1px;
  height: 100%;
  background-color: var(--hover-blue-gray);
  opacity: 0.3;
  transition: all 0.2s ease;
}

/* Enhance the gutter indicator on hover */
.as-split-gutter:hover::after {
  width: 2px;
  background-color: var(--primary-purple);
  opacity: 1;
}

/* Add resize cursor */
.as-split-gutter {
  cursor: col-resize !important;
}

/* Create a larger hover area for the gutter */
.as-split-gutter::before {
  content: "";
  position: absolute;
  top: 0;
  left: -10px;
  width: 20px;
  height: 100%;
  background-color: transparent;
  z-index: 39;
}

/* Ensure the split areas take full height */
as-split-area {
  height: 100%;
  overflow: hidden;
  transition: all 0.3s ease;
}

/* Sidebar state indicators */
.sidebar-collapsed-area {
  border-right: 2px solid transparent;
}

.sidebar-narrow-area {
  border-right: 2px solid var(--hover-blue-gray);
}

.sidebar-expanded-area {
  border-right: 2px solid var(--primary-purple);
}

/* Theme-specific sidebar styling */
:host-context(.dark-theme) .sidebar-narrow-area,
:host-context(.dark-theme) .sidebar-expanded-area {
  border-right-color: #3a3a45;
}

:host-context(:not(.dark-theme)) .sidebar-narrow-area,
:host-context(:not(.dark-theme)) .sidebar-expanded-area {
  border-right-color: var(--hover-blue-gray);
}

/* Width indicator during drag */
.width-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 100;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  animation: fadeIn 0.3s ease;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.file-display-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 10px 0;
}

.file-item {
  position: relative;
  width: 120px;
  height: 140px;
  border: 1px solid #ddd;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview {
  position: relative;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
}

.file-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.file-icon {
  font-size: 36px;
  color: #6c757d;
}

.file-name {
  padding: 5px;
  text-align: center;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #f8f9fa;
}

.delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #dc3545;
  transition: all 0.2s;
  z-index: 5;
}

.delete-btn:hover {
  background-color: #dc3545;
  color: white;
}

.file-upload-section {
  margin-top: 15px;
}

.file-input {
  display: none;
}

.file-upload-btn {
  background-color: #f0f0f0;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-btn:hover {
  background-color: #e0e0e0;
}

/* Document attachments styling */
.document-attachments-section {
  margin: 20px 0;
}

/* File gallery styling - similar to your document details display */
.file-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin: 10px 0;
}

.file-item {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.file-preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-delete-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.file-delete-btn:hover {
  background-color: rgba(220, 53, 69, 0.8);
}

.file-upload-section {
  margin: 15px 0;
}

.file-input {
  display: none;
}

.file-upload-btn {
  padding: 6px 12px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-btn:hover {
  background-color: #e9ecef;
}

/* Chat Section Fixed Positioning */
.chat-container-fixed {
  position: fixed !important;
  top: 0 !important;
  right: 0 !important;
  max-height: calc(100vh - 64px) !important;
  height: calc(100vh - 64px) !important;
  z-index: 9999 !important;
  overflow: hidden !important;
}

.chat-toggle-button-fixed {
  position: fixed !important;
  bottom: 24px !important;
  right: 24px !important;
  z-index: 9998 !important;
}

.chat-options-dropdown-fixed {
  position: absolute !important;
  z-index: 10000 !important;
}

/* Prevent chat from scrolling with page content */
.chat-container-fixed * {
  position: relative;
}

/* Ensure chat messages area scrolls independently */
.chat-messages-scroll {
  overflow-y: auto !important;
  height: 100% !important;
  max-height: 100% !important;
}

/* Fix for mobile devices */
@media (max-width: 640px) {
  .chat-container-fixed {
    width: 100vw !important;
    height: 100vh !important;
    height: 100dvh !important; /* Dynamic viewport height for mobile */
  }
}

/* Ensure proper stacking context */
.chat-container-fixed {
  isolation: isolate;
  contain: layout style paint;
}
