{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\nexport default function secondsToMinutes(seconds) {\n  requiredArgs(1, arguments);\n  var minutes = seconds / secondsInMinute;\n  return Math.floor(minutes);\n}", "map": {"version": 3, "names": ["requiredArgs", "secondsInMinute", "secondsToMinutes", "seconds", "arguments", "minutes", "Math", "floor"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/secondsToMinutes/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { secondsInMinute } from \"../constants/index.js\";\n/**\n * @name secondsToMinutes\n * @category Conversion Helpers\n * @summary Convert seconds to minutes.\n *\n * @description\n * Convert a number of seconds to a full number of minutes.\n *\n * @param {number} seconds - number of seconds to be converted\n *\n * @returns {number} the number of seconds converted in minutes\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 120 seconds into minutes\n * const result = secondsToMinutes(120)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = secondsToMinutes(119)\n * //=> 1\n */\nexport default function secondsToMinutes(seconds) {\n  requiredArgs(1, arguments);\n  var minutes = seconds / secondsInMinute;\n  return Math.floor(minutes);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,OAAO,EAAE;EAChDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,OAAO,GAAGF,OAAO,GAAGF,eAAe;EACvC,OAAOK,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}