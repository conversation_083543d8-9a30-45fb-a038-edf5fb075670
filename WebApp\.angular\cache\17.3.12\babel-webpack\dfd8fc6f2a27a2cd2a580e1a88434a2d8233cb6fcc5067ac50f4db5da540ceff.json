{"ast": null, "code": "/**\n * Days in 1 week.\n *\n * @name daysInWeek\n * @constant\n * @type {number}\n * @default\n */\nexport var daysInWeek = 7;\n\n/**\n * Days in 1 year\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * @name daysInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var daysInYear = 365.2425;\n\n/**\n * Maximum allowed time.\n *\n * @name maxTime\n * @constant\n * @type {number}\n * @default\n */\nexport var maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\n\n/**\n * Milliseconds in 1 minute\n *\n * @name millisecondsInMinute\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInMinute = 60000;\n\n/**\n * Milliseconds in 1 hour\n *\n * @name millisecondsInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInHour = 3600000;\n\n/**\n * Milliseconds in 1 second\n *\n * @name millisecondsInSecond\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInSecond = 1000;\n\n/**\n * Minimum allowed time.\n *\n * @name minTime\n * @constant\n * @type {number}\n * @default\n */\nexport var minTime = -maxTime;\n\n/**\n * Minutes in 1 hour\n *\n * @name minutesInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var minutesInHour = 60;\n\n/**\n * Months in 1 quarter\n *\n * @name monthsInQuarter\n * @constant\n * @type {number}\n * @default\n */\nexport var monthsInQuarter = 3;\n\n/**\n * Months in 1 year\n *\n * @name monthsInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var monthsInYear = 12;\n\n/**\n * Quarters in 1 year\n *\n * @name quartersInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var quartersInYear = 4;\n\n/**\n * Seconds in 1 hour\n *\n * @name secondsInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInHour = 3600;\n\n/**\n * Seconds in 1 minute\n *\n * @name secondsInMinute\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInMinute = 60;\n\n/**\n * Seconds in 1 day\n *\n * @name secondsInDay\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInDay = secondsInHour * 24;\n\n/**\n * Seconds in 1 week\n *\n * @name secondsInWeek\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInWeek = secondsInDay * 7;\n\n/**\n * Seconds in 1 year\n *\n * @name secondsInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInYear = secondsInDay * daysInYear;\n\n/**\n * Seconds in 1 month\n *\n * @name secondsInMonth\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInMonth = secondsInYear / 12;\n\n/**\n * Seconds in 1 quarter\n *\n * @name secondsInQuarter\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInQuarter = secondsInMonth * 3;", "map": {"version": 3, "names": ["daysInWeek", "daysInYear", "maxTime", "Math", "pow", "millisecondsInMinute", "millisecondsInHour", "millisecondsInSecond", "minTime", "minutesInHour", "monthsInQuarter", "monthsInYear", "quartersInYear", "secondsInHour", "secondsInMinute", "secondsInDay", "secondsInWeek", "secondsInYear", "secondsIn<PERSON><PERSON><PERSON>", "secondsInQuarter"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/constants/index.js"], "sourcesContent": ["/**\n * Days in 1 week.\n *\n * @name daysInWeek\n * @constant\n * @type {number}\n * @default\n */\nexport var daysInWeek = 7;\n\n/**\n * Days in 1 year\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occures every 4 years, except for years that are divisable by 100 and not divisable by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n *\n * @name daysInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var daysInYear = 365.2425;\n\n/**\n * Maximum allowed time.\n *\n * @name maxTime\n * @constant\n * @type {number}\n * @default\n */\nexport var maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\n\n/**\n * Milliseconds in 1 minute\n *\n * @name millisecondsInMinute\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInMinute = 60000;\n\n/**\n * Milliseconds in 1 hour\n *\n * @name millisecondsInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInHour = 3600000;\n\n/**\n * Milliseconds in 1 second\n *\n * @name millisecondsInSecond\n * @constant\n * @type {number}\n * @default\n */\nexport var millisecondsInSecond = 1000;\n\n/**\n * Minimum allowed time.\n *\n * @name minTime\n * @constant\n * @type {number}\n * @default\n */\nexport var minTime = -maxTime;\n\n/**\n * Minutes in 1 hour\n *\n * @name minutesInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var minutesInHour = 60;\n\n/**\n * Months in 1 quarter\n *\n * @name monthsInQuarter\n * @constant\n * @type {number}\n * @default\n */\nexport var monthsInQuarter = 3;\n\n/**\n * Months in 1 year\n *\n * @name monthsInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var monthsInYear = 12;\n\n/**\n * Quarters in 1 year\n *\n * @name quartersInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var quartersInYear = 4;\n\n/**\n * Seconds in 1 hour\n *\n * @name secondsInHour\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInHour = 3600;\n\n/**\n * Seconds in 1 minute\n *\n * @name secondsInMinute\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInMinute = 60;\n\n/**\n * Seconds in 1 day\n *\n * @name secondsInDay\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInDay = secondsInHour * 24;\n\n/**\n * Seconds in 1 week\n *\n * @name secondsInWeek\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInWeek = secondsInDay * 7;\n\n/**\n * Seconds in 1 year\n *\n * @name secondsInYear\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInYear = secondsInDay * daysInYear;\n\n/**\n * Seconds in 1 month\n *\n * @name secondsInMonth\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInMonth = secondsInYear / 12;\n\n/**\n * Seconds in 1 quarter\n *\n * @name secondsInQuarter\n * @constant\n * @type {number}\n * @default\n */\nexport var secondsInQuarter = secondsInMonth * 3;"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIA,UAAU,GAAG,CAAC;;AAEzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,QAAQ;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,KAAK;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,kBAAkB,GAAG,OAAO;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,oBAAoB,GAAG,IAAI;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,OAAO,GAAG,CAACN,OAAO;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIO,aAAa,GAAG,EAAE;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,CAAC;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,YAAY,GAAG,EAAE;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,cAAc,GAAG,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,aAAa,GAAG,IAAI;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,eAAe,GAAG,EAAE;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIC,YAAY,GAAGF,aAAa,GAAG,EAAE;;AAE5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIG,aAAa,GAAGD,YAAY,GAAG,CAAC;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,aAAa,GAAGF,YAAY,GAAGd,UAAU;;AAEpD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIiB,cAAc,GAAGD,aAAa,GAAG,EAAE;;AAE9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAIE,gBAAgB,GAAGD,cAAc,GAAG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}