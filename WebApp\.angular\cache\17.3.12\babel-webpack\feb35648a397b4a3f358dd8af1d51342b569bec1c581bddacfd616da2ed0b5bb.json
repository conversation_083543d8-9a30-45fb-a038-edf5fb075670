{"ast": null, "code": "import eachDayOfInterval from \"../eachDayOfInterval/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @param {Interval} interval - the given interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport default function eachWeekendOfInterval(interval) {\n  requiredArgs(1, arguments);\n  var dateInterval = eachDayOfInterval(interval);\n  var weekends = [];\n  var index = 0;\n  while (index < dateInterval.length) {\n    var date = dateInterval[index++];\n    if (isWeekend(date)) {\n      weekends.push(date);\n      if (isSunday(date)) index = index + 5;\n    }\n  }\n  return weekends;\n}", "map": {"version": 3, "names": ["eachDayOfInterval", "is<PERSON><PERSON><PERSON>", "isWeekend", "requiredArgs", "eachWeekendOfInterval", "interval", "arguments", "dateInterval", "weekends", "index", "length", "date", "push"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/date-fns/esm/eachWeekendOfInterval/index.js"], "sourcesContent": ["import eachDayOfInterval from \"../eachDayOfInterval/index.js\";\nimport isSunday from \"../isSunday/index.js\";\nimport isWeekend from \"../isWeekend/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name eachWeekendOfInterval\n * @category Interval Helpers\n * @summary List all the Saturdays and Sundays in the given date interval.\n *\n * @description\n * Get all the Saturdays and Sundays in the given date interval.\n *\n * @param {Interval} interval - the given interval. See [Interval]{@link https://date-fns.org/docs/Interval}\n * @returns {Date[]} an array containing all the Saturdays and Sundays\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} The start of an interval cannot be after its end\n * @throws {RangeError} Date in interval cannot be `Invalid Date`\n *\n * @example\n * // Lists all Saturdays and Sundays in the given date interval\n * const result = eachWeekendOfInterval({\n *   start: new Date(2018, 8, 17),\n *   end: new Date(2018, 8, 30)\n * })\n * //=> [\n * //   Sat Sep 22 2018 00:00:00,\n * //   Sun Sep 23 2018 00:00:00,\n * //   Sat Sep 29 2018 00:00:00,\n * //   Sun Sep 30 2018 00:00:00\n * // ]\n */\nexport default function eachWeekendOfInterval(interval) {\n  requiredArgs(1, arguments);\n  var dateInterval = eachDayOfInterval(interval);\n  var weekends = [];\n  var index = 0;\n  while (index < dateInterval.length) {\n    var date = dateInterval[index++];\n    if (isWeekend(date)) {\n      weekends.push(date);\n      if (isSunday(date)) index = index + 5;\n    }\n  }\n  return weekends;\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,QAAQ,EAAE;EACtDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,YAAY,GAAGP,iBAAiB,CAACK,QAAQ,CAAC;EAC9C,IAAIG,QAAQ,GAAG,EAAE;EACjB,IAAIC,KAAK,GAAG,CAAC;EACb,OAAOA,KAAK,GAAGF,YAAY,CAACG,MAAM,EAAE;IAClC,IAAIC,IAAI,GAAGJ,YAAY,CAACE,KAAK,EAAE,CAAC;IAChC,IAAIP,SAAS,CAACS,IAAI,CAAC,EAAE;MACnBH,QAAQ,CAACI,IAAI,CAACD,IAAI,CAAC;MACnB,IAAIV,QAAQ,CAACU,IAAI,CAAC,EAAEF,KAAK,GAAGA,KAAK,GAAG,CAAC;IACvC;EACF;EACA,OAAOD,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}