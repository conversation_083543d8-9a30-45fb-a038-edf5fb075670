{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./blog-share-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./blog-share-dialog.component.css?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NZ_MODAL_DATA, NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { ChatServiceProxy, SaveBlogRequestDto } from '../../../shared/service-proxies/service-proxies';\nlet BlogShareDialogComponent = class BlogShareDialogComponent {\n  constructor(modalRef, messageService, chatService, data) {\n    this.modalRef = modalRef;\n    this.messageService = messageService;\n    this.chatService = chatService;\n    this.data = data;\n    this.blogContent = '';\n    this.blogTitle = '';\n    this.shareUrl = '';\n    this.isLoading = false;\n  }\n  ngOnInit() {\n    if (this.data && this.data.blogContent) {\n      this.blogContent = this.data.blogContent;\n      this.blogTitle = this.extractBlogTitle(this.blogContent);\n    }\n  }\n  /**\n   * Extracts the title from a blog post\n   * @param response The blog post content\n   * @returns The blog title or a default title\n   */\n  extractBlogTitle(response) {\n    if (!response) return 'Blog Post';\n    // Look for the first heading (# Heading)\n    const titleRegex = /^#\\s+(.+)$/m;\n    const match = response.match(titleRegex);\n    if (match && match[1]) {\n      return match[1].trim();\n    }\n    return 'Blog Post';\n  }\n  /**\n   * Closes the dialog without sending\n   */\n  cancel() {\n    this.modalRef.close();\n  }\n  /**\n   * Sends the blog content to the specified URL\n   */\n  sendBlog() {\n    if (!this.shareUrl.trim()) {\n      this.messageService.error('Please enter a URL to share the blog');\n      return;\n    }\n    if (!this.blogContent.trim()) {\n      this.messageService.error('No blog content to share');\n      return;\n    }\n    if (!this.blogTitle.trim()) {\n      this.messageService.error('Please enter a blog title');\n      return;\n    }\n    this.isLoading = true;\n    // Create the request DTO with title included\n    const request = new SaveBlogRequestDto({\n      url: this.shareUrl.trim(),\n      content: this.blogContent.trim(),\n      title: this.blogTitle.trim()\n    });\n    // Call the API\n    this.chatService.saveBlog(request).subscribe({\n      next: response => {\n        this.isLoading = false;\n        if (response.isError) {\n          this.messageService.error(response.message || 'Failed to share blog');\n        } else {\n          this.messageService.success(response.message || 'Blog shared successfully!');\n          this.modalRef.close({\n            url: this.shareUrl,\n            content: this.blogContent,\n            title: this.blogTitle,\n            response: response\n          });\n        }\n      },\n      error: error => {\n        this.isLoading = false;\n        console.error('Error sharing blog:', error);\n        // Handle different types of errors\n        let errorMessage = 'Failed to share blog. Please try again.';\n        this.messageService.error(errorMessage);\n      }\n    });\n  }\n  /**\n   * Copies the blog content to clipboard\n   */\n  copyBlogContent() {\n    if (!this.blogContent) {\n      this.messageService.error('No content to copy');\n      return;\n    }\n    navigator.clipboard.writeText(this.blogContent).then(() => {\n      this.messageService.success('Blog content copied to clipboard');\n    }).catch(() => {\n      this.messageService.error('Failed to copy content');\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NzModalRef\n    }, {\n      type: NzMessageService\n    }, {\n      type: ChatServiceProxy\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NZ_MODAL_DATA]\n      }]\n    }];\n  }\n};\nBlogShareDialogComponent = __decorate([Component({\n  selector: 'app-blog-share-dialog',\n  standalone: true,\n  imports: [CommonModule, FormsModule, NzModalModule, NzInputModule, NzButtonModule, MarkdownModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], BlogShareDialogComponent);\nexport { BlogShareDialogComponent };", "map": {"version": 3, "names": ["Component", "Inject", "CommonModule", "FormsModule", "NZ_MODAL_DATA", "NzModalModule", "NzModalRef", "NzInputModule", "NzButtonModule", "NzMessageService", "MarkdownModule", "ChatServiceProxy", "SaveBlogRequestDto", "BlogShareDialogComponent", "constructor", "modalRef", "messageService", "chatService", "data", "blogContent", "blogTitle", "shareUrl", "isLoading", "ngOnInit", "extractBlogTitle", "response", "titleRegex", "match", "trim", "cancel", "close", "sendBlog", "error", "request", "url", "content", "title", "saveBlog", "subscribe", "next", "isError", "message", "success", "console", "errorMessage", "copyBlogContent", "navigator", "clipboard", "writeText", "then", "catch", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\components\\blog-share-dialog\\blog-share-dialog.component.ts"], "sourcesContent": ["import { Component, OnInit, Inject } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { FormsModule } from '@angular/forms';\nimport { NZ_MODAL_DATA, NzModalModule, NzModalRef } from 'ng-zorro-antd/modal';\nimport { NzInputModule } from 'ng-zorro-antd/input';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { NzMessageService } from 'ng-zorro-antd/message';\nimport { MarkdownModule } from 'ngx-markdown';\nimport { ChatServiceProxy, SaveBlogRequestDto } from '../../../shared/service-proxies/service-proxies';\n\n@Component({\n  selector: 'app-blog-share-dialog',\n  standalone: true,\n  imports: [\n    CommonModule,\n    FormsModule,\n    NzModalModule,\n    NzInputModule,\n    NzButtonModule,\n    MarkdownModule\n  ],\n  templateUrl: './blog-share-dialog.component.html',\n  styleUrls: ['./blog-share-dialog.component.css']\n})\nexport class BlogShareDialogComponent implements OnInit {\n  blogContent: string = '';\n  blogTitle: string = '';\n  shareUrl: string = '';\n  isLoading: boolean = false;\n\n  constructor(\n    private modalRef: NzModalRef,\n    private messageService: NzMessageService,\n    private chatService: ChatServiceProxy,\n    @Inject(NZ_MODAL_DATA) public data: { blogContent: string }\n  ) { }\n\n  ngOnInit(): void {\n    if (this.data && this.data.blogContent) {\n      this.blogContent = this.data.blogContent;\n      this.blogTitle = this.extractBlogTitle(this.blogContent);\n    }\n  }\n\n  /**\n   * Extracts the title from a blog post\n   * @param response The blog post content\n   * @returns The blog title or a default title\n   */\n  extractBlogTitle(response: string): string {\n    if (!response) return 'Blog Post';\n\n    // Look for the first heading (# Heading)\n    const titleRegex = /^#\\s+(.+)$/m;\n    const match = response.match(titleRegex);\n\n    if (match && match[1]) {\n      return match[1].trim();\n    }\n\n    return 'Blog Post';\n  }\n\n  /**\n   * Closes the dialog without sending\n   */\n  cancel(): void {\n    this.modalRef.close();\n  }\n\n  /**\n   * Sends the blog content to the specified URL\n   */\n  sendBlog(): void {\n    if (!this.shareUrl.trim()) {\n      this.messageService.error('Please enter a URL to share the blog');\n      return;\n    }\n\n    if (!this.blogContent.trim()) {\n      this.messageService.error('No blog content to share');\n      return;\n    }\n\n    if (!this.blogTitle.trim()) {\n      this.messageService.error('Please enter a blog title');\n      return;\n    }\n\n    this.isLoading = true;\n\n    // Create the request DTO with title included\n    const request = new SaveBlogRequestDto({\n      url: this.shareUrl.trim(),\n      content: this.blogContent.trim(),\n      title: this.blogTitle.trim()\n    });\n\n    // Call the API\n    this.chatService.saveBlog(request).subscribe({\n      next: (response) => {\n        this.isLoading = false;\n\n        if (response.isError) {\n          this.messageService.error(response.message || 'Failed to share blog');\n        } else {\n          this.messageService.success(response.message || 'Blog shared successfully!');\n          this.modalRef.close({\n            url: this.shareUrl,\n            content: this.blogContent,\n            title: this.blogTitle,\n            response: response\n          });\n        }\n      },\n      error: (error) => {\n        this.isLoading = false;\n        console.error('Error sharing blog:', error);\n\n        // Handle different types of errors\n        let errorMessage = 'Failed to share blog. Please try again.';\n\n        this.messageService.error(errorMessage);\n      }\n    });\n  }\n\n  /**\n   * Copies the blog content to clipboard\n   */\n  copyBlogContent(): void {\n    if (!this.blogContent) {\n      this.messageService.error('No content to copy');\n      return;\n    }\n\n    navigator.clipboard.writeText(this.blogContent).then(() => {\n      this.messageService.success('Blog content copied to clipboard');\n    }).catch(() => {\n      this.messageService.error('Failed to copy content');\n    });\n  }\n}\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAUC,MAAM,QAAQ,eAAe;AACzD,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,WAAW,QAAQ,gBAAgB;AAC5C,SAASC,aAAa,EAAEC,aAAa,EAAEC,UAAU,QAAQ,qBAAqB;AAC9E,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,gBAAgB,QAAQ,uBAAuB;AACxD,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,gBAAgB,EAAEC,kBAAkB,QAAQ,iDAAiD;AAgB/F,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAMnCC,YACUC,QAAoB,EACpBC,cAAgC,EAChCC,WAA6B,EACPC,IAA6B;IAHnD,KAAAH,QAAQ,GAARA,QAAQ;IACR,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACW,KAAAC,IAAI,GAAJA,IAAI;IATpC,KAAAC,WAAW,GAAW,EAAE;IACxB,KAAAC,SAAS,GAAW,EAAE;IACtB,KAAAC,QAAQ,GAAW,EAAE;IACrB,KAAAC,SAAS,GAAY,KAAK;EAOtB;EAEJC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACL,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,WAAW,EAAE;MACtC,IAAI,CAACA,WAAW,GAAG,IAAI,CAACD,IAAI,CAACC,WAAW;MACxC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACI,gBAAgB,CAAC,IAAI,CAACL,WAAW,CAAC;;EAE5D;EAEA;;;;;EAKAK,gBAAgBA,CAACC,QAAgB;IAC/B,IAAI,CAACA,QAAQ,EAAE,OAAO,WAAW;IAEjC;IACA,MAAMC,UAAU,GAAG,aAAa;IAChC,MAAMC,KAAK,GAAGF,QAAQ,CAACE,KAAK,CAACD,UAAU,CAAC;IAExC,IAAIC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;MACrB,OAAOA,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,EAAE;;IAGxB,OAAO,WAAW;EACpB;EAEA;;;EAGAC,MAAMA,CAAA;IACJ,IAAI,CAACd,QAAQ,CAACe,KAAK,EAAE;EACvB;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAACV,QAAQ,CAACO,IAAI,EAAE,EAAE;MACzB,IAAI,CAACZ,cAAc,CAACgB,KAAK,CAAC,sCAAsC,CAAC;MACjE;;IAGF,IAAI,CAAC,IAAI,CAACb,WAAW,CAACS,IAAI,EAAE,EAAE;MAC5B,IAAI,CAACZ,cAAc,CAACgB,KAAK,CAAC,0BAA0B,CAAC;MACrD;;IAGF,IAAI,CAAC,IAAI,CAACZ,SAAS,CAACQ,IAAI,EAAE,EAAE;MAC1B,IAAI,CAACZ,cAAc,CAACgB,KAAK,CAAC,2BAA2B,CAAC;MACtD;;IAGF,IAAI,CAACV,SAAS,GAAG,IAAI;IAErB;IACA,MAAMW,OAAO,GAAG,IAAIrB,kBAAkB,CAAC;MACrCsB,GAAG,EAAE,IAAI,CAACb,QAAQ,CAACO,IAAI,EAAE;MACzBO,OAAO,EAAE,IAAI,CAAChB,WAAW,CAACS,IAAI,EAAE;MAChCQ,KAAK,EAAE,IAAI,CAAChB,SAAS,CAACQ,IAAI;KAC3B,CAAC;IAEF;IACA,IAAI,CAACX,WAAW,CAACoB,QAAQ,CAACJ,OAAO,CAAC,CAACK,SAAS,CAAC;MAC3CC,IAAI,EAAGd,QAAQ,IAAI;QACjB,IAAI,CAACH,SAAS,GAAG,KAAK;QAEtB,IAAIG,QAAQ,CAACe,OAAO,EAAE;UACpB,IAAI,CAACxB,cAAc,CAACgB,KAAK,CAACP,QAAQ,CAACgB,OAAO,IAAI,sBAAsB,CAAC;SACtE,MAAM;UACL,IAAI,CAACzB,cAAc,CAAC0B,OAAO,CAACjB,QAAQ,CAACgB,OAAO,IAAI,2BAA2B,CAAC;UAC5E,IAAI,CAAC1B,QAAQ,CAACe,KAAK,CAAC;YAClBI,GAAG,EAAE,IAAI,CAACb,QAAQ;YAClBc,OAAO,EAAE,IAAI,CAAChB,WAAW;YACzBiB,KAAK,EAAE,IAAI,CAAChB,SAAS;YACrBK,QAAQ,EAAEA;WACX,CAAC;;MAEN,CAAC;MACDO,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACV,SAAS,GAAG,KAAK;QACtBqB,OAAO,CAACX,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAE3C;QACA,IAAIY,YAAY,GAAG,yCAAyC;QAE5D,IAAI,CAAC5B,cAAc,CAACgB,KAAK,CAACY,YAAY,CAAC;MACzC;KACD,CAAC;EACJ;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC1B,WAAW,EAAE;MACrB,IAAI,CAACH,cAAc,CAACgB,KAAK,CAAC,oBAAoB,CAAC;MAC/C;;IAGFc,SAAS,CAACC,SAAS,CAACC,SAAS,CAAC,IAAI,CAAC7B,WAAW,CAAC,CAAC8B,IAAI,CAAC,MAAK;MACxD,IAAI,CAACjC,cAAc,CAAC0B,OAAO,CAAC,kCAAkC,CAAC;IACjE,CAAC,CAAC,CAACQ,KAAK,CAAC,MAAK;MACZ,IAAI,CAAClC,cAAc,CAACgB,KAAK,CAAC,wBAAwB,CAAC;IACrD,CAAC,CAAC;EACJ;;;;;;;;;;;cA3GG/B,MAAM;QAAAkD,IAAA,GAAC/C,aAAa;MAAA;IAAA,E;;;AAVZS,wBAAwB,GAAAuC,UAAA,EAdpCpD,SAAS,CAAC;EACTqD,QAAQ,EAAE,uBAAuB;EACjCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPrD,YAAY,EACZC,WAAW,EACXE,aAAa,EACbE,aAAa,EACbC,cAAc,EACdE,cAAc,CACf;EACD8C,QAAA,EAAAC,oBAAiD;;CAElD,CAAC,C,EACW5C,wBAAwB,CAsHpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}