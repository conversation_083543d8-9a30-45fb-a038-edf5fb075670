{"ast": null, "code": "import baseNth from './_baseNth.js';\nimport baseRest from './_baseRest.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a function that gets the argument at index `n`. If `n` is negative,\n * the nth argument from the end is returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {number} [n=0] The index of the argument to return.\n * @returns {Function} Returns the new pass-thru function.\n * @example\n *\n * var func = _.nthArg(1);\n * func('a', 'b', 'c', 'd');\n * // => 'b'\n *\n * var func = _.nthArg(-2);\n * func('a', 'b', 'c', 'd');\n * // => 'c'\n */\nfunction nthArg(n) {\n  n = toInteger(n);\n  return baseRest(function (args) {\n    return baseNth(args, n);\n  });\n}\nexport default nthArg;", "map": {"version": 3, "names": ["baseNth", "baseRest", "toInteger", "nthArg", "n", "args"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/nthArg.js"], "sourcesContent": ["import baseNth from './_baseNth.js';\nimport baseRest from './_baseRest.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Creates a function that gets the argument at index `n`. If `n` is negative,\n * the nth argument from the end is returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Util\n * @param {number} [n=0] The index of the argument to return.\n * @returns {Function} Returns the new pass-thru function.\n * @example\n *\n * var func = _.nthArg(1);\n * func('a', 'b', 'c', 'd');\n * // => 'b'\n *\n * var func = _.nthArg(-2);\n * func('a', 'b', 'c', 'd');\n * // => 'c'\n */\nfunction nthArg(n) {\n  n = toInteger(n);\n  return baseRest(function(args) {\n    return baseNth(args, n);\n  });\n}\n\nexport default nthArg;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,CAAC,EAAE;EACjBA,CAAC,GAAGF,SAAS,CAACE,CAAC,CAAC;EAChB,OAAOH,QAAQ,CAAC,UAASI,IAAI,EAAE;IAC7B,OAAOL,OAAO,CAACK,IAAI,EAAED,CAAC,CAAC;EACzB,CAAC,CAAC;AACJ;AAEA,eAAeD,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}