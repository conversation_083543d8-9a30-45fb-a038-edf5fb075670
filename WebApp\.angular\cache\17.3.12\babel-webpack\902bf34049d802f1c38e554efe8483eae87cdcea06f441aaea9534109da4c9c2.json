{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport __NG_CLI_RESOURCE__0 from \"./workspace-users-dialog.component.html?ngResource\";\nimport __NG_CLI_RESOURCE__1 from \"./workspace-users-dialog.component.css?ngResource\";\nimport { Component, Inject } from '@angular/core';\nimport { NzModalRef, NzModalService, NZ_MODAL_DATA } from 'ng-zorro-antd/modal';\nimport { UserAccountServiceProxy, AssignWorkspaceServiceProxy } from '../../../shared/service-proxies/service-proxies';\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\nimport { CommonModule } from '@angular/common';\nimport { NzTableModule } from 'ng-zorro-antd/table';\nimport { AddUserComponent } from '../add-user/add-user.component';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport { AuthService } from '../../../shared/services/auth.service';\nlet WorkspaceUsersDialogComponent = class WorkspaceUsersDialogComponent {\n  constructor(modalRef, userAccountService, modalService, assignWorkspaceService, authService, data) {\n    this.modalRef = modalRef;\n    this.userAccountService = userAccountService;\n    this.modalService = modalService;\n    this.assignWorkspaceService = assignWorkspaceService;\n    this.authService = authService;\n    this.data = data;\n    this.users = []; // Array to hold user data\n    this.workspaceId = this.data.id; // Get the workspace ID from modal data\n  }\n  ngOnInit() {\n    console.log(this.data.id);\n    if (this.data.id) {\n      this.loadUsers();\n    }\n  }\n  get isAdmin() {\n    return this.authService.isAdmin();\n  }\n  loadUsers() {\n    console.log(this.data.id);\n    this.assignWorkspaceService.getUsersByWorkspaceId(this.data.id).subscribe(users => {\n      this.users = users;\n      console.log(users);\n    });\n    // this.userAccountService.getAll().subscribe((users: any) => {\n    //   this.users = users;\n    //   console.log(this.users);\n    // });\n  }\n  addUser() {\n    const modelRef = this.modalService.create({\n      nzTitle: 'User List',\n      nzContent: AddUserComponent,\n      nzData: {\n        id: this.data.id,\n        users: this.users // Pass the current user list to the dialog component\n      },\n      nzWidth: '800px',\n      nzFooter: null,\n      nzMaskClosable: true,\n      nzClosable: true\n    });\n    modelRef.afterClose.subscribe(result => {\n      if (result && result.addedUsers) {\n        // Update the user list with added users\n        this.users = [...this.users, ...result.addedUsers];\n      }\n    });\n  }\n  removeUser(user) {\n    this.assignWorkspaceService.removeUser(this.data.id, user.email).subscribe(response => {\n      console.log('User removed:', response);\n      // Remove the user from the list\n      this.users = this.users.filter(u => u.email !== user.email);\n    });\n  }\n  closeDialog() {\n    this.modalRef.close({\n      // Return any necessary data to the parent component\n    });\n  }\n  static {\n    this.ctorParameters = () => [{\n      type: NzModalRef\n    }, {\n      type: UserAccountServiceProxy\n    }, {\n      type: NzModalService\n    }, {\n      type: AssignWorkspaceServiceProxy\n    }, {\n      type: AuthService\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [NZ_MODAL_DATA]\n      }]\n    }];\n  }\n};\nWorkspaceUsersDialogComponent = __decorate([Component({\n  selector: 'app-workspace-users-dialog',\n  standalone: true,\n  imports: [ServiceProxyModule, CommonModule, NzTableModule, NzButtonModule],\n  template: __NG_CLI_RESOURCE__0,\n  styles: [__NG_CLI_RESOURCE__1]\n})], WorkspaceUsersDialogComponent);\nexport { WorkspaceUsersDialogComponent };", "map": {"version": 3, "names": ["Component", "Inject", "NzModalRef", "NzModalService", "NZ_MODAL_DATA", "UserAccountServiceProxy", "AssignWorkspaceServiceProxy", "ServiceProxyModule", "CommonModule", "NzTableModule", "AddUserComponent", "NzButtonModule", "AuthService", "WorkspaceUsersDialogComponent", "constructor", "modalRef", "userAccountService", "modalService", "assignWorkspaceService", "authService", "data", "users", "workspaceId", "id", "ngOnInit", "console", "log", "loadUsers", "isAdmin", "getUsersByWorkspaceId", "subscribe", "addUser", "modelRef", "create", "nzTitle", "nzContent", "nzData", "nzWidth", "nz<PERSON><PERSON>er", "nzMaskClosable", "nzClosable", "afterClose", "result", "addedUsers", "removeUser", "user", "email", "response", "filter", "u", "closeDialog", "close", "args", "__decorate", "selector", "standalone", "imports", "template", "__NG_CLI_RESOURCE__0"], "sources": ["C:\\Users\\<USER>\\source\\ai-hub\\WebApp\\src\\app\\dialogs\\workspace-users-dialog\\workspace-users-dialog.component.ts"], "sourcesContent": ["import { Component, Inject } from '@angular/core';\r\nimport { NzModalRef, NzModalService, NZ_MODAL_DATA } from 'ng-zorro-antd/modal';\r\nimport {\r\n  UserAccountServiceProxy,\r\n  AssignWorkspaceServiceProxy,\r\n} from '../../../shared/service-proxies/service-proxies';\r\nimport { ServiceProxyModule } from '../../../shared/service-proxies/service-proxy.module';\r\nimport { CommonModule } from '@angular/common';\r\nimport { NzTableModule } from 'ng-zorro-antd/table';\r\nimport { AddUserComponent } from '../add-user/add-user.component';\r\nimport { NzButtonModule } from 'ng-zorro-antd/button';\r\nimport { AuthService } from '../../../shared/services/auth.service';\r\n\r\n@Component({\r\n  selector: 'app-workspace-users-dialog',\r\n  standalone: true,\r\n  imports: [ServiceProxyModule, CommonModule, NzTableModule, NzButtonModule],\r\n  templateUrl: './workspace-users-dialog.component.html',\r\n  styleUrl: './workspace-users-dialog.component.css',\r\n})\r\nexport class WorkspaceUsersDialogComponent {\r\n  constructor(\r\n    private modalRef: NzModalRef,\r\n    private userAccountService: UserAccountServiceProxy,\r\n    private modalService: NzModalService,\r\n    private assignWorkspaceService: AssignWorkspaceServiceProxy,\r\n    public authService: AuthService,\r\n    @Inject(NZ_MODAL_DATA) public data: { id: number }\r\n  ) { }\r\n  users: any = []; // Array to hold user data\r\n  workspaceId: number = this.data.id; // Get the workspace ID from modal data\r\n\r\n  ngOnInit(): void {\r\n    console.log(this.data.id);\r\n    if (this.data.id) {\r\n      this.loadUsers();\r\n    }\r\n  }\r\n  get isAdmin() {\r\n    return this.authService.isAdmin();\r\n  }\r\n  loadUsers() {\r\n    console.log(this.data.id);\r\n\r\n    this.assignWorkspaceService\r\n      .getUsersByWorkspaceId(this.data.id)\r\n      .subscribe((users: any) => {\r\n        this.users = users;\r\n        console.log(users);\r\n      });\r\n    // this.userAccountService.getAll().subscribe((users: any) => {\r\n    //   this.users = users;\r\n    //   console.log(this.users);\r\n    // });\r\n  }\r\n  addUser() {\r\n    const modelRef = this.modalService.create({\r\n      nzTitle: 'User List',\r\n      nzContent: AddUserComponent,\r\n      nzData: {\r\n        id: this.data.id, // Pass any necessary parameters to the dialog component\r\n        users: this.users, // Pass the current user list to the dialog component\r\n      },\r\n      nzWidth: '800px',\r\n      nzFooter: null, // We handle the footer in the dialog component\r\n\r\n      nzMaskClosable: true,  // Allows closing on click outside the modal\r\n      nzClosable: true,\r\n    });\r\n    modelRef.afterClose.subscribe((result) => {\r\n      if (result && result.addedUsers) {\r\n        // Update the user list with added users\r\n        this.users = [...this.users, ...result.addedUsers];\r\n      }\r\n    });\r\n  }\r\n  removeUser(user: any) {\r\n    this.assignWorkspaceService\r\n      .removeUser(this.data.id, user.email)\r\n      .subscribe((response: any) => {\r\n        console.log('User removed:', response);\r\n        // Remove the user from the list\r\n        this.users = this.users.filter((u: any) => u.email !== user.email);\r\n      });\r\n  }\r\n  closeDialog(): void {\r\n    this.modalRef.close({\r\n      // Return any necessary data to the parent component\r\n    });\r\n  }\r\n}\r\n"], "mappings": ";;;AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,UAAU,EAAEC,cAAc,EAAEC,aAAa,QAAQ,qBAAqB;AAC/E,SACEC,uBAAuB,EACvBC,2BAA2B,QACtB,iDAAiD;AACxD,SAASC,kBAAkB,QAAQ,sDAAsD;AACzF,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,qBAAqB;AACnD,SAASC,gBAAgB,QAAQ,gCAAgC;AACjE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,WAAW,QAAQ,uCAAuC;AAS5D,IAAMC,6BAA6B,GAAnC,MAAMA,6BAA6B;EACxCC,YACUC,QAAoB,EACpBC,kBAA2C,EAC3CC,YAA4B,EAC5BC,sBAAmD,EACpDC,WAAwB,EACDC,IAAoB;IAL1C,KAAAL,QAAQ,GAARA,QAAQ;IACR,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,sBAAsB,GAAtBA,sBAAsB;IACvB,KAAAC,WAAW,GAAXA,WAAW;IACY,KAAAC,IAAI,GAAJA,IAAI;IAEpC,KAAAC,KAAK,GAAQ,EAAE,CAAC,CAAC;IACjB,KAAAC,WAAW,GAAW,IAAI,CAACF,IAAI,CAACG,EAAE,CAAC,CAAC;EAFhC;EAIJC,QAAQA,CAAA;IACNC,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,CAACG,EAAE,CAAC;IACzB,IAAI,IAAI,CAACH,IAAI,CAACG,EAAE,EAAE;MAChB,IAAI,CAACI,SAAS,EAAE;;EAEpB;EACA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAACT,WAAW,CAACS,OAAO,EAAE;EACnC;EACAD,SAASA,CAAA;IACPF,OAAO,CAACC,GAAG,CAAC,IAAI,CAACN,IAAI,CAACG,EAAE,CAAC;IAEzB,IAAI,CAACL,sBAAsB,CACxBW,qBAAqB,CAAC,IAAI,CAACT,IAAI,CAACG,EAAE,CAAC,CACnCO,SAAS,CAAET,KAAU,IAAI;MACxB,IAAI,CAACA,KAAK,GAAGA,KAAK;MAClBI,OAAO,CAACC,GAAG,CAACL,KAAK,CAAC;IACpB,CAAC,CAAC;IACJ;IACA;IACA;IACA;EACF;EACAU,OAAOA,CAAA;IACL,MAAMC,QAAQ,GAAG,IAAI,CAACf,YAAY,CAACgB,MAAM,CAAC;MACxCC,OAAO,EAAE,WAAW;MACpBC,SAAS,EAAEzB,gBAAgB;MAC3B0B,MAAM,EAAE;QACNb,EAAE,EAAE,IAAI,CAACH,IAAI,CAACG,EAAE;QAChBF,KAAK,EAAE,IAAI,CAACA,KAAK,CAAE;OACpB;MACDgB,OAAO,EAAE,OAAO;MAChBC,QAAQ,EAAE,IAAI;MAEdC,cAAc,EAAE,IAAI;MACpBC,UAAU,EAAE;KACb,CAAC;IACFR,QAAQ,CAACS,UAAU,CAACX,SAAS,CAAEY,MAAM,IAAI;MACvC,IAAIA,MAAM,IAAIA,MAAM,CAACC,UAAU,EAAE;QAC/B;QACA,IAAI,CAACtB,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,EAAE,GAAGqB,MAAM,CAACC,UAAU,CAAC;;IAEtD,CAAC,CAAC;EACJ;EACAC,UAAUA,CAACC,IAAS;IAClB,IAAI,CAAC3B,sBAAsB,CACxB0B,UAAU,CAAC,IAAI,CAACxB,IAAI,CAACG,EAAE,EAAEsB,IAAI,CAACC,KAAK,CAAC,CACpChB,SAAS,CAAEiB,QAAa,IAAI;MAC3BtB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqB,QAAQ,CAAC;MACtC;MACA,IAAI,CAAC1B,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC2B,MAAM,CAAEC,CAAM,IAAKA,CAAC,CAACH,KAAK,KAAKD,IAAI,CAACC,KAAK,CAAC;IACpE,CAAC,CAAC;EACN;EACAI,WAAWA,CAAA;IACT,IAAI,CAACnC,QAAQ,CAACoC,KAAK,CAAC;MAClB;IAAA,CACD,CAAC;EACJ;;;;;;;;;;;;;;;cA9DGlD,MAAM;QAAAmD,IAAA,GAAChD,aAAa;MAAA;IAAA,E;;;AAPZS,6BAA6B,GAAAwC,UAAA,EAPzCrD,SAAS,CAAC;EACTsD,QAAQ,EAAE,4BAA4B;EACtCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACjD,kBAAkB,EAAEC,YAAY,EAAEC,aAAa,EAAEE,cAAc,CAAC;EAC1E8C,QAAA,EAAAC,oBAAsD;;CAEvD,CAAC,C,EACW7C,6BAA6B,CAsEzC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}