{"ast": null, "code": "import createToPairs from './_createToPairs.js';\nimport keys from './keys.js';\n\n/**\n * Creates an array of own enumerable string keyed-value pairs for `object`\n * which can be consumed by `_.fromPairs`. If `object` is a map or set, its\n * entries are returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias entries\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the key-value pairs.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.toPairs(new Foo);\n * // => [['a', 1], ['b', 2]] (iteration order is not guaranteed)\n */\nvar toPairs = createToPairs(keys);\nexport default toPairs;", "map": {"version": 3, "names": ["createToPairs", "keys", "toPairs"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/toPairs.js"], "sourcesContent": ["import createToPairs from './_createToPairs.js';\nimport keys from './keys.js';\n\n/**\n * Creates an array of own enumerable string keyed-value pairs for `object`\n * which can be consumed by `_.fromPairs`. If `object` is a map or set, its\n * entries are returned.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @alias entries\n * @category Object\n * @param {Object} object The object to query.\n * @returns {Array} Returns the key-value pairs.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.toPairs(new Foo);\n * // => [['a', 1], ['b', 2]] (iteration order is not guaranteed)\n */\nvar toPairs = createToPairs(keys);\n\nexport default toPairs;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,IAAI,MAAM,WAAW;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGF,aAAa,CAACC,IAAI,CAAC;AAEjC,eAAeC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}