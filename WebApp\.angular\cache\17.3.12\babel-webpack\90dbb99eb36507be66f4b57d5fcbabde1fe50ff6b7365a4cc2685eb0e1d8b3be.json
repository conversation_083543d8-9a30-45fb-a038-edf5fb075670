{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// 0, 2, 10, 30 second delays before reconnect attempts.\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\n/** @private */\nexport class DefaultReconnectPolicy {\n  constructor(retryDelays) {\n    this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\n  }\n  nextRetryDelayInMilliseconds(retryContext) {\n    return this._retryDelays[retryContext.previousRetryCount];\n  }\n}", "map": {"version": 3, "names": ["DEFAULT_RETRY_DELAYS_IN_MILLISECONDS", "DefaultReconnectPolicy", "constructor", "re<PERSON><PERSON><PERSON><PERSON>", "_retryD<PERSON>ys", "undefined", "nextRetryDelayInMilliseconds", "retryContext", "previousRetryCount"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/DefaultReconnectPolicy.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n/** @private */\r\nexport class DefaultReconnectPolicy {\r\n    constructor(retryDelays) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n    nextRetryDelayInMilliseconds(retryContext) {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA,MAAMA,oCAAoC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;AAC1E;AACA,OAAO,MAAMC,sBAAsB,CAAC;EAChCC,WAAWA,CAACC,WAAW,EAAE;IACrB,IAAI,CAACC,YAAY,GAAGD,WAAW,KAAKE,SAAS,GAAG,CAAC,GAAGF,WAAW,EAAE,IAAI,CAAC,GAAGH,oCAAoC;EACjH;EACAM,4BAA4BA,CAACC,YAAY,EAAE;IACvC,OAAO,IAAI,CAACH,YAAY,CAACG,YAAY,CAACC,kBAAkB,CAAC;EAC7D;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}