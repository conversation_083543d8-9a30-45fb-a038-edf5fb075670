{"ast": null, "code": "const ignoreRegexp = /\\bmso-list:[^;]*ignore/i;\nconst idRegexp = /\\bmso-list:[^;]*\\bl(\\d+)/i;\nconst indentRegexp = /\\bmso-list:[^;]*\\blevel(\\d+)/i;\nconst parseListItem = (element, html) => {\n  const style = element.getAttribute('style');\n  const idMatch = style?.match(idRegexp);\n  if (!idMatch) {\n    return null;\n  }\n  const id = Number(idMatch[1]);\n  const indentMatch = style?.match(indentRegexp);\n  const indent = indentMatch ? Number(indentMatch[1]) : 1;\n  const typeRegexp = new RegExp(`@list l${id}:level${indent}\\\\s*\\\\{[^\\\\}]*mso-level-number-format:\\\\s*([\\\\w-]+)`, 'i');\n  const typeMatch = html.match(typeRegexp);\n  const type = typeMatch && typeMatch[1] === 'bullet' ? 'bullet' : 'ordered';\n  return {\n    id,\n    indent,\n    type,\n    element\n  };\n};\n\n// list items are represented as `p` tags with styles like `mso-list: l0 level1` where:\n// 1. \"0\" in \"l0\" means the list item id;\n// 2. \"1\" in \"level1\" means the indent level, starting from 1.\nconst normalizeListItem = doc => {\n  const msoList = Array.from(doc.querySelectorAll('[style*=mso-list]'));\n  const ignored = [];\n  const others = [];\n  msoList.forEach(node => {\n    const shouldIgnore = (node.getAttribute('style') || '').match(ignoreRegexp);\n    if (shouldIgnore) {\n      ignored.push(node);\n    } else {\n      others.push(node);\n    }\n  });\n\n  // Each list item contains a marker wrapped with \"mso-list: Ignore\".\n  ignored.forEach(node => node.parentNode?.removeChild(node));\n\n  // The list stype is not defined inline with the tag, instead, it's in the\n  // style tag so we need to pass the html as a string.\n  const html = doc.documentElement.innerHTML;\n  const listItems = others.map(element => parseListItem(element, html)).filter(parsed => parsed);\n  while (listItems.length) {\n    const childListItems = [];\n    let current = listItems.shift();\n    // Group continuous items into the same group (aka \"ul\")\n    while (current) {\n      childListItems.push(current);\n      current = listItems.length && listItems[0]?.element === current.element.nextElementSibling &&\n      // Different id means the next item doesn't belong to this group.\n      listItems[0].id === current.id ? listItems.shift() : null;\n    }\n    const ul = document.createElement('ul');\n    childListItems.forEach(listItem => {\n      const li = document.createElement('li');\n      li.setAttribute('data-list', listItem.type);\n      if (listItem.indent > 1) {\n        li.setAttribute('class', `ql-indent-${listItem.indent - 1}`);\n      }\n      li.innerHTML = listItem.element.innerHTML;\n      ul.appendChild(li);\n    });\n    const element = childListItems[0]?.element;\n    const {\n      parentNode\n    } = element ?? {};\n    if (element) {\n      parentNode?.replaceChild(ul, element);\n    }\n    childListItems.slice(1).forEach(_ref => {\n      let {\n        element: e\n      } = _ref;\n      parentNode?.removeChild(e);\n    });\n  }\n};\nexport default function normalize(doc) {\n  if (doc.documentElement.getAttribute('xmlns:w') === 'urn:schemas-microsoft-com:office:word') {\n    normalizeListItem(doc);\n  }\n}", "map": {"version": 3, "names": ["ignoreRegexp", "idRegexp", "indentRegexp", "parseListItem", "element", "html", "style", "getAttribute", "idMatch", "match", "id", "Number", "indentMatch", "indent", "typeRegexp", "RegExp", "typeMatch", "type", "normalizeListItem", "doc", "msoList", "Array", "from", "querySelectorAll", "ignored", "others", "for<PERSON>ach", "node", "shouldIgnore", "push", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "documentElement", "innerHTML", "listItems", "map", "filter", "parsed", "length", "childListItems", "current", "shift", "nextElement<PERSON><PERSON>ling", "ul", "document", "createElement", "listItem", "li", "setAttribute", "append<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "slice", "_ref", "e", "normalize"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill/modules/normalizeExternalHTML/normalizers/msWord.js"], "sourcesContent": ["const ignoreRegexp = /\\bmso-list:[^;]*ignore/i;\nconst idRegexp = /\\bmso-list:[^;]*\\bl(\\d+)/i;\nconst indentRegexp = /\\bmso-list:[^;]*\\blevel(\\d+)/i;\nconst parseListItem = (element, html) => {\n  const style = element.getAttribute('style');\n  const idMatch = style?.match(idRegexp);\n  if (!idMatch) {\n    return null;\n  }\n  const id = Number(idMatch[1]);\n  const indentMatch = style?.match(indentRegexp);\n  const indent = indentMatch ? Number(indentMatch[1]) : 1;\n  const typeRegexp = new RegExp(`@list l${id}:level${indent}\\\\s*\\\\{[^\\\\}]*mso-level-number-format:\\\\s*([\\\\w-]+)`, 'i');\n  const typeMatch = html.match(typeRegexp);\n  const type = typeMatch && typeMatch[1] === 'bullet' ? 'bullet' : 'ordered';\n  return {\n    id,\n    indent,\n    type,\n    element\n  };\n};\n\n// list items are represented as `p` tags with styles like `mso-list: l0 level1` where:\n// 1. \"0\" in \"l0\" means the list item id;\n// 2. \"1\" in \"level1\" means the indent level, starting from 1.\nconst normalizeListItem = doc => {\n  const msoList = Array.from(doc.querySelectorAll('[style*=mso-list]'));\n  const ignored = [];\n  const others = [];\n  msoList.forEach(node => {\n    const shouldIgnore = (node.getAttribute('style') || '').match(ignoreRegexp);\n    if (shouldIgnore) {\n      ignored.push(node);\n    } else {\n      others.push(node);\n    }\n  });\n\n  // Each list item contains a marker wrapped with \"mso-list: Ignore\".\n  ignored.forEach(node => node.parentNode?.removeChild(node));\n\n  // The list stype is not defined inline with the tag, instead, it's in the\n  // style tag so we need to pass the html as a string.\n  const html = doc.documentElement.innerHTML;\n  const listItems = others.map(element => parseListItem(element, html)).filter(parsed => parsed);\n  while (listItems.length) {\n    const childListItems = [];\n    let current = listItems.shift();\n    // Group continuous items into the same group (aka \"ul\")\n    while (current) {\n      childListItems.push(current);\n      current = listItems.length && listItems[0]?.element === current.element.nextElementSibling &&\n      // Different id means the next item doesn't belong to this group.\n      listItems[0].id === current.id ? listItems.shift() : null;\n    }\n    const ul = document.createElement('ul');\n    childListItems.forEach(listItem => {\n      const li = document.createElement('li');\n      li.setAttribute('data-list', listItem.type);\n      if (listItem.indent > 1) {\n        li.setAttribute('class', `ql-indent-${listItem.indent - 1}`);\n      }\n      li.innerHTML = listItem.element.innerHTML;\n      ul.appendChild(li);\n    });\n    const element = childListItems[0]?.element;\n    const {\n      parentNode\n    } = element ?? {};\n    if (element) {\n      parentNode?.replaceChild(ul, element);\n    }\n    childListItems.slice(1).forEach(_ref => {\n      let {\n        element: e\n      } = _ref;\n      parentNode?.removeChild(e);\n    });\n  }\n};\nexport default function normalize(doc) {\n  if (doc.documentElement.getAttribute('xmlns:w') === 'urn:schemas-microsoft-com:office:word') {\n    normalizeListItem(doc);\n  }\n}\n"], "mappings": "AAAA,MAAMA,YAAY,GAAG,yBAAyB;AAC9C,MAAMC,QAAQ,GAAG,2BAA2B;AAC5C,MAAMC,YAAY,GAAG,+BAA+B;AACpD,MAAMC,aAAa,GAAGA,CAACC,OAAO,EAAEC,IAAI,KAAK;EACvC,MAAMC,KAAK,GAAGF,OAAO,CAACG,YAAY,CAAC,OAAO,CAAC;EAC3C,MAAMC,OAAO,GAAGF,KAAK,EAAEG,KAAK,CAACR,QAAQ,CAAC;EACtC,IAAI,CAACO,OAAO,EAAE;IACZ,OAAO,IAAI;EACb;EACA,MAAME,EAAE,GAAGC,MAAM,CAACH,OAAO,CAAC,CAAC,CAAC,CAAC;EAC7B,MAAMI,WAAW,GAAGN,KAAK,EAAEG,KAAK,CAACP,YAAY,CAAC;EAC9C,MAAMW,MAAM,GAAGD,WAAW,GAAGD,MAAM,CAACC,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;EACvD,MAAME,UAAU,GAAG,IAAIC,MAAM,CAAE,UAASL,EAAG,SAAQG,MAAO,qDAAoD,EAAE,GAAG,CAAC;EACpH,MAAMG,SAAS,GAAGX,IAAI,CAACI,KAAK,CAACK,UAAU,CAAC;EACxC,MAAMG,IAAI,GAAGD,SAAS,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,QAAQ,GAAG,SAAS;EAC1E,OAAO;IACLN,EAAE;IACFG,MAAM;IACNI,IAAI;IACJb;EACF,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,MAAMc,iBAAiB,GAAGC,GAAG,IAAI;EAC/B,MAAMC,OAAO,GAAGC,KAAK,CAACC,IAAI,CAACH,GAAG,CAACI,gBAAgB,CAAC,mBAAmB,CAAC,CAAC;EACrE,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAMC,MAAM,GAAG,EAAE;EACjBL,OAAO,CAACM,OAAO,CAACC,IAAI,IAAI;IACtB,MAAMC,YAAY,GAAG,CAACD,IAAI,CAACpB,YAAY,CAAC,OAAO,CAAC,IAAI,EAAE,EAAEE,KAAK,CAACT,YAAY,CAAC;IAC3E,IAAI4B,YAAY,EAAE;MAChBJ,OAAO,CAACK,IAAI,CAACF,IAAI,CAAC;IACpB,CAAC,MAAM;MACLF,MAAM,CAACI,IAAI,CAACF,IAAI,CAAC;IACnB;EACF,CAAC,CAAC;;EAEF;EACAH,OAAO,CAACE,OAAO,CAACC,IAAI,IAAIA,IAAI,CAACG,UAAU,EAAEC,WAAW,CAACJ,IAAI,CAAC,CAAC;;EAE3D;EACA;EACA,MAAMtB,IAAI,GAAGc,GAAG,CAACa,eAAe,CAACC,SAAS;EAC1C,MAAMC,SAAS,GAAGT,MAAM,CAACU,GAAG,CAAC/B,OAAO,IAAID,aAAa,CAACC,OAAO,EAAEC,IAAI,CAAC,CAAC,CAAC+B,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAC;EAC9F,OAAOH,SAAS,CAACI,MAAM,EAAE;IACvB,MAAMC,cAAc,GAAG,EAAE;IACzB,IAAIC,OAAO,GAAGN,SAAS,CAACO,KAAK,CAAC,CAAC;IAC/B;IACA,OAAOD,OAAO,EAAE;MACdD,cAAc,CAACV,IAAI,CAACW,OAAO,CAAC;MAC5BA,OAAO,GAAGN,SAAS,CAACI,MAAM,IAAIJ,SAAS,CAAC,CAAC,CAAC,EAAE9B,OAAO,KAAKoC,OAAO,CAACpC,OAAO,CAACsC,kBAAkB;MAC1F;MACAR,SAAS,CAAC,CAAC,CAAC,CAACxB,EAAE,KAAK8B,OAAO,CAAC9B,EAAE,GAAGwB,SAAS,CAACO,KAAK,CAAC,CAAC,GAAG,IAAI;IAC3D;IACA,MAAME,EAAE,GAAGC,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;IACvCN,cAAc,CAACb,OAAO,CAACoB,QAAQ,IAAI;MACjC,MAAMC,EAAE,GAAGH,QAAQ,CAACC,aAAa,CAAC,IAAI,CAAC;MACvCE,EAAE,CAACC,YAAY,CAAC,WAAW,EAAEF,QAAQ,CAAC7B,IAAI,CAAC;MAC3C,IAAI6B,QAAQ,CAACjC,MAAM,GAAG,CAAC,EAAE;QACvBkC,EAAE,CAACC,YAAY,CAAC,OAAO,EAAG,aAAYF,QAAQ,CAACjC,MAAM,GAAG,CAAE,EAAC,CAAC;MAC9D;MACAkC,EAAE,CAACd,SAAS,GAAGa,QAAQ,CAAC1C,OAAO,CAAC6B,SAAS;MACzCU,EAAE,CAACM,WAAW,CAACF,EAAE,CAAC;IACpB,CAAC,CAAC;IACF,MAAM3C,OAAO,GAAGmC,cAAc,CAAC,CAAC,CAAC,EAAEnC,OAAO;IAC1C,MAAM;MACJ0B;IACF,CAAC,GAAG1B,OAAO,IAAI,CAAC,CAAC;IACjB,IAAIA,OAAO,EAAE;MACX0B,UAAU,EAAEoB,YAAY,CAACP,EAAE,EAAEvC,OAAO,CAAC;IACvC;IACAmC,cAAc,CAACY,KAAK,CAAC,CAAC,CAAC,CAACzB,OAAO,CAAC0B,IAAI,IAAI;MACtC,IAAI;QACFhD,OAAO,EAAEiD;MACX,CAAC,GAAGD,IAAI;MACRtB,UAAU,EAAEC,WAAW,CAACsB,CAAC,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF,CAAC;AACD,eAAe,SAASC,SAASA,CAACnC,GAAG,EAAE;EACrC,IAAIA,GAAG,CAACa,eAAe,CAACzB,YAAY,CAAC,SAAS,CAAC,KAAK,uCAAuC,EAAE;IAC3FW,iBAAiB,CAACC,GAAG,CAAC;EACxB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}