{"ast": null, "code": "import createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is less than or equal to `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than or equal to\n *  `other`, else `false`.\n * @see _.gte\n * @example\n *\n * _.lte(1, 3);\n * // => true\n *\n * _.lte(3, 3);\n * // => true\n *\n * _.lte(3, 1);\n * // => false\n */\nvar lte = createRelationalOperation(function (value, other) {\n  return value <= other;\n});\nexport default lte;", "map": {"version": 3, "names": ["createRelationalOperation", "lte", "value", "other"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/lte.js"], "sourcesContent": ["import createRelationalOperation from './_createRelationalOperation.js';\n\n/**\n * Checks if `value` is less than or equal to `other`.\n *\n * @static\n * @memberOf _\n * @since 3.9.0\n * @category Lang\n * @param {*} value The value to compare.\n * @param {*} other The other value to compare.\n * @returns {boolean} Returns `true` if `value` is less than or equal to\n *  `other`, else `false`.\n * @see _.gte\n * @example\n *\n * _.lte(1, 3);\n * // => true\n *\n * _.lte(3, 3);\n * // => true\n *\n * _.lte(3, 1);\n * // => false\n */\nvar lte = createRelationalOperation(function(value, other) {\n  return value <= other;\n});\n\nexport default lte;\n"], "mappings": "AAAA,OAAOA,yBAAyB,MAAM,iCAAiC;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,GAAG,GAAGD,yBAAyB,CAAC,UAASE,KAAK,EAAEC,KAAK,EAAE;EACzD,OAAOD,KAAK,IAAIC,KAAK;AACvB,CAAC,CAAC;AAEF,eAAeF,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}