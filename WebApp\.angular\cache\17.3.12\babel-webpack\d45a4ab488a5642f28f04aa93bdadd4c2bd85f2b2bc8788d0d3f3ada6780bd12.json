{"ast": null, "code": "import basePullAll from './_basePullAll.js';\n\n/**\n * This method is like `_.pullAll` except that it accepts `comparator` which\n * is invoked to compare elements of `array` to `values`. The comparator is\n * invoked with two arguments: (arrVal, othVal).\n *\n * **Note:** Unlike `_.differenceWith`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.6.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [{ 'x': 1, 'y': 2 }, { 'x': 3, 'y': 4 }, { 'x': 5, 'y': 6 }];\n *\n * _.pullAllWith(array, [{ 'x': 3, 'y': 4 }], _.isEqual);\n * console.log(array);\n * // => [{ 'x': 1, 'y': 2 }, { 'x': 5, 'y': 6 }]\n */\nfunction pullAllWith(array, values, comparator) {\n  return array && array.length && values && values.length ? basePullAll(array, values, undefined, comparator) : array;\n}\nexport default pullAllWith;", "map": {"version": 3, "names": ["basePullAll", "pullAllWith", "array", "values", "comparator", "length", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/pullAllWith.js"], "sourcesContent": ["import basePullAll from './_basePullAll.js';\n\n/**\n * This method is like `_.pullAll` except that it accepts `comparator` which\n * is invoked to compare elements of `array` to `values`. The comparator is\n * invoked with two arguments: (arrVal, othVal).\n *\n * **Note:** Unlike `_.differenceWith`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.6.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [{ 'x': 1, 'y': 2 }, { 'x': 3, 'y': 4 }, { 'x': 5, 'y': 6 }];\n *\n * _.pullAllWith(array, [{ 'x': 3, 'y': 4 }], _.isEqual);\n * console.log(array);\n * // => [{ 'x': 1, 'y': 2 }, { 'x': 5, 'y': 6 }]\n */\nfunction pullAllWith(array, values, comparator) {\n  return (array && array.length && values && values.length)\n    ? basePullAll(array, values, undefined, comparator)\n    : array;\n}\n\nexport default pullAllWith;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;EAC9C,OAAQF,KAAK,IAAIA,KAAK,CAACG,MAAM,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GACpDL,WAAW,CAACE,KAAK,EAAEC,MAAM,EAAEG,SAAS,EAAEF,UAAU,CAAC,GACjDF,KAAK;AACX;AAEA,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}