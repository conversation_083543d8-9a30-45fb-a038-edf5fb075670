{"ast": null, "code": "import baseIndexOf from './_baseIndexOf.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport toInteger from './toInteger.js';\nimport values from './values.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Checks if `value` is in `collection`. If `collection` is a string, it's\n * checked for a substring of `value`, otherwise\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * is used for equality comparisons. If `fromIndex` is negative, it's used as\n * the offset from the end of `collection`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=0] The index to search from.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {boolean} Returns `true` if `value` is found, else `false`.\n * @example\n *\n * _.includes([1, 2, 3], 1);\n * // => true\n *\n * _.includes([1, 2, 3], 1, 2);\n * // => false\n *\n * _.includes({ 'a': 1, 'b': 2 }, 1);\n * // => true\n *\n * _.includes('abcd', 'bc');\n * // => true\n */\nfunction includes(collection, value, fromIndex, guard) {\n  collection = isArrayLike(collection) ? collection : values(collection);\n  fromIndex = fromIndex && !guard ? toInteger(fromIndex) : 0;\n  var length = collection.length;\n  if (fromIndex < 0) {\n    fromIndex = nativeMax(length + fromIndex, 0);\n  }\n  return isString(collection) ? fromIndex <= length && collection.indexOf(value, fromIndex) > -1 : !!length && baseIndexOf(collection, value, fromIndex) > -1;\n}\nexport default includes;", "map": {"version": 3, "names": ["baseIndexOf", "isArrayLike", "isString", "toInteger", "values", "nativeMax", "Math", "max", "includes", "collection", "value", "fromIndex", "guard", "length", "indexOf"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/includes.js"], "sourcesContent": ["import baseIndexOf from './_baseIndexOf.js';\nimport isArrayLike from './isArrayLike.js';\nimport isString from './isString.js';\nimport toInteger from './toInteger.js';\nimport values from './values.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Checks if `value` is in `collection`. If `collection` is a string, it's\n * checked for a substring of `value`, otherwise\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * is used for equality comparisons. If `fromIndex` is negative, it's used as\n * the offset from the end of `collection`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Collection\n * @param {Array|Object|string} collection The collection to inspect.\n * @param {*} value The value to search for.\n * @param {number} [fromIndex=0] The index to search from.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.reduce`.\n * @returns {boolean} Returns `true` if `value` is found, else `false`.\n * @example\n *\n * _.includes([1, 2, 3], 1);\n * // => true\n *\n * _.includes([1, 2, 3], 1, 2);\n * // => false\n *\n * _.includes({ 'a': 1, 'b': 2 }, 1);\n * // => true\n *\n * _.includes('abcd', 'bc');\n * // => true\n */\nfunction includes(collection, value, fromIndex, guard) {\n  collection = isArrayLike(collection) ? collection : values(collection);\n  fromIndex = (fromIndex && !guard) ? toInteger(fromIndex) : 0;\n\n  var length = collection.length;\n  if (fromIndex < 0) {\n    fromIndex = nativeMax(length + fromIndex, 0);\n  }\n  return isString(collection)\n    ? (fromIndex <= length && collection.indexOf(value, fromIndex) > -1)\n    : (!!length && baseIndexOf(collection, value, fromIndex) > -1);\n}\n\nexport default includes;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,WAAW,MAAM,kBAAkB;AAC1C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,UAAU,EAAEC,KAAK,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACrDH,UAAU,GAAGR,WAAW,CAACQ,UAAU,CAAC,GAAGA,UAAU,GAAGL,MAAM,CAACK,UAAU,CAAC;EACtEE,SAAS,GAAIA,SAAS,IAAI,CAACC,KAAK,GAAIT,SAAS,CAACQ,SAAS,CAAC,GAAG,CAAC;EAE5D,IAAIE,MAAM,GAAGJ,UAAU,CAACI,MAAM;EAC9B,IAAIF,SAAS,GAAG,CAAC,EAAE;IACjBA,SAAS,GAAGN,SAAS,CAACQ,MAAM,GAAGF,SAAS,EAAE,CAAC,CAAC;EAC9C;EACA,OAAOT,QAAQ,CAACO,UAAU,CAAC,GACtBE,SAAS,IAAIE,MAAM,IAAIJ,UAAU,CAACK,OAAO,CAACJ,KAAK,EAAEC,SAAS,CAAC,GAAG,CAAC,CAAC,GAChE,CAAC,CAACE,MAAM,IAAIb,WAAW,CAACS,UAAU,EAAEC,KAAK,EAAEC,SAAS,CAAC,GAAG,CAAC,CAAE;AAClE;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}