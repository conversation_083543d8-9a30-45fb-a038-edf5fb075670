<div
  class="h-full bg-[var(--background-white)] text-[var(--text-dark)] flex showSidebar transition-all"
  [ngClass]="{
    'is-admin-mode': isSettingsMode,
    'is-daily-insight-mode': isDailyInsightMode,
    'sidebar-collapsed': !togglingService.isNavbarOpen,
    'dark-theme': themeService.isDarkMode(),
    'light-theme': !themeService.isDarkMode()
  }"
>
  <div class="flex w-full">
    <!-- Left Icon Bar -->
    <div
      class="flex flex-col justify-between h-full sidebar-main"
      style="
        min-width: 4rem;
        max-width: 4rem;
        display: flex;
        align-items: center;
      "
    >
      <div class="*:cursor-pointer *:transition-all *:duration-300 w-full">
        <!-- Daily Insight Icon - Clean and eye-catching style -->
        <div
          class="sidebar-nav-item relative flex flex-col items-center"
          (click)="navigateToDailyInsight($event)"
          [class.active]="isDailyInsightMode"
        >
          <div
            class="icon-container"
            [ngClass]="{ 'bg-[#343541]': isDailyInsightMode }"
          >
            <i
              class="ri-dashboard-line text-xl"
              [ngClass]="
                isDailyInsightMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
          </div>
          <span
            class="button-text text-center"
            [ngClass]="
              isDailyInsightMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            "
            >Daily<br />Insights</span
          >
        </div>

        <div
          class="sidebar-nav-item relative flex flex-col items-center"
          (click)="navigateToNotes($event)"
          [class.active]="isNotesMode"
        >
          <div
            class="icon-container"
            [ngClass]="{ 'bg-[#343541]': isNotesMode }"
          >
            <i
              class="ri-sticky-note-line text-xl"
              [ngClass]="
                isNotesMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
          </div>
          <span
            class="button-text text-center"
            [ngClass]="
              isNotesMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            "
            >Notes</span
          >
        </div>

        <div
          class="sidebar-nav-item relative flex flex-col items-center"
          (click)="navigateToChat($event)"
          [class.active]="isChatMode"
        >
          <div
            class="icon-container"
            [ngClass]="{ 'bg-[#343541]': isChatMode }"
          >
            <i
              class="ri-chat-ai-line text-xl"
              [ngClass]="
                isChatMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
          </div>
          <span
            class="button-text text-center"
            [ngClass]="
              isChatMode
                ? themeService.isDarkMode()
                  ? 'text-[var(--secondary-purple)]'
                  : 'text-[var(--primary-purple)]'
                : ''
            "
            >Chat</span
          >
        </div>
      </div>
      <div class="*:cursor-pointer *:transition-all *:duration-300 w-full">
        <div class="relative">
          <div
            class="sidebar-nav-item relative flex flex-col items-center"
            [routerLink]="['/settings', 'prompt-library']"
            [class.active]="isSettingsMode"
            (click)="
              isSettingsMode && !togglingService.isNavbarOpen
                ? togglingService.toggleNavbar()
                : (chatListService.chatId = 0)
            "
          >
            <div
              class="icon-container"
              [ngClass]="{ 'bg-[#343541]': isSettingsMode }"
            >
              <i
                class="ri-settings-3-line text-xl"
                [ngClass]="
                  isSettingsMode
                    ? themeService.isDarkMode()
                      ? 'text-[var(--secondary-purple)]'
                      : 'text-[var(--primary-purple)]'
                    : 'text-white'
                "
              ></i>
            </div>
            <span
              class="button-text text-center"
              [ngClass]="
                isSettingsMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : ''
              "
              >Settings</span
            >
          </div>

          <div
            class="sidebar-nav-item relative flex flex-col items-center"
            [routerLink]="['/workspaces']"
            [class.active]="isWorkspaceMode"
            (click)="navigateToWorkspace($event)"
          >
            <div
              class="icon-container"
              [ngClass]="{ 'bg-[#343541]': isWorkspaceMode }"
            >
              <i
                class="ri-home-line text-xl"
                [ngClass]="
                  isWorkspaceMode
                    ? themeService.isDarkMode()
                      ? 'text-[var(--secondary-purple)]'
                      : 'text-[var(--primary-purple)]'
                    : 'text-white'
                "
              ></i>
            </div>
            <span
              class="button-text text-center"
              [ngClass]="
                isWorkspaceMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : ''
              "
              >Workspaces</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Expanded Sidebar (Shows on hover only when mini sidebar is active) -->
    <div
      class="expanded-sidebar"
      [ngClass]="{ hidden: togglingService.isNavbarOpen }"
    >
      <div class="p-4 sidebar-expanded-content h-full">
        <div class="mb-6">
          <h3
            class="text-lg font-semibold text-white mb-4 border-b border-[#3a3a45] pb-2"
          >
            Navigation
          </h3>

          <!-- Daily Insights - Keep this in the sidebar navigation -->
          <div
            class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-[#3a3a45] cursor-pointer transition-all mb-3 relative"
            [ngClass]="{ 'bg-[#1e1e24] bg-opacity-90': isDailyInsightMode }"
            (click)="navigateToDailyInsight($event)"
          >
            <div
              *ngIf="isDailyInsightMode"
              class="absolute left-0 top-0 w-1 h-full bg-[var(--secondary-purple)]"
            ></div>
            <i
              class="ri-dashboard-line text-lg"
              [ngClass]="
                isDailyInsightMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
            <span
              class="font-medium text-sm"
              [ngClass]="
                isDailyInsightMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
              >Daily Insights</span
            >
          </div>

          <!-- All Chats -->
          <div
            class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-[#3a3a45] cursor-pointer transition-all mb-3 relative"
            [ngClass]="{
              'bg-[#1e1e24] bg-opacity-90':
                !isSettingsMode &&
                !isWorkspaceMode &&
                !isDailyInsightMode &&
                !isNotesMode &&
                !isChatMode
            }"
            (click)="navigateToChatPage($event)"
          >
            <div
              *ngIf="
                !isSettingsMode &&
                !isWorkspaceMode &&
                !isDailyInsightMode &&
                !isNotesMode &&
                !isChatMode
              "
              class="absolute left-0 top-0 w-1 h-full bg-[var(--secondary-purple)]"
            ></div>
            <i
              class="ri-chat-ai-line text-lg"
              [ngClass]="
                !isSettingsMode &&
                !isWorkspaceMode &&
                !isDailyInsightMode &&
                !isNotesMode &&
                !isChatMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
            <span
              class="font-medium text-sm"
              [ngClass]="
                !isSettingsMode &&
                !isWorkspaceMode &&
                !isDailyInsightMode &&
                !isNotesMode &&
                !isChatMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
              >All Chats</span
            >
          </div>

          <!-- Notes -->
          <div
            class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-[#3a3a45] cursor-pointer transition-all mb-3 relative"
            [ngClass]="{ 'bg-[#1e1e24] bg-opacity-90': isNotesMode }"
            (click)="navigateToNotes($event)"
          >
            <div
              *ngIf="isNotesMode"
              class="absolute left-0 top-0 w-1 h-full bg-[var(--secondary-purple)]"
            ></div>
            <i
              class="ri-sticky-note-line text-lg"
              [ngClass]="
                isNotesMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
            <span
              class="font-medium text-sm"
              [ngClass]="
                isNotesMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
              >Notes</span
            >
          </div>
        </div>

        <div class="mb-6">
          <h3
            class="text-lg font-semibold text-white mb-4 border-b border-[#3a3a45] pb-2"
          >
            Settings
          </h3>

          <!-- Settings -->
          <div
            class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-[#3a3a45] cursor-pointer transition-all mb-3 relative"
            [ngClass]="{ 'bg-[#1e1e24] bg-opacity-90': isSettingsMode }"
            [routerLink]="['/settings', 'prompt-library']"
            (click)="
              isSettingsMode && !togglingService.isNavbarOpen
                ? togglingService.toggleNavbar()
                : (chatListService.chatId = 0)
            "
          >
            <div
              *ngIf="isSettingsMode"
              class="absolute left-0 top-0 w-1 h-full bg-[var(--secondary-purple)]"
            ></div>
            <i
              class="ri-settings-3-line text-lg"
              [ngClass]="
                isSettingsMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
            <span
              class="font-medium text-sm"
              [ngClass]="
                isSettingsMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
              >Settings</span
            >
          </div>

          <!-- Workspaces -->
          <div
            class="flex items-center gap-3 py-3 px-4 rounded-lg hover:bg-[#3a3a45] cursor-pointer transition-all mb-3 relative"
            [ngClass]="{ 'bg-[#1e1e24] bg-opacity-90': isWorkspaceMode }"
            [routerLink]="['/workspaces']"
            (click)="navigateToWorkspace($event)"
          >
            <div
              *ngIf="isWorkspaceMode"
              class="absolute left-0 top-0 w-1 h-full bg-[var(--secondary-purple)]"
            ></div>
            <i
              class="ri-home-line text-lg"
              [ngClass]="
                isWorkspaceMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
            ></i>
            <span
              class="font-medium text-sm"
              [ngClass]="
                isWorkspaceMode
                  ? themeService.isDarkMode()
                    ? 'text-[var(--secondary-purple)]'
                    : 'text-[var(--primary-purple)]'
                  : 'text-white'
              "
              >Workspaces</span
            >
          </div>
        </div>
      </div>
    </div>

    <!-- Right Content Area -->
    <div class="flex-1 flex flex-col h-full sidebar-content relative">
      <!-- CHAT CONTENT - Show when in daily insight mode or not in admin mode and not in notes mode and not in chat mode -->
      <div
        *ngIf="!isSettingsMode && !isNotesMode && !isChatMode"
        class="flex-1 flex flex-col h-full"
      >
        <!-- Daily Insight Content -->
        <app-daily-insights-sidebar
          *ngIf="isDailyInsightMode"
          [workspaceName]="workspaceName"
        ></app-daily-insights-sidebar>

        <!-- Regular Chat Content - Only show when not in daily insight mode -->
        <app-rightsidebarchat
          *ngIf="!isDailyInsightMode"
          [workspaceName]="workspaceName"
          [userInput]="userInput"
          [activeTab]="activeTab"
          [groupedChats]="groupedChats"
          [filteredGroupedChats]="filteredGroupedChats"
          [pinnedChats]="pinnedChats"
          [favoriteChats]="favoriteChats"
          [archivedChats]="archivedChats"
          [hasMoreMessages]="hasMoreMessages"
          [isAllChatsOpen]="isAllChatsOpen"
          (tabChanged)="toggleTab($event)"
          (loadMore)="loadMoreChatList()"
          (allChatsToggled)="isAllChatsOpen = $event"
          (filteredChatsChanged)="filteredGroupedChats = $event"
        >
        </app-rightsidebarchat>
      </div>

      <!-- NOTES CONTENT - Show when in notes mode -->
      <div *ngIf="isNotesMode" class="flex-1 flex flex-col h-full">
        <app-notes-sidebar [workspaceName]="workspaceName"></app-notes-sidebar>
      </div>

      <!-- AGENT CHAT CONTENT - Show when in chat mode -->
      <div *ngIf="isChatMode" class="flex-1 flex flex-col h-full">
        <app-agent-and-workspace-sidebar></app-agent-and-workspace-sidebar>
      </div>

      <!-- ADMIN CONTENT - Show when in admin mode -->
      <div *ngIf="isSettingsMode" class="flex-1 flex flex-col h-full">
        <app-settingsidebarcomponent
          [activeAdminTab]="activeAdminTab"
        ></app-settingsidebarcomponent>
      </div>
    </div>
  </div>
</div>
