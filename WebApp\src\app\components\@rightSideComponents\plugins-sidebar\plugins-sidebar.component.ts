import { Component, Input, Output, EventEmitter, inject, OnChanges, SimpleChanges, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ThemeService } from '../../../../shared/services/theam.service';
import { PluginResponseDto, PluginServiceProxy } from '../../../../shared/service-proxies/service-proxies';
import { DateTime } from 'luxon';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-plugins-sidebar',
  standalone: true,
  imports: [
    CommonModule
  ],
  templateUrl: './plugins-sidebar.component.html',
  styleUrls: ['./plugins-sidebar.component.css']
})
export class PluginsSidebarComponent implements OnChanges, OnDestroy {
  // Inject services
  themeService = inject(ThemeService);
  private pluginService = inject(PluginServiceProxy);

  // Input properties
  @Input() isPluginSidebarOpen: boolean = false;
  @Input() pluginSidebarTitle: string = 'Agent Plugins';
  @Input() selectedAgent: string = '';

  // Output events
  @Output() onClose = new EventEmitter<void>();
  @Output() onSelectPlugin = new EventEmitter<PluginResponseDto>();

  // Internal state
  agentPlugins: PluginResponseDto[] = [];
  isLoading: boolean = false;
  hasError: boolean = false;
  errorMessage: string = '';

  // Subscription management
  private pluginSubscription?: Subscription;

  /**
   * Lifecycle hook - responds to input property changes
   */
  ngOnChanges(changes: SimpleChanges): void {
    // Load plugins when selectedAgent changes
    if (changes['selectedAgent'] && changes['selectedAgent'].currentValue) {
      const agentName = changes['selectedAgent'].currentValue;
      if (agentName && agentName !== changes['selectedAgent'].previousValue) {
        this.loadPlugins(agentName);
      }
    }
  }

  /**
   * Lifecycle hook - cleanup subscriptions
   */
  ngOnDestroy(): void {
    if (this.pluginSubscription) {
      this.pluginSubscription.unsubscribe();
    }
  }

  /**
   * Getter for showSidebar to match source-references component property name
   * This allows for consistent template usage
   */
  get showSidebar(): boolean {
    return this.isPluginSidebarOpen;
  }

  /**
   * Handles the sidebar close event
   */
  closeSidebar(): void {
    this.onClose.emit();
  }

  /**
   * Alternative method name to match source-references component
   */
  onCloseSidebar(): void {
    this.closeSidebar();
  }

  /**
   * Loads plugins for the specified agent
   * @param agentName The name of the agent to load plugins for
   */
  loadPlugins(agentName: string): void {
    if (!agentName) {
      console.warn('No agent name provided for loading plugins');
      this.resetPluginState();
      return;
    }

    console.log('Loading plugins for agent:', agentName);
    this.isLoading = true;
    this.hasError = false;
    this.errorMessage = '';

    // Clean up previous subscription
    if (this.pluginSubscription) {
      this.pluginSubscription.unsubscribe();
    }

    // Load plugins from API
    this.pluginSubscription = this.pluginService.getByAgentName(agentName).subscribe({
      next: (plugins) => {
        console.log('Loaded plugins for agent:', agentName, plugins);
        this.agentPlugins = plugins || [];
        this.updateSidebarTitle(agentName);
        this.isLoading = false;
        this.hasError = false;

        // Add mock data for demonstration if no plugins are returned
        if (this.agentPlugins.length === 0) {
        }
      },
      error: (error) => {
        console.error('Error loading plugins for agent:', error);
        this.hasError = true;
        this.errorMessage = `Failed to load plugins for ${agentName}`;
        this.isLoading = false;

      },
    });
  }

  /**
   * Resets the plugin state to initial values
   */
  private resetPluginState(): void {
    this.agentPlugins = [];
    this.isLoading = false;
    this.hasError = false;
    this.errorMessage = '';
    this.pluginSidebarTitle = 'Agent Plugins';
  }

  /**
   * Updates the sidebar title with plugin count
   * @param agentName The agent name
   */
  private updateSidebarTitle(agentName: string): void {
    this.pluginSidebarTitle = `${agentName} Plugins (${this.agentPlugins.length})`;
  }

  /**
   * Handles plugin selection
   * @param plugin The selected plugin
   */
  selectPlugin(plugin: PluginResponseDto): void {
    if (!plugin) return;
    this.onSelectPlugin.emit(plugin);
  }

  /**
   * Gets the plugin type color for display
   * @param type The plugin type
   * @returns The color class for the plugin type
   */
  getPluginTypeColor(type: string | undefined): string {
    if (!type) return 'default';
    switch (type.toLowerCase()) {
      case 'openapi':
        return 'blue';
      case 'customplugin':
        return 'green';
      default:
        return 'default';
    }
  }

  /**
   * Gets the plugin type badge class
   * @param type The plugin type
   * @returns The CSS class for the badge
   */
  getPluginTypeBadgeClass(type: string | undefined): string {
    if (!type) return '';
    switch (type.toLowerCase()) {
      case 'openapi':
        return this.themeService.isDarkMode()
          ? 'bg-blue-900 text-blue-300'
          : 'bg-blue-100 text-blue-600';
      case 'customplugin':
        return this.themeService.isDarkMode()
          ? 'bg-green-900 text-green-300'
          : 'bg-green-100 text-green-600';
      default:
        return this.themeService.isDarkMode()
          ? 'bg-gray-700 text-gray-300'
          : 'bg-gray-100 text-gray-600';
    }
  }


  getFunctionList(functions: string | undefined): Array<{ name: string, description: string }> {
    if (!functions) return [];
    try {
      const parsed = JSON.parse(functions);
      if (Array.isArray(parsed)) {
        return parsed.map(func => ({
          name: func.name || func.toString(),
          description: func.description || `Function for ${func.name || func.toString()}`
        })).filter(f => f.name);
      }
      return [];
    } catch {
      // If not JSON, try to split by common delimiters
      return functions.split(/[,;\n]/).map(f => f.trim()).filter(Boolean).map(name => ({
        name: name,
        description: `Function for ${name}`
      }));
    }
  }

}
