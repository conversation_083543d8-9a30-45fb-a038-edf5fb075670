{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nconst cloneDeep = require(\"lodash.clonedeep\");\nconst isEqual = require(\"lodash.isequal\");\nvar AttributeMap;\n(function (AttributeMap) {\n  function compose(a = {}, b = {}, keepNull = false) {\n    if (typeof a !== 'object') {\n      a = {};\n    }\n    if (typeof b !== 'object') {\n      b = {};\n    }\n    let attributes = cloneDeep(b);\n    if (!keepNull) {\n      attributes = Object.keys(attributes).reduce((copy, key) => {\n        if (attributes[key] != null) {\n          copy[key] = attributes[key];\n        }\n        return copy;\n      }, {});\n    }\n    for (const key in a) {\n      if (a[key] !== undefined && b[key] === undefined) {\n        attributes[key] = a[key];\n      }\n    }\n    return Object.keys(attributes).length > 0 ? attributes : undefined;\n  }\n  AttributeMap.compose = compose;\n  function diff(a = {}, b = {}) {\n    if (typeof a !== 'object') {\n      a = {};\n    }\n    if (typeof b !== 'object') {\n      b = {};\n    }\n    const attributes = Object.keys(a).concat(Object.keys(b)).reduce((attrs, key) => {\n      if (!isEqual(a[key], b[key])) {\n        attrs[key] = b[key] === undefined ? null : b[key];\n      }\n      return attrs;\n    }, {});\n    return Object.keys(attributes).length > 0 ? attributes : undefined;\n  }\n  AttributeMap.diff = diff;\n  function invert(attr = {}, base = {}) {\n    attr = attr || {};\n    const baseInverted = Object.keys(base).reduce((memo, key) => {\n      if (base[key] !== attr[key] && attr[key] !== undefined) {\n        memo[key] = base[key];\n      }\n      return memo;\n    }, {});\n    return Object.keys(attr).reduce((memo, key) => {\n      if (attr[key] !== base[key] && base[key] === undefined) {\n        memo[key] = null;\n      }\n      return memo;\n    }, baseInverted);\n  }\n  AttributeMap.invert = invert;\n  function transform(a, b, priority = false) {\n    if (typeof a !== 'object') {\n      return b;\n    }\n    if (typeof b !== 'object') {\n      return undefined;\n    }\n    if (!priority) {\n      return b; // b simply overwrites us without priority\n    }\n    const attributes = Object.keys(b).reduce((attrs, key) => {\n      if (a[key] === undefined) {\n        attrs[key] = b[key]; // null is a valid value\n      }\n      return attrs;\n    }, {});\n    return Object.keys(attributes).length > 0 ? attributes : undefined;\n  }\n  AttributeMap.transform = transform;\n})(AttributeMap || (AttributeMap = {}));\nexports.default = AttributeMap;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "cloneDeep", "require", "isEqual", "AttributeMap", "compose", "a", "b", "keep<PERSON>ull", "attributes", "keys", "reduce", "copy", "key", "undefined", "length", "diff", "concat", "attrs", "invert", "attr", "base", "baseInverted", "memo", "transform", "priority", "default"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/quill-delta/dist/AttributeMap.js"], "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst cloneDeep = require(\"lodash.clonedeep\");\nconst isEqual = require(\"lodash.isequal\");\nvar AttributeMap;\n(function (AttributeMap) {\n    function compose(a = {}, b = {}, keepNull = false) {\n        if (typeof a !== 'object') {\n            a = {};\n        }\n        if (typeof b !== 'object') {\n            b = {};\n        }\n        let attributes = cloneDeep(b);\n        if (!keepNull) {\n            attributes = Object.keys(attributes).reduce((copy, key) => {\n                if (attributes[key] != null) {\n                    copy[key] = attributes[key];\n                }\n                return copy;\n            }, {});\n        }\n        for (const key in a) {\n            if (a[key] !== undefined && b[key] === undefined) {\n                attributes[key] = a[key];\n            }\n        }\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.compose = compose;\n    function diff(a = {}, b = {}) {\n        if (typeof a !== 'object') {\n            a = {};\n        }\n        if (typeof b !== 'object') {\n            b = {};\n        }\n        const attributes = Object.keys(a)\n            .concat(Object.keys(b))\n            .reduce((attrs, key) => {\n            if (!isEqual(a[key], b[key])) {\n                attrs[key] = b[key] === undefined ? null : b[key];\n            }\n            return attrs;\n        }, {});\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.diff = diff;\n    function invert(attr = {}, base = {}) {\n        attr = attr || {};\n        const baseInverted = Object.keys(base).reduce((memo, key) => {\n            if (base[key] !== attr[key] && attr[key] !== undefined) {\n                memo[key] = base[key];\n            }\n            return memo;\n        }, {});\n        return Object.keys(attr).reduce((memo, key) => {\n            if (attr[key] !== base[key] && base[key] === undefined) {\n                memo[key] = null;\n            }\n            return memo;\n        }, baseInverted);\n    }\n    AttributeMap.invert = invert;\n    function transform(a, b, priority = false) {\n        if (typeof a !== 'object') {\n            return b;\n        }\n        if (typeof b !== 'object') {\n            return undefined;\n        }\n        if (!priority) {\n            return b; // b simply overwrites us without priority\n        }\n        const attributes = Object.keys(b).reduce((attrs, key) => {\n            if (a[key] === undefined) {\n                attrs[key] = b[key]; // null is a valid value\n            }\n            return attrs;\n        }, {});\n        return Object.keys(attributes).length > 0 ? attributes : undefined;\n    }\n    AttributeMap.transform = transform;\n})(AttributeMap || (AttributeMap = {}));\nexports.default = AttributeMap;\n"], "mappings": "AAAA,YAAY;;AACZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAAEC,KAAK,EAAE;AAAK,CAAC,CAAC;AAC7D,MAAMC,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,MAAMC,OAAO,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AACzC,IAAIE,YAAY;AAChB,CAAC,UAAUA,YAAY,EAAE;EACrB,SAASC,OAAOA,CAACC,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEC,QAAQ,GAAG,KAAK,EAAE;IAC/C,IAAI,OAAOF,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAG,CAAC,CAAC;IACV;IACA,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAG,CAAC,CAAC;IACV;IACA,IAAIE,UAAU,GAAGR,SAAS,CAACM,CAAC,CAAC;IAC7B,IAAI,CAACC,QAAQ,EAAE;MACXC,UAAU,GAAGZ,MAAM,CAACa,IAAI,CAACD,UAAU,CAAC,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,KAAK;QACvD,IAAIJ,UAAU,CAACI,GAAG,CAAC,IAAI,IAAI,EAAE;UACzBD,IAAI,CAACC,GAAG,CAAC,GAAGJ,UAAU,CAACI,GAAG,CAAC;QAC/B;QACA,OAAOD,IAAI;MACf,CAAC,EAAE,CAAC,CAAC,CAAC;IACV;IACA,KAAK,MAAMC,GAAG,IAAIP,CAAC,EAAE;MACjB,IAAIA,CAAC,CAACO,GAAG,CAAC,KAAKC,SAAS,IAAIP,CAAC,CAACM,GAAG,CAAC,KAAKC,SAAS,EAAE;QAC9CL,UAAU,CAACI,GAAG,CAAC,GAAGP,CAAC,CAACO,GAAG,CAAC;MAC5B;IACJ;IACA,OAAOhB,MAAM,CAACa,IAAI,CAACD,UAAU,CAAC,CAACM,MAAM,GAAG,CAAC,GAAGN,UAAU,GAAGK,SAAS;EACtE;EACAV,YAAY,CAACC,OAAO,GAAGA,OAAO;EAC9B,SAASW,IAAIA,CAACV,CAAC,GAAG,CAAC,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1B,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAG,CAAC,CAAC;IACV;IACA,IAAI,OAAOC,CAAC,KAAK,QAAQ,EAAE;MACvBA,CAAC,GAAG,CAAC,CAAC;IACV;IACA,MAAME,UAAU,GAAGZ,MAAM,CAACa,IAAI,CAACJ,CAAC,CAAC,CAC5BW,MAAM,CAACpB,MAAM,CAACa,IAAI,CAACH,CAAC,CAAC,CAAC,CACtBI,MAAM,CAAC,CAACO,KAAK,EAAEL,GAAG,KAAK;MACxB,IAAI,CAACV,OAAO,CAACG,CAAC,CAACO,GAAG,CAAC,EAAEN,CAAC,CAACM,GAAG,CAAC,CAAC,EAAE;QAC1BK,KAAK,CAACL,GAAG,CAAC,GAAGN,CAAC,CAACM,GAAG,CAAC,KAAKC,SAAS,GAAG,IAAI,GAAGP,CAAC,CAACM,GAAG,CAAC;MACrD;MACA,OAAOK,KAAK;IAChB,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,OAAOrB,MAAM,CAACa,IAAI,CAACD,UAAU,CAAC,CAACM,MAAM,GAAG,CAAC,GAAGN,UAAU,GAAGK,SAAS;EACtE;EACAV,YAAY,CAACY,IAAI,GAAGA,IAAI;EACxB,SAASG,MAAMA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAEC,IAAI,GAAG,CAAC,CAAC,EAAE;IAClCD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;IACjB,MAAME,YAAY,GAAGzB,MAAM,CAACa,IAAI,CAACW,IAAI,CAAC,CAACV,MAAM,CAAC,CAACY,IAAI,EAAEV,GAAG,KAAK;MACzD,IAAIQ,IAAI,CAACR,GAAG,CAAC,KAAKO,IAAI,CAACP,GAAG,CAAC,IAAIO,IAAI,CAACP,GAAG,CAAC,KAAKC,SAAS,EAAE;QACpDS,IAAI,CAACV,GAAG,CAAC,GAAGQ,IAAI,CAACR,GAAG,CAAC;MACzB;MACA,OAAOU,IAAI;IACf,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,OAAO1B,MAAM,CAACa,IAAI,CAACU,IAAI,CAAC,CAACT,MAAM,CAAC,CAACY,IAAI,EAAEV,GAAG,KAAK;MAC3C,IAAIO,IAAI,CAACP,GAAG,CAAC,KAAKQ,IAAI,CAACR,GAAG,CAAC,IAAIQ,IAAI,CAACR,GAAG,CAAC,KAAKC,SAAS,EAAE;QACpDS,IAAI,CAACV,GAAG,CAAC,GAAG,IAAI;MACpB;MACA,OAAOU,IAAI;IACf,CAAC,EAAED,YAAY,CAAC;EACpB;EACAlB,YAAY,CAACe,MAAM,GAAGA,MAAM;EAC5B,SAASK,SAASA,CAAClB,CAAC,EAAEC,CAAC,EAAEkB,QAAQ,GAAG,KAAK,EAAE;IACvC,IAAI,OAAOnB,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAOC,CAAC;IACZ;IACA,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE;MACvB,OAAOO,SAAS;IACpB;IACA,IAAI,CAACW,QAAQ,EAAE;MACX,OAAOlB,CAAC,CAAC,CAAC;IACd;IACA,MAAME,UAAU,GAAGZ,MAAM,CAACa,IAAI,CAACH,CAAC,CAAC,CAACI,MAAM,CAAC,CAACO,KAAK,EAAEL,GAAG,KAAK;MACrD,IAAIP,CAAC,CAACO,GAAG,CAAC,KAAKC,SAAS,EAAE;QACtBI,KAAK,CAACL,GAAG,CAAC,GAAGN,CAAC,CAACM,GAAG,CAAC,CAAC,CAAC;MACzB;MACA,OAAOK,KAAK;IAChB,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,OAAOrB,MAAM,CAACa,IAAI,CAACD,UAAU,CAAC,CAACM,MAAM,GAAG,CAAC,GAAGN,UAAU,GAAGK,SAAS;EACtE;EACAV,YAAY,CAACoB,SAAS,GAAGA,SAAS;AACtC,CAAC,EAAEpB,YAAY,KAAKA,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;AACvCL,OAAO,CAAC2B,OAAO,GAAGtB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}