{"ast": null, "code": "import baseRepeat from './_baseRepeat.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Repeats the given string `n` times.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to repeat.\n * @param {number} [n=1] The number of times to repeat the string.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the repeated string.\n * @example\n *\n * _.repeat('*', 3);\n * // => '***'\n *\n * _.repeat('abc', 2);\n * // => 'abcabc'\n *\n * _.repeat('abc', 0);\n * // => ''\n */\nfunction repeat(string, n, guard) {\n  if (guard ? isIterateeCall(string, n, guard) : n === undefined) {\n    n = 1;\n  } else {\n    n = toInteger(n);\n  }\n  return baseRepeat(toString(string), n);\n}\nexport default repeat;", "map": {"version": 3, "names": ["baseRepeat", "isIterateeCall", "toInteger", "toString", "repeat", "string", "n", "guard", "undefined"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/repeat.js"], "sourcesContent": ["import baseRepeat from './_baseRepeat.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toInteger from './toInteger.js';\nimport toString from './toString.js';\n\n/**\n * Repeats the given string `n` times.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to repeat.\n * @param {number} [n=1] The number of times to repeat the string.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {string} Returns the repeated string.\n * @example\n *\n * _.repeat('*', 3);\n * // => '***'\n *\n * _.repeat('abc', 2);\n * // => 'abcabc'\n *\n * _.repeat('abc', 0);\n * // => ''\n */\nfunction repeat(string, n, guard) {\n  if ((guard ? isIterateeCall(string, n, guard) : n === undefined)) {\n    n = 1;\n  } else {\n    n = toInteger(n);\n  }\n  return baseRepeat(toString(string), n);\n}\n\nexport default repeat;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,CAAC,EAAEC,KAAK,EAAE;EAChC,IAAKA,KAAK,GAAGN,cAAc,CAACI,MAAM,EAAEC,CAAC,EAAEC,KAAK,CAAC,GAAGD,CAAC,KAAKE,SAAS,EAAG;IAChEF,CAAC,GAAG,CAAC;EACP,CAAC,MAAM;IACLA,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC;EAClB;EACA,OAAON,UAAU,CAACG,QAAQ,CAACE,MAAM,CAAC,EAAEC,CAAC,CAAC;AACxC;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}