{"ast": null, "code": "import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null ? object : baseFor(object, castFunction(iteratee), keysIn);\n}\nexport default forIn;", "map": {"version": 3, "names": ["baseFor", "castFunction", "keysIn", "forIn", "object", "iteratee"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/forIn.js"], "sourcesContent": ["import baseFor from './_baseFor.js';\nimport castFunction from './_castFunction.js';\nimport keysIn from './keysIn.js';\n\n/**\n * Iterates over own and inherited enumerable string keyed properties of an\n * object and invokes `iteratee` for each property. The iteratee is invoked\n * with three arguments: (value, key, object). Iteratee functions may exit\n * iteration early by explicitly returning `false`.\n *\n * @static\n * @memberOf _\n * @since 0.3.0\n * @category Object\n * @param {Object} object The object to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Object} Returns `object`.\n * @see _.forInRight\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n *   this.b = 2;\n * }\n *\n * Foo.prototype.c = 3;\n *\n * _.forIn(new Foo, function(value, key) {\n *   console.log(key);\n * });\n * // => Logs 'a', 'b', then 'c' (iteration order is not guaranteed).\n */\nfunction forIn(object, iteratee) {\n  return object == null\n    ? object\n    : baseFor(object, castFunction(iteratee), keysIn);\n}\n\nexport default forIn;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,MAAM,MAAM,aAAa;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC/B,OAAOD,MAAM,IAAI,IAAI,GACjBA,MAAM,GACNJ,OAAO,CAACI,MAAM,EAAEH,YAAY,CAACI,QAAQ,CAAC,EAAEH,MAAM,CAAC;AACrD;AAEA,eAAeC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}