{"ast": null, "code": "import baseAssignValue from './_baseAssignValue.js';\nimport createAggregator from './_createAggregator.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an object composed of keys generated from the results of running\n * each element of `collection` thru `iteratee`. The corresponding value of\n * each key is the number of times the key was returned by `iteratee`. The\n * iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee to transform keys.\n * @returns {Object} Returns the composed aggregate object.\n * @example\n *\n * _.countBy([6.1, 4.2, 6.3], Math.floor);\n * // => { '4': 1, '6': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.countBy(['one', 'two', 'three'], 'length');\n * // => { '3': 2, '5': 1 }\n */\nvar countBy = createAggregator(function (result, value, key) {\n  if (hasOwnProperty.call(result, key)) {\n    ++result[key];\n  } else {\n    baseAssignValue(result, key, 1);\n  }\n});\nexport default countBy;", "map": {"version": 3, "names": ["baseAssignValue", "createAggregator", "objectProto", "Object", "prototype", "hasOwnProperty", "countBy", "result", "value", "key", "call"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/countBy.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport createAggregator from './_createAggregator.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Creates an object composed of keys generated from the results of running\n * each element of `collection` thru `iteratee`. The corresponding value of\n * each key is the number of times the key was returned by `iteratee`. The\n * iteratee is invoked with one argument: (value).\n *\n * @static\n * @memberOf _\n * @since 0.5.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The iteratee to transform keys.\n * @returns {Object} Returns the composed aggregate object.\n * @example\n *\n * _.countBy([6.1, 4.2, 6.3], Math.floor);\n * // => { '4': 1, '6': 2 }\n *\n * // The `_.property` iteratee shorthand.\n * _.countBy(['one', 'two', 'three'], 'length');\n * // => { '3': 2, '5': 1 }\n */\nvar countBy = createAggregator(function(result, value, key) {\n  if (hasOwnProperty.call(result, key)) {\n    ++result[key];\n  } else {\n    baseAssignValue(result, key, 1);\n  }\n});\n\nexport default countBy;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,gBAAgB,MAAM,wBAAwB;;AAErD;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,OAAO,GAAGL,gBAAgB,CAAC,UAASM,MAAM,EAAEC,KAAK,EAAEC,GAAG,EAAE;EAC1D,IAAIJ,cAAc,CAACK,IAAI,CAACH,MAAM,EAAEE,GAAG,CAAC,EAAE;IACpC,EAAEF,MAAM,CAACE,GAAG,CAAC;EACf,CAAC,MAAM;IACLT,eAAe,CAACO,MAAM,EAAEE,GAAG,EAAE,CAAC,CAAC;EACjC;AACF,CAAC,CAAC;AAEF,eAAeH,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}