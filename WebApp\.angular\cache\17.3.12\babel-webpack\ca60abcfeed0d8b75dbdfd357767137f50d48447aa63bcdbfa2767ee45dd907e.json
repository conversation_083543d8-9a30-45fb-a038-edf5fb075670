{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Factory that creates a new ResizeObserver and allows us to stub it out in unit tests.\n */\nclass NzResizeObserverFactory {\n  create(callback) {\n    return typeof ResizeObserver === 'undefined' ? null : new ResizeObserver(callback);\n  }\n  static {\n    this.ɵfac = function NzResizeObserverFactory_Factory(t) {\n      return new (t || NzResizeObserverFactory)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserverFactory,\n      factory: NzResizeObserverFactory.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverFactory, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\n/** An injectable service that allows watching elements for changes to their content. */\nclass NzResizeObserver {\n  constructor(nzResizeObserverFactory) {\n    this.nzResizeObserverFactory = nzResizeObserverFactory;\n    /** Keeps track of the existing ResizeObservers so they can be reused. */\n    this.observedElements = new Map();\n  }\n  ngOnDestroy() {\n    this.observedElements.forEach((_, element) => this.cleanupObserver(element));\n  }\n  observe(elementOrRef) {\n    const element = coerceElement(elementOrRef);\n    return new Observable(observer => {\n      const stream = this.observeElement(element);\n      const subscription = stream.subscribe(observer);\n      return () => {\n        subscription.unsubscribe();\n        this.unobserveElement(element);\n      };\n    });\n  }\n  /**\n   * Observes the given element by using the existing ResizeObserver if available, or creating a\n   * new one if not.\n   */\n  observeElement(element) {\n    if (!this.observedElements.has(element)) {\n      const stream = new Subject();\n      const observer = this.nzResizeObserverFactory.create(mutations => stream.next(mutations));\n      if (observer) {\n        observer.observe(element);\n      }\n      this.observedElements.set(element, {\n        observer,\n        stream,\n        count: 1\n      });\n    } else {\n      this.observedElements.get(element).count++;\n    }\n    return this.observedElements.get(element).stream;\n  }\n  /**\n   * Un-observes the given element and cleans up the underlying ResizeObserver if nobody else is\n   * observing this element.\n   */\n  unobserveElement(element) {\n    if (this.observedElements.has(element)) {\n      this.observedElements.get(element).count--;\n      if (!this.observedElements.get(element).count) {\n        this.cleanupObserver(element);\n      }\n    }\n  }\n  /** Clean up the underlying ResizeObserver for the specified element. */\n  cleanupObserver(element) {\n    if (this.observedElements.has(element)) {\n      const {\n        observer,\n        stream\n      } = this.observedElements.get(element);\n      if (observer) {\n        observer.disconnect();\n      }\n      stream.complete();\n      this.observedElements.delete(element);\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserver_Factory(t) {\n      return new (t || NzResizeObserver)(i0.ɵɵinject(NzResizeObserverFactory));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: NzResizeObserver,\n      factory: NzResizeObserver.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserver, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: NzResizeObserverFactory\n  }], null);\n})();\nclass NzResizeObserverDirective {\n  subscribe() {\n    this.unsubscribe();\n    this.currentSubscription = this.nzResizeObserver.observe(this.elementRef).subscribe(this.nzResizeObserve);\n  }\n  unsubscribe() {\n    this.currentSubscription?.unsubscribe();\n  }\n  constructor(nzResizeObserver, elementRef) {\n    this.nzResizeObserver = nzResizeObserver;\n    this.elementRef = elementRef;\n    this.nzResizeObserve = new EventEmitter();\n    this.nzResizeObserverDisabled = false;\n    this.currentSubscription = null;\n  }\n  ngAfterContentInit() {\n    if (!this.currentSubscription && !this.nzResizeObserverDisabled) {\n      this.subscribe();\n    }\n  }\n  ngOnDestroy() {\n    this.unsubscribe();\n  }\n  ngOnChanges(changes) {\n    const {\n      nzResizeObserve\n    } = changes;\n    if (nzResizeObserve) {\n      if (this.nzResizeObserverDisabled) {\n        this.unsubscribe();\n      } else {\n        this.subscribe();\n      }\n    }\n  }\n  static {\n    this.ɵfac = function NzResizeObserverDirective_Factory(t) {\n      return new (t || NzResizeObserverDirective)(i0.ɵɵdirectiveInject(NzResizeObserver), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: NzResizeObserverDirective,\n      selectors: [[\"\", \"nzResizeObserver\", \"\"]],\n      inputs: {\n        nzResizeObserverDisabled: \"nzResizeObserverDisabled\"\n      },\n      outputs: {\n        nzResizeObserve: \"nzResizeObserve\"\n      },\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([NzResizeObserverFactory]), i0.ɵɵNgOnChangesFeature]\n    });\n  }\n}\n__decorate([InputBoolean()], NzResizeObserverDirective.prototype, \"nzResizeObserverDisabled\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[nzResizeObserver]',\n      standalone: true,\n      providers: [NzResizeObserverFactory]\n    }]\n  }], () => [{\n    type: NzResizeObserver\n  }, {\n    type: i0.ElementRef\n  }], {\n    nzResizeObserve: [{\n      type: Output\n    }],\n    nzResizeObserverDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzResizeObserverModule {\n  static {\n    this.ɵfac = function NzResizeObserverModule_Factory(t) {\n      return new (t || NzResizeObserverModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzResizeObserverModule,\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzResizeObserverModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzResizeObserverDirective],\n      exports: [NzResizeObserverDirective]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzResizeObserver, NzResizeObserverDirective, NzResizeObserverFactory, NzResizeObserverModule };", "map": {"version": 3, "names": ["i0", "Injectable", "EventEmitter", "Directive", "Output", "Input", "NgModule", "__decorate", "InputBoolean", "coerceElement", "Observable", "Subject", "NzResizeObserverFactory", "create", "callback", "ResizeObserver", "ɵfac", "NzResizeObserverFactory_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "NzResizeObserver", "constructor", "nzResizeObserverFactory", "observedElements", "Map", "ngOnDestroy", "for<PERSON>ach", "_", "element", "cleanupObserver", "observe", "elementOrRef", "observer", "stream", "observeElement", "subscription", "subscribe", "unsubscribe", "unobserveElement", "has", "mutations", "next", "set", "count", "get", "disconnect", "complete", "delete", "NzResizeObserver_Factory", "ɵɵinject", "NzResizeObserverDirective", "currentSubscription", "nzResizeObserver", "elementRef", "nzResizeObserve", "nzResizeObserverDisabled", "ngAfterContentInit", "ngOnChanges", "changes", "NzResizeObserverDirective_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "inputs", "outputs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵNgOnChangesFeature", "prototype", "selector", "providers", "NzResizeObserverModule", "NzResizeObserverModule_Factory", "ɵmod", "ɵɵdefineNgModule", "imports", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-cdk-resize-observer.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, Input, NgModule } from '@angular/core';\nimport { __decorate } from 'tslib';\nimport { InputBoolean } from 'ng-zorro-antd/core/util';\nimport { coerceElement } from '@angular/cdk/coercion';\nimport { Observable, Subject } from 'rxjs';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n/**\n * Factory that creates a new ResizeObserver and allows us to stub it out in unit tests.\n */\nclass NzResizeObserverFactory {\n    create(callback) {\n        return typeof ResizeObserver === 'undefined' ? null : new ResizeObserver(callback);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverFactory, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverFactory, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverFactory, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }] });\n/** An injectable service that allows watching elements for changes to their content. */\nclass NzResizeObserver {\n    constructor(nzResizeObserverFactory) {\n        this.nzResizeObserverFactory = nzResizeObserverFactory;\n        /** Keeps track of the existing ResizeObservers so they can be reused. */\n        this.observedElements = new Map();\n    }\n    ngOnDestroy() {\n        this.observedElements.forEach((_, element) => this.cleanupObserver(element));\n    }\n    observe(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        return new Observable((observer) => {\n            const stream = this.observeElement(element);\n            const subscription = stream.subscribe(observer);\n            return () => {\n                subscription.unsubscribe();\n                this.unobserveElement(element);\n            };\n        });\n    }\n    /**\n     * Observes the given element by using the existing ResizeObserver if available, or creating a\n     * new one if not.\n     */\n    observeElement(element) {\n        if (!this.observedElements.has(element)) {\n            const stream = new Subject();\n            const observer = this.nzResizeObserverFactory.create((mutations) => stream.next(mutations));\n            if (observer) {\n                observer.observe(element);\n            }\n            this.observedElements.set(element, { observer, stream, count: 1 });\n        }\n        else {\n            this.observedElements.get(element).count++;\n        }\n        return this.observedElements.get(element).stream;\n    }\n    /**\n     * Un-observes the given element and cleans up the underlying ResizeObserver if nobody else is\n     * observing this element.\n     */\n    unobserveElement(element) {\n        if (this.observedElements.has(element)) {\n            this.observedElements.get(element).count--;\n            if (!this.observedElements.get(element).count) {\n                this.cleanupObserver(element);\n            }\n        }\n    }\n    /** Clean up the underlying ResizeObserver for the specified element. */\n    cleanupObserver(element) {\n        if (this.observedElements.has(element)) {\n            const { observer, stream } = this.observedElements.get(element);\n            if (observer) {\n                observer.disconnect();\n            }\n            stream.complete();\n            this.observedElements.delete(element);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserver, deps: [{ token: NzResizeObserverFactory }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserver, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: NzResizeObserverFactory }] });\n\nclass NzResizeObserverDirective {\n    subscribe() {\n        this.unsubscribe();\n        this.currentSubscription = this.nzResizeObserver.observe(this.elementRef).subscribe(this.nzResizeObserve);\n    }\n    unsubscribe() {\n        this.currentSubscription?.unsubscribe();\n    }\n    constructor(nzResizeObserver, elementRef) {\n        this.nzResizeObserver = nzResizeObserver;\n        this.elementRef = elementRef;\n        this.nzResizeObserve = new EventEmitter();\n        this.nzResizeObserverDisabled = false;\n        this.currentSubscription = null;\n    }\n    ngAfterContentInit() {\n        if (!this.currentSubscription && !this.nzResizeObserverDisabled) {\n            this.subscribe();\n        }\n    }\n    ngOnDestroy() {\n        this.unsubscribe();\n    }\n    ngOnChanges(changes) {\n        const { nzResizeObserve } = changes;\n        if (nzResizeObserve) {\n            if (this.nzResizeObserverDisabled) {\n                this.unsubscribe();\n            }\n            else {\n                this.subscribe();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverDirective, deps: [{ token: NzResizeObserver }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzResizeObserverDirective, isStandalone: true, selector: \"[nzResizeObserver]\", inputs: { nzResizeObserverDisabled: \"nzResizeObserverDisabled\" }, outputs: { nzResizeObserve: \"nzResizeObserve\" }, providers: [NzResizeObserverFactory], usesOnChanges: true, ngImport: i0 }); }\n}\n__decorate([\n    InputBoolean()\n], NzResizeObserverDirective.prototype, \"nzResizeObserverDisabled\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverDirective, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[nzResizeObserver]',\n                    standalone: true,\n                    providers: [NzResizeObserverFactory]\n                }]\n        }], ctorParameters: () => [{ type: NzResizeObserver }, { type: i0.ElementRef }], propDecorators: { nzResizeObserve: [{\n                type: Output\n            }], nzResizeObserverDisabled: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzResizeObserverModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverModule, imports: [NzResizeObserverDirective], exports: [NzResizeObserverDirective] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzResizeObserverModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzResizeObserverDirective],\n                    exports: [NzResizeObserverDirective]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzResizeObserver, NzResizeObserverDirective, NzResizeObserverFactory, NzResizeObserverModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,YAAY,EAAEC,SAAS,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAC5F,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,aAAa,QAAQ,uBAAuB;AACrD,SAASC,UAAU,EAAEC,OAAO,QAAQ,MAAM;;AAE1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,uBAAuB,CAAC;EAC1BC,MAAMA,CAACC,QAAQ,EAAE;IACb,OAAO,OAAOC,cAAc,KAAK,WAAW,GAAG,IAAI,GAAG,IAAIA,cAAc,CAACD,QAAQ,CAAC;EACtF;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,gCAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,uBAAuB;IAAA,CAAoD;EAAE;EACvL;IAAS,IAAI,CAACO,KAAK,kBAD6EnB,EAAE,CAAAoB,kBAAA;MAAAC,KAAA,EACYT,uBAAuB;MAAAU,OAAA,EAAvBV,uBAAuB,CAAAI,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAClK;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGxB,EAAE,CAAAyB,iBAAA,CAGXb,uBAAuB,EAAc,CAAC;IACrHc,IAAI,EAAEzB,UAAU;IAChB0B,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC;AAAA;AACV;AACA,MAAMK,gBAAgB,CAAC;EACnBC,WAAWA,CAACC,uBAAuB,EAAE;IACjC,IAAI,CAACA,uBAAuB,GAAGA,uBAAuB;IACtD;IACA,IAAI,CAACC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;EACrC;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACF,gBAAgB,CAACG,OAAO,CAAC,CAACC,CAAC,EAAEC,OAAO,KAAK,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC,CAAC;EAChF;EACAE,OAAOA,CAACC,YAAY,EAAE;IAClB,MAAMH,OAAO,GAAG3B,aAAa,CAAC8B,YAAY,CAAC;IAC3C,OAAO,IAAI7B,UAAU,CAAE8B,QAAQ,IAAK;MAChC,MAAMC,MAAM,GAAG,IAAI,CAACC,cAAc,CAACN,OAAO,CAAC;MAC3C,MAAMO,YAAY,GAAGF,MAAM,CAACG,SAAS,CAACJ,QAAQ,CAAC;MAC/C,OAAO,MAAM;QACTG,YAAY,CAACE,WAAW,CAAC,CAAC;QAC1B,IAAI,CAACC,gBAAgB,CAACV,OAAO,CAAC;MAClC,CAAC;IACL,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIM,cAAcA,CAACN,OAAO,EAAE;IACpB,IAAI,CAAC,IAAI,CAACL,gBAAgB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACrC,MAAMK,MAAM,GAAG,IAAI9B,OAAO,CAAC,CAAC;MAC5B,MAAM6B,QAAQ,GAAG,IAAI,CAACV,uBAAuB,CAACjB,MAAM,CAAEmC,SAAS,IAAKP,MAAM,CAACQ,IAAI,CAACD,SAAS,CAAC,CAAC;MAC3F,IAAIR,QAAQ,EAAE;QACVA,QAAQ,CAACF,OAAO,CAACF,OAAO,CAAC;MAC7B;MACA,IAAI,CAACL,gBAAgB,CAACmB,GAAG,CAACd,OAAO,EAAE;QAAEI,QAAQ;QAAEC,MAAM;QAAEU,KAAK,EAAE;MAAE,CAAC,CAAC;IACtE,CAAC,MACI;MACD,IAAI,CAACpB,gBAAgB,CAACqB,GAAG,CAAChB,OAAO,CAAC,CAACe,KAAK,EAAE;IAC9C;IACA,OAAO,IAAI,CAACpB,gBAAgB,CAACqB,GAAG,CAAChB,OAAO,CAAC,CAACK,MAAM;EACpD;EACA;AACJ;AACA;AACA;EACIK,gBAAgBA,CAACV,OAAO,EAAE;IACtB,IAAI,IAAI,CAACL,gBAAgB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACpC,IAAI,CAACL,gBAAgB,CAACqB,GAAG,CAAChB,OAAO,CAAC,CAACe,KAAK,EAAE;MAC1C,IAAI,CAAC,IAAI,CAACpB,gBAAgB,CAACqB,GAAG,CAAChB,OAAO,CAAC,CAACe,KAAK,EAAE;QAC3C,IAAI,CAACd,eAAe,CAACD,OAAO,CAAC;MACjC;IACJ;EACJ;EACA;EACAC,eAAeA,CAACD,OAAO,EAAE;IACrB,IAAI,IAAI,CAACL,gBAAgB,CAACgB,GAAG,CAACX,OAAO,CAAC,EAAE;MACpC,MAAM;QAAEI,QAAQ;QAAEC;MAAO,CAAC,GAAG,IAAI,CAACV,gBAAgB,CAACqB,GAAG,CAAChB,OAAO,CAAC;MAC/D,IAAII,QAAQ,EAAE;QACVA,QAAQ,CAACa,UAAU,CAAC,CAAC;MACzB;MACAZ,MAAM,CAACa,QAAQ,CAAC,CAAC;MACjB,IAAI,CAACvB,gBAAgB,CAACwB,MAAM,CAACnB,OAAO,CAAC;IACzC;EACJ;EACA;IAAS,IAAI,CAACpB,IAAI,YAAAwC,yBAAAtC,CAAA;MAAA,YAAAA,CAAA,IAAwFU,gBAAgB,EArE1B5B,EAAE,CAAAyD,QAAA,CAqE0C7C,uBAAuB;IAAA,CAA6C;EAAE;EAClN;IAAS,IAAI,CAACO,KAAK,kBAtE6EnB,EAAE,CAAAoB,kBAAA;MAAAC,KAAA,EAsEYO,gBAAgB;MAAAN,OAAA,EAAhBM,gBAAgB,CAAAZ,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC3J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAxEoGxB,EAAE,CAAAyB,iBAAA,CAwEXG,gBAAgB,EAAc,CAAC;IAC9GF,IAAI,EAAEzB,UAAU;IAChB0B,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEd;EAAwB,CAAC,CAAC;AAAA;AAErE,MAAM8C,yBAAyB,CAAC;EAC5Bd,SAASA,CAAA,EAAG;IACR,IAAI,CAACC,WAAW,CAAC,CAAC;IAClB,IAAI,CAACc,mBAAmB,GAAG,IAAI,CAACC,gBAAgB,CAACtB,OAAO,CAAC,IAAI,CAACuB,UAAU,CAAC,CAACjB,SAAS,CAAC,IAAI,CAACkB,eAAe,CAAC;EAC7G;EACAjB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACc,mBAAmB,EAAEd,WAAW,CAAC,CAAC;EAC3C;EACAhB,WAAWA,CAAC+B,gBAAgB,EAAEC,UAAU,EAAE;IACtC,IAAI,CAACD,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,eAAe,GAAG,IAAI5D,YAAY,CAAC,CAAC;IACzC,IAAI,CAAC6D,wBAAwB,GAAG,KAAK;IACrC,IAAI,CAACJ,mBAAmB,GAAG,IAAI;EACnC;EACAK,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC,IAAI,CAACL,mBAAmB,IAAI,CAAC,IAAI,CAACI,wBAAwB,EAAE;MAC7D,IAAI,CAACnB,SAAS,CAAC,CAAC;IACpB;EACJ;EACAX,WAAWA,CAAA,EAAG;IACV,IAAI,CAACY,WAAW,CAAC,CAAC;EACtB;EACAoB,WAAWA,CAACC,OAAO,EAAE;IACjB,MAAM;MAAEJ;IAAgB,CAAC,GAAGI,OAAO;IACnC,IAAIJ,eAAe,EAAE;MACjB,IAAI,IAAI,CAACC,wBAAwB,EAAE;QAC/B,IAAI,CAAClB,WAAW,CAAC,CAAC;MACtB,CAAC,MACI;QACD,IAAI,CAACD,SAAS,CAAC,CAAC;MACpB;IACJ;EACJ;EACA;IAAS,IAAI,CAAC5B,IAAI,YAAAmD,kCAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwFwC,yBAAyB,EA/GnC1D,EAAE,CAAAoE,iBAAA,CA+GmDxC,gBAAgB,GA/GrE5B,EAAE,CAAAoE,iBAAA,CA+GgFpE,EAAE,CAACqE,UAAU;IAAA,CAA4C;EAAE;EAC7O;IAAS,IAAI,CAACC,IAAI,kBAhH8EtE,EAAE,CAAAuE,iBAAA;MAAA7C,IAAA,EAgHJgC,yBAAyB;MAAAc,SAAA;MAAAC,MAAA;QAAAV,wBAAA;MAAA;MAAAW,OAAA;QAAAZ,eAAA;MAAA;MAAAa,UAAA;MAAAC,QAAA,GAhHvB5E,EAAE,CAAA6E,kBAAA,CAgHyM,CAACjE,uBAAuB,CAAC,GAhHpOZ,EAAE,CAAA8E,oBAAA;IAAA,EAgHwQ;EAAE;AAChX;AACAvE,UAAU,CAAC,CACPC,YAAY,CAAC,CAAC,CACjB,EAAEkD,yBAAyB,CAACqB,SAAS,EAAE,0BAA0B,EAAE,KAAK,CAAC,CAAC;AAC3E;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KArHoGxB,EAAE,CAAAyB,iBAAA,CAqHXiC,yBAAyB,EAAc,CAAC;IACvHhC,IAAI,EAAEvB,SAAS;IACfwB,IAAI,EAAE,CAAC;MACCqD,QAAQ,EAAE,oBAAoB;MAC9BL,UAAU,EAAE,IAAI;MAChBM,SAAS,EAAE,CAACrE,uBAAuB;IACvC,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEc,IAAI,EAAEE;EAAiB,CAAC,EAAE;IAAEF,IAAI,EAAE1B,EAAE,CAACqE;EAAW,CAAC,CAAC,EAAkB;IAAEP,eAAe,EAAE,CAAC;MAC7GpC,IAAI,EAAEtB;IACV,CAAC,CAAC;IAAE2D,wBAAwB,EAAE,CAAC;MAC3BrC,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM6E,sBAAsB,CAAC;EACzB;IAAS,IAAI,CAAClE,IAAI,YAAAmE,+BAAAjE,CAAA;MAAA,YAAAA,CAAA,IAAwFgE,sBAAsB;IAAA,CAAkD;EAAE;EACpL;IAAS,IAAI,CAACE,IAAI,kBAxI8EpF,EAAE,CAAAqF,gBAAA;MAAA3D,IAAA,EAwISwD,sBAAsB;MAAAI,OAAA,GAAY5B,yBAAyB;MAAA6B,OAAA,GAAa7B,yBAAyB;IAAA,EAAI;EAAE;EAClN;IAAS,IAAI,CAAC8B,IAAI,kBAzI8ExF,EAAE,CAAAyF,gBAAA,IAyIkC;EAAE;AAC1I;AACA;EAAA,QAAAjE,SAAA,oBAAAA,SAAA,KA3IoGxB,EAAE,CAAAyB,iBAAA,CA2IXyD,sBAAsB,EAAc,CAAC;IACpHxD,IAAI,EAAEpB,QAAQ;IACdqB,IAAI,EAAE,CAAC;MACC2D,OAAO,EAAE,CAAC5B,yBAAyB,CAAC;MACpC6B,OAAO,EAAE,CAAC7B,yBAAyB;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAAS9B,gBAAgB,EAAE8B,yBAAyB,EAAE9C,uBAAuB,EAAEsE,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}