{"ast": null, "code": "import { ENTER } from '@angular/cdk/keycodes';\nimport * as i1 from '@angular/common/http';\nimport { HttpRequest, HttpHeaders, HttpEventType, HttpResponse } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, ViewChild, Input, ChangeDetectionStrategy, Inject, EventEmitter, Output, NgModule } from '@angular/core';\nimport { of, Observable, Subscription, Subject, fromEvent } from 'rxjs';\nimport { switchMap, map, tap, takeUntil, filter } from 'rxjs/operators';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { DOCUMENT, NgForOf, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NgStyle, NgClass } from '@angular/common';\nimport * as i4 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i6 from 'ng-zorro-antd/progress';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport * as i2 from 'ng-zorro-antd/tooltip';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport * as i1$1 from '@angular/cdk/platform';\nimport * as i5 from 'ng-zorro-antd/core/transition-patch';\nimport { __decorate } from 'tslib';\nimport { toBoolean, InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1$2 from 'ng-zorro-antd/i18n';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst _c0 = [\"file\"];\nconst _c1 = [\"nz-upload-btn\", \"\"];\nconst _c2 = [\"*\"];\nconst _c3 = a0 => ({\n  $implicit: a0\n});\nconst _c4 = () => ({\n  opacity: 0.5,\n  \"pointer-events\": \"none\"\n});\nfunction NzUploadListComponent_div_0_ng_template_2_div_1_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_1_ng_template_1_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_a_2_img_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"img\", 24);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵproperty(\"src\", file_r1.thumbUrl || file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"alt\", file_r1.name);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 22);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_2_a_2_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_a_2_img_1_Template, 1, 2, \"img\", 23);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const noImageThumbTpl_r5 = i0.ɵɵreference(5);\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵclassProp(\"ant-upload-list-item-file\", !file_r1.isImageUrl);\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.isImageUrl)(\"ngIfElse\", noImageThumbTpl_r5);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_div_3_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_3_ng_template_1_Template, 0, 0, \"ng-template\", 21);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_ng_template_4_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_2_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_2_ng_template_4_ng_template_0_Template, 0, 0, \"ng-template\", 21);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const iconNode_r2 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", iconNode_r2)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, file_r1));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0, 16);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_2_div_1_Template, 2, 6, \"div\", 17)(2, NzUploadListComponent_div_0_ng_template_2_a_2_Template, 2, 5, \"a\", 18)(3, NzUploadListComponent_div_0_ng_template_2_div_3_Template, 2, 4, \"div\", 19);\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵtemplate(4, NzUploadListComponent_div_0_ng_template_2_ng_template_4_Template, 1, 4, \"ng-template\", null, 6, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngSwitch\", file_r1.iconType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"uploading\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"thumbnail\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"span\", 29);\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_ng_container_1_Template, 2, 0, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    const iconNodeFileIcon_r7 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r6.isUploading)(\"ngIfElse\", iconNodeFileIcon_r7);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r3.locale.uploading, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_ng_container_1_Template, 2, 1, \"ng-container\", 26);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    const iconNodeFileIcon_r7 = i0.ɵɵreference(4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r6.isUploading)(\"ngIfElse\", iconNodeFileIcon_r7);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 30);\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r6.isUploading ? \"loading\" : \"paper-clip\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0)(1, 16);\n    i0.ɵɵtemplate(2, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_Template, 2, 2, \"ng-container\", 27)(3, NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_Template, 2, 2, \"ng-container\", 27)(4, NzUploadListComponent_div_0_ng_template_4_ng_container_0_span_4_Template, 1, 1, \"span\", 28);\n    i0.ɵɵelementContainerEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitch\", ctx_r3.listType);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"picture\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngSwitchCase\", \"picture-card\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 31);\n  }\n  if (rf & 2) {\n    const file_r6 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"nzType\", file_r6.isImageUrl ? \"picture\" : \"file\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_4_ng_container_0_Template, 5, 3, \"ng-container\", 26)(1, NzUploadListComponent_div_0_ng_template_4_ng_template_1_Template, 0, 0, \"ng-template\", 21, 7, i0.ɵɵtemplateRefExtractor)(3, NzUploadListComponent_div_0_ng_template_4_ng_template_3_Template, 1, 1, \"ng-template\", null, 8, i0.ɵɵtemplateRefExtractor);\n  }\n  if (rf & 2) {\n    const file_r6 = ctx.$implicit;\n    const customIconRender_r8 = i0.ɵɵreference(2);\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r3.iconRender)(\"ngIfElse\", customIconRender_r8);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r3.iconRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(4, _c3, file_r6));\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_6_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_6_button_0_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r9);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleRemove(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"span\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.removeFile);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_6_button_0_Template, 2, 1, \"button\", 32);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.icons.showRemoveIcon);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_8_button_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_8_button_0_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handleDownload(file_r1));\n    });\n    i0.ɵɵelement(1, \"span\", 35);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(3);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.downloadFile);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_8_button_0_Template, 2, 1, \"button\", 32);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"ngIf\", file_r1.showDownload);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_10_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_1_Template, 0, 0, \"ng-template\", 13)(2, NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_2_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const removeIcon_r11 = i0.ɵɵreference(7);\n    const downloadIcon_r12 = i0.ɵɵreference(9);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-item-card-actions \", ctx_r3.listType === \"picture\" ? \"picture\" : \"\", \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r12);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r11);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_10_span_0_Template, 3, 5, \"span\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listType !== \"picture-card\");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_a_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_12_a_0_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r13);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵproperty(\"href\", file_r1.url, i0.ɵɵsanitizeUrl);\n    i0.ɵɵattribute(\"title\", file_r1.name)(\"download\", file_r1.linkProps && file_r1.linkProps.download);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_ng_template_12_span_1_Template_span_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r14);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵattribute(\"title\", file_r1.name);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", file_r1.name, \" \");\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_12_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadListComponent_div_0_ng_template_12_a_0_Template, 2, 4, \"a\", 37)(1, NzUploadListComponent_div_0_ng_template_12_span_1_Template, 2, 2, \"span\", 38)(2, NzUploadListComponent_div_0_ng_template_12_ng_template_2_Template, 0, 0, \"ng-template\", 13);\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const downloadOrDelete_r15 = i0.ɵɵreference(11);\n    i0.ɵɵproperty(\"ngIf\", file_r1.url);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !file_r1.url);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadOrDelete_r15);\n  }\n}\nfunction NzUploadListComponent_div_0_ng_template_16_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_ng_template_17_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 44);\n    i0.ɵɵlistener(\"click\", function NzUploadListComponent_div_0_span_18_a_1_Template_a_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r16);\n      const file_r1 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.handlePreview(file_r1, $event));\n    });\n    i0.ɵɵelement(1, \"span\", 45);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"href\", file_r1.url || file_r1.thumbUrl, i0.ɵɵsanitizeUrl)(\"ngStyle\", !(file_r1.url || file_r1.thumbUrl) ? i0.ɵɵpureFunction0(3, _c4) : null);\n    i0.ɵɵattribute(\"title\", ctx_r3.locale.previewFile);\n  }\n}\nfunction NzUploadListComponent_div_0_span_18_ng_container_2_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_span_18_ng_container_2_ng_template_1_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const downloadIcon_r12 = i0.ɵɵreference(9);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", downloadIcon_r12);\n  }\n}\nfunction NzUploadListComponent_div_0_span_18_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadListComponent_div_0_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 41);\n    i0.ɵɵtemplate(1, NzUploadListComponent_div_0_span_18_a_1_Template, 2, 4, \"a\", 42)(2, NzUploadListComponent_div_0_span_18_ng_container_2_Template, 2, 1, \"ng-container\", 43)(3, NzUploadListComponent_div_0_span_18_ng_template_3_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    const removeIcon_r11 = i0.ɵɵreference(7);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.icons.showPreviewIcon);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.status === \"done\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", removeIcon_r11);\n  }\n}\nfunction NzUploadListComponent_div_0_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46);\n    i0.ɵɵelement(1, \"nz-progress\", 47);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const file_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"nzPercent\", file_r1.percent)(\"nzShowInfo\", false)(\"nzStrokeWidth\", 2);\n  }\n}\nfunction NzUploadListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"div\", 10);\n    i0.ɵɵtemplate(2, NzUploadListComponent_div_0_ng_template_2_Template, 6, 3, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(4, NzUploadListComponent_div_0_ng_template_4_Template, 5, 6, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(6, NzUploadListComponent_div_0_ng_template_6_Template, 1, 1, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(8, NzUploadListComponent_div_0_ng_template_8_Template, 1, 1, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(10, NzUploadListComponent_div_0_ng_template_10_Template, 1, 1, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor)(12, NzUploadListComponent_div_0_ng_template_12_Template, 3, 3, \"ng-template\", null, 5, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"span\", 12);\n    i0.ɵɵtemplate(16, NzUploadListComponent_div_0_ng_template_16_Template, 0, 0, \"ng-template\", 13)(17, NzUploadListComponent_div_0_ng_template_17_Template, 0, 0, \"ng-template\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(18, NzUploadListComponent_div_0_span_18_Template, 4, 3, \"span\", 14)(19, NzUploadListComponent_div_0_div_19_Template, 2, 3, \"div\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const file_r1 = ctx.$implicit;\n    const icon_r17 = i0.ɵɵreference(3);\n    const preview_r18 = i0.ɵɵreference(13);\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵclassMapInterpolate1(\"ant-upload-list-\", ctx_r3.listType, \"-container\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMapInterpolate2(\"ant-upload-list-item ant-upload-list-item-\", file_r1.status, \" ant-upload-list-item-list-type-\", ctx_r3.listType, \"\");\n    i0.ɵɵproperty(\"@itemState\", undefined)(\"nzTooltipTitle\", file_r1.status === \"error\" ? file_r1.message : null);\n    i0.ɵɵattribute(\"data-key\", file_r1.key);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", icon_r17);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", preview_r18);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.listType === \"picture-card\" && !file_r1.isUploading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", file_r1.isUploading);\n  }\n}\nconst _c5 = [\"uploadComp\"];\nconst _c6 = [\"listComp\"];\nconst _c7 = () => [];\nfunction NzUploadComponent_ng_template_0_nz_upload_list_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"nz-upload-list\", 10, 5);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowUploadList ? \"\" : \"none\");\n    i0.ɵɵproperty(\"locale\", ctx_r0.locale)(\"listType\", ctx_r0.nzListType)(\"items\", ctx_r0.nzFileList || i0.ɵɵpureFunction0(13, _c7))(\"icons\", ctx_r0.nzShowUploadList)(\"iconRender\", ctx_r0.nzIconRender)(\"previewFile\", ctx_r0.nzPreviewFile)(\"previewIsImage\", ctx_r0.nzPreviewIsImage)(\"onPreview\", ctx_r0.nzPreview)(\"onRemove\", ctx_r0.onRemove)(\"onDownload\", ctx_r0.nzDownload)(\"dir\", ctx_r0.dir);\n  }\n}\nfunction NzUploadComponent_ng_template_0_ng_container_1_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainer(0);\n  }\n}\nfunction NzUploadComponent_ng_template_0_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadComponent_ng_template_0_ng_container_1_ng_container_1_Template, 1, 0, \"ng-container\", 11);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", ctx_r0.nzFileListRender)(\"ngTemplateOutletContext\", i0.ɵɵpureFunction1(2, _c3, ctx_r0.nzFileList));\n  }\n}\nfunction NzUploadComponent_ng_template_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_nz_upload_list_0_Template, 2, 14, \"nz-upload-list\", 8)(1, NzUploadComponent_ng_template_0_ng_container_1_Template, 2, 4, \"ng-container\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.locale && !ctx_r0.nzFileListRender);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzFileListRender);\n  }\n}\nfunction NzUploadComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0);\n  }\n}\nfunction NzUploadComponent_ng_template_4_ng_template_3_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"div\", 13, 6);\n    i0.ɵɵtemplate(3, NzUploadComponent_ng_template_4_ng_template_3_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵstyleProp(\"display\", ctx_r0.nzShowButton ? \"\" : \"none\");\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.classList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n  }\n}\nfunction NzUploadComponent_ng_container_6_ng_template_5_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_container_6_ng_template_6_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_container_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 15);\n    i0.ɵɵlistener(\"drop\", function NzUploadComponent_ng_container_6_Template_div_drop_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragover\", function NzUploadComponent_ng_container_6_Template_div_dragover_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    })(\"dragleave\", function NzUploadComponent_ng_container_6_Template_div_dragleave_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.fileDrop($event));\n    });\n    i0.ɵɵelementStart(2, \"div\", 16, 6)(4, \"div\", 17);\n    i0.ɵɵtemplate(5, NzUploadComponent_ng_container_6_ng_template_5_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(6, NzUploadComponent_ng_container_6_ng_template_6_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const list_r4 = i0.ɵɵreference(1);\n    const con_r2 = i0.ɵɵreference(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.classList);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"options\", ctx_r0._btnOptions);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", con_r2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nfunction NzUploadComponent_ng_template_7_ng_container_0_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_7_ng_container_0_ng_template_2_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_7_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NzUploadComponent_ng_template_7_ng_container_0_ng_template_1_Template, 0, 0, \"ng-template\", 14)(2, NzUploadComponent_ng_template_7_ng_container_0_ng_template_2_Template, 0, 0, \"ng-template\", 14);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext(2);\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n  }\n}\nfunction NzUploadComponent_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_7_ng_container_0_Template, 3, 2, \"ng-container\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    const pic_r6 = i0.ɵɵreference(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nzListType === \"picture-card\")(\"ngIfElse\", pic_r6);\n  }\n}\nfunction NzUploadComponent_ng_template_9_ng_template_0_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_9_ng_template_1_Template(rf, ctx) {}\nfunction NzUploadComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, NzUploadComponent_ng_template_9_ng_template_0_Template, 0, 0, \"ng-template\", 14)(1, NzUploadComponent_ng_template_9_ng_template_1_Template, 0, 0, \"ng-template\", 14);\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const list_r4 = i0.ɵɵreference(1);\n    const btn_r5 = i0.ɵɵreference(5);\n    i0.ɵɵproperty(\"ngTemplateOutlet\", btn_r5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngTemplateOutlet\", list_r4);\n  }\n}\nclass NzUploadBtnComponent {\n  onClick() {\n    if (this.options.disabled || !this.options.openFileDialogOnClick) {\n      return;\n    }\n    this.file.nativeElement.click();\n  }\n  // skip safari bug\n  onFileDrop(e) {\n    if (this.options.disabled || e.type === 'dragover') {\n      e.preventDefault();\n      return;\n    }\n    if (this.options.directory) {\n      this.traverseFileTree(e.dataTransfer.items);\n    } else {\n      const files = Array.prototype.slice.call(e.dataTransfer.files).filter(file => this.attrAccept(file, this.options.accept));\n      if (files.length) {\n        this.uploadFiles(files);\n      }\n    }\n    e.preventDefault();\n  }\n  onChange(e) {\n    if (this.options.disabled) {\n      return;\n    }\n    const hie = e.target;\n    this.uploadFiles(hie.files);\n    hie.value = '';\n  }\n  traverseFileTree(files) {\n    const _traverseFileTree = (item, path) => {\n      if (item.isFile) {\n        item.file(file => {\n          if (this.attrAccept(file, this.options.accept)) {\n            this.uploadFiles([file]);\n          }\n        });\n      } else if (item.isDirectory) {\n        const dirReader = item.createReader();\n        dirReader.readEntries(entries => {\n          for (const entrieItem of entries) {\n            _traverseFileTree(entrieItem, `${path}${item.name}/`);\n          }\n        });\n      }\n    };\n    for (const file of files) {\n      _traverseFileTree(file.webkitGetAsEntry(), '');\n    }\n  }\n  attrAccept(file, acceptedFiles) {\n    if (file && acceptedFiles) {\n      const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n      const fileName = `${file.name}`;\n      const mimeType = `${file.type}`;\n      const baseMimeType = mimeType.replace(/\\/.*$/, '');\n      return acceptedFilesArray.some(type => {\n        const validType = type.trim();\n        if (validType.charAt(0) === '.') {\n          return fileName.toLowerCase().indexOf(validType.toLowerCase(), fileName.toLowerCase().length - validType.toLowerCase().length) !== -1;\n        } else if (/\\/\\*$/.test(validType)) {\n          // This is something like a image/* mime type\n          return baseMimeType === validType.replace(/\\/.*$/, '');\n        }\n        return mimeType === validType;\n      });\n    }\n    return true;\n  }\n  attachUid(file) {\n    if (!file.uid) {\n      file.uid = Math.random().toString(36).substring(2);\n    }\n    return file;\n  }\n  uploadFiles(fileList) {\n    let filters$ = of(Array.prototype.slice.call(fileList));\n    if (this.options.filters) {\n      this.options.filters.forEach(f => {\n        filters$ = filters$.pipe(switchMap(list => {\n          const fnRes = f.fn(list);\n          return fnRes instanceof Observable ? fnRes : of(fnRes);\n        }));\n      });\n    }\n    filters$.subscribe(list => {\n      list.forEach(file => {\n        this.attachUid(file);\n        this.upload(file, list);\n      });\n    }, e => {\n      warn(`Unhandled upload filter error`, e);\n    });\n  }\n  upload(file, fileList) {\n    if (!this.options.beforeUpload) {\n      return this.post(file);\n    }\n    const before = this.options.beforeUpload(file, fileList);\n    if (before instanceof Observable) {\n      before.subscribe(processedFile => {\n        const processedFileType = Object.prototype.toString.call(processedFile);\n        if (processedFileType === '[object File]' || processedFileType === '[object Blob]') {\n          this.attachUid(processedFile);\n          this.post(processedFile);\n        } else if (typeof processedFile === 'boolean' && processedFile !== false) {\n          this.post(file);\n        }\n      }, e => {\n        warn(`Unhandled upload beforeUpload error`, e);\n      });\n    } else if (before !== false) {\n      return this.post(file);\n    }\n  }\n  post(file) {\n    if (this.destroy) {\n      return;\n    }\n    let process$ = of(file);\n    let transformedFile;\n    const opt = this.options;\n    const {\n      uid\n    } = file;\n    const {\n      action,\n      data,\n      headers,\n      transformFile\n    } = opt;\n    const args = {\n      action: typeof action === 'string' ? action : '',\n      name: opt.name,\n      headers,\n      file,\n      postFile: file,\n      data,\n      withCredentials: opt.withCredentials,\n      onProgress: opt.onProgress ? e => {\n        opt.onProgress(e, file);\n      } : undefined,\n      onSuccess: (ret, xhr) => {\n        this.clean(uid);\n        opt.onSuccess(ret, file, xhr);\n      },\n      onError: xhr => {\n        this.clean(uid);\n        opt.onError(xhr, file);\n      }\n    };\n    if (typeof action === 'function') {\n      const actionResult = action(file);\n      if (actionResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => actionResult), map(res => {\n          args.action = res;\n          return file;\n        }));\n      } else {\n        args.action = actionResult;\n      }\n    }\n    if (typeof transformFile === 'function') {\n      const transformResult = transformFile(file);\n      process$ = process$.pipe(switchMap(() => transformResult instanceof Observable ? transformResult : of(transformResult)), tap(newFile => transformedFile = newFile));\n    }\n    if (typeof data === 'function') {\n      const dataResult = data(file);\n      if (dataResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => dataResult), map(res => {\n          args.data = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.data = dataResult;\n      }\n    }\n    if (typeof headers === 'function') {\n      const headersResult = headers(file);\n      if (headersResult instanceof Observable) {\n        process$ = process$.pipe(switchMap(() => headersResult), map(res => {\n          args.headers = res;\n          return transformedFile ?? file;\n        }));\n      } else {\n        args.headers = headersResult;\n      }\n    }\n    process$.subscribe(newFile => {\n      args.postFile = newFile;\n      const req$ = (opt.customRequest || this.xhr).call(this, args);\n      if (!(req$ instanceof Subscription)) {\n        warn(`Must return Subscription type in '[nzCustomRequest]' property`);\n      }\n      this.reqs[uid] = req$;\n      opt.onStart(file);\n    });\n  }\n  xhr(args) {\n    const formData = new FormData();\n    if (args.data) {\n      Object.keys(args.data).map(key => {\n        formData.append(key, args.data[key]);\n      });\n    }\n    formData.append(args.name, args.postFile);\n    if (!args.headers) {\n      args.headers = {};\n    }\n    if (args.headers['X-Requested-With'] !== null) {\n      args.headers['X-Requested-With'] = `XMLHttpRequest`;\n    } else {\n      delete args.headers['X-Requested-With'];\n    }\n    const req = new HttpRequest('POST', args.action, formData, {\n      reportProgress: true,\n      withCredentials: args.withCredentials,\n      headers: new HttpHeaders(args.headers)\n    });\n    return this.http.request(req).subscribe(event => {\n      if (event.type === HttpEventType.UploadProgress) {\n        if (event.total > 0) {\n          event.percent = event.loaded / event.total * 100;\n        }\n        args.onProgress(event, args.file);\n      } else if (event instanceof HttpResponse) {\n        args.onSuccess(event.body, args.file, event);\n      }\n    }, err => {\n      this.abort(args.file);\n      args.onError(err, args.file);\n    });\n  }\n  clean(uid) {\n    const req$ = this.reqs[uid];\n    if (req$ instanceof Subscription) {\n      req$.unsubscribe();\n    }\n    delete this.reqs[uid];\n  }\n  abort(file) {\n    if (file) {\n      this.clean(file && file.uid);\n    } else {\n      Object.keys(this.reqs).forEach(uid => this.clean(uid));\n    }\n  }\n  constructor(ngZone, http, elementRef) {\n    this.ngZone = ngZone;\n    this.http = http;\n    this.elementRef = elementRef;\n    this.reqs = {};\n    this.destroy = false;\n    this.destroy$ = new Subject();\n    if (!http) {\n      throw new Error(`Not found 'HttpClient', You can import 'HttpClientModule' in your root module.`);\n    }\n  }\n  ngOnInit() {\n    // Caretaker note: `input[type=file].click()` will open a native OS file picker,\n    // it doesn't require Angular to run `ApplicationRef.tick()`.\n    this.ngZone.runOutsideAngular(() => {\n      fromEvent(this.elementRef.nativeElement, 'click').pipe(takeUntil(this.destroy$)).subscribe(() => this.onClick());\n      fromEvent(this.elementRef.nativeElement, 'keydown').pipe(takeUntil(this.destroy$)).subscribe(event => {\n        if (this.options.disabled) {\n          return;\n        }\n        if (event.key === 'Enter' || event.keyCode === ENTER) {\n          this.onClick();\n        }\n      });\n    });\n  }\n  ngOnDestroy() {\n    this.destroy = true;\n    this.destroy$.next();\n    this.abort();\n  }\n  static {\n    this.ɵfac = function NzUploadBtnComponent_Factory(t) {\n      return new (t || NzUploadBtnComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1.HttpClient, 8), i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadBtnComponent,\n      selectors: [[\"\", \"nz-upload-btn\", \"\"]],\n      viewQuery: function NzUploadBtnComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.file = _t.first);\n        }\n      },\n      hostAttrs: [1, \"ant-upload\"],\n      hostVars: 4,\n      hostBindings: function NzUploadBtnComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"drop\", function NzUploadBtnComponent_drop_HostBindingHandler($event) {\n            return ctx.onFileDrop($event);\n          })(\"dragover\", function NzUploadBtnComponent_dragover_HostBindingHandler($event) {\n            return ctx.onFileDrop($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵattribute(\"tabindex\", \"0\")(\"role\", \"button\");\n          i0.ɵɵclassProp(\"ant-upload-disabled\", ctx.options.disabled);\n        }\n      },\n      inputs: {\n        options: \"options\"\n      },\n      exportAs: [\"nzUploadBtn\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      attrs: _c1,\n      ngContentSelectors: _c2,\n      decls: 3,\n      vars: 6,\n      consts: [[\"file\", \"\"], [\"type\", \"file\", 3, \"change\", \"multiple\"]],\n      template: function NzUploadBtnComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"input\", 1, 0);\n          i0.ɵɵlistener(\"change\", function NzUploadBtnComponent_Template_input_change_0_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onChange($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2);\n        }\n        if (rf & 2) {\n          i0.ɵɵstyleProp(\"display\", \"none\");\n          i0.ɵɵproperty(\"multiple\", ctx.options.multiple);\n          i0.ɵɵattribute(\"accept\", ctx.options.accept)(\"directory\", ctx.options.directory ? \"directory\" : null)(\"webkitdirectory\", ctx.options.directory ? \"webkitdirectory\" : null);\n        }\n      },\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadBtnComponent, [{\n    type: Component,\n    args: [{\n      selector: '[nz-upload-btn]',\n      exportAs: 'nzUploadBtn',\n      host: {\n        class: 'ant-upload',\n        '[attr.tabindex]': '\"0\"',\n        '[attr.role]': '\"button\"',\n        '[class.ant-upload-disabled]': 'options.disabled',\n        '(drop)': 'onFileDrop($event)',\n        '(dragover)': 'onFileDrop($event)'\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<!--\\n  We explicitly bind `style.display` to avoid using an inline style\\n  attribute property (which is not allowed when CSP `unsafe-inline`\\n  is not specified).\\n-->\\n<input\\n  type=\\\"file\\\"\\n  #file\\n  (change)=\\\"onChange($event)\\\"\\n  [attr.accept]=\\\"options.accept\\\"\\n  [attr.directory]=\\\"options.directory ? 'directory' : null\\\"\\n  [attr.webkitdirectory]=\\\"options.directory ? 'webkitdirectory' : null\\\"\\n  [multiple]=\\\"options.multiple\\\"\\n  [style.display]=\\\"'none'\\\"\\n/>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i0.ElementRef\n  }], {\n    file: [{\n      type: ViewChild,\n      args: ['file', {\n        static: true\n      }]\n    }],\n    options: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isImageFileType = type => !!type && type.indexOf('image/') === 0;\nconst MEASURE_SIZE = 200;\nclass NzUploadListComponent {\n  get showPic() {\n    return this.listType === 'picture' || this.listType === 'picture-card';\n  }\n  set items(list) {\n    this.list = list;\n  }\n  genErr(file) {\n    if (file.response && typeof file.response === 'string') {\n      return file.response;\n    }\n    return file.error && file.error.statusText || this.locale.uploadError;\n  }\n  extname(url) {\n    const temp = url.split('/');\n    const filename = temp[temp.length - 1];\n    const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n    return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n  }\n  isImageUrl(file) {\n    if (isImageFileType(file.type)) {\n      return true;\n    }\n    const url = file.thumbUrl || file.url || '';\n    if (!url) {\n      return false;\n    }\n    const extension = this.extname(url);\n    if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg)$/i.test(extension)) {\n      return true;\n    } else if (/^data:/.test(url)) {\n      // other file types of base64\n      return false;\n    } else if (extension) {\n      // other file types which have extension\n      return false;\n    }\n    return true;\n  }\n  getIconType(file) {\n    if (!this.showPic) {\n      return '';\n    }\n    if (file.isUploading || !file.thumbUrl && !file.url) {\n      return 'uploading';\n    } else {\n      return 'thumbnail';\n    }\n  }\n  previewImage(file) {\n    if (!isImageFileType(file.type) || !this.platform.isBrowser) {\n      return of('');\n    }\n    const canvas = this.doc.createElement('canvas');\n    canvas.width = MEASURE_SIZE;\n    canvas.height = MEASURE_SIZE;\n    canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n    this.doc.body.appendChild(canvas);\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    const objectUrl = URL.createObjectURL(file);\n    img.src = objectUrl;\n    return fromEvent(img, 'load').pipe(map(() => {\n      const {\n        width,\n        height\n      } = img;\n      let drawWidth = MEASURE_SIZE;\n      let drawHeight = MEASURE_SIZE;\n      let offsetX = 0;\n      let offsetY = 0;\n      if (width < height) {\n        drawHeight = height * (MEASURE_SIZE / width);\n        offsetY = -(drawHeight - drawWidth) / 2;\n      } else {\n        drawWidth = width * (MEASURE_SIZE / height);\n        offsetX = -(drawWidth - drawHeight) / 2;\n      }\n      try {\n        ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n      } catch {}\n      const dataURL = canvas.toDataURL();\n      this.doc.body.removeChild(canvas);\n      URL.revokeObjectURL(objectUrl);\n      return dataURL;\n    }));\n  }\n  genThumb() {\n    if (!this.platform.isBrowser) {\n      return;\n    }\n    const win = window;\n    if (!this.showPic || typeof document === 'undefined' || typeof win === 'undefined' || !win.FileReader || !win.File) {\n      return;\n    }\n    this.list.filter(file => file.originFileObj instanceof File && file.thumbUrl === undefined).forEach(file => {\n      file.thumbUrl = '';\n      // Caretaker note: we shouldn't use promises here since they're not cancellable.\n      // A promise microtask can be resolved after the view is destroyed. Thus running `detectChanges()`\n      // will cause a runtime exception (`detectChanges()` cannot be run on destroyed views).\n      const dataUrl$ = (this.previewFile ? this.previewFile(file) : this.previewImage(file.originFileObj)).pipe(takeUntil(this.destroy$));\n      this.ngZone.runOutsideAngular(() => {\n        dataUrl$.subscribe(dataUrl => {\n          this.ngZone.run(() => {\n            file.thumbUrl = dataUrl;\n            this.detectChanges();\n          });\n        });\n      });\n    });\n  }\n  showDownload(file) {\n    return !!(this.icons.showDownloadIcon && file.status === 'done');\n  }\n  fixData() {\n    this.list.forEach(file => {\n      file.isUploading = file.status === 'uploading';\n      file.message = this.genErr(file);\n      file.linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n      file.isImageUrl = this.previewIsImage ? this.previewIsImage(file) : this.isImageUrl(file);\n      file.iconType = this.getIconType(file);\n      file.showDownload = this.showDownload(file);\n    });\n  }\n  handlePreview(file, e) {\n    if (!this.onPreview) {\n      return;\n    }\n    e.preventDefault();\n    return this.onPreview(file);\n  }\n  handleRemove(file, e) {\n    e.preventDefault();\n    if (this.onRemove) {\n      this.onRemove(file);\n    }\n    return;\n  }\n  handleDownload(file) {\n    if (typeof this.onDownload === 'function') {\n      this.onDownload(file);\n    } else if (file.url) {\n      window.open(file.url);\n    }\n  }\n  // #endregion\n  constructor(cdr, doc, ngZone, platform) {\n    this.cdr = cdr;\n    this.doc = doc;\n    this.ngZone = ngZone;\n    this.platform = platform;\n    this.list = [];\n    this.locale = {};\n    this.iconRender = null;\n    this.dir = 'ltr';\n    this.destroy$ = new Subject();\n  }\n  detectChanges() {\n    this.fixData();\n    this.cdr.detectChanges();\n  }\n  ngOnChanges() {\n    this.fixData();\n    this.genThumb();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n  }\n  static {\n    this.ɵfac = function NzUploadListComponent_Factory(t) {\n      return new (t || NzUploadListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadListComponent,\n      selectors: [[\"nz-upload-list\"]],\n      hostAttrs: [1, \"ant-upload-list\"],\n      hostVars: 8,\n      hostBindings: function NzUploadListComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-upload-list-rtl\", ctx.dir === \"rtl\")(\"ant-upload-list-text\", ctx.listType === \"text\")(\"ant-upload-list-picture\", ctx.listType === \"picture\")(\"ant-upload-list-picture-card\", ctx.listType === \"picture-card\");\n        }\n      },\n      inputs: {\n        locale: \"locale\",\n        listType: \"listType\",\n        items: \"items\",\n        icons: \"icons\",\n        onPreview: \"onPreview\",\n        onRemove: \"onRemove\",\n        onDownload: \"onDownload\",\n        previewFile: \"previewFile\",\n        previewIsImage: \"previewIsImage\",\n        iconRender: \"iconRender\",\n        dir: \"dir\"\n      },\n      exportAs: [\"nzUploadList\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 1,\n      consts: [[\"icon\", \"\"], [\"iconNode\", \"\"], [\"removeIcon\", \"\"], [\"downloadIcon\", \"\"], [\"downloadOrDelete\", \"\"], [\"preview\", \"\"], [\"noImageThumbTpl\", \"\"], [\"customIconRender\", \"\"], [\"iconNodeFileIcon\", \"\"], [3, \"class\", 4, \"ngFor\", \"ngForOf\"], [\"nz-tooltip\", \"\", 3, \"nzTooltipTitle\"], [1, \"ant-upload-list-item-info\"], [1, \"ant-upload-span\"], [3, \"ngTemplateOutlet\"], [\"class\", \"ant-upload-list-item-actions\", 4, \"ngIf\"], [\"class\", \"ant-upload-list-item-progress\", 4, \"ngIf\"], [3, \"ngSwitch\"], [\"class\", \"ant-upload-list-item-thumbnail\", 3, \"ant-upload-list-item-file\", 4, \"ngSwitchCase\"], [\"class\", \"ant-upload-list-item-thumbnail\", \"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"ant-upload-list-item-file\", \"href\", \"click\", 4, \"ngSwitchCase\"], [\"class\", \"ant-upload-text-icon\", 4, \"ngSwitchDefault\"], [1, \"ant-upload-list-item-thumbnail\"], [3, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-thumbnail\", 3, \"click\", \"href\"], [\"class\", \"ant-upload-list-item-image\", 3, \"src\", 4, \"ngIf\", \"ngIfElse\"], [1, \"ant-upload-list-item-image\", 3, \"src\"], [1, \"ant-upload-text-icon\"], [4, \"ngIf\", \"ngIfElse\"], [4, \"ngSwitchCase\"], [\"nz-icon\", \"\", 3, \"nzType\", 4, \"ngSwitchDefault\"], [\"nz-icon\", \"\", \"nzType\", \"loading\"], [\"nz-icon\", \"\", 3, \"nzType\"], [\"nz-icon\", \"\", \"nzTheme\", \"twotone\", 3, \"nzType\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", \"class\", \"ant-upload-list-item-card-actions-btn\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"nz-button\", \"\", \"nzType\", \"text\", \"nzSize\", \"small\", 1, \"ant-upload-list-item-card-actions-btn\", 3, \"click\"], [\"nz-icon\", \"\", \"nzType\", \"delete\"], [\"nz-icon\", \"\", \"nzType\", \"download\"], [3, \"class\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", \"class\", \"ant-upload-list-item-name\", 3, \"href\", \"click\", 4, \"ngIf\"], [\"class\", \"ant-upload-list-item-name\", 3, \"click\", 4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 1, \"ant-upload-list-item-name\", 3, \"click\", \"href\"], [1, \"ant-upload-list-item-name\", 3, \"click\"], [1, \"ant-upload-list-item-actions\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"href\", \"ngStyle\", \"click\", 4, \"ngIf\"], [4, \"ngIf\"], [\"target\", \"_blank\", \"rel\", \"noopener noreferrer\", 3, \"click\", \"href\", \"ngStyle\"], [\"nz-icon\", \"\", \"nzType\", \"eye\"], [1, \"ant-upload-list-item-progress\"], [\"nzType\", \"line\", 3, \"nzPercent\", \"nzShowInfo\", \"nzStrokeWidth\"]],\n      template: function NzUploadListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, NzUploadListComponent_div_0_Template, 20, 14, \"div\", 9);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngForOf\", ctx.list);\n        }\n      },\n      dependencies: [NgForOf, NzToolTipModule, i2.NzTooltipDirective, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NzIconModule, i3.NzIconDirective, NzButtonModule, i4.NzButtonComponent, i5.ɵNzTransitionPatchDirective, NgStyle, NzProgressModule, i6.NzProgressComponent],\n      encapsulation: 2,\n      data: {\n        animation: [trigger('itemState', [transition(':enter', [style({\n          height: '0',\n          width: '0',\n          opacity: 0\n        }), animate(150, style({\n          height: '*',\n          width: '*',\n          opacity: 1\n        }))]), transition(':leave', [animate(150, style({\n          height: '0',\n          width: '0',\n          opacity: 0\n        }))])])]\n      },\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadListComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload-list',\n      exportAs: 'nzUploadList',\n      animations: [trigger('itemState', [transition(':enter', [style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }), animate(150, style({\n        height: '*',\n        width: '*',\n        opacity: 1\n      }))]), transition(':leave', [animate(150, style({\n        height: '0',\n        width: '0',\n        opacity: 0\n      }))])])],\n      host: {\n        class: 'ant-upload-list',\n        '[class.ant-upload-list-rtl]': `dir === 'rtl'`,\n        '[class.ant-upload-list-text]': `listType === 'text'`,\n        '[class.ant-upload-list-picture]': `listType === 'picture'`,\n        '[class.ant-upload-list-picture-card]': `listType === 'picture-card'`\n      },\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [NgForOf, NzToolTipModule, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NzIconModule, NzButtonModule, NgStyle, NzProgressModule],\n      standalone: true,\n      template: \"<div *ngFor=\\\"let file of list\\\" class=\\\"ant-upload-list-{{ listType }}-container\\\">\\n  <div\\n    class=\\\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\\\"\\n    [attr.data-key]=\\\"file.key\\\"\\n    @itemState\\n    nz-tooltip\\n    [nzTooltipTitle]=\\\"file.status === 'error' ? file.message : null\\\"\\n  >\\n    <ng-template #icon>\\n      <ng-container [ngSwitch]=\\\"file.iconType\\\">\\n        <div\\n          *ngSwitchCase=\\\"'uploading'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isUploading\\\"\\n        >\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n        <a\\n          *ngSwitchCase=\\\"'thumbnail'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isImageUrl\\\"\\n          target=\\\"_blank\\\"\\n          rel=\\\"noopener noreferrer\\\"\\n          [href]=\\\"file.url || file.thumbUrl\\\"\\n          (click)=\\\"handlePreview(file, $event)\\\"\\n        >\\n          <img\\n            *ngIf=\\\"file.isImageUrl; else noImageThumbTpl\\\"\\n            class=\\\"ant-upload-list-item-image\\\"\\n            [src]=\\\"file.thumbUrl || file.url\\\"\\n            [attr.alt]=\\\"file.name\\\"\\n          />\\n        </a>\\n        <div *ngSwitchDefault class=\\\"ant-upload-text-icon\\\">\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n      </ng-container>\\n      <ng-template #noImageThumbTpl>\\n        <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #iconNode let-file>\\n      <ng-container *ngIf=\\\"!iconRender; else customIconRender\\\">\\n        <ng-container [ngSwitch]=\\\"listType\\\">\\n          <ng-container *ngSwitchCase=\\\"'picture'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              <span nz-icon nzType=\\\"loading\\\"></span>\\n            </ng-container>\\n          </ng-container>\\n          <ng-container *ngSwitchCase=\\\"'picture-card'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              {{ locale.uploading }}\\n            </ng-container>\\n          </ng-container>\\n          <span *ngSwitchDefault nz-icon [nzType]=\\\"file.isUploading ? 'loading' : 'paper-clip'\\\"></span>\\n        </ng-container>\\n      </ng-container>\\n      <ng-template\\n        #customIconRender\\n        [ngTemplateOutlet]=\\\"iconRender\\\"\\n        [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"\\n      ></ng-template>\\n      <ng-template #iconNodeFileIcon>\\n        <span nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\"></span>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #removeIcon>\\n      <button\\n        *ngIf=\\\"icons.showRemoveIcon\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleRemove(file, $event)\\\"\\n        [attr.title]=\\\"locale.removeFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"delete\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadIcon>\\n      <button\\n        *ngIf=\\\"file.showDownload\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleDownload(file)\\\"\\n        [attr.title]=\\\"locale.downloadFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"download\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadOrDelete>\\n      <span\\n        *ngIf=\\\"listType !== 'picture-card'\\\"\\n        class=\\\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\\\"\\n      >\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n      </span>\\n    </ng-template>\\n    <ng-template #preview>\\n      <a\\n        *ngIf=\\\"file.url\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        [href]=\\\"file.url\\\"\\n        [attr.download]=\\\"file.linkProps && file.linkProps.download\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </a>\\n      <span\\n        *ngIf=\\\"!file.url\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </span>\\n      <ng-template [ngTemplateOutlet]=\\\"downloadOrDelete\\\"></ng-template>\\n    </ng-template>\\n    <div class=\\\"ant-upload-list-item-info\\\">\\n      <span class=\\\"ant-upload-span\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"icon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"preview\\\"></ng-template>\\n      </span>\\n    </div>\\n    <span *ngIf=\\\"listType === 'picture-card' && !file.isUploading\\\" class=\\\"ant-upload-list-item-actions\\\">\\n      <a\\n        *ngIf=\\\"icons.showPreviewIcon\\\"\\n        [href]=\\\"file.url || file.thumbUrl\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        [attr.title]=\\\"locale.previewFile\\\"\\n        [ngStyle]=\\\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        <span nz-icon nzType=\\\"eye\\\"></span>\\n      </a>\\n      <ng-container *ngIf=\\\"file.status === 'done'\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n      </ng-container>\\n      <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n    </span>\\n    <div *ngIf=\\\"file.isUploading\\\" class=\\\"ant-upload-list-item-progress\\\">\\n      <nz-progress [nzPercent]=\\\"file.percent!\\\" nzType=\\\"line\\\" [nzShowInfo]=\\\"false\\\" [nzStrokeWidth]=\\\"2\\\"></nz-progress>\\n    </div>\\n  </div>\\n</div>\\n\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.Platform\n  }], {\n    locale: [{\n      type: Input\n    }],\n    listType: [{\n      type: Input\n    }],\n    items: [{\n      type: Input\n    }],\n    icons: [{\n      type: Input\n    }],\n    onPreview: [{\n      type: Input\n    }],\n    onRemove: [{\n      type: Input\n    }],\n    onDownload: [{\n      type: Input\n    }],\n    previewFile: [{\n      type: Input\n    }],\n    previewIsImage: [{\n      type: Input\n    }],\n    iconRender: [{\n      type: Input\n    }],\n    dir: [{\n      type: Input\n    }]\n  });\n})();\nclass NzUploadComponent {\n  set nzShowUploadList(value) {\n    this._showUploadList = typeof value === 'boolean' ? toBoolean(value) : value;\n  }\n  get nzShowUploadList() {\n    return this._showUploadList;\n  }\n  zipOptions() {\n    if (typeof this.nzShowUploadList === 'boolean' && this.nzShowUploadList) {\n      this.nzShowUploadList = {\n        showPreviewIcon: true,\n        showRemoveIcon: true,\n        showDownloadIcon: true\n      };\n    }\n    // filters\n    const filters = this.nzFilter.slice();\n    if (this.nzMultiple && this.nzLimit > 0 && filters.findIndex(w => w.name === 'limit') === -1) {\n      filters.push({\n        name: 'limit',\n        fn: fileList => fileList.slice(-this.nzLimit)\n      });\n    }\n    if (this.nzSize > 0 && filters.findIndex(w => w.name === 'size') === -1) {\n      filters.push({\n        name: 'size',\n        fn: fileList => fileList.filter(w => w.size / 1024 <= this.nzSize)\n      });\n    }\n    if (this.nzFileType && this.nzFileType.length > 0 && filters.findIndex(w => w.name === 'type') === -1) {\n      const types = this.nzFileType.split(',');\n      filters.push({\n        name: 'type',\n        fn: fileList => fileList.filter(w => ~types.indexOf(w.type))\n      });\n    }\n    this._btnOptions = {\n      disabled: this.nzDisabled,\n      accept: this.nzAccept,\n      action: this.nzAction,\n      directory: this.nzDirectory,\n      openFileDialogOnClick: this.nzOpenFileDialogOnClick,\n      beforeUpload: this.nzBeforeUpload,\n      customRequest: this.nzCustomRequest,\n      data: this.nzData,\n      headers: this.nzHeaders,\n      name: this.nzName,\n      multiple: this.nzMultiple,\n      withCredentials: this.nzWithCredentials,\n      filters,\n      transformFile: this.nzTransformFile,\n      onStart: this.onStart,\n      onProgress: this.onProgress,\n      onSuccess: this.onSuccess,\n      onError: this.onError\n    };\n    return this;\n  }\n  // #endregion\n  constructor(ngZone, document, cdr, i18n, directionality) {\n    this.ngZone = ngZone;\n    this.document = document;\n    this.cdr = cdr;\n    this.i18n = i18n;\n    this.directionality = directionality;\n    this.destroy$ = new Subject();\n    this.dir = 'ltr';\n    // #region fields\n    this.nzType = 'select';\n    this.nzLimit = 0;\n    this.nzSize = 0;\n    this.nzDirectory = false;\n    this.nzOpenFileDialogOnClick = true;\n    this.nzFilter = [];\n    this.nzFileList = [];\n    this.nzDisabled = false;\n    this.nzListType = 'text';\n    this.nzMultiple = false;\n    this.nzName = 'file';\n    this._showUploadList = true;\n    this.nzShowButton = true;\n    this.nzWithCredentials = false;\n    this.nzIconRender = null;\n    this.nzFileListRender = null;\n    this.nzChange = new EventEmitter();\n    this.nzFileListChange = new EventEmitter();\n    this.onStart = file => {\n      if (!this.nzFileList) {\n        this.nzFileList = [];\n      }\n      const targetItem = this.fileToObject(file);\n      targetItem.status = 'uploading';\n      this.nzFileList = this.nzFileList.concat(targetItem);\n      this.nzFileListChange.emit(this.nzFileList);\n      this.nzChange.emit({\n        file: targetItem,\n        fileList: this.nzFileList,\n        type: 'start'\n      });\n      this.detectChangesList();\n    };\n    this.onProgress = (e, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.percent = e.percent;\n      this.nzChange.emit({\n        event: e,\n        file: {\n          ...targetItem\n        },\n        fileList: this.nzFileList,\n        type: 'progress'\n      });\n      this.detectChangesList();\n    };\n    this.onSuccess = (res, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.status = 'done';\n      targetItem.response = res;\n      this.nzChange.emit({\n        file: {\n          ...targetItem\n        },\n        fileList,\n        type: 'success'\n      });\n      this.detectChangesList();\n    };\n    this.onError = (err, file) => {\n      const fileList = this.nzFileList;\n      const targetItem = this.getFileItem(file, fileList);\n      targetItem.error = err;\n      targetItem.status = 'error';\n      this.nzChange.emit({\n        file: {\n          ...targetItem\n        },\n        fileList,\n        type: 'error'\n      });\n      this.detectChangesList();\n    };\n    this.onRemove = file => {\n      this.uploadComp.abort(file);\n      file.status = 'removed';\n      const fnRes = typeof this.nzRemove === 'function' ? this.nzRemove(file) : this.nzRemove == null ? true : this.nzRemove;\n      (fnRes instanceof Observable ? fnRes : of(fnRes)).pipe(filter(res => res)).subscribe(() => {\n        this.nzFileList = this.removeFileItem(file, this.nzFileList);\n        this.nzChange.emit({\n          file,\n          fileList: this.nzFileList,\n          type: 'removed'\n        });\n        this.nzFileListChange.emit(this.nzFileList);\n        this.cdr.detectChanges();\n      });\n    };\n    // #endregion\n    // #region styles\n    this.prefixCls = 'ant-upload';\n    this.classList = [];\n  }\n  // #region upload\n  fileToObject(file) {\n    return {\n      lastModified: file.lastModified,\n      lastModifiedDate: file.lastModifiedDate,\n      name: file.filename || file.name,\n      size: file.size,\n      type: file.type,\n      uid: file.uid,\n      response: file.response,\n      error: file.error,\n      percent: 0,\n      originFileObj: file\n    };\n  }\n  getFileItem(file, fileList) {\n    return fileList.filter(item => item.uid === file.uid)[0];\n  }\n  removeFileItem(file, fileList) {\n    return fileList.filter(item => item.uid !== file.uid);\n  }\n  // skip safari bug\n  fileDrop(e) {\n    if (e.type === this.dragState) {\n      return;\n    }\n    this.dragState = e.type;\n    this.setClassMap();\n  }\n  // #endregion\n  // #region list\n  detectChangesList() {\n    this.cdr.detectChanges();\n    this.listComp?.detectChanges();\n  }\n  setClassMap() {\n    let subCls = [];\n    if (this.nzType === 'drag') {\n      if (this.nzFileList.some(file => file.status === 'uploading')) {\n        subCls.push(`${this.prefixCls}-drag-uploading`);\n      }\n      if (this.dragState === 'dragover') {\n        subCls.push(`${this.prefixCls}-drag-hover`);\n      }\n    } else {\n      subCls = [`${this.prefixCls}-select-${this.nzListType}`];\n    }\n    this.classList = [this.prefixCls, `${this.prefixCls}-${this.nzType}`, ...subCls, this.nzDisabled && `${this.prefixCls}-disabled` || '', this.dir === 'rtl' && `${this.prefixCls}-rtl` || ''].filter(item => !!item);\n    this.cdr.detectChanges();\n  }\n  // #endregion\n  ngOnInit() {\n    this.dir = this.directionality.value;\n    this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe(direction => {\n      this.dir = direction;\n      this.setClassMap();\n      this.cdr.detectChanges();\n    });\n    this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n      this.locale = this.i18n.getLocaleData('Upload');\n      this.detectChangesList();\n    });\n  }\n  ngAfterViewInit() {\n    // fix firefox drop open new tab\n    this.ngZone.runOutsideAngular(() => fromEvent(this.document.body, 'drop').pipe(takeUntil(this.destroy$)).subscribe(event => {\n      event.preventDefault();\n      event.stopPropagation();\n    }));\n  }\n  ngOnChanges() {\n    this.zipOptions().setClassMap();\n  }\n  ngOnDestroy() {\n    this.destroy$.next();\n    this.destroy$.complete();\n  }\n  static {\n    this.ɵfac = function NzUploadComponent_Factory(t) {\n      return new (t || NzUploadComponent)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(DOCUMENT), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1$2.NzI18nService), i0.ɵɵdirectiveInject(i2$1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: NzUploadComponent,\n      selectors: [[\"nz-upload\"]],\n      viewQuery: function NzUploadComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c5, 5);\n          i0.ɵɵviewQuery(_c6, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.uploadComp = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.listComp = _t.first);\n        }\n      },\n      hostVars: 2,\n      hostBindings: function NzUploadComponent_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"ant-upload-picture-card-wrapper\", ctx.nzListType === \"picture-card\");\n        }\n      },\n      inputs: {\n        nzType: \"nzType\",\n        nzLimit: \"nzLimit\",\n        nzSize: \"nzSize\",\n        nzFileType: \"nzFileType\",\n        nzAccept: \"nzAccept\",\n        nzAction: \"nzAction\",\n        nzDirectory: \"nzDirectory\",\n        nzOpenFileDialogOnClick: \"nzOpenFileDialogOnClick\",\n        nzBeforeUpload: \"nzBeforeUpload\",\n        nzCustomRequest: \"nzCustomRequest\",\n        nzData: \"nzData\",\n        nzFilter: \"nzFilter\",\n        nzFileList: \"nzFileList\",\n        nzDisabled: \"nzDisabled\",\n        nzHeaders: \"nzHeaders\",\n        nzListType: \"nzListType\",\n        nzMultiple: \"nzMultiple\",\n        nzName: \"nzName\",\n        nzShowUploadList: \"nzShowUploadList\",\n        nzShowButton: \"nzShowButton\",\n        nzWithCredentials: \"nzWithCredentials\",\n        nzRemove: \"nzRemove\",\n        nzPreview: \"nzPreview\",\n        nzPreviewFile: \"nzPreviewFile\",\n        nzPreviewIsImage: \"nzPreviewIsImage\",\n        nzTransformFile: \"nzTransformFile\",\n        nzDownload: \"nzDownload\",\n        nzIconRender: \"nzIconRender\",\n        nzFileListRender: \"nzFileListRender\"\n      },\n      outputs: {\n        nzChange: \"nzChange\",\n        nzFileListChange: \"nzFileListChange\"\n      },\n      exportAs: [\"nzUpload\"],\n      standalone: true,\n      features: [i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 11,\n      vars: 2,\n      consts: [[\"list\", \"\"], [\"con\", \"\"], [\"btn\", \"\"], [\"select\", \"\"], [\"pic\", \"\"], [\"listComp\", \"\"], [\"uploadComp\", \"\"], [4, \"ngIf\", \"ngIfElse\"], [3, \"display\", \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"locale\", \"listType\", \"items\", \"icons\", \"iconRender\", \"previewFile\", \"previewIsImage\", \"onPreview\", \"onRemove\", \"onDownload\", \"dir\"], [4, \"ngTemplateOutlet\", \"ngTemplateOutletContext\"], [3, \"ngClass\"], [\"nz-upload-btn\", \"\", 3, \"options\"], [3, \"ngTemplateOutlet\"], [3, \"drop\", \"dragover\", \"dragleave\", \"ngClass\"], [\"nz-upload-btn\", \"\", 1, \"ant-upload-btn\", 3, \"options\"], [1, \"ant-upload-drag-container\"]],\n      template: function NzUploadComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵtemplate(0, NzUploadComponent_ng_template_0_Template, 2, 2, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(2, NzUploadComponent_ng_template_2_Template, 1, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor)(4, NzUploadComponent_ng_template_4_Template, 4, 5, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor)(6, NzUploadComponent_ng_container_6_Template, 7, 4, \"ng-container\", 7)(7, NzUploadComponent_ng_template_7_Template, 1, 2, \"ng-template\", null, 3, i0.ɵɵtemplateRefExtractor)(9, NzUploadComponent_ng_template_9_Template, 2, 2, \"ng-template\", null, 4, i0.ɵɵtemplateRefExtractor);\n        }\n        if (rf & 2) {\n          const select_r7 = i0.ɵɵreference(8);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.nzType === \"drag\")(\"ngIfElse\", select_r7);\n        }\n      },\n      dependencies: [NzUploadListComponent, NgIf, NgTemplateOutlet, NgClass, NzUploadBtnComponent],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n__decorate([InputNumber()], NzUploadComponent.prototype, \"nzLimit\", void 0);\n__decorate([InputNumber()], NzUploadComponent.prototype, \"nzSize\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzDirectory\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzOpenFileDialogOnClick\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzDisabled\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzMultiple\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzShowButton\", void 0);\n__decorate([InputBoolean()], NzUploadComponent.prototype, \"nzWithCredentials\", void 0);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadComponent, [{\n    type: Component,\n    args: [{\n      selector: 'nz-upload',\n      exportAs: 'nzUpload',\n      preserveWhitespaces: false,\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        '[class.ant-upload-picture-card-wrapper]': 'nzListType === \"picture-card\"'\n      },\n      imports: [NzUploadListComponent, NgIf, NgTemplateOutlet, NgClass, NzUploadBtnComponent],\n      standalone: true,\n      template: \"<ng-template #list>\\n  <nz-upload-list\\n    *ngIf=\\\"locale && !nzFileListRender\\\"\\n    #listComp\\n    [style.display]=\\\"nzShowUploadList ? '' : 'none'\\\"\\n    [locale]=\\\"locale\\\"\\n    [listType]=\\\"nzListType\\\"\\n    [items]=\\\"nzFileList || []\\\"\\n    [icons]=\\\"$any(nzShowUploadList)\\\"\\n    [iconRender]=\\\"nzIconRender\\\"\\n    [previewFile]=\\\"nzPreviewFile\\\"\\n    [previewIsImage]=\\\"nzPreviewIsImage\\\"\\n    [onPreview]=\\\"nzPreview\\\"\\n    [onRemove]=\\\"onRemove\\\"\\n    [onDownload]=\\\"nzDownload\\\"\\n    [dir]=\\\"dir\\\"\\n  ></nz-upload-list>\\n  <ng-container *ngIf=\\\"nzFileListRender\\\">\\n    <ng-container *ngTemplateOutlet=\\\"nzFileListRender; context: { $implicit: nzFileList }\\\"></ng-container>\\n  </ng-container>\\n</ng-template>\\n<ng-template #con><ng-content></ng-content></ng-template>\\n<ng-template #btn>\\n  <div [ngClass]=\\\"classList\\\" [style.display]=\\\"nzShowButton ? '' : 'none'\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\">\\n      <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n    </div>\\n  </div>\\n</ng-template>\\n<ng-container *ngIf=\\\"nzType === 'drag'; else select\\\">\\n  <div [ngClass]=\\\"classList\\\" (drop)=\\\"fileDrop($event)\\\" (dragover)=\\\"fileDrop($event)\\\" (dragleave)=\\\"fileDrop($event)\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\" class=\\\"ant-upload-btn\\\">\\n      <div class=\\\"ant-upload-drag-container\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n      </div>\\n    </div>\\n  </div>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-container>\\n<ng-template #select>\\n  <ng-container *ngIf=\\\"nzListType === 'picture-card'; else pic\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  </ng-container>\\n</ng-template>\\n<ng-template #pic>\\n  <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-template>\\n\"\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1$2.NzI18nService\n  }, {\n    type: i2$1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    uploadComp: [{\n      type: ViewChild,\n      args: ['uploadComp', {\n        static: false\n      }]\n    }],\n    listComp: [{\n      type: ViewChild,\n      args: ['listComp', {\n        static: false\n      }]\n    }],\n    nzType: [{\n      type: Input\n    }],\n    nzLimit: [{\n      type: Input\n    }],\n    nzSize: [{\n      type: Input\n    }],\n    nzFileType: [{\n      type: Input\n    }],\n    nzAccept: [{\n      type: Input\n    }],\n    nzAction: [{\n      type: Input\n    }],\n    nzDirectory: [{\n      type: Input\n    }],\n    nzOpenFileDialogOnClick: [{\n      type: Input\n    }],\n    nzBeforeUpload: [{\n      type: Input\n    }],\n    nzCustomRequest: [{\n      type: Input\n    }],\n    nzData: [{\n      type: Input\n    }],\n    nzFilter: [{\n      type: Input\n    }],\n    nzFileList: [{\n      type: Input\n    }],\n    nzDisabled: [{\n      type: Input\n    }],\n    nzHeaders: [{\n      type: Input\n    }],\n    nzListType: [{\n      type: Input\n    }],\n    nzMultiple: [{\n      type: Input\n    }],\n    nzName: [{\n      type: Input\n    }],\n    nzShowUploadList: [{\n      type: Input\n    }],\n    nzShowButton: [{\n      type: Input\n    }],\n    nzWithCredentials: [{\n      type: Input\n    }],\n    nzRemove: [{\n      type: Input\n    }],\n    nzPreview: [{\n      type: Input\n    }],\n    nzPreviewFile: [{\n      type: Input\n    }],\n    nzPreviewIsImage: [{\n      type: Input\n    }],\n    nzTransformFile: [{\n      type: Input\n    }],\n    nzDownload: [{\n      type: Input\n    }],\n    nzIconRender: [{\n      type: Input\n    }],\n    nzFileListRender: [{\n      type: Input\n    }],\n    nzChange: [{\n      type: Output\n    }],\n    nzFileListChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzUploadModule {\n  static {\n    this.ɵfac = function NzUploadModule_Factory(t) {\n      return new (t || NzUploadModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: NzUploadModule,\n      imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n      exports: [NzUploadComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [NzUploadComponent, NzUploadListComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(NzUploadModule, [{\n    type: NgModule,\n    args: [{\n      imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n      exports: [NzUploadComponent]\n    }]\n  }], null, null);\n})();\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzUploadBtnComponent, NzUploadComponent, NzUploadListComponent, NzUploadModule };", "map": {"version": 3, "names": ["ENTER", "i1", "HttpRequest", "HttpHeaders", "HttpEventType", "HttpResponse", "i0", "Component", "ViewEncapsulation", "Optional", "ViewChild", "Input", "ChangeDetectionStrategy", "Inject", "EventEmitter", "Output", "NgModule", "of", "Observable", "Subscription", "Subject", "fromEvent", "switchMap", "map", "tap", "takeUntil", "filter", "warn", "trigger", "transition", "style", "animate", "DOCUMENT", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgSwitch", "NgTemplateOutlet", "NgIf", "NgSwitchDefault", "NgSwitchCase", "NgStyle", "Ng<PERSON><PERSON>", "i4", "NzButtonModule", "i3", "NzIconModule", "i6", "NzProgressModule", "i2", "NzToolTipModule", "i1$1", "i5", "__decorate", "toBoolean", "InputNumber", "InputBoolean", "i1$2", "i2$1", "_c0", "_c1", "_c2", "_c3", "a0", "$implicit", "_c4", "opacity", "NzUploadListComponent_div_0_ng_template_2_div_1_ng_template_1_Template", "rf", "ctx", "NzUploadListComponent_div_0_ng_template_2_div_1_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "file_r1", "ɵɵnextContext", "iconNode_r2", "ɵɵreference", "ɵɵclassProp", "isUploading", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "NzUploadListComponent_div_0_ng_template_2_a_2_img_1_Template", "ɵɵelement", "thumbUrl", "url", "ɵɵsanitizeUrl", "ɵɵattribute", "name", "NzUploadListComponent_div_0_ng_template_2_a_2_Template", "_r3", "ɵɵgetCurrentView", "ɵɵlistener", "NzUploadListComponent_div_0_ng_template_2_a_2_Template_a_click_0_listener", "$event", "ɵɵrestoreView", "ctx_r3", "ɵɵresetView", "handlePreview", "noImageThumbTpl_r5", "isImageUrl", "NzUploadListComponent_div_0_ng_template_2_div_3_ng_template_1_Template", "NzUploadListComponent_div_0_ng_template_2_div_3_Template", "NzUploadListComponent_div_0_ng_template_2_ng_template_4_ng_template_0_Template", "NzUploadListComponent_div_0_ng_template_2_ng_template_4_Template", "NzUploadListComponent_div_0_ng_template_2_Template", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "ɵɵtemplateRefExtractor", "iconType", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_ng_container_1_Template", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_2_Template", "file_r6", "iconNodeFileIcon_r7", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_ng_container_1_Template", "ɵɵtext", "ɵɵtextInterpolate1", "locale", "uploading", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_ng_container_3_Template", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_span_4_Template", "NzUploadListComponent_div_0_ng_template_4_ng_container_0_Template", "listType", "NzUploadListComponent_div_0_ng_template_4_ng_template_1_Template", "NzUploadListComponent_div_0_ng_template_4_ng_template_3_Template", "NzUploadListComponent_div_0_ng_template_4_Template", "customIconRender_r8", "iconRender", "NzUploadListComponent_div_0_ng_template_6_button_0_Template", "_r9", "NzUploadListComponent_div_0_ng_template_6_button_0_Template_button_click_0_listener", "handleRemove", "removeFile", "NzUploadListComponent_div_0_ng_template_6_Template", "icons", "showRemoveIcon", "NzUploadListComponent_div_0_ng_template_8_button_0_Template", "_r10", "NzUploadListComponent_div_0_ng_template_8_button_0_Template_button_click_0_listener", "handleDownload", "downloadFile", "NzUploadListComponent_div_0_ng_template_8_Template", "showDownload", "NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_1_Template", "NzUploadListComponent_div_0_ng_template_10_span_0_ng_template_2_Template", "NzUploadListComponent_div_0_ng_template_10_span_0_Template", "removeIcon_r11", "downloadIcon_r12", "ɵɵclassMapInterpolate1", "NzUploadListComponent_div_0_ng_template_10_Template", "NzUploadListComponent_div_0_ng_template_12_a_0_Template", "_r13", "NzUploadListComponent_div_0_ng_template_12_a_0_Template_a_click_0_listener", "linkProps", "download", "NzUploadListComponent_div_0_ng_template_12_span_1_Template", "_r14", "NzUploadListComponent_div_0_ng_template_12_span_1_Template_span_click_0_listener", "NzUploadListComponent_div_0_ng_template_12_ng_template_2_Template", "NzUploadListComponent_div_0_ng_template_12_Template", "downloadOrDelete_r15", "NzUploadListComponent_div_0_ng_template_16_Template", "NzUploadListComponent_div_0_ng_template_17_Template", "NzUploadListComponent_div_0_span_18_a_1_Template", "_r16", "NzUploadListComponent_div_0_span_18_a_1_Template_a_click_0_listener", "ɵɵpureFunction0", "previewFile", "NzUploadListComponent_div_0_span_18_ng_container_2_ng_template_1_Template", "NzUploadListComponent_div_0_span_18_ng_container_2_Template", "NzUploadListComponent_div_0_span_18_ng_template_3_Template", "NzUploadListComponent_div_0_span_18_Template", "showPreviewIcon", "status", "NzUploadListComponent_div_0_div_19_Template", "percent", "NzUploadListComponent_div_0_Template", "icon_r17", "preview_r18", "ɵɵclassMapInterpolate2", "undefined", "message", "key", "_c5", "_c6", "_c7", "NzUploadComponent_ng_template_0_nz_upload_list_0_Template", "ctx_r0", "ɵɵstyleProp", "nzShowUploadList", "nzListType", "nzFileList", "nzIconRender", "nzPreviewFile", "nzPreviewIsImage", "nzPreview", "onRemove", "nzDownload", "dir", "NzUploadComponent_ng_template_0_ng_container_1_ng_container_1_Template", "ɵɵelementContainer", "NzUploadComponent_ng_template_0_ng_container_1_Template", "nzFileListRender", "NzUploadComponent_ng_template_0_Template", "NzUploadComponent_ng_template_2_Template", "ɵɵprojection", "NzUploadComponent_ng_template_4_ng_template_3_Template", "NzUploadComponent_ng_template_4_Template", "con_r2", "nzShowButton", "classList", "_btnOptions", "NzUploadComponent_ng_container_6_ng_template_5_Template", "NzUploadComponent_ng_container_6_ng_template_6_Template", "NzUploadComponent_ng_container_6_Template", "NzUploadComponent_ng_container_6_Template_div_drop_1_listener", "fileDrop", "NzUploadComponent_ng_container_6_Template_div_dragover_1_listener", "NzUploadComponent_ng_container_6_Template_div_dragleave_1_listener", "list_r4", "NzUploadComponent_ng_template_7_ng_container_0_ng_template_1_Template", "NzUploadComponent_ng_template_7_ng_container_0_ng_template_2_Template", "NzUploadComponent_ng_template_7_ng_container_0_Template", "btn_r5", "NzUploadComponent_ng_template_7_Template", "pic_r6", "NzUploadComponent_ng_template_9_ng_template_0_Template", "NzUploadComponent_ng_template_9_ng_template_1_Template", "NzUploadComponent_ng_template_9_Template", "NzUploadBtnComponent", "onClick", "options", "disabled", "openFileDialogOnClick", "file", "nativeElement", "click", "onFileDrop", "e", "type", "preventDefault", "directory", "traverseFileTree", "dataTransfer", "items", "files", "Array", "prototype", "slice", "call", "attrAccept", "accept", "length", "uploadFiles", "onChange", "hie", "target", "value", "_traverseFileTree", "item", "path", "isFile", "isDirectory", "<PERSON><PERSON><PERSON><PERSON>", "createReader", "readEntries", "entries", "entrieItem", "webkitGetAsEntry", "acceptedFiles", "acceptedFilesArray", "isArray", "split", "fileName", "mimeType", "baseMimeType", "replace", "some", "validType", "trim", "char<PERSON>t", "toLowerCase", "indexOf", "test", "attachUid", "uid", "Math", "random", "toString", "substring", "fileList", "filters$", "filters", "for<PERSON>ach", "f", "pipe", "list", "fnRes", "fn", "subscribe", "upload", "beforeUpload", "post", "before", "processedFile", "processedFileType", "Object", "destroy", "process$", "transformedFile", "opt", "action", "data", "headers", "transformFile", "args", "postFile", "withCredentials", "onProgress", "onSuccess", "ret", "xhr", "clean", "onError", "actionResult", "res", "transformResult", "newFile", "dataResult", "headersResult", "req$", "customRequest", "reqs", "onStart", "formData", "FormData", "keys", "append", "req", "reportProgress", "http", "request", "event", "UploadProgress", "total", "loaded", "body", "err", "abort", "unsubscribe", "constructor", "ngZone", "elementRef", "destroy$", "Error", "ngOnInit", "runOutsideAngular", "keyCode", "ngOnDestroy", "next", "ɵfac", "NzUploadBtnComponent_Factory", "t", "ɵɵdirectiveInject", "NgZone", "HttpClient", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "selectors", "viewQuery", "NzUploadBtnComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "NzUploadBtnComponent_HostBindings", "NzUploadBtnComponent_drop_HostBindingHandler", "NzUploadBtnComponent_dragover_HostBindingHandler", "inputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "NzUploadBtnComponent_Template", "_r1", "ɵɵprojectionDef", "NzUploadBtnComponent_Template_input_change_0_listener", "multiple", "encapsulation", "ngDevMode", "ɵsetClassMetadata", "selector", "host", "class", "preserveWhitespaces", "None", "decorators", "static", "isImageFileType", "MEASURE_SIZE", "NzUploadListComponent", "showPic", "gen<PERSON>rr", "response", "error", "statusText", "uploadError", "extname", "temp", "filename", "filenameWithoutSuffix", "exec", "extension", "getIconType", "previewImage", "platform", "<PERSON><PERSON><PERSON><PERSON>", "canvas", "doc", "createElement", "width", "height", "cssText", "append<PERSON><PERSON><PERSON>", "getContext", "img", "Image", "objectUrl", "URL", "createObjectURL", "src", "drawWidth", "drawHeight", "offsetX", "offsetY", "drawImage", "dataURL", "toDataURL", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "gen<PERSON><PERSON>b", "win", "window", "document", "FileReader", "File", "originFileObj", "dataUrl$", "dataUrl", "run", "detectChanges", "showDownloadIcon", "fixData", "JSON", "parse", "previewIsImage", "onPreview", "onDownload", "open", "cdr", "ngOnChanges", "NzUploadListComponent_Factory", "ChangeDetectorRef", "Platform", "NzUploadListComponent_HostBindings", "ɵɵNgOnChangesFeature", "NzUploadListComponent_Template", "dependencies", "NzTooltipDirective", "NzIconDirective", "NzButtonComponent", "ɵNzTransitionPatchDirective", "NzProgressComponent", "animation", "changeDetection", "animations", "OnPush", "imports", "NzUploadComponent", "_showUploadList", "zipOptions", "nzFilter", "nzMultiple", "nzLimit", "findIndex", "w", "push", "nzSize", "size", "nzFileType", "types", "nzDisabled", "nzAccept", "nzAction", "nzDirectory", "nzOpenFileDialogOnClick", "nzBeforeUpload", "nzCustomRequest", "nzData", "nzHeaders", "nzName", "nzWithCredentials", "nzTransformFile", "i18n", "directionality", "nzType", "nzChange", "nzFileListChange", "targetItem", "fileToObject", "concat", "emit", "detectChangesList", "getFileItem", "uploadComp", "nzRemove", "removeFileItem", "prefixCls", "lastModified", "lastModifiedDate", "dragState", "setClassMap", "listComp", "subCls", "change", "direction", "localeChange", "getLocaleData", "ngAfterViewInit", "stopPropagation", "complete", "NzUploadComponent_Factory", "NzI18nService", "Directionality", "NzUploadComponent_Query", "NzUploadComponent_HostBindings", "outputs", "NzUploadComponent_Template", "select_r7", "NzUploadModule", "NzUploadModule_Factory", "ɵmod", "ɵɵdefineNgModule", "exports", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/ng-zorro-antd/fesm2022/ng-zorro-antd-upload.mjs"], "sourcesContent": ["import { ENTER } from '@angular/cdk/keycodes';\nimport * as i1 from '@angular/common/http';\nimport { HttpRequest, HttpHeaders, HttpEventType, HttpResponse } from '@angular/common/http';\nimport * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, Optional, ViewChild, Input, ChangeDetectionStrategy, Inject, EventEmitter, Output, NgModule } from '@angular/core';\nimport { of, Observable, Subscription, Subject, fromEvent } from 'rxjs';\nimport { switchMap, map, tap, takeUntil, filter } from 'rxjs/operators';\nimport { warn } from 'ng-zorro-antd/core/logger';\nimport { trigger, transition, style, animate } from '@angular/animations';\nimport { DOCUMENT, NgForOf, NgSwitch, NgTemplateOutlet, NgIf, NgSwitchDefault, NgSwitchCase, NgStyle, NgClass } from '@angular/common';\nimport * as i4 from 'ng-zorro-antd/button';\nimport { NzButtonModule } from 'ng-zorro-antd/button';\nimport * as i3 from 'ng-zorro-antd/icon';\nimport { NzIconModule } from 'ng-zorro-antd/icon';\nimport * as i6 from 'ng-zorro-antd/progress';\nimport { NzProgressModule } from 'ng-zorro-antd/progress';\nimport * as i2 from 'ng-zorro-antd/tooltip';\nimport { NzToolTipModule } from 'ng-zorro-antd/tooltip';\nimport * as i1$1 from '@angular/cdk/platform';\nimport * as i5 from 'ng-zorro-antd/core/transition-patch';\nimport { __decorate } from 'tslib';\nimport { toBoolean, InputNumber, InputBoolean } from 'ng-zorro-antd/core/util';\nimport * as i1$2 from 'ng-zorro-antd/i18n';\nimport * as i2$1 from '@angular/cdk/bidi';\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzUploadBtnComponent {\n    onClick() {\n        if (this.options.disabled || !this.options.openFileDialogOnClick) {\n            return;\n        }\n        this.file.nativeElement.click();\n    }\n    // skip safari bug\n    onFileDrop(e) {\n        if (this.options.disabled || e.type === 'dragover') {\n            e.preventDefault();\n            return;\n        }\n        if (this.options.directory) {\n            this.traverseFileTree(e.dataTransfer.items);\n        }\n        else {\n            const files = Array.prototype.slice\n                .call(e.dataTransfer.files)\n                .filter((file) => this.attrAccept(file, this.options.accept));\n            if (files.length) {\n                this.uploadFiles(files);\n            }\n        }\n        e.preventDefault();\n    }\n    onChange(e) {\n        if (this.options.disabled) {\n            return;\n        }\n        const hie = e.target;\n        this.uploadFiles(hie.files);\n        hie.value = '';\n    }\n    traverseFileTree(files) {\n        const _traverseFileTree = (item, path) => {\n            if (item.isFile) {\n                item.file((file) => {\n                    if (this.attrAccept(file, this.options.accept)) {\n                        this.uploadFiles([file]);\n                    }\n                });\n            }\n            else if (item.isDirectory) {\n                const dirReader = item.createReader();\n                dirReader.readEntries((entries) => {\n                    for (const entrieItem of entries) {\n                        _traverseFileTree(entrieItem, `${path}${item.name}/`);\n                    }\n                });\n            }\n        };\n        for (const file of files) {\n            _traverseFileTree(file.webkitGetAsEntry(), '');\n        }\n    }\n    attrAccept(file, acceptedFiles) {\n        if (file && acceptedFiles) {\n            const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(',');\n            const fileName = `${file.name}`;\n            const mimeType = `${file.type}`;\n            const baseMimeType = mimeType.replace(/\\/.*$/, '');\n            return acceptedFilesArray.some(type => {\n                const validType = type.trim();\n                if (validType.charAt(0) === '.') {\n                    return (fileName\n                        .toLowerCase()\n                        .indexOf(validType.toLowerCase(), fileName.toLowerCase().length - validType.toLowerCase().length) !== -1);\n                }\n                else if (/\\/\\*$/.test(validType)) {\n                    // This is something like a image/* mime type\n                    return baseMimeType === validType.replace(/\\/.*$/, '');\n                }\n                return mimeType === validType;\n            });\n        }\n        return true;\n    }\n    attachUid(file) {\n        if (!file.uid) {\n            file.uid = Math.random().toString(36).substring(2);\n        }\n        return file;\n    }\n    uploadFiles(fileList) {\n        let filters$ = of(Array.prototype.slice.call(fileList));\n        if (this.options.filters) {\n            this.options.filters.forEach(f => {\n                filters$ = filters$.pipe(switchMap(list => {\n                    const fnRes = f.fn(list);\n                    return fnRes instanceof Observable ? fnRes : of(fnRes);\n                }));\n            });\n        }\n        filters$.subscribe(list => {\n            list.forEach((file) => {\n                this.attachUid(file);\n                this.upload(file, list);\n            });\n        }, e => {\n            warn(`Unhandled upload filter error`, e);\n        });\n    }\n    upload(file, fileList) {\n        if (!this.options.beforeUpload) {\n            return this.post(file);\n        }\n        const before = this.options.beforeUpload(file, fileList);\n        if (before instanceof Observable) {\n            before.subscribe((processedFile) => {\n                const processedFileType = Object.prototype.toString.call(processedFile);\n                if (processedFileType === '[object File]' || processedFileType === '[object Blob]') {\n                    this.attachUid(processedFile);\n                    this.post(processedFile);\n                }\n                else if (typeof processedFile === 'boolean' && processedFile !== false) {\n                    this.post(file);\n                }\n            }, e => {\n                warn(`Unhandled upload beforeUpload error`, e);\n            });\n        }\n        else if (before !== false) {\n            return this.post(file);\n        }\n    }\n    post(file) {\n        if (this.destroy) {\n            return;\n        }\n        let process$ = of(file);\n        let transformedFile;\n        const opt = this.options;\n        const { uid } = file;\n        const { action, data, headers, transformFile } = opt;\n        const args = {\n            action: typeof action === 'string' ? action : '',\n            name: opt.name,\n            headers,\n            file,\n            postFile: file,\n            data,\n            withCredentials: opt.withCredentials,\n            onProgress: opt.onProgress\n                ? e => {\n                    opt.onProgress(e, file);\n                }\n                : undefined,\n            onSuccess: (ret, xhr) => {\n                this.clean(uid);\n                opt.onSuccess(ret, file, xhr);\n            },\n            onError: xhr => {\n                this.clean(uid);\n                opt.onError(xhr, file);\n            }\n        };\n        if (typeof action === 'function') {\n            const actionResult = action(file);\n            if (actionResult instanceof Observable) {\n                process$ = process$.pipe(switchMap(() => actionResult), map(res => {\n                    args.action = res;\n                    return file;\n                }));\n            }\n            else {\n                args.action = actionResult;\n            }\n        }\n        if (typeof transformFile === 'function') {\n            const transformResult = transformFile(file);\n            process$ = process$.pipe(switchMap(() => (transformResult instanceof Observable ? transformResult : of(transformResult))), tap(newFile => (transformedFile = newFile)));\n        }\n        if (typeof data === 'function') {\n            const dataResult = data(file);\n            if (dataResult instanceof Observable) {\n                process$ = process$.pipe(switchMap(() => dataResult), map(res => {\n                    args.data = res;\n                    return transformedFile ?? file;\n                }));\n            }\n            else {\n                args.data = dataResult;\n            }\n        }\n        if (typeof headers === 'function') {\n            const headersResult = headers(file);\n            if (headersResult instanceof Observable) {\n                process$ = process$.pipe(switchMap(() => headersResult), map(res => {\n                    args.headers = res;\n                    return transformedFile ?? file;\n                }));\n            }\n            else {\n                args.headers = headersResult;\n            }\n        }\n        process$.subscribe(newFile => {\n            args.postFile = newFile;\n            const req$ = (opt.customRequest || this.xhr).call(this, args);\n            if (!(req$ instanceof Subscription)) {\n                warn(`Must return Subscription type in '[nzCustomRequest]' property`);\n            }\n            this.reqs[uid] = req$;\n            opt.onStart(file);\n        });\n    }\n    xhr(args) {\n        const formData = new FormData();\n        if (args.data) {\n            Object.keys(args.data).map(key => {\n                formData.append(key, args.data[key]);\n            });\n        }\n        formData.append(args.name, args.postFile);\n        if (!args.headers) {\n            args.headers = {};\n        }\n        if (args.headers['X-Requested-With'] !== null) {\n            args.headers['X-Requested-With'] = `XMLHttpRequest`;\n        }\n        else {\n            delete args.headers['X-Requested-With'];\n        }\n        const req = new HttpRequest('POST', args.action, formData, {\n            reportProgress: true,\n            withCredentials: args.withCredentials,\n            headers: new HttpHeaders(args.headers)\n        });\n        return this.http.request(req).subscribe((event) => {\n            if (event.type === HttpEventType.UploadProgress) {\n                if (event.total > 0) {\n                    event.percent = (event.loaded / event.total) * 100;\n                }\n                args.onProgress(event, args.file);\n            }\n            else if (event instanceof HttpResponse) {\n                args.onSuccess(event.body, args.file, event);\n            }\n        }, err => {\n            this.abort(args.file);\n            args.onError(err, args.file);\n        });\n    }\n    clean(uid) {\n        const req$ = this.reqs[uid];\n        if (req$ instanceof Subscription) {\n            req$.unsubscribe();\n        }\n        delete this.reqs[uid];\n    }\n    abort(file) {\n        if (file) {\n            this.clean(file && file.uid);\n        }\n        else {\n            Object.keys(this.reqs).forEach(uid => this.clean(uid));\n        }\n    }\n    constructor(ngZone, http, elementRef) {\n        this.ngZone = ngZone;\n        this.http = http;\n        this.elementRef = elementRef;\n        this.reqs = {};\n        this.destroy = false;\n        this.destroy$ = new Subject();\n        if (!http) {\n            throw new Error(`Not found 'HttpClient', You can import 'HttpClientModule' in your root module.`);\n        }\n    }\n    ngOnInit() {\n        // Caretaker note: `input[type=file].click()` will open a native OS file picker,\n        // it doesn't require Angular to run `ApplicationRef.tick()`.\n        this.ngZone.runOutsideAngular(() => {\n            fromEvent(this.elementRef.nativeElement, 'click')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(() => this.onClick());\n            fromEvent(this.elementRef.nativeElement, 'keydown')\n                .pipe(takeUntil(this.destroy$))\n                .subscribe(event => {\n                if (this.options.disabled) {\n                    return;\n                }\n                if (event.key === 'Enter' || event.keyCode === ENTER) {\n                    this.onClick();\n                }\n            });\n        });\n    }\n    ngOnDestroy() {\n        this.destroy = true;\n        this.destroy$.next();\n        this.abort();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadBtnComponent, deps: [{ token: i0.NgZone }, { token: i1.HttpClient, optional: true }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzUploadBtnComponent, isStandalone: true, selector: \"[nz-upload-btn]\", inputs: { options: \"options\" }, host: { listeners: { \"drop\": \"onFileDrop($event)\", \"dragover\": \"onFileDrop($event)\" }, properties: { \"attr.tabindex\": \"\\\"0\\\"\", \"attr.role\": \"\\\"button\\\"\", \"class.ant-upload-disabled\": \"options.disabled\" }, classAttribute: \"ant-upload\" }, viewQueries: [{ propertyName: \"file\", first: true, predicate: [\"file\"], descendants: true, static: true }], exportAs: [\"nzUploadBtn\"], ngImport: i0, template: \"<!--\\n  We explicitly bind `style.display` to avoid using an inline style\\n  attribute property (which is not allowed when CSP `unsafe-inline`\\n  is not specified).\\n-->\\n<input\\n  type=\\\"file\\\"\\n  #file\\n  (change)=\\\"onChange($event)\\\"\\n  [attr.accept]=\\\"options.accept\\\"\\n  [attr.directory]=\\\"options.directory ? 'directory' : null\\\"\\n  [attr.webkitdirectory]=\\\"options.directory ? 'webkitdirectory' : null\\\"\\n  [multiple]=\\\"options.multiple\\\"\\n  [style.display]=\\\"'none'\\\"\\n/>\\n<ng-content></ng-content>\\n\", encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadBtnComponent, decorators: [{\n            type: Component,\n            args: [{ selector: '[nz-upload-btn]', exportAs: 'nzUploadBtn', host: {\n                        class: 'ant-upload',\n                        '[attr.tabindex]': '\"0\"',\n                        '[attr.role]': '\"button\"',\n                        '[class.ant-upload-disabled]': 'options.disabled',\n                        '(drop)': 'onFileDrop($event)',\n                        '(dragover)': 'onFileDrop($event)'\n                    }, preserveWhitespaces: false, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<!--\\n  We explicitly bind `style.display` to avoid using an inline style\\n  attribute property (which is not allowed when CSP `unsafe-inline`\\n  is not specified).\\n-->\\n<input\\n  type=\\\"file\\\"\\n  #file\\n  (change)=\\\"onChange($event)\\\"\\n  [attr.accept]=\\\"options.accept\\\"\\n  [attr.directory]=\\\"options.directory ? 'directory' : null\\\"\\n  [attr.webkitdirectory]=\\\"options.directory ? 'webkitdirectory' : null\\\"\\n  [multiple]=\\\"options.multiple\\\"\\n  [style.display]=\\\"'none'\\\"\\n/>\\n<ng-content></ng-content>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i1.HttpClient, decorators: [{\n                    type: Optional\n                }] }, { type: i0.ElementRef }], propDecorators: { file: [{\n                type: ViewChild,\n                args: ['file', { static: true }]\n            }], options: [{\n                type: Input\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nconst isImageFileType = (type) => !!type && type.indexOf('image/') === 0;\nconst MEASURE_SIZE = 200;\nclass NzUploadListComponent {\n    get showPic() {\n        return this.listType === 'picture' || this.listType === 'picture-card';\n    }\n    set items(list) {\n        this.list = list;\n    }\n    genErr(file) {\n        if (file.response && typeof file.response === 'string') {\n            return file.response;\n        }\n        return (file.error && file.error.statusText) || this.locale.uploadError;\n    }\n    extname(url) {\n        const temp = url.split('/');\n        const filename = temp[temp.length - 1];\n        const filenameWithoutSuffix = filename.split(/#|\\?/)[0];\n        return (/\\.[^./\\\\]*$/.exec(filenameWithoutSuffix) || [''])[0];\n    }\n    isImageUrl(file) {\n        if (isImageFileType(file.type)) {\n            return true;\n        }\n        const url = (file.thumbUrl || file.url || '');\n        if (!url) {\n            return false;\n        }\n        const extension = this.extname(url);\n        if (/^data:image\\//.test(url) || /(webp|svg|png|gif|jpg|jpeg|jfif|bmp|dpg)$/i.test(extension)) {\n            return true;\n        }\n        else if (/^data:/.test(url)) {\n            // other file types of base64\n            return false;\n        }\n        else if (extension) {\n            // other file types which have extension\n            return false;\n        }\n        return true;\n    }\n    getIconType(file) {\n        if (!this.showPic) {\n            return '';\n        }\n        if (file.isUploading || (!file.thumbUrl && !file.url)) {\n            return 'uploading';\n        }\n        else {\n            return 'thumbnail';\n        }\n    }\n    previewImage(file) {\n        if (!isImageFileType(file.type) || !this.platform.isBrowser) {\n            return of('');\n        }\n        const canvas = this.doc.createElement('canvas');\n        canvas.width = MEASURE_SIZE;\n        canvas.height = MEASURE_SIZE;\n        canvas.style.cssText = `position: fixed; left: 0; top: 0; width: ${MEASURE_SIZE}px; height: ${MEASURE_SIZE}px; z-index: 9999; display: none;`;\n        this.doc.body.appendChild(canvas);\n        const ctx = canvas.getContext('2d');\n        const img = new Image();\n        const objectUrl = URL.createObjectURL(file);\n        img.src = objectUrl;\n        return fromEvent(img, 'load').pipe(map(() => {\n            const { width, height } = img;\n            let drawWidth = MEASURE_SIZE;\n            let drawHeight = MEASURE_SIZE;\n            let offsetX = 0;\n            let offsetY = 0;\n            if (width < height) {\n                drawHeight = height * (MEASURE_SIZE / width);\n                offsetY = -(drawHeight - drawWidth) / 2;\n            }\n            else {\n                drawWidth = width * (MEASURE_SIZE / height);\n                offsetX = -(drawWidth - drawHeight) / 2;\n            }\n            try {\n                ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);\n            }\n            catch { }\n            const dataURL = canvas.toDataURL();\n            this.doc.body.removeChild(canvas);\n            URL.revokeObjectURL(objectUrl);\n            return dataURL;\n        }));\n    }\n    genThumb() {\n        if (!this.platform.isBrowser) {\n            return;\n        }\n        const win = window;\n        if (!this.showPic ||\n            typeof document === 'undefined' ||\n            typeof win === 'undefined' ||\n            !win.FileReader ||\n            !win.File) {\n            return;\n        }\n        this.list\n            .filter(file => file.originFileObj instanceof File && file.thumbUrl === undefined)\n            .forEach(file => {\n            file.thumbUrl = '';\n            // Caretaker note: we shouldn't use promises here since they're not cancellable.\n            // A promise microtask can be resolved after the view is destroyed. Thus running `detectChanges()`\n            // will cause a runtime exception (`detectChanges()` cannot be run on destroyed views).\n            const dataUrl$ = (this.previewFile ? this.previewFile(file) : this.previewImage(file.originFileObj)).pipe(takeUntil(this.destroy$));\n            this.ngZone.runOutsideAngular(() => {\n                dataUrl$.subscribe(dataUrl => {\n                    this.ngZone.run(() => {\n                        file.thumbUrl = dataUrl;\n                        this.detectChanges();\n                    });\n                });\n            });\n        });\n    }\n    showDownload(file) {\n        return !!(this.icons.showDownloadIcon && file.status === 'done');\n    }\n    fixData() {\n        this.list.forEach(file => {\n            file.isUploading = file.status === 'uploading';\n            file.message = this.genErr(file);\n            file.linkProps = typeof file.linkProps === 'string' ? JSON.parse(file.linkProps) : file.linkProps;\n            file.isImageUrl = this.previewIsImage ? this.previewIsImage(file) : this.isImageUrl(file);\n            file.iconType = this.getIconType(file);\n            file.showDownload = this.showDownload(file);\n        });\n    }\n    handlePreview(file, e) {\n        if (!this.onPreview) {\n            return;\n        }\n        e.preventDefault();\n        return this.onPreview(file);\n    }\n    handleRemove(file, e) {\n        e.preventDefault();\n        if (this.onRemove) {\n            this.onRemove(file);\n        }\n        return;\n    }\n    handleDownload(file) {\n        if (typeof this.onDownload === 'function') {\n            this.onDownload(file);\n        }\n        else if (file.url) {\n            window.open(file.url);\n        }\n    }\n    // #endregion\n    constructor(cdr, doc, ngZone, platform) {\n        this.cdr = cdr;\n        this.doc = doc;\n        this.ngZone = ngZone;\n        this.platform = platform;\n        this.list = [];\n        this.locale = {};\n        this.iconRender = null;\n        this.dir = 'ltr';\n        this.destroy$ = new Subject();\n    }\n    detectChanges() {\n        this.fixData();\n        this.cdr.detectChanges();\n    }\n    ngOnChanges() {\n        this.fixData();\n        this.genThumb();\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadListComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: DOCUMENT }, { token: i0.NgZone }, { token: i1$1.Platform }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzUploadListComponent, isStandalone: true, selector: \"nz-upload-list\", inputs: { locale: \"locale\", listType: \"listType\", items: \"items\", icons: \"icons\", onPreview: \"onPreview\", onRemove: \"onRemove\", onDownload: \"onDownload\", previewFile: \"previewFile\", previewIsImage: \"previewIsImage\", iconRender: \"iconRender\", dir: \"dir\" }, host: { properties: { \"class.ant-upload-list-rtl\": \"dir === 'rtl'\", \"class.ant-upload-list-text\": \"listType === 'text'\", \"class.ant-upload-list-picture\": \"listType === 'picture'\", \"class.ant-upload-list-picture-card\": \"listType === 'picture-card'\" }, classAttribute: \"ant-upload-list\" }, exportAs: [\"nzUploadList\"], usesOnChanges: true, ngImport: i0, template: \"<div *ngFor=\\\"let file of list\\\" class=\\\"ant-upload-list-{{ listType }}-container\\\">\\n  <div\\n    class=\\\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\\\"\\n    [attr.data-key]=\\\"file.key\\\"\\n    @itemState\\n    nz-tooltip\\n    [nzTooltipTitle]=\\\"file.status === 'error' ? file.message : null\\\"\\n  >\\n    <ng-template #icon>\\n      <ng-container [ngSwitch]=\\\"file.iconType\\\">\\n        <div\\n          *ngSwitchCase=\\\"'uploading'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isUploading\\\"\\n        >\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n        <a\\n          *ngSwitchCase=\\\"'thumbnail'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isImageUrl\\\"\\n          target=\\\"_blank\\\"\\n          rel=\\\"noopener noreferrer\\\"\\n          [href]=\\\"file.url || file.thumbUrl\\\"\\n          (click)=\\\"handlePreview(file, $event)\\\"\\n        >\\n          <img\\n            *ngIf=\\\"file.isImageUrl; else noImageThumbTpl\\\"\\n            class=\\\"ant-upload-list-item-image\\\"\\n            [src]=\\\"file.thumbUrl || file.url\\\"\\n            [attr.alt]=\\\"file.name\\\"\\n          />\\n        </a>\\n        <div *ngSwitchDefault class=\\\"ant-upload-text-icon\\\">\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n      </ng-container>\\n      <ng-template #noImageThumbTpl>\\n        <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #iconNode let-file>\\n      <ng-container *ngIf=\\\"!iconRender; else customIconRender\\\">\\n        <ng-container [ngSwitch]=\\\"listType\\\">\\n          <ng-container *ngSwitchCase=\\\"'picture'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              <span nz-icon nzType=\\\"loading\\\"></span>\\n            </ng-container>\\n          </ng-container>\\n          <ng-container *ngSwitchCase=\\\"'picture-card'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              {{ locale.uploading }}\\n            </ng-container>\\n          </ng-container>\\n          <span *ngSwitchDefault nz-icon [nzType]=\\\"file.isUploading ? 'loading' : 'paper-clip'\\\"></span>\\n        </ng-container>\\n      </ng-container>\\n      <ng-template\\n        #customIconRender\\n        [ngTemplateOutlet]=\\\"iconRender\\\"\\n        [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"\\n      ></ng-template>\\n      <ng-template #iconNodeFileIcon>\\n        <span nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\"></span>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #removeIcon>\\n      <button\\n        *ngIf=\\\"icons.showRemoveIcon\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleRemove(file, $event)\\\"\\n        [attr.title]=\\\"locale.removeFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"delete\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadIcon>\\n      <button\\n        *ngIf=\\\"file.showDownload\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleDownload(file)\\\"\\n        [attr.title]=\\\"locale.downloadFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"download\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadOrDelete>\\n      <span\\n        *ngIf=\\\"listType !== 'picture-card'\\\"\\n        class=\\\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\\\"\\n      >\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n      </span>\\n    </ng-template>\\n    <ng-template #preview>\\n      <a\\n        *ngIf=\\\"file.url\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        [href]=\\\"file.url\\\"\\n        [attr.download]=\\\"file.linkProps && file.linkProps.download\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </a>\\n      <span\\n        *ngIf=\\\"!file.url\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </span>\\n      <ng-template [ngTemplateOutlet]=\\\"downloadOrDelete\\\"></ng-template>\\n    </ng-template>\\n    <div class=\\\"ant-upload-list-item-info\\\">\\n      <span class=\\\"ant-upload-span\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"icon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"preview\\\"></ng-template>\\n      </span>\\n    </div>\\n    <span *ngIf=\\\"listType === 'picture-card' && !file.isUploading\\\" class=\\\"ant-upload-list-item-actions\\\">\\n      <a\\n        *ngIf=\\\"icons.showPreviewIcon\\\"\\n        [href]=\\\"file.url || file.thumbUrl\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        [attr.title]=\\\"locale.previewFile\\\"\\n        [ngStyle]=\\\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        <span nz-icon nzType=\\\"eye\\\"></span>\\n      </a>\\n      <ng-container *ngIf=\\\"file.status === 'done'\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n      </ng-container>\\n      <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n    </span>\\n    <div *ngIf=\\\"file.isUploading\\\" class=\\\"ant-upload-list-item-progress\\\">\\n      <nz-progress [nzPercent]=\\\"file.percent!\\\" nzType=\\\"line\\\" [nzShowInfo]=\\\"false\\\" [nzStrokeWidth]=\\\"2\\\"></nz-progress>\\n    </div>\\n  </div>\\n</div>\\n\", dependencies: [{ kind: \"directive\", type: NgForOf, selector: \"[ngFor][ngForOf]\", inputs: [\"ngForOf\", \"ngForTrackBy\", \"ngForTemplate\"] }, { kind: \"ngmodule\", type: NzToolTipModule }, { kind: \"directive\", type: i2.NzTooltipDirective, selector: \"[nz-tooltip]\", inputs: [\"nzTooltipTitle\", \"nzTooltipTitleContext\", \"nz-tooltip\", \"nzTooltipTrigger\", \"nzTooltipPlacement\", \"nzTooltipOrigin\", \"nzTooltipVisible\", \"nzTooltipMouseEnterDelay\", \"nzTooltipMouseLeaveDelay\", \"nzTooltipOverlayClassName\", \"nzTooltipOverlayStyle\", \"nzTooltipArrowPointAtCenter\", \"cdkConnectedOverlayPush\", \"nzTooltipColor\"], outputs: [\"nzTooltipVisibleChange\"], exportAs: [\"nzTooltip\"] }, { kind: \"directive\", type: NgSwitch, selector: \"[ngSwitch]\", inputs: [\"ngSwitch\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgSwitchDefault, selector: \"[ngSwitchDefault]\" }, { kind: \"directive\", type: NgSwitchCase, selector: \"[ngSwitchCase]\", inputs: [\"ngSwitchCase\"] }, { kind: \"ngmodule\", type: NzIconModule }, { kind: \"directive\", type: i3.NzIconDirective, selector: \"[nz-icon]\", inputs: [\"nzSpin\", \"nzRotate\", \"nzType\", \"nzTheme\", \"nzTwotoneColor\", \"nzIconfont\"], exportAs: [\"nzIcon\"] }, { kind: \"ngmodule\", type: NzButtonModule }, { kind: \"component\", type: i4.NzButtonComponent, selector: \"button[nz-button], a[nz-button]\", inputs: [\"nzBlock\", \"nzGhost\", \"nzSearch\", \"nzLoading\", \"nzDanger\", \"disabled\", \"tabIndex\", \"nzType\", \"nzShape\", \"nzSize\"], exportAs: [\"nzButton\"] }, { kind: \"directive\", type: i5.ɵNzTransitionPatchDirective, selector: \"[nz-button], nz-button-group, [nz-icon], [nz-menu-item], [nz-submenu], nz-select-top-control, nz-select-placeholder, nz-input-group\", inputs: [\"hidden\"] }, { kind: \"directive\", type: NgStyle, selector: \"[ngStyle]\", inputs: [\"ngStyle\"] }, { kind: \"ngmodule\", type: NzProgressModule }, { kind: \"component\", type: i6.NzProgressComponent, selector: \"nz-progress\", inputs: [\"nzShowInfo\", \"nzWidth\", \"nzStrokeColor\", \"nzSize\", \"nzFormat\", \"nzSuccessPercent\", \"nzPercent\", \"nzStrokeWidth\", \"nzGapDegree\", \"nzStatus\", \"nzType\", \"nzGapPosition\", \"nzStrokeLinecap\", \"nzSteps\"], exportAs: [\"nzProgress\"] }], animations: [\n            trigger('itemState', [\n                transition(':enter', [\n                    style({ height: '0', width: '0', opacity: 0 }),\n                    animate(150, style({ height: '*', width: '*', opacity: 1 }))\n                ]),\n                transition(':leave', [animate(150, style({ height: '0', width: '0', opacity: 0 }))])\n            ])\n        ], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadListComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nz-upload-list', exportAs: 'nzUploadList', animations: [\n                        trigger('itemState', [\n                            transition(':enter', [\n                                style({ height: '0', width: '0', opacity: 0 }),\n                                animate(150, style({ height: '*', width: '*', opacity: 1 }))\n                            ]),\n                            transition(':leave', [animate(150, style({ height: '0', width: '0', opacity: 0 }))])\n                        ])\n                    ], host: {\n                        class: 'ant-upload-list',\n                        '[class.ant-upload-list-rtl]': `dir === 'rtl'`,\n                        '[class.ant-upload-list-text]': `listType === 'text'`,\n                        '[class.ant-upload-list-picture]': `listType === 'picture'`,\n                        '[class.ant-upload-list-picture-card]': `listType === 'picture-card'`\n                    }, preserveWhitespaces: false, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [\n                        NgForOf,\n                        NzToolTipModule,\n                        NgSwitch,\n                        NgTemplateOutlet,\n                        NgIf,\n                        NgSwitchDefault,\n                        NgSwitchCase,\n                        NzIconModule,\n                        NzButtonModule,\n                        NgStyle,\n                        NzProgressModule\n                    ], standalone: true, template: \"<div *ngFor=\\\"let file of list\\\" class=\\\"ant-upload-list-{{ listType }}-container\\\">\\n  <div\\n    class=\\\"ant-upload-list-item ant-upload-list-item-{{ file.status }} ant-upload-list-item-list-type-{{ listType }}\\\"\\n    [attr.data-key]=\\\"file.key\\\"\\n    @itemState\\n    nz-tooltip\\n    [nzTooltipTitle]=\\\"file.status === 'error' ? file.message : null\\\"\\n  >\\n    <ng-template #icon>\\n      <ng-container [ngSwitch]=\\\"file.iconType\\\">\\n        <div\\n          *ngSwitchCase=\\\"'uploading'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isUploading\\\"\\n        >\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n        <a\\n          *ngSwitchCase=\\\"'thumbnail'\\\"\\n          class=\\\"ant-upload-list-item-thumbnail\\\"\\n          [class.ant-upload-list-item-file]=\\\"!file.isImageUrl\\\"\\n          target=\\\"_blank\\\"\\n          rel=\\\"noopener noreferrer\\\"\\n          [href]=\\\"file.url || file.thumbUrl\\\"\\n          (click)=\\\"handlePreview(file, $event)\\\"\\n        >\\n          <img\\n            *ngIf=\\\"file.isImageUrl; else noImageThumbTpl\\\"\\n            class=\\\"ant-upload-list-item-image\\\"\\n            [src]=\\\"file.thumbUrl || file.url\\\"\\n            [attr.alt]=\\\"file.name\\\"\\n          />\\n        </a>\\n        <div *ngSwitchDefault class=\\\"ant-upload-text-icon\\\">\\n          <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n        </div>\\n      </ng-container>\\n      <ng-template #noImageThumbTpl>\\n        <ng-template [ngTemplateOutlet]=\\\"iconNode\\\" [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"></ng-template>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #iconNode let-file>\\n      <ng-container *ngIf=\\\"!iconRender; else customIconRender\\\">\\n        <ng-container [ngSwitch]=\\\"listType\\\">\\n          <ng-container *ngSwitchCase=\\\"'picture'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              <span nz-icon nzType=\\\"loading\\\"></span>\\n            </ng-container>\\n          </ng-container>\\n          <ng-container *ngSwitchCase=\\\"'picture-card'\\\">\\n            <ng-container *ngIf=\\\"file.isUploading; else iconNodeFileIcon\\\">\\n              {{ locale.uploading }}\\n            </ng-container>\\n          </ng-container>\\n          <span *ngSwitchDefault nz-icon [nzType]=\\\"file.isUploading ? 'loading' : 'paper-clip'\\\"></span>\\n        </ng-container>\\n      </ng-container>\\n      <ng-template\\n        #customIconRender\\n        [ngTemplateOutlet]=\\\"iconRender\\\"\\n        [ngTemplateOutletContext]=\\\"{ $implicit: file }\\\"\\n      ></ng-template>\\n      <ng-template #iconNodeFileIcon>\\n        <span nz-icon [nzType]=\\\"file.isImageUrl ? 'picture' : 'file'\\\" nzTheme=\\\"twotone\\\"></span>\\n      </ng-template>\\n    </ng-template>\\n    <ng-template #removeIcon>\\n      <button\\n        *ngIf=\\\"icons.showRemoveIcon\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleRemove(file, $event)\\\"\\n        [attr.title]=\\\"locale.removeFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"delete\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadIcon>\\n      <button\\n        *ngIf=\\\"file.showDownload\\\"\\n        type=\\\"button\\\"\\n        nz-button\\n        nzType=\\\"text\\\"\\n        nzSize=\\\"small\\\"\\n        (click)=\\\"handleDownload(file)\\\"\\n        [attr.title]=\\\"locale.downloadFile\\\"\\n        class=\\\"ant-upload-list-item-card-actions-btn\\\"\\n      >\\n        <span nz-icon nzType=\\\"download\\\"></span>\\n      </button>\\n    </ng-template>\\n    <ng-template #downloadOrDelete>\\n      <span\\n        *ngIf=\\\"listType !== 'picture-card'\\\"\\n        class=\\\"ant-upload-list-item-card-actions {{ listType === 'picture' ? 'picture' : '' }}\\\"\\n      >\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n      </span>\\n    </ng-template>\\n    <ng-template #preview>\\n      <a\\n        *ngIf=\\\"file.url\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        [href]=\\\"file.url\\\"\\n        [attr.download]=\\\"file.linkProps && file.linkProps.download\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </a>\\n      <span\\n        *ngIf=\\\"!file.url\\\"\\n        class=\\\"ant-upload-list-item-name\\\"\\n        [attr.title]=\\\"file.name\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        {{ file.name }}\\n      </span>\\n      <ng-template [ngTemplateOutlet]=\\\"downloadOrDelete\\\"></ng-template>\\n    </ng-template>\\n    <div class=\\\"ant-upload-list-item-info\\\">\\n      <span class=\\\"ant-upload-span\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"icon\\\"></ng-template>\\n        <ng-template [ngTemplateOutlet]=\\\"preview\\\"></ng-template>\\n      </span>\\n    </div>\\n    <span *ngIf=\\\"listType === 'picture-card' && !file.isUploading\\\" class=\\\"ant-upload-list-item-actions\\\">\\n      <a\\n        *ngIf=\\\"icons.showPreviewIcon\\\"\\n        [href]=\\\"file.url || file.thumbUrl\\\"\\n        target=\\\"_blank\\\"\\n        rel=\\\"noopener noreferrer\\\"\\n        [attr.title]=\\\"locale.previewFile\\\"\\n        [ngStyle]=\\\"!(file.url || file.thumbUrl) ? { opacity: 0.5, 'pointer-events': 'none' } : null\\\"\\n        (click)=\\\"handlePreview(file, $event)\\\"\\n      >\\n        <span nz-icon nzType=\\\"eye\\\"></span>\\n      </a>\\n      <ng-container *ngIf=\\\"file.status === 'done'\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"downloadIcon\\\"></ng-template>\\n      </ng-container>\\n      <ng-template [ngTemplateOutlet]=\\\"removeIcon\\\"></ng-template>\\n    </span>\\n    <div *ngIf=\\\"file.isUploading\\\" class=\\\"ant-upload-list-item-progress\\\">\\n      <nz-progress [nzPercent]=\\\"file.percent!\\\" nzType=\\\"line\\\" [nzShowInfo]=\\\"false\\\" [nzStrokeWidth]=\\\"2\\\"></nz-progress>\\n    </div>\\n  </div>\\n</div>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.NgZone }, { type: i1$1.Platform }], propDecorators: { locale: [{\n                type: Input\n            }], listType: [{\n                type: Input\n            }], items: [{\n                type: Input\n            }], icons: [{\n                type: Input\n            }], onPreview: [{\n                type: Input\n            }], onRemove: [{\n                type: Input\n            }], onDownload: [{\n                type: Input\n            }], previewFile: [{\n                type: Input\n            }], previewIsImage: [{\n                type: Input\n            }], iconRender: [{\n                type: Input\n            }], dir: [{\n                type: Input\n            }] } });\n\nclass NzUploadComponent {\n    set nzShowUploadList(value) {\n        this._showUploadList = typeof value === 'boolean' ? toBoolean(value) : value;\n    }\n    get nzShowUploadList() {\n        return this._showUploadList;\n    }\n    zipOptions() {\n        if (typeof this.nzShowUploadList === 'boolean' && this.nzShowUploadList) {\n            this.nzShowUploadList = {\n                showPreviewIcon: true,\n                showRemoveIcon: true,\n                showDownloadIcon: true\n            };\n        }\n        // filters\n        const filters = this.nzFilter.slice();\n        if (this.nzMultiple && this.nzLimit > 0 && filters.findIndex(w => w.name === 'limit') === -1) {\n            filters.push({\n                name: 'limit',\n                fn: (fileList) => fileList.slice(-this.nzLimit)\n            });\n        }\n        if (this.nzSize > 0 && filters.findIndex(w => w.name === 'size') === -1) {\n            filters.push({\n                name: 'size',\n                fn: (fileList) => fileList.filter(w => w.size / 1024 <= this.nzSize)\n            });\n        }\n        if (this.nzFileType && this.nzFileType.length > 0 && filters.findIndex(w => w.name === 'type') === -1) {\n            const types = this.nzFileType.split(',');\n            filters.push({\n                name: 'type',\n                fn: (fileList) => fileList.filter(w => ~types.indexOf(w.type))\n            });\n        }\n        this._btnOptions = {\n            disabled: this.nzDisabled,\n            accept: this.nzAccept,\n            action: this.nzAction,\n            directory: this.nzDirectory,\n            openFileDialogOnClick: this.nzOpenFileDialogOnClick,\n            beforeUpload: this.nzBeforeUpload,\n            customRequest: this.nzCustomRequest,\n            data: this.nzData,\n            headers: this.nzHeaders,\n            name: this.nzName,\n            multiple: this.nzMultiple,\n            withCredentials: this.nzWithCredentials,\n            filters,\n            transformFile: this.nzTransformFile,\n            onStart: this.onStart,\n            onProgress: this.onProgress,\n            onSuccess: this.onSuccess,\n            onError: this.onError\n        };\n        return this;\n    }\n    // #endregion\n    constructor(ngZone, document, cdr, i18n, directionality) {\n        this.ngZone = ngZone;\n        this.document = document;\n        this.cdr = cdr;\n        this.i18n = i18n;\n        this.directionality = directionality;\n        this.destroy$ = new Subject();\n        this.dir = 'ltr';\n        // #region fields\n        this.nzType = 'select';\n        this.nzLimit = 0;\n        this.nzSize = 0;\n        this.nzDirectory = false;\n        this.nzOpenFileDialogOnClick = true;\n        this.nzFilter = [];\n        this.nzFileList = [];\n        this.nzDisabled = false;\n        this.nzListType = 'text';\n        this.nzMultiple = false;\n        this.nzName = 'file';\n        this._showUploadList = true;\n        this.nzShowButton = true;\n        this.nzWithCredentials = false;\n        this.nzIconRender = null;\n        this.nzFileListRender = null;\n        this.nzChange = new EventEmitter();\n        this.nzFileListChange = new EventEmitter();\n        this.onStart = (file) => {\n            if (!this.nzFileList) {\n                this.nzFileList = [];\n            }\n            const targetItem = this.fileToObject(file);\n            targetItem.status = 'uploading';\n            this.nzFileList = this.nzFileList.concat(targetItem);\n            this.nzFileListChange.emit(this.nzFileList);\n            this.nzChange.emit({ file: targetItem, fileList: this.nzFileList, type: 'start' });\n            this.detectChangesList();\n        };\n        this.onProgress = (e, file) => {\n            const fileList = this.nzFileList;\n            const targetItem = this.getFileItem(file, fileList);\n            targetItem.percent = e.percent;\n            this.nzChange.emit({\n                event: e,\n                file: { ...targetItem },\n                fileList: this.nzFileList,\n                type: 'progress'\n            });\n            this.detectChangesList();\n        };\n        this.onSuccess = (res, file) => {\n            const fileList = this.nzFileList;\n            const targetItem = this.getFileItem(file, fileList);\n            targetItem.status = 'done';\n            targetItem.response = res;\n            this.nzChange.emit({\n                file: { ...targetItem },\n                fileList,\n                type: 'success'\n            });\n            this.detectChangesList();\n        };\n        this.onError = (err, file) => {\n            const fileList = this.nzFileList;\n            const targetItem = this.getFileItem(file, fileList);\n            targetItem.error = err;\n            targetItem.status = 'error';\n            this.nzChange.emit({\n                file: { ...targetItem },\n                fileList,\n                type: 'error'\n            });\n            this.detectChangesList();\n        };\n        this.onRemove = (file) => {\n            this.uploadComp.abort(file);\n            file.status = 'removed';\n            const fnRes = typeof this.nzRemove === 'function' ? this.nzRemove(file) : this.nzRemove == null ? true : this.nzRemove;\n            (fnRes instanceof Observable ? fnRes : of(fnRes)).pipe(filter((res) => res)).subscribe(() => {\n                this.nzFileList = this.removeFileItem(file, this.nzFileList);\n                this.nzChange.emit({\n                    file,\n                    fileList: this.nzFileList,\n                    type: 'removed'\n                });\n                this.nzFileListChange.emit(this.nzFileList);\n                this.cdr.detectChanges();\n            });\n        };\n        // #endregion\n        // #region styles\n        this.prefixCls = 'ant-upload';\n        this.classList = [];\n    }\n    // #region upload\n    fileToObject(file) {\n        return {\n            lastModified: file.lastModified,\n            lastModifiedDate: file.lastModifiedDate,\n            name: file.filename || file.name,\n            size: file.size,\n            type: file.type,\n            uid: file.uid,\n            response: file.response,\n            error: file.error,\n            percent: 0,\n            originFileObj: file\n        };\n    }\n    getFileItem(file, fileList) {\n        return fileList.filter(item => item.uid === file.uid)[0];\n    }\n    removeFileItem(file, fileList) {\n        return fileList.filter(item => item.uid !== file.uid);\n    }\n    // skip safari bug\n    fileDrop(e) {\n        if (e.type === this.dragState) {\n            return;\n        }\n        this.dragState = e.type;\n        this.setClassMap();\n    }\n    // #endregion\n    // #region list\n    detectChangesList() {\n        this.cdr.detectChanges();\n        this.listComp?.detectChanges();\n    }\n    setClassMap() {\n        let subCls = [];\n        if (this.nzType === 'drag') {\n            if (this.nzFileList.some(file => file.status === 'uploading')) {\n                subCls.push(`${this.prefixCls}-drag-uploading`);\n            }\n            if (this.dragState === 'dragover') {\n                subCls.push(`${this.prefixCls}-drag-hover`);\n            }\n        }\n        else {\n            subCls = [`${this.prefixCls}-select-${this.nzListType}`];\n        }\n        this.classList = [\n            this.prefixCls,\n            `${this.prefixCls}-${this.nzType}`,\n            ...subCls,\n            (this.nzDisabled && `${this.prefixCls}-disabled`) || '',\n            (this.dir === 'rtl' && `${this.prefixCls}-rtl`) || ''\n        ].filter(item => !!item);\n        this.cdr.detectChanges();\n    }\n    // #endregion\n    ngOnInit() {\n        this.dir = this.directionality.value;\n        this.directionality.change?.pipe(takeUntil(this.destroy$)).subscribe((direction) => {\n            this.dir = direction;\n            this.setClassMap();\n            this.cdr.detectChanges();\n        });\n        this.i18n.localeChange.pipe(takeUntil(this.destroy$)).subscribe(() => {\n            this.locale = this.i18n.getLocaleData('Upload');\n            this.detectChangesList();\n        });\n    }\n    ngAfterViewInit() {\n        // fix firefox drop open new tab\n        this.ngZone.runOutsideAngular(() => fromEvent(this.document.body, 'drop')\n            .pipe(takeUntil(this.destroy$))\n            .subscribe(event => {\n            event.preventDefault();\n            event.stopPropagation();\n        }));\n    }\n    ngOnChanges() {\n        this.zipOptions().setClassMap();\n    }\n    ngOnDestroy() {\n        this.destroy$.next();\n        this.destroy$.complete();\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadComponent, deps: [{ token: i0.NgZone }, { token: DOCUMENT }, { token: i0.ChangeDetectorRef }, { token: i1$2.NzI18nService }, { token: i2$1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.3.8\", type: NzUploadComponent, isStandalone: true, selector: \"nz-upload\", inputs: { nzType: \"nzType\", nzLimit: \"nzLimit\", nzSize: \"nzSize\", nzFileType: \"nzFileType\", nzAccept: \"nzAccept\", nzAction: \"nzAction\", nzDirectory: \"nzDirectory\", nzOpenFileDialogOnClick: \"nzOpenFileDialogOnClick\", nzBeforeUpload: \"nzBeforeUpload\", nzCustomRequest: \"nzCustomRequest\", nzData: \"nzData\", nzFilter: \"nzFilter\", nzFileList: \"nzFileList\", nzDisabled: \"nzDisabled\", nzHeaders: \"nzHeaders\", nzListType: \"nzListType\", nzMultiple: \"nzMultiple\", nzName: \"nzName\", nzShowUploadList: \"nzShowUploadList\", nzShowButton: \"nzShowButton\", nzWithCredentials: \"nzWithCredentials\", nzRemove: \"nzRemove\", nzPreview: \"nzPreview\", nzPreviewFile: \"nzPreviewFile\", nzPreviewIsImage: \"nzPreviewIsImage\", nzTransformFile: \"nzTransformFile\", nzDownload: \"nzDownload\", nzIconRender: \"nzIconRender\", nzFileListRender: \"nzFileListRender\" }, outputs: { nzChange: \"nzChange\", nzFileListChange: \"nzFileListChange\" }, host: { properties: { \"class.ant-upload-picture-card-wrapper\": \"nzListType === \\\"picture-card\\\"\" } }, viewQueries: [{ propertyName: \"uploadComp\", first: true, predicate: [\"uploadComp\"], descendants: true }, { propertyName: \"listComp\", first: true, predicate: [\"listComp\"], descendants: true }], exportAs: [\"nzUpload\"], usesOnChanges: true, ngImport: i0, template: \"<ng-template #list>\\n  <nz-upload-list\\n    *ngIf=\\\"locale && !nzFileListRender\\\"\\n    #listComp\\n    [style.display]=\\\"nzShowUploadList ? '' : 'none'\\\"\\n    [locale]=\\\"locale\\\"\\n    [listType]=\\\"nzListType\\\"\\n    [items]=\\\"nzFileList || []\\\"\\n    [icons]=\\\"$any(nzShowUploadList)\\\"\\n    [iconRender]=\\\"nzIconRender\\\"\\n    [previewFile]=\\\"nzPreviewFile\\\"\\n    [previewIsImage]=\\\"nzPreviewIsImage\\\"\\n    [onPreview]=\\\"nzPreview\\\"\\n    [onRemove]=\\\"onRemove\\\"\\n    [onDownload]=\\\"nzDownload\\\"\\n    [dir]=\\\"dir\\\"\\n  ></nz-upload-list>\\n  <ng-container *ngIf=\\\"nzFileListRender\\\">\\n    <ng-container *ngTemplateOutlet=\\\"nzFileListRender; context: { $implicit: nzFileList }\\\"></ng-container>\\n  </ng-container>\\n</ng-template>\\n<ng-template #con><ng-content></ng-content></ng-template>\\n<ng-template #btn>\\n  <div [ngClass]=\\\"classList\\\" [style.display]=\\\"nzShowButton ? '' : 'none'\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\">\\n      <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n    </div>\\n  </div>\\n</ng-template>\\n<ng-container *ngIf=\\\"nzType === 'drag'; else select\\\">\\n  <div [ngClass]=\\\"classList\\\" (drop)=\\\"fileDrop($event)\\\" (dragover)=\\\"fileDrop($event)\\\" (dragleave)=\\\"fileDrop($event)\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\" class=\\\"ant-upload-btn\\\">\\n      <div class=\\\"ant-upload-drag-container\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n      </div>\\n    </div>\\n  </div>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-container>\\n<ng-template #select>\\n  <ng-container *ngIf=\\\"nzListType === 'picture-card'; else pic\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  </ng-container>\\n</ng-template>\\n<ng-template #pic>\\n  <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-template>\\n\", dependencies: [{ kind: \"component\", type: NzUploadListComponent, selector: \"nz-upload-list\", inputs: [\"locale\", \"listType\", \"items\", \"icons\", \"onPreview\", \"onRemove\", \"onDownload\", \"previewFile\", \"previewIsImage\", \"iconRender\", \"dir\"], exportAs: [\"nzUploadList\"] }, { kind: \"directive\", type: NgIf, selector: \"[ngIf]\", inputs: [\"ngIf\", \"ngIfThen\", \"ngIfElse\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }, { kind: \"component\", type: NzUploadBtnComponent, selector: \"[nz-upload-btn]\", inputs: [\"options\"], exportAs: [\"nzUploadBtn\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\n__decorate([\n    InputNumber()\n], NzUploadComponent.prototype, \"nzLimit\", void 0);\n__decorate([\n    InputNumber()\n], NzUploadComponent.prototype, \"nzSize\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzDirectory\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzOpenFileDialogOnClick\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzDisabled\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzMultiple\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzShowButton\", void 0);\n__decorate([\n    InputBoolean()\n], NzUploadComponent.prototype, \"nzWithCredentials\", void 0);\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'nz-upload', exportAs: 'nzUpload', preserveWhitespaces: false, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        '[class.ant-upload-picture-card-wrapper]': 'nzListType === \"picture-card\"'\n                    }, imports: [NzUploadListComponent, NgIf, NgTemplateOutlet, NgClass, NzUploadBtnComponent], standalone: true, template: \"<ng-template #list>\\n  <nz-upload-list\\n    *ngIf=\\\"locale && !nzFileListRender\\\"\\n    #listComp\\n    [style.display]=\\\"nzShowUploadList ? '' : 'none'\\\"\\n    [locale]=\\\"locale\\\"\\n    [listType]=\\\"nzListType\\\"\\n    [items]=\\\"nzFileList || []\\\"\\n    [icons]=\\\"$any(nzShowUploadList)\\\"\\n    [iconRender]=\\\"nzIconRender\\\"\\n    [previewFile]=\\\"nzPreviewFile\\\"\\n    [previewIsImage]=\\\"nzPreviewIsImage\\\"\\n    [onPreview]=\\\"nzPreview\\\"\\n    [onRemove]=\\\"onRemove\\\"\\n    [onDownload]=\\\"nzDownload\\\"\\n    [dir]=\\\"dir\\\"\\n  ></nz-upload-list>\\n  <ng-container *ngIf=\\\"nzFileListRender\\\">\\n    <ng-container *ngTemplateOutlet=\\\"nzFileListRender; context: { $implicit: nzFileList }\\\"></ng-container>\\n  </ng-container>\\n</ng-template>\\n<ng-template #con><ng-content></ng-content></ng-template>\\n<ng-template #btn>\\n  <div [ngClass]=\\\"classList\\\" [style.display]=\\\"nzShowButton ? '' : 'none'\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\">\\n      <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n    </div>\\n  </div>\\n</ng-template>\\n<ng-container *ngIf=\\\"nzType === 'drag'; else select\\\">\\n  <div [ngClass]=\\\"classList\\\" (drop)=\\\"fileDrop($event)\\\" (dragover)=\\\"fileDrop($event)\\\" (dragleave)=\\\"fileDrop($event)\\\">\\n    <div nz-upload-btn #uploadComp [options]=\\\"_btnOptions!\\\" class=\\\"ant-upload-btn\\\">\\n      <div class=\\\"ant-upload-drag-container\\\">\\n        <ng-template [ngTemplateOutlet]=\\\"con\\\"></ng-template>\\n      </div>\\n    </div>\\n  </div>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-container>\\n<ng-template #select>\\n  <ng-container *ngIf=\\\"nzListType === 'picture-card'; else pic\\\">\\n    <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n    <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  </ng-container>\\n</ng-template>\\n<ng-template #pic>\\n  <ng-template [ngTemplateOutlet]=\\\"btn\\\"></ng-template>\\n  <ng-template [ngTemplateOutlet]=\\\"list\\\"></ng-template>\\n</ng-template>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ChangeDetectorRef }, { type: i1$2.NzI18nService }, { type: i2$1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { uploadComp: [{\n                type: ViewChild,\n                args: ['uploadComp', { static: false }]\n            }], listComp: [{\n                type: ViewChild,\n                args: ['listComp', { static: false }]\n            }], nzType: [{\n                type: Input\n            }], nzLimit: [{\n                type: Input\n            }], nzSize: [{\n                type: Input\n            }], nzFileType: [{\n                type: Input\n            }], nzAccept: [{\n                type: Input\n            }], nzAction: [{\n                type: Input\n            }], nzDirectory: [{\n                type: Input\n            }], nzOpenFileDialogOnClick: [{\n                type: Input\n            }], nzBeforeUpload: [{\n                type: Input\n            }], nzCustomRequest: [{\n                type: Input\n            }], nzData: [{\n                type: Input\n            }], nzFilter: [{\n                type: Input\n            }], nzFileList: [{\n                type: Input\n            }], nzDisabled: [{\n                type: Input\n            }], nzHeaders: [{\n                type: Input\n            }], nzListType: [{\n                type: Input\n            }], nzMultiple: [{\n                type: Input\n            }], nzName: [{\n                type: Input\n            }], nzShowUploadList: [{\n                type: Input\n            }], nzShowButton: [{\n                type: Input\n            }], nzWithCredentials: [{\n                type: Input\n            }], nzRemove: [{\n                type: Input\n            }], nzPreview: [{\n                type: Input\n            }], nzPreviewFile: [{\n                type: Input\n            }], nzPreviewIsImage: [{\n                type: Input\n            }], nzTransformFile: [{\n                type: Input\n            }], nzDownload: [{\n                type: Input\n            }], nzIconRender: [{\n                type: Input\n            }], nzFileListRender: [{\n                type: Input\n            }], nzChange: [{\n                type: Output\n            }], nzFileListChange: [{\n                type: Output\n            }] } });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\nclass NzUploadModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadModule, imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent], exports: [NzUploadComponent] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadModule, imports: [NzUploadComponent, NzUploadListComponent] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.8\", ngImport: i0, type: NzUploadModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [NzUploadComponent, NzUploadBtnComponent, NzUploadListComponent],\n                    exports: [NzUploadComponent]\n                }]\n        }] });\n\n/**\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://github.com/NG-ZORRO/ng-zorro-antd/blob/master/LICENSE\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { NzUploadBtnComponent, NzUploadComponent, NzUploadListComponent, NzUploadModule };\n"], "mappings": "AAAA,SAASA,KAAK,QAAQ,uBAAuB;AAC7C,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,WAAW,EAAEC,WAAW,EAAEC,aAAa,EAAEC,YAAY,QAAQ,sBAAsB;AAC5F,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,SAAS,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AACzJ,SAASC,EAAE,EAAEC,UAAU,EAAEC,YAAY,EAAEC,OAAO,EAAEC,SAAS,QAAQ,MAAM;AACvE,SAASC,SAAS,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,MAAM,QAAQ,gBAAgB;AACvE,SAASC,IAAI,QAAQ,2BAA2B;AAChD,SAASC,OAAO,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,QAAQ,qBAAqB;AACzE,SAASC,QAAQ,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,gBAAgB,EAAEC,IAAI,EAAEC,eAAe,EAAEC,YAAY,EAAEC,OAAO,EAAEC,OAAO,QAAQ,iBAAiB;AACtI,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAO,KAAKC,EAAE,MAAM,oBAAoB;AACxC,SAASC,YAAY,QAAQ,oBAAoB;AACjD,OAAO,KAAKC,EAAE,MAAM,wBAAwB;AAC5C,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,SAASC,eAAe,QAAQ,uBAAuB;AACvD,OAAO,KAAKC,IAAI,MAAM,uBAAuB;AAC7C,OAAO,KAAKC,EAAE,MAAM,qCAAqC;AACzD,SAASC,UAAU,QAAQ,OAAO;AAClC,SAASC,SAAS,EAAEC,WAAW,EAAEC,YAAY,QAAQ,yBAAyB;AAC9E,OAAO,KAAKC,IAAI,MAAM,oBAAoB;AAC1C,OAAO,KAAKC,IAAI,MAAM,mBAAmB;;AAEzC;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAC,EAAA;EAAAC,SAAA,EAAAD;AAAA;AAAA,MAAAE,GAAA,GAAAA,CAAA;EAAAC,OAAA;EAAA;AAAA;AAAA,SAAAC,uEAAAC,EAAA,EAAAC,GAAA;AAAA,SAAAC,yDAAAF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA0SoG5D,EAAE,CAAA+D,cAAA,aA8MqxC,CAAC;IA9MxxC/D,EAAE,CAAAgE,UAAA,IAAAL,sEAAA,yBA8Mg4C,CAAC;IA9Mn4C3D,EAAE,CAAAiE,YAAA,CA8M85C,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9Mj6ClE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAY,WAAA,GAAFpE,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAsE,WAAA,+BAAAJ,OAAA,CAAAK,WA8M0wC,CAAC;IA9M7wCvE,EAAE,CAAAwE,SAAA,CA8M60C,CAAC;IA9Mh1CxE,EAAE,CAAAyE,UAAA,qBAAAL,WA8M60C,CAAC,4BA9Mh1CpE,EAAE,CAAA0E,eAAA,IAAApB,GAAA,EAAAY,OAAA,CA8M+3C,CAAC;EAAA;AAAA;AAAA,SAAAS,6DAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Ml4C5D,EAAE,CAAA4E,SAAA,aA8M+9D,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAM,OAAA,GA9Ml+DlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,QAAAP,OAAA,CAAAW,QAAA,IAAAX,OAAA,CAAAY,GAAA,EAAF9E,EAAE,CAAA+E,aA8M26D,CAAC;IA9M96D/E,EAAE,CAAAgF,WAAA,QAAAd,OAAA,CAAAe,IAAA;EAAA;AAAA;AAAA,SAAAC,uDAAAtB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuB,GAAA,GAAFnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,WA8M2vD,CAAC;IA9M9vD/D,EAAE,CAAAqF,UAAA,mBAAAC,0EAAAC,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAjB,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8MotDD,MAAA,CAAAE,aAAA,CAAAzB,OAAA,EAAAqB,MAA0B,CAAC;IAAA,CAAC,CAAC;IA9MnvDvF,EAAE,CAAAgE,UAAA,IAAAW,4DAAA,iBA8M+9D,CAAC;IA9Ml+D3E,EAAE,CAAAiE,YAAA,CA8M6+D,CAAC;EAAA;EAAA,IAAAL,EAAA;IA9Mh/D5D,EAAE,CAAAmE,aAAA;IAAA,MAAAyB,kBAAA,GAAF5F,EAAE,CAAAqE,WAAA;IAAA,MAAAH,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAFxD,EAAE,CAAAsE,WAAA,+BAAAJ,OAAA,CAAA2B,UA8MykD,CAAC;IA9M5kD7F,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAY,GAAA,IAAAZ,OAAA,CAAAW,QAAA,EAAF7E,EAAE,CAAA+E,aA8M6rD,CAAC;IA9MhsD/E,EAAE,CAAAwE,SAAA,CA8MkzD,CAAC;IA9MrzDxE,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAA2B,UA8MkzD,CAAC,aAAAD,kBAAmB,CAAC;EAAA;AAAA;AAAA,SAAAE,uEAAAlC,EAAA,EAAAC,GAAA;AAAA,SAAAkC,yDAAAnC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mz0D5D,EAAE,CAAA+D,cAAA,aA8M4iE,CAAC;IA9M/iE/D,EAAE,CAAAgE,UAAA,IAAA8B,sEAAA,yBA8MupE,CAAC;IA9M1pE9F,EAAE,CAAAiE,YAAA,CA8MqrE,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9MxrElE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAY,WAAA,GAAFpE,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CA8MomE,CAAC;IA9MvmExE,EAAE,CAAAyE,UAAA,qBAAAL,WA8MomE,CAAC,4BA9MvmEpE,EAAE,CAAA0E,eAAA,IAAApB,GAAA,EAAAY,OAAA,CA8MspE,CAAC;EAAA;AAAA;AAAA,SAAA8B,+EAAApC,EAAA,EAAAC,GAAA;AAAA,SAAAoC,iEAAArC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9MzpE5D,EAAE,CAAAgE,UAAA,IAAAgC,8EAAA,yBA8M21E,CAAC;EAAA;EAAA,IAAApC,EAAA;IAAA,MAAAM,OAAA,GA9M91ElE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAY,WAAA,GAAFpE,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAyE,UAAA,qBAAAL,WA8MwyE,CAAC,4BA9M3yEpE,EAAE,CAAA0E,eAAA,IAAApB,GAAA,EAAAY,OAAA,CA8M01E,CAAC;EAAA;AAAA;AAAA,SAAAgC,mDAAAtC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9M71E5D,EAAE,CAAAmG,uBAAA,MA8M4lC,CAAC;IA9M/lCnG,EAAE,CAAAgE,UAAA,IAAAF,wDAAA,iBA8MqxC,CAAC,IAAAoB,sDAAA,eAAqe,CAAC,IAAAa,wDAAA,iBAAgT,CAAC;IA9M/iE/F,EAAE,CAAAoG,qBAAA;IAAFpG,EAAE,CAAAgE,UAAA,IAAAiC,gEAAA,gCAAFjG,EAAE,CAAAqG,sBA8MkvE,CAAC;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAAM,OAAA,GA9MrvElE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,aAAAP,OAAA,CAAAoC,QA8M2lC,CAAC;IA9M9lCtG,EAAE,CAAAwE,SAAA,CA8MipC,CAAC;IA9MppCxE,EAAE,CAAAyE,UAAA,4BA8MipC,CAAC;IA9MppCzE,EAAE,CAAAwE,SAAA,CA8Mi9C,CAAC;IA9Mp9CxE,EAAE,CAAAyE,UAAA,4BA8Mi9C,CAAC;EAAA;AAAA;AAAA,SAAA8B,gGAAA3C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mp9C5D,EAAE,CAAAmG,uBAAA,EA8MgrF,CAAC;IA9MnrFnG,EAAE,CAAA4E,SAAA,cA8MwuF,CAAC;IA9M3uF5E,EAAE,CAAAoG,qBAAA;EAAA;AAAA;AAAA,SAAAI,iFAAA5C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5D,EAAE,CAAAmG,uBAAA,EA8MkmF,CAAC;IA9MrmFnG,EAAE,CAAAgE,UAAA,IAAAuC,+FAAA,0BA8MgrF,CAAC;IA9MnrFvG,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA6C,OAAA,GAAFzG,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAkD,mBAAA,GAAF1G,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CA8MwpF,CAAC;IA9M3pFxE,EAAE,CAAAyE,UAAA,SAAAgC,OAAA,CAAAlC,WA8MwpF,CAAC,aAAAmC,mBAAoB,CAAC;EAAA;AAAA;AAAA,SAAAC,gGAAA/C,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9MhrF5D,EAAE,CAAAmG,uBAAA,EA8My6F,CAAC;IA9M56FnG,EAAE,CAAA4G,MAAA,EA8M69F,CAAC;IA9Mh+F5G,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA6B,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAwE,SAAA,CA8M69F,CAAC;IA9Mh+FxE,EAAE,CAAA6G,kBAAA,MAAApB,MAAA,CAAAqB,MAAA,CAAAC,SAAA,KA8M69F,CAAC;EAAA;AAAA;AAAA,SAAAC,iFAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mh+F5D,EAAE,CAAAmG,uBAAA,EA8M21F,CAAC;IA9M91FnG,EAAE,CAAAgE,UAAA,IAAA2C,+FAAA,0BA8My6F,CAAC;IA9M56F3G,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA6C,OAAA,GAAFzG,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAkD,mBAAA,GAAF1G,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CA8Mi5F,CAAC;IA9Mp5FxE,EAAE,CAAAyE,UAAA,SAAAgC,OAAA,CAAAlC,WA8Mi5F,CAAC,aAAAmC,mBAAoB,CAAC;EAAA;AAAA;AAAA,SAAAO,yEAAArD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mz6F5D,EAAE,CAAA4E,SAAA,cA8MknG,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA6C,OAAA,GA9MrnGzG,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,WAAAgC,OAAA,CAAAlC,WAAA,2BA8M0mG,CAAC;EAAA;AAAA;AAAA,SAAA2C,kEAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9M7mG5D,EAAE,CAAAmG,uBAAA,EA8M4/E,CAAC,MAA+C,CAAC;IA9M/iFnG,EAAE,CAAAgE,UAAA,IAAAwC,gFAAA,0BA8MkmF,CAAC,IAAAQ,gFAAA,0BAAwP,CAAC,IAAAC,wEAAA,kBAA+Q,CAAC;IA9M9mGjH,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAA6B,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAwE,SAAA,CA8M2iF,CAAC;IA9M9iFxE,EAAE,CAAAyE,UAAA,aAAAgB,MAAA,CAAA0B,QA8M2iF,CAAC;IA9M9iFnH,EAAE,CAAAwE,SAAA,CA8M+lF,CAAC;IA9MlmFxE,EAAE,CAAAyE,UAAA,0BA8M+lF,CAAC;IA9MlmFzE,EAAE,CAAAwE,SAAA,CA8Mw1F,CAAC;IA9M31FxE,EAAE,CAAAyE,UAAA,+BA8Mw1F,CAAC;EAAA;AAAA;AAAA,SAAA2C,iEAAAxD,EAAA,EAAAC,GAAA;AAAA,SAAAwD,iEAAAzD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9M31F5D,EAAE,CAAA4E,SAAA,cA8M09G,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAA6C,OAAA,GA9M79GzG,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,WAAAgC,OAAA,CAAAZ,UAAA,qBA8M87G,CAAC;EAAA;AAAA;AAAA,SAAAyB,mDAAA1D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mj8G5D,EAAE,CAAAgE,UAAA,IAAAkD,iEAAA,0BA8M4/E,CAAC,IAAAE,gEAAA,8BA9M//EpH,EAAE,CAAAqG,sBA8Mg0G,CAAC,IAAAgB,gEAAA,gCA9Mn0GrH,EAAE,CAAAqG,sBA8Mq3G,CAAC;EAAA;EAAA,IAAAzC,EAAA;IAAA,MAAA6C,OAAA,GAAA5C,GAAA,CAAAL,SAAA;IAAA,MAAA+D,mBAAA,GA9Mx3GvH,EAAE,CAAAqE,WAAA;IAAA,MAAAoB,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyE,UAAA,UAAAgB,MAAA,CAAA+B,UA8Mo+E,CAAC,aAAAD,mBAAoB,CAAC;IA9M5/EvH,EAAE,CAAAwE,SAAA,CA8M4vG,CAAC;IA9M/vGxE,EAAE,CAAAyE,UAAA,qBAAAgB,MAAA,CAAA+B,UA8M4vG,CAAC,4BA9M/vGxH,EAAE,CAAA0E,eAAA,IAAApB,GAAA,EAAAmD,OAAA,CA8MuzG,CAAC;EAAA;AAAA;AAAA,SAAAgB,4DAAA7D,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAA8D,GAAA,GA9M1zG1H,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,gBA8Mu1H,CAAC;IA9M11H/D,EAAE,CAAAqF,UAAA,mBAAAsC,oFAAApC,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAkC,GAAA;MAAA,MAAAxD,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8M8sHD,MAAA,CAAAmC,YAAA,CAAA1D,OAAA,EAAAqB,MAAyB,CAAC;IAAA,CAAC,CAAC;IA9M5uHvF,EAAE,CAAA4E,SAAA,cA8Mw4H,CAAC;IA9M34H5E,EAAE,CAAAiE,YAAA,CA8My5H,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAA6B,MAAA,GA9M55HzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAgF,WAAA,UAAAS,MAAA,CAAAqB,MAAA,CAAAe,UAAA;EAAA;AAAA;AAAA,SAAAC,mDAAAlE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5D,EAAE,CAAAgE,UAAA,IAAAyD,2DAAA,oBA8Mu1H,CAAC;EAAA;EAAA,IAAA7D,EAAA;IAAA,MAAA6B,MAAA,GA9M11HzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyE,UAAA,SAAAgB,MAAA,CAAAsC,KAAA,CAAAC,cA8MwlH,CAAC;EAAA;AAAA;AAAA,SAAAC,4DAAArE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAsE,IAAA,GA9M3lHlI,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,gBA8M2vI,CAAC;IA9M9vI/D,EAAE,CAAAqF,UAAA,mBAAA8C,oFAAA;MAAFnI,EAAE,CAAAwF,aAAA,CAAA0C,IAAA;MAAA,MAAAhE,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8MsnID,MAAA,CAAA2C,cAAA,CAAAlE,OAAmB,CAAC;IAAA,CAAC,CAAC;IA9M9oIlE,EAAE,CAAA4E,SAAA,cA8M8yI,CAAC;IA9MjzI5E,EAAE,CAAAiE,YAAA,CA8M+zI,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAA6B,MAAA,GA9Ml0IzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAgF,WAAA,UAAAS,MAAA,CAAAqB,MAAA,CAAAuB,YAAA;EAAA;AAAA;AAAA,SAAAC,mDAAA1E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5D,EAAE,CAAAgE,UAAA,IAAAiE,2DAAA,oBA8M2vI,CAAC;EAAA;EAAA,IAAArE,EAAA;IAAA,MAAAM,OAAA,GA9M9vIlE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAqE,YA8MggI,CAAC;EAAA;AAAA;AAAA,SAAAC,yEAAA5E,EAAA,EAAAC,GAAA;AAAA,SAAA4E,yEAAA7E,EAAA,EAAAC,GAAA;AAAA,SAAA6E,2DAAA9E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9MngI5D,EAAE,CAAA+D,cAAA,UA8MgiJ,CAAC;IA9MniJ/D,EAAE,CAAAgE,UAAA,IAAAwE,wEAAA,yBA8M2lJ,CAAC,IAAAC,wEAAA,yBAAsE,CAAC;IA9MrqJzI,EAAE,CAAAiE,YAAA,CA8M+rJ,CAAC;EAAA;EAAA,IAAAL,EAAA;IA9MlsJ5D,EAAE,CAAAmE,aAAA;IAAA,MAAAwE,cAAA,GAAF3I,EAAE,CAAAqE,WAAA;IAAA,MAAAuE,gBAAA,GAAF5I,EAAE,CAAAqE,WAAA;IAAA,MAAAoB,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA6I,sBAAA,uCAAApD,MAAA,CAAA0B,QAAA,mCA8MuhJ,CAAC;IA9M1hJnH,EAAE,CAAAwE,SAAA,CA8M0lJ,CAAC;IA9M7lJxE,EAAE,CAAAyE,UAAA,qBAAAmE,gBA8M0lJ,CAAC;IA9M7lJ5I,EAAE,CAAAwE,SAAA,CA8MiqJ,CAAC;IA9MpqJxE,EAAE,CAAAyE,UAAA,qBAAAkE,cA8MiqJ,CAAC;EAAA;AAAA;AAAA,SAAAG,oDAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9MpqJ5D,EAAE,CAAAgE,UAAA,IAAA0E,0DAAA,kBA8MgiJ,CAAC;EAAA;EAAA,IAAA9E,EAAA;IAAA,MAAA6B,MAAA,GA9MniJzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyE,UAAA,SAAAgB,MAAA,CAAA0B,QAAA,mBA8Mk7I,CAAC;EAAA;AAAA;AAAA,SAAA4B,wDAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAoF,IAAA,GA9Mr7IhJ,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,WA8MokK,CAAC;IA9MvkK/D,EAAE,CAAAqF,UAAA,mBAAA4D,2EAAA1D,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAwD,IAAA;MAAA,MAAA9E,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8M+hKD,MAAA,CAAAE,aAAA,CAAAzB,OAAA,EAAAqB,MAA0B,CAAC;IAAA,CAAC,CAAC;IA9M9jKvF,EAAE,CAAA4G,MAAA,EA8MqmK,CAAC;IA9MxmK5G,EAAE,CAAAiE,YAAA,CA8MymK,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9M5mKlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAFxD,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAY,GAAA,EAAF9E,EAAE,CAAA+E,aA8Mm8J,CAAC;IA9Mt8J/E,EAAE,CAAAgF,WAAA,UAAAd,OAAA,CAAAe,IAAA,cAAAf,OAAA,CAAAgF,SAAA,IAAAhF,OAAA,CAAAgF,SAAA,CAAAC,QAAA;IAAFnJ,EAAE,CAAAwE,SAAA,CA8MqmK,CAAC;IA9MxmKxE,EAAE,CAAA6G,kBAAA,MAAA3C,OAAA,CAAAe,IAAA,KA8MqmK,CAAC;EAAA;AAAA;AAAA,SAAAmE,2DAAAxF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAyF,IAAA,GA9MxmKrJ,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,cA8M8xK,CAAC;IA9MjyK/D,EAAE,CAAAqF,UAAA,mBAAAiE,iFAAA/D,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAA6D,IAAA;MAAA,MAAAnF,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8MyvKD,MAAA,CAAAE,aAAA,CAAAzB,OAAA,EAAAqB,MAA0B,CAAC;IAAA,CAAC,CAAC;IA9MxxKvF,EAAE,CAAA4G,MAAA,EA8M+zK,CAAC;IA9Ml0K5G,EAAE,CAAAiE,YAAA,CA8Ms0K,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9Mz0KlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAFxD,EAAE,CAAAgF,WAAA,UAAAd,OAAA,CAAAe,IAAA;IAAFjF,EAAE,CAAAwE,SAAA,CA8M+zK,CAAC;IA9Ml0KxE,EAAE,CAAA6G,kBAAA,MAAA3C,OAAA,CAAAe,IAAA,KA8M+zK,CAAC;EAAA;AAAA;AAAA,SAAAsE,kEAAA3F,EAAA,EAAAC,GAAA;AAAA,SAAA2F,oDAAA5F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Ml0K5D,EAAE,CAAAgE,UAAA,IAAA+E,uDAAA,eA8MokK,CAAC,IAAAK,0DAAA,kBAAyN,CAAC,IAAAG,iEAAA,yBAAoG,CAAC;EAAA;EAAA,IAAA3F,EAAA;IAAA,MAAAM,OAAA,GA9Mt4KlE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAA,MAAAiG,oBAAA,GAAFzJ,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAY,GA8MmxJ,CAAC;IA9MtxJ9E,EAAE,CAAAwE,SAAA,CA8MipK,CAAC;IA9MppKxE,EAAE,CAAAyE,UAAA,UAAAP,OAAA,CAAAY,GA8MipK,CAAC;IA9MppK9E,EAAE,CAAAwE,SAAA,CA8Mk4K,CAAC;IA9Mr4KxE,EAAE,CAAAyE,UAAA,qBAAAgF,oBA8Mk4K,CAAC;EAAA;AAAA;AAAA,SAAAC,oDAAA9F,EAAA,EAAAC,GAAA;AAAA,SAAA8F,oDAAA/F,EAAA,EAAAC,GAAA;AAAA,SAAA+F,iDAAAhG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAiG,IAAA,GA9Mr4K7J,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAA+D,cAAA,WA8M0nM,CAAC;IA9M7nM/D,EAAE,CAAAqF,UAAA,mBAAAyE,oEAAAvE,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAqE,IAAA;MAAA,MAAA3F,OAAA,GAAFlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;MAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CA8MqlMD,MAAA,CAAAE,aAAA,CAAAzB,OAAA,EAAAqB,MAA0B,CAAC;IAAA,CAAC,CAAC;IA9MpnMvF,EAAE,CAAA4E,SAAA,cA8MwqM,CAAC;IA9M3qM5E,EAAE,CAAAiE,YAAA,CA8MorM,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9MvrMlE,EAAE,CAAAmE,aAAA,IAAAX,SAAA;IAAA,MAAAiC,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAY,GAAA,IAAAZ,OAAA,CAAAW,QAAA,EAAF7E,EAAE,CAAA+E,aA8M22L,CAAC,cAAAb,OAAA,CAAAY,GAAA,IAAAZ,OAAA,CAAAW,QAAA,IA9M92L7E,EAAE,CAAA+J,eAAA,IAAAtG,GAAA,QA8MgkM,CAAC;IA9MnkMzD,EAAE,CAAAgF,WAAA,UAAAS,MAAA,CAAAqB,MAAA,CAAAkD,WAAA;EAAA;AAAA;AAAA,SAAAC,0EAAArG,EAAA,EAAAC,GAAA;AAAA,SAAAqG,4DAAAtG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAF5D,EAAE,CAAAmG,uBAAA,EA8M2uM,CAAC;IA9M9uMnG,EAAE,CAAAgE,UAAA,IAAAiG,yEAAA,yBA8MsyM,CAAC;IA9MzyMjK,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAF5D,EAAE,CAAAmE,aAAA;IAAA,MAAAyE,gBAAA,GAAF5I,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CA8MqyM,CAAC;IA9MxyMxE,EAAE,CAAAyE,UAAA,qBAAAmE,gBA8MqyM,CAAC;EAAA;AAAA;AAAA,SAAAuB,2DAAAvG,EAAA,EAAAC,GAAA;AAAA,SAAAuG,6CAAAxG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9MxyM5D,EAAE,CAAA+D,cAAA,cA8M0wL,CAAC;IA9M7wL/D,EAAE,CAAAgE,UAAA,IAAA4F,gDAAA,eA8M0nM,CAAC,IAAAM,2DAAA,0BAAgH,CAAC,IAAAC,0DAAA,yBAAsJ,CAAC;IA9Mr4MnK,EAAE,CAAAiE,YAAA,CA8M65M,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9Mh6MlE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAA,MAAAmF,cAAA,GAAF3I,EAAE,CAAAqE,WAAA;IAAA,MAAAoB,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAwE,SAAA,CA8M2zL,CAAC;IA9M9zLxE,EAAE,CAAAyE,UAAA,SAAAgB,MAAA,CAAAsC,KAAA,CAAAsC,eA8M2zL,CAAC;IA9M9zLrK,EAAE,CAAAwE,SAAA,CA8MwuM,CAAC;IA9M3uMxE,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAoG,MAAA,WA8MwuM,CAAC;IA9M3uMtK,EAAE,CAAAwE,SAAA,CA8Mi4M,CAAC;IA9Mp4MxE,EAAE,CAAAyE,UAAA,qBAAAkE,cA8Mi4M,CAAC;EAAA;AAAA;AAAA,SAAA4B,4CAAA3G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mp4M5D,EAAE,CAAA+D,cAAA,aA8M2+M,CAAC;IA9M9+M/D,EAAE,CAAA4E,SAAA,qBA8MymN,CAAC;IA9M5mN5E,EAAE,CAAAiE,YAAA,CA8MqnN,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GA9MxnNlE,EAAE,CAAAmE,aAAA,GAAAX,SAAA;IAAFxD,EAAE,CAAAwE,SAAA,CA8M6hN,CAAC;IA9MhiNxE,EAAE,CAAAyE,UAAA,cAAAP,OAAA,CAAAsG,OA8M6hN,CAAC,oBAAsC,CAAC,mBAAqB,CAAC;EAAA;AAAA;AAAA,SAAAC,qCAAA7G,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9M7lN5D,EAAE,CAAA+D,cAAA,SA8MgwB,CAAC,aAA+Q,CAAC;IA9MnhC/D,EAAE,CAAAgE,UAAA,IAAAkC,kDAAA,gCAAFlG,EAAE,CAAAqG,sBA8MyiC,CAAC,IAAAiB,kDAAA,gCA9M5iCtH,EAAE,CAAAqG,sBA8My7E,CAAC,IAAAyB,kDAAA,gCA9M57E9H,EAAE,CAAAqG,sBA8MmiH,CAAC,IAAAiC,kDAAA,gCA9MtiHtI,EAAE,CAAAqG,sBA8M88H,CAAC,KAAAyC,mDAAA,gCA9Mj9H9I,EAAE,CAAAqG,sBA8Mw3I,CAAC,KAAAmD,mDAAA,gCA9M33IxJ,EAAE,CAAAqG,sBA8M+uJ,CAAC;IA9MlvJrG,EAAE,CAAA+D,cAAA,cA8Mo9K,CAAC,eAAuC,CAAC;IA9M//K/D,EAAE,CAAAgE,UAAA,KAAA0F,mDAAA,yBA8M+iL,CAAC,KAAAC,mDAAA,yBAAmE,CAAC;IA9MtnL3J,EAAE,CAAAiE,YAAA,CA8MgpL,CAAC,CAAW,CAAC;IA9M/pLjE,EAAE,CAAAgE,UAAA,KAAAoG,4CAAA,kBA8M0wL,CAAC,KAAAG,2CAAA,iBAAguB,CAAC;IA9M9+MvK,EAAE,CAAAiE,YAAA,CA8M+nN,CAAC,CAAO,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAM,OAAA,GAAAL,GAAA,CAAAL,SAAA;IAAA,MAAAkH,QAAA,GA9M1oN1K,EAAE,CAAAqE,WAAA;IAAA,MAAAsG,WAAA,GAAF3K,EAAE,CAAAqE,WAAA;IAAA,MAAAoB,MAAA,GAAFzF,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAA6I,sBAAA,qBAAApD,MAAA,CAAA0B,QAAA,cA8M+vB,CAAC;IA9MlwBnH,EAAE,CAAAwE,SAAA,CA8Mi4B,CAAC;IA9Mp4BxE,EAAE,CAAA4K,sBAAA,+CAAA1G,OAAA,CAAAoG,MAAA,sCAAA7E,MAAA,CAAA0B,QAAA,IA8Mi4B,CAAC;IA9Mp4BnH,EAAE,CAAAyE,UAAA,eAAAoG,SA8Mm7B,CAAC,mBAAA3G,OAAA,CAAAoG,MAAA,eAAApG,OAAA,CAAA4G,OAAA,OAAuF,CAAC;IA9M9gC9K,EAAE,CAAAgF,WAAA,aAAAd,OAAA,CAAA6G,GAAA;IAAF/K,EAAE,CAAAwE,SAAA,GA8M8iL,CAAC;IA9MjjLxE,EAAE,CAAAyE,UAAA,qBAAAiG,QA8M8iL,CAAC;IA9MjjL1K,EAAE,CAAAwE,SAAA,CA8MknL,CAAC;IA9MrnLxE,EAAE,CAAAyE,UAAA,qBAAAkG,WA8MknL,CAAC;IA9MrnL3K,EAAE,CAAAwE,SAAA,CA8MguL,CAAC;IA9MnuLxE,EAAE,CAAAyE,UAAA,SAAAgB,MAAA,CAAA0B,QAAA,wBAAAjD,OAAA,CAAAK,WA8MguL,CAAC;IA9MnuLvE,EAAE,CAAAwE,SAAA,CA8Mg8M,CAAC;IA9Mn8MxE,EAAE,CAAAyE,UAAA,SAAAP,OAAA,CAAAK,WA8Mg8M,CAAC;EAAA;AAAA;AAAA,MAAAyG,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA,GAAAA,CAAA;AAAA,SAAAC,0DAAAvH,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9Mn8M5D,EAAE,CAAA4E,SAAA,2BAggB8zD,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAwH,MAAA,GAhgBj0DpL,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAqL,WAAA,YAAAD,MAAA,CAAAE,gBAAA,cAggBm8C,CAAC;IAhgBt8CtL,EAAE,CAAAyE,UAAA,WAAA2G,MAAA,CAAAtE,MAggB49C,CAAC,aAAAsE,MAAA,CAAAG,UAA8B,CAAC,UAAAH,MAAA,CAAAI,UAAA,IAhgB9/CxL,EAAE,CAAA+J,eAAA,KAAAmB,GAAA,CAggB6hD,CAAC,UAAAE,MAAA,CAAAE,gBAAuC,CAAC,eAAAF,MAAA,CAAAK,YAAkC,CAAC,gBAAAL,MAAA,CAAAM,aAAoC,CAAC,mBAAAN,MAAA,CAAAO,gBAA0C,CAAC,cAAAP,MAAA,CAAAQ,SAA8B,CAAC,aAAAR,MAAA,CAAAS,QAA4B,CAAC,eAAAT,MAAA,CAAAU,UAAgC,CAAC,QAAAV,MAAA,CAAAW,GAAkB,CAAC;EAAA;AAAA;AAAA,SAAAC,uEAAApI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB3yD5D,EAAE,CAAAiM,kBAAA,EAggBy9D,CAAC;EAAA;AAAA;AAAA,SAAAC,wDAAAtI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB59D5D,EAAE,CAAAmG,uBAAA,EAggB22D,CAAC;IAhgB92DnG,EAAE,CAAAgE,UAAA,IAAAgI,sEAAA,0BAggB08D,CAAC;IAhgB78DhM,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAAwH,MAAA,GAAFpL,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAwE,SAAA,CAggBq6D,CAAC;IAhgBx6DxE,EAAE,CAAAyE,UAAA,qBAAA2G,MAAA,CAAAe,gBAggBq6D,CAAC,4BAhgBx6DnM,EAAE,CAAA0E,eAAA,IAAApB,GAAA,EAAA8H,MAAA,CAAAI,UAAA,CAggBu8D,CAAC;EAAA;AAAA;AAAA,SAAAY,yCAAAxI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB18D5D,EAAE,CAAAgE,UAAA,IAAAmH,yDAAA,4BAggB6yD,CAAC,IAAAe,uDAAA,yBAA6D,CAAC;EAAA;EAAA,IAAAtI,EAAA;IAAA,MAAAwH,MAAA,GAhgB92DpL,EAAE,CAAAmE,aAAA;IAAFnE,EAAE,CAAAyE,UAAA,SAAA2G,MAAA,CAAAtE,MAAA,KAAAsE,MAAA,CAAAe,gBAggB03C,CAAC;IAhgB73CnM,EAAE,CAAAwE,SAAA,CAggBw2D,CAAC;IAhgB32DxE,EAAE,CAAAyE,UAAA,SAAA2G,MAAA,CAAAe,gBAggBw2D,CAAC;EAAA;AAAA;AAAA,SAAAE,yCAAAzI,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB32D5D,EAAE,CAAAsM,YAAA,EAggByiE,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAA3I,EAAA,EAAAC,GAAA;AAAA,SAAA2I,yCAAA5I,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB5iE5D,EAAE,CAAA+D,cAAA,aAggB2pE,CAAC,gBAA+D,CAAC;IAhgB9tE/D,EAAE,CAAAgE,UAAA,IAAAuI,sDAAA,yBAggB2wE,CAAC;IAhgB9wEvM,EAAE,CAAAiE,YAAA,CAggBqyE,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAL,EAAA;IAAA,MAAAwH,MAAA,GAhgBlzEpL,EAAE,CAAAmE,aAAA;IAAA,MAAAsI,MAAA,GAAFzM,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAqL,WAAA,YAAAD,MAAA,CAAAsB,YAAA,cAggB0pE,CAAC;IAhgB7pE1M,EAAE,CAAAyE,UAAA,YAAA2G,MAAA,CAAAuB,SAggB2mE,CAAC;IAhgB9mE3M,EAAE,CAAAwE,SAAA,CAggB0tE,CAAC;IAhgB7tExE,EAAE,CAAAyE,UAAA,YAAA2G,MAAA,CAAAwB,WAggB0tE,CAAC;IAhgB7tE5M,EAAE,CAAAwE,SAAA,EAggB0wE,CAAC;IAhgB7wExE,EAAE,CAAAyE,UAAA,qBAAAgI,MAggB0wE,CAAC;EAAA;AAAA;AAAA,SAAAI,wDAAAjJ,EAAA,EAAAC,GAAA;AAAA,SAAAiJ,wDAAAlJ,EAAA,EAAAC,GAAA;AAAA,SAAAkJ,0CAAAnJ,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuB,GAAA,GAhgB7wEnF,EAAE,CAAAoF,gBAAA;IAAFpF,EAAE,CAAAmG,uBAAA,EAggBw3E,CAAC;IAhgB33EnG,EAAE,CAAA+D,cAAA,aAggBs/E,CAAC;IAhgBz/E/D,EAAE,CAAAqF,UAAA,kBAAA2H,8DAAAzH,MAAA;MAAFvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAiG,MAAA,GAAFpL,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CAggBm6E0F,MAAA,CAAA6B,QAAA,CAAA1H,MAAe,CAAC;IAAA,CAAC,CAAC,sBAAA2H,kEAAA3H,MAAA;MAhgBv7EvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAiG,MAAA,GAAFpL,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CAggBm8E0F,MAAA,CAAA6B,QAAA,CAAA1H,MAAe,CAAC;IAAA,CAAC,CAAC,uBAAA4H,mEAAA5H,MAAA;MAhgBv9EvF,EAAE,CAAAwF,aAAA,CAAAL,GAAA;MAAA,MAAAiG,MAAA,GAAFpL,EAAE,CAAAmE,aAAA;MAAA,OAAFnE,EAAE,CAAA0F,WAAA,CAggBo+E0F,MAAA,CAAA6B,QAAA,CAAA1H,MAAe,CAAC;IAAA,CAAC,CAAC;IAhgBx/EvF,EAAE,CAAA+D,cAAA,gBAggB+kF,CAAC,aAAgD,CAAC;IAhgBnoF/D,EAAE,CAAAgE,UAAA,IAAA6I,uDAAA,yBAggBkrF,CAAC;IAhgBrrF7M,EAAE,CAAAiE,YAAA,CAggB8sF,CAAC,CAAW,CAAC,CAAS,CAAC;IAhgBvuFjE,EAAE,CAAAgE,UAAA,IAAA8I,uDAAA,yBAggBixF,CAAC;IAhgBpxF9M,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAA,MAAAwH,MAAA,GAAFpL,EAAE,CAAAmE,aAAA;IAAA,MAAAiJ,OAAA,GAAFpN,EAAE,CAAAqE,WAAA;IAAA,MAAAoI,MAAA,GAAFzM,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CAggBw5E,CAAC;IAhgB35ExE,EAAE,CAAAyE,UAAA,YAAA2G,MAAA,CAAAuB,SAggBw5E,CAAC;IAhgB35E3M,EAAE,CAAAwE,SAAA,CAggBqjF,CAAC;IAhgBxjFxE,EAAE,CAAAyE,UAAA,YAAA2G,MAAA,CAAAwB,WAggBqjF,CAAC;IAhgBxjF5M,EAAE,CAAAwE,SAAA,EAggBirF,CAAC;IAhgBprFxE,EAAE,CAAAyE,UAAA,qBAAAgI,MAggBirF,CAAC;IAhgBprFzM,EAAE,CAAAwE,SAAA,CAggBgxF,CAAC;IAhgBnxFxE,EAAE,CAAAyE,UAAA,qBAAA2I,OAggBgxF,CAAC;EAAA;AAAA;AAAA,SAAAC,sEAAAzJ,EAAA,EAAAC,GAAA;AAAA,SAAAyJ,sEAAA1J,EAAA,EAAAC,GAAA;AAAA,SAAA0J,wDAAA3J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgBnxF5D,EAAE,CAAAmG,uBAAA,EAggB24F,CAAC;IAhgB94FnG,EAAE,CAAAgE,UAAA,IAAAqJ,qEAAA,yBAggB07F,CAAC,IAAAC,qEAAA,yBAA2D,CAAC;IAhgBz/FtN,EAAE,CAAAoG,qBAAA;EAAA;EAAA,IAAAxC,EAAA;IAAF5D,EAAE,CAAAmE,aAAA;IAAA,MAAAiJ,OAAA,GAAFpN,EAAE,CAAAqE,WAAA;IAAA,MAAAmJ,MAAA,GAAFxN,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAwE,SAAA,CAggBy7F,CAAC;IAhgB57FxE,EAAE,CAAAyE,UAAA,qBAAA2I,OAggBy7F,CAAC;IAhgB57FpN,EAAE,CAAAwE,SAAA,CAggBq/F,CAAC;IAhgBx/FxE,EAAE,CAAAyE,UAAA,qBAAA+I,MAggBq/F,CAAC;EAAA;AAAA;AAAA,SAAAC,yCAAA7J,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgBx/F5D,EAAE,CAAAgE,UAAA,IAAAuJ,uDAAA,yBAggB24F,CAAC;EAAA;EAAA,IAAA3J,EAAA;IAAA,MAAAwH,MAAA,GAhgB94FpL,EAAE,CAAAmE,aAAA;IAAA,MAAAuJ,MAAA,GAAF1N,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAyE,UAAA,SAAA2G,MAAA,CAAAG,UAAA,mBAggBg4F,CAAC,aAAAmC,MAAO,CAAC;EAAA;AAAA;AAAA,SAAAC,uDAAA/J,EAAA,EAAAC,GAAA;AAAA,SAAA+J,uDAAAhK,EAAA,EAAAC,GAAA;AAAA,SAAAgK,yCAAAjK,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhgB34F5D,EAAE,CAAAgE,UAAA,IAAA2J,sDAAA,yBAggBumG,CAAC,IAAAC,sDAAA,yBAA0D,CAAC;EAAA;EAAA,IAAAhK,EAAA;IAhgBrqG5D,EAAE,CAAAmE,aAAA;IAAA,MAAAiJ,OAAA,GAAFpN,EAAE,CAAAqE,WAAA;IAAA,MAAAmJ,MAAA,GAAFxN,EAAE,CAAAqE,WAAA;IAAFrE,EAAE,CAAAyE,UAAA,qBAAA+I,MAggBsmG,CAAC;IAhgBzmGxN,EAAE,CAAAwE,SAAA,CAggBiqG,CAAC;IAhgBpqGxE,EAAE,CAAAyE,UAAA,qBAAA2I,OAggBiqG,CAAC;EAAA;AAAA;AAtyBxwG,MAAMU,oBAAoB,CAAC;EACvBC,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACC,OAAO,CAACC,QAAQ,IAAI,CAAC,IAAI,CAACD,OAAO,CAACE,qBAAqB,EAAE;MAC9D;IACJ;IACA,IAAI,CAACC,IAAI,CAACC,aAAa,CAACC,KAAK,CAAC,CAAC;EACnC;EACA;EACAC,UAAUA,CAACC,CAAC,EAAE;IACV,IAAI,IAAI,CAACP,OAAO,CAACC,QAAQ,IAAIM,CAAC,CAACC,IAAI,KAAK,UAAU,EAAE;MAChDD,CAAC,CAACE,cAAc,CAAC,CAAC;MAClB;IACJ;IACA,IAAI,IAAI,CAACT,OAAO,CAACU,SAAS,EAAE;MACxB,IAAI,CAACC,gBAAgB,CAACJ,CAAC,CAACK,YAAY,CAACC,KAAK,CAAC;IAC/C,CAAC,MACI;MACD,MAAMC,KAAK,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAC9BC,IAAI,CAACX,CAAC,CAACK,YAAY,CAACE,KAAK,CAAC,CAC1B1N,MAAM,CAAE+M,IAAI,IAAK,IAAI,CAACgB,UAAU,CAAChB,IAAI,EAAE,IAAI,CAACH,OAAO,CAACoB,MAAM,CAAC,CAAC;MACjE,IAAIN,KAAK,CAACO,MAAM,EAAE;QACd,IAAI,CAACC,WAAW,CAACR,KAAK,CAAC;MAC3B;IACJ;IACAP,CAAC,CAACE,cAAc,CAAC,CAAC;EACtB;EACAc,QAAQA,CAAChB,CAAC,EAAE;IACR,IAAI,IAAI,CAACP,OAAO,CAACC,QAAQ,EAAE;MACvB;IACJ;IACA,MAAMuB,GAAG,GAAGjB,CAAC,CAACkB,MAAM;IACpB,IAAI,CAACH,WAAW,CAACE,GAAG,CAACV,KAAK,CAAC;IAC3BU,GAAG,CAACE,KAAK,GAAG,EAAE;EAClB;EACAf,gBAAgBA,CAACG,KAAK,EAAE;IACpB,MAAMa,iBAAiB,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;MACtC,IAAID,IAAI,CAACE,MAAM,EAAE;QACbF,IAAI,CAACzB,IAAI,CAAEA,IAAI,IAAK;UAChB,IAAI,IAAI,CAACgB,UAAU,CAAChB,IAAI,EAAE,IAAI,CAACH,OAAO,CAACoB,MAAM,CAAC,EAAE;YAC5C,IAAI,CAACE,WAAW,CAAC,CAACnB,IAAI,CAAC,CAAC;UAC5B;QACJ,CAAC,CAAC;MACN,CAAC,MACI,IAAIyB,IAAI,CAACG,WAAW,EAAE;QACvB,MAAMC,SAAS,GAAGJ,IAAI,CAACK,YAAY,CAAC,CAAC;QACrCD,SAAS,CAACE,WAAW,CAAEC,OAAO,IAAK;UAC/B,KAAK,MAAMC,UAAU,IAAID,OAAO,EAAE;YAC9BR,iBAAiB,CAACS,UAAU,EAAG,GAAEP,IAAK,GAAED,IAAI,CAAC3K,IAAK,GAAE,CAAC;UACzD;QACJ,CAAC,CAAC;MACN;IACJ,CAAC;IACD,KAAK,MAAMkJ,IAAI,IAAIW,KAAK,EAAE;MACtBa,iBAAiB,CAACxB,IAAI,CAACkC,gBAAgB,CAAC,CAAC,EAAE,EAAE,CAAC;IAClD;EACJ;EACAlB,UAAUA,CAAChB,IAAI,EAAEmC,aAAa,EAAE;IAC5B,IAAInC,IAAI,IAAImC,aAAa,EAAE;MACvB,MAAMC,kBAAkB,GAAGxB,KAAK,CAACyB,OAAO,CAACF,aAAa,CAAC,GAAGA,aAAa,GAAGA,aAAa,CAACG,KAAK,CAAC,GAAG,CAAC;MAClG,MAAMC,QAAQ,GAAI,GAAEvC,IAAI,CAAClJ,IAAK,EAAC;MAC/B,MAAM0L,QAAQ,GAAI,GAAExC,IAAI,CAACK,IAAK,EAAC;MAC/B,MAAMoC,YAAY,GAAGD,QAAQ,CAACE,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;MAClD,OAAON,kBAAkB,CAACO,IAAI,CAACtC,IAAI,IAAI;QACnC,MAAMuC,SAAS,GAAGvC,IAAI,CAACwC,IAAI,CAAC,CAAC;QAC7B,IAAID,SAAS,CAACE,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UAC7B,OAAQP,QAAQ,CACXQ,WAAW,CAAC,CAAC,CACbC,OAAO,CAACJ,SAAS,CAACG,WAAW,CAAC,CAAC,EAAER,QAAQ,CAACQ,WAAW,CAAC,CAAC,CAAC7B,MAAM,GAAG0B,SAAS,CAACG,WAAW,CAAC,CAAC,CAAC7B,MAAM,CAAC,KAAK,CAAC,CAAC;QAChH,CAAC,MACI,IAAI,OAAO,CAAC+B,IAAI,CAACL,SAAS,CAAC,EAAE;UAC9B;UACA,OAAOH,YAAY,KAAKG,SAAS,CAACF,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;QAC1D;QACA,OAAOF,QAAQ,KAAKI,SAAS;MACjC,CAAC,CAAC;IACN;IACA,OAAO,IAAI;EACf;EACAM,SAASA,CAAClD,IAAI,EAAE;IACZ,IAAI,CAACA,IAAI,CAACmD,GAAG,EAAE;MACXnD,IAAI,CAACmD,GAAG,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,SAAS,CAAC,CAAC,CAAC;IACtD;IACA,OAAOvD,IAAI;EACf;EACAmB,WAAWA,CAACqC,QAAQ,EAAE;IAClB,IAAIC,QAAQ,GAAGjR,EAAE,CAACoO,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACyC,QAAQ,CAAC,CAAC;IACvD,IAAI,IAAI,CAAC3D,OAAO,CAAC6D,OAAO,EAAE;MACtB,IAAI,CAAC7D,OAAO,CAAC6D,OAAO,CAACC,OAAO,CAACC,CAAC,IAAI;QAC9BH,QAAQ,GAAGA,QAAQ,CAACI,IAAI,CAAChR,SAAS,CAACiR,IAAI,IAAI;UACvC,MAAMC,KAAK,GAAGH,CAAC,CAACI,EAAE,CAACF,IAAI,CAAC;UACxB,OAAOC,KAAK,YAAYtR,UAAU,GAAGsR,KAAK,GAAGvR,EAAE,CAACuR,KAAK,CAAC;QAC1D,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACN;IACAN,QAAQ,CAACQ,SAAS,CAACH,IAAI,IAAI;MACvBA,IAAI,CAACH,OAAO,CAAE3D,IAAI,IAAK;QACnB,IAAI,CAACkD,SAAS,CAAClD,IAAI,CAAC;QACpB,IAAI,CAACkE,MAAM,CAAClE,IAAI,EAAE8D,IAAI,CAAC;MAC3B,CAAC,CAAC;IACN,CAAC,EAAE1D,CAAC,IAAI;MACJlN,IAAI,CAAE,+BAA8B,EAAEkN,CAAC,CAAC;IAC5C,CAAC,CAAC;EACN;EACA8D,MAAMA,CAAClE,IAAI,EAAEwD,QAAQ,EAAE;IACnB,IAAI,CAAC,IAAI,CAAC3D,OAAO,CAACsE,YAAY,EAAE;MAC5B,OAAO,IAAI,CAACC,IAAI,CAACpE,IAAI,CAAC;IAC1B;IACA,MAAMqE,MAAM,GAAG,IAAI,CAACxE,OAAO,CAACsE,YAAY,CAACnE,IAAI,EAAEwD,QAAQ,CAAC;IACxD,IAAIa,MAAM,YAAY5R,UAAU,EAAE;MAC9B4R,MAAM,CAACJ,SAAS,CAAEK,aAAa,IAAK;QAChC,MAAMC,iBAAiB,GAAGC,MAAM,CAAC3D,SAAS,CAACyC,QAAQ,CAACvC,IAAI,CAACuD,aAAa,CAAC;QACvE,IAAIC,iBAAiB,KAAK,eAAe,IAAIA,iBAAiB,KAAK,eAAe,EAAE;UAChF,IAAI,CAACrB,SAAS,CAACoB,aAAa,CAAC;UAC7B,IAAI,CAACF,IAAI,CAACE,aAAa,CAAC;QAC5B,CAAC,MACI,IAAI,OAAOA,aAAa,KAAK,SAAS,IAAIA,aAAa,KAAK,KAAK,EAAE;UACpE,IAAI,CAACF,IAAI,CAACpE,IAAI,CAAC;QACnB;MACJ,CAAC,EAAEI,CAAC,IAAI;QACJlN,IAAI,CAAE,qCAAoC,EAAEkN,CAAC,CAAC;MAClD,CAAC,CAAC;IACN,CAAC,MACI,IAAIiE,MAAM,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI,CAACD,IAAI,CAACpE,IAAI,CAAC;IAC1B;EACJ;EACAoE,IAAIA,CAACpE,IAAI,EAAE;IACP,IAAI,IAAI,CAACyE,OAAO,EAAE;MACd;IACJ;IACA,IAAIC,QAAQ,GAAGlS,EAAE,CAACwN,IAAI,CAAC;IACvB,IAAI2E,eAAe;IACnB,MAAMC,GAAG,GAAG,IAAI,CAAC/E,OAAO;IACxB,MAAM;MAAEsD;IAAI,CAAC,GAAGnD,IAAI;IACpB,MAAM;MAAE6E,MAAM;MAAEC,IAAI;MAAEC,OAAO;MAAEC;IAAc,CAAC,GAAGJ,GAAG;IACpD,MAAMK,IAAI,GAAG;MACTJ,MAAM,EAAE,OAAOA,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAG,EAAE;MAChD/N,IAAI,EAAE8N,GAAG,CAAC9N,IAAI;MACdiO,OAAO;MACP/E,IAAI;MACJkF,QAAQ,EAAElF,IAAI;MACd8E,IAAI;MACJK,eAAe,EAAEP,GAAG,CAACO,eAAe;MACpCC,UAAU,EAAER,GAAG,CAACQ,UAAU,GACpBhF,CAAC,IAAI;QACHwE,GAAG,CAACQ,UAAU,CAAChF,CAAC,EAAEJ,IAAI,CAAC;MAC3B,CAAC,GACCtD,SAAS;MACf2I,SAAS,EAAEA,CAACC,GAAG,EAAEC,GAAG,KAAK;QACrB,IAAI,CAACC,KAAK,CAACrC,GAAG,CAAC;QACfyB,GAAG,CAACS,SAAS,CAACC,GAAG,EAAEtF,IAAI,EAAEuF,GAAG,CAAC;MACjC,CAAC;MACDE,OAAO,EAAEF,GAAG,IAAI;QACZ,IAAI,CAACC,KAAK,CAACrC,GAAG,CAAC;QACfyB,GAAG,CAACa,OAAO,CAACF,GAAG,EAAEvF,IAAI,CAAC;MAC1B;IACJ,CAAC;IACD,IAAI,OAAO6E,MAAM,KAAK,UAAU,EAAE;MAC9B,MAAMa,YAAY,GAAGb,MAAM,CAAC7E,IAAI,CAAC;MACjC,IAAI0F,YAAY,YAAYjT,UAAU,EAAE;QACpCiS,QAAQ,GAAGA,QAAQ,CAACb,IAAI,CAAChR,SAAS,CAAC,MAAM6S,YAAY,CAAC,EAAE5S,GAAG,CAAC6S,GAAG,IAAI;UAC/DV,IAAI,CAACJ,MAAM,GAAGc,GAAG;UACjB,OAAO3F,IAAI;QACf,CAAC,CAAC,CAAC;MACP,CAAC,MACI;QACDiF,IAAI,CAACJ,MAAM,GAAGa,YAAY;MAC9B;IACJ;IACA,IAAI,OAAOV,aAAa,KAAK,UAAU,EAAE;MACrC,MAAMY,eAAe,GAAGZ,aAAa,CAAChF,IAAI,CAAC;MAC3C0E,QAAQ,GAAGA,QAAQ,CAACb,IAAI,CAAChR,SAAS,CAAC,MAAO+S,eAAe,YAAYnT,UAAU,GAAGmT,eAAe,GAAGpT,EAAE,CAACoT,eAAe,CAAE,CAAC,EAAE7S,GAAG,CAAC8S,OAAO,IAAKlB,eAAe,GAAGkB,OAAQ,CAAC,CAAC;IAC3K;IACA,IAAI,OAAOf,IAAI,KAAK,UAAU,EAAE;MAC5B,MAAMgB,UAAU,GAAGhB,IAAI,CAAC9E,IAAI,CAAC;MAC7B,IAAI8F,UAAU,YAAYrT,UAAU,EAAE;QAClCiS,QAAQ,GAAGA,QAAQ,CAACb,IAAI,CAAChR,SAAS,CAAC,MAAMiT,UAAU,CAAC,EAAEhT,GAAG,CAAC6S,GAAG,IAAI;UAC7DV,IAAI,CAACH,IAAI,GAAGa,GAAG;UACf,OAAOhB,eAAe,IAAI3E,IAAI;QAClC,CAAC,CAAC,CAAC;MACP,CAAC,MACI;QACDiF,IAAI,CAACH,IAAI,GAAGgB,UAAU;MAC1B;IACJ;IACA,IAAI,OAAOf,OAAO,KAAK,UAAU,EAAE;MAC/B,MAAMgB,aAAa,GAAGhB,OAAO,CAAC/E,IAAI,CAAC;MACnC,IAAI+F,aAAa,YAAYtT,UAAU,EAAE;QACrCiS,QAAQ,GAAGA,QAAQ,CAACb,IAAI,CAAChR,SAAS,CAAC,MAAMkT,aAAa,CAAC,EAAEjT,GAAG,CAAC6S,GAAG,IAAI;UAChEV,IAAI,CAACF,OAAO,GAAGY,GAAG;UAClB,OAAOhB,eAAe,IAAI3E,IAAI;QAClC,CAAC,CAAC,CAAC;MACP,CAAC,MACI;QACDiF,IAAI,CAACF,OAAO,GAAGgB,aAAa;MAChC;IACJ;IACArB,QAAQ,CAACT,SAAS,CAAC4B,OAAO,IAAI;MAC1BZ,IAAI,CAACC,QAAQ,GAAGW,OAAO;MACvB,MAAMG,IAAI,GAAG,CAACpB,GAAG,CAACqB,aAAa,IAAI,IAAI,CAACV,GAAG,EAAExE,IAAI,CAAC,IAAI,EAAEkE,IAAI,CAAC;MAC7D,IAAI,EAAEe,IAAI,YAAYtT,YAAY,CAAC,EAAE;QACjCQ,IAAI,CAAE,+DAA8D,CAAC;MACzE;MACA,IAAI,CAACgT,IAAI,CAAC/C,GAAG,CAAC,GAAG6C,IAAI;MACrBpB,GAAG,CAACuB,OAAO,CAACnG,IAAI,CAAC;IACrB,CAAC,CAAC;EACN;EACAuF,GAAGA,CAACN,IAAI,EAAE;IACN,MAAMmB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/B,IAAIpB,IAAI,CAACH,IAAI,EAAE;MACXN,MAAM,CAAC8B,IAAI,CAACrB,IAAI,CAACH,IAAI,CAAC,CAAChS,GAAG,CAAC8J,GAAG,IAAI;QAC9BwJ,QAAQ,CAACG,MAAM,CAAC3J,GAAG,EAAEqI,IAAI,CAACH,IAAI,CAAClI,GAAG,CAAC,CAAC;MACxC,CAAC,CAAC;IACN;IACAwJ,QAAQ,CAACG,MAAM,CAACtB,IAAI,CAACnO,IAAI,EAAEmO,IAAI,CAACC,QAAQ,CAAC;IACzC,IAAI,CAACD,IAAI,CAACF,OAAO,EAAE;MACfE,IAAI,CAACF,OAAO,GAAG,CAAC,CAAC;IACrB;IACA,IAAIE,IAAI,CAACF,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,EAAE;MAC3CE,IAAI,CAACF,OAAO,CAAC,kBAAkB,CAAC,GAAI,gBAAe;IACvD,CAAC,MACI;MACD,OAAOE,IAAI,CAACF,OAAO,CAAC,kBAAkB,CAAC;IAC3C;IACA,MAAMyB,GAAG,GAAG,IAAI/U,WAAW,CAAC,MAAM,EAAEwT,IAAI,CAACJ,MAAM,EAAEuB,QAAQ,EAAE;MACvDK,cAAc,EAAE,IAAI;MACpBtB,eAAe,EAAEF,IAAI,CAACE,eAAe;MACrCJ,OAAO,EAAE,IAAIrT,WAAW,CAACuT,IAAI,CAACF,OAAO;IACzC,CAAC,CAAC;IACF,OAAO,IAAI,CAAC2B,IAAI,CAACC,OAAO,CAACH,GAAG,CAAC,CAACvC,SAAS,CAAE2C,KAAK,IAAK;MAC/C,IAAIA,KAAK,CAACvG,IAAI,KAAK1O,aAAa,CAACkV,cAAc,EAAE;QAC7C,IAAID,KAAK,CAACE,KAAK,GAAG,CAAC,EAAE;UACjBF,KAAK,CAACvK,OAAO,GAAIuK,KAAK,CAACG,MAAM,GAAGH,KAAK,CAACE,KAAK,GAAI,GAAG;QACtD;QACA7B,IAAI,CAACG,UAAU,CAACwB,KAAK,EAAE3B,IAAI,CAACjF,IAAI,CAAC;MACrC,CAAC,MACI,IAAI4G,KAAK,YAAYhV,YAAY,EAAE;QACpCqT,IAAI,CAACI,SAAS,CAACuB,KAAK,CAACI,IAAI,EAAE/B,IAAI,CAACjF,IAAI,EAAE4G,KAAK,CAAC;MAChD;IACJ,CAAC,EAAEK,GAAG,IAAI;MACN,IAAI,CAACC,KAAK,CAACjC,IAAI,CAACjF,IAAI,CAAC;MACrBiF,IAAI,CAACQ,OAAO,CAACwB,GAAG,EAAEhC,IAAI,CAACjF,IAAI,CAAC;IAChC,CAAC,CAAC;EACN;EACAwF,KAAKA,CAACrC,GAAG,EAAE;IACP,MAAM6C,IAAI,GAAG,IAAI,CAACE,IAAI,CAAC/C,GAAG,CAAC;IAC3B,IAAI6C,IAAI,YAAYtT,YAAY,EAAE;MAC9BsT,IAAI,CAACmB,WAAW,CAAC,CAAC;IACtB;IACA,OAAO,IAAI,CAACjB,IAAI,CAAC/C,GAAG,CAAC;EACzB;EACA+D,KAAKA,CAAClH,IAAI,EAAE;IACR,IAAIA,IAAI,EAAE;MACN,IAAI,CAACwF,KAAK,CAACxF,IAAI,IAAIA,IAAI,CAACmD,GAAG,CAAC;IAChC,CAAC,MACI;MACDqB,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAACJ,IAAI,CAAC,CAACvC,OAAO,CAACR,GAAG,IAAI,IAAI,CAACqC,KAAK,CAACrC,GAAG,CAAC,CAAC;IAC1D;EACJ;EACAiE,WAAWA,CAACC,MAAM,EAAEX,IAAI,EAAEY,UAAU,EAAE;IAClC,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACX,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACY,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACpB,IAAI,GAAG,CAAC,CAAC;IACd,IAAI,CAACzB,OAAO,GAAG,KAAK;IACpB,IAAI,CAAC8C,QAAQ,GAAG,IAAI5U,OAAO,CAAC,CAAC;IAC7B,IAAI,CAAC+T,IAAI,EAAE;MACP,MAAM,IAAIc,KAAK,CAAE,gFAA+E,CAAC;IACrG;EACJ;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,CAACJ,MAAM,CAACK,iBAAiB,CAAC,MAAM;MAChC9U,SAAS,CAAC,IAAI,CAAC0U,UAAU,CAACrH,aAAa,EAAE,OAAO,CAAC,CAC5C4D,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC,CAC9BtD,SAAS,CAAC,MAAM,IAAI,CAACrE,OAAO,CAAC,CAAC,CAAC;MACpChN,SAAS,CAAC,IAAI,CAAC0U,UAAU,CAACrH,aAAa,EAAE,SAAS,CAAC,CAC9C4D,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC,CAC9BtD,SAAS,CAAC2C,KAAK,IAAI;QACpB,IAAI,IAAI,CAAC/G,OAAO,CAACC,QAAQ,EAAE;UACvB;QACJ;QACA,IAAI8G,KAAK,CAAChK,GAAG,KAAK,OAAO,IAAIgK,KAAK,CAACe,OAAO,KAAKpW,KAAK,EAAE;UAClD,IAAI,CAACqO,OAAO,CAAC,CAAC;QAClB;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACAgI,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnD,OAAO,GAAG,IAAI;IACnB,IAAI,CAAC8C,QAAQ,CAACM,IAAI,CAAC,CAAC;IACpB,IAAI,CAACX,KAAK,CAAC,CAAC;EAChB;EACA;IAAS,IAAI,CAACY,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFrI,oBAAoB,EAA9B9N,EAAE,CAAAoW,iBAAA,CAA8CpW,EAAE,CAACqW,MAAM,GAAzDrW,EAAE,CAAAoW,iBAAA,CAAoEzW,EAAE,CAAC2W,UAAU,MAAnFtW,EAAE,CAAAoW,iBAAA,CAA8GpW,EAAE,CAACuW,UAAU;IAAA,CAA4C;EAAE;EAC3Q;IAAS,IAAI,CAACC,IAAI,kBAD8ExW,EAAE,CAAAyW,iBAAA;MAAAjI,IAAA,EACJV,oBAAoB;MAAA4I,SAAA;MAAAC,SAAA,WAAAC,2BAAAhT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADlB5D,EAAE,CAAA6W,WAAA,CAAA1T,GAAA;QAAA;QAAA,IAAAS,EAAA;UAAA,IAAAkT,EAAA;UAAF9W,EAAE,CAAA+W,cAAA,CAAAD,EAAA,GAAF9W,EAAE,CAAAgX,WAAA,QAAAnT,GAAA,CAAAsK,IAAA,GAAA2I,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,kCAAAzT,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAqF,UAAA,kBAAAiS,6CAAA/R,MAAA;YAAA,OACJ1B,GAAA,CAAAyK,UAAA,CAAA/I,MAAiB,CAAC;UAAA,CAAC,CAAC,sBAAAgS,iDAAAhS,MAAA;YAAA,OAApB1B,GAAA,CAAAyK,UAAA,CAAA/I,MAAiB,CAAC;UAAA,CAAC,CAAC;QAAA;QAAA,IAAA3B,EAAA;UADlB5D,EAAE,CAAAgF,WAAA,aACJ,GAAG,UAAH,QAAQ;UADNhF,EAAE,CAAAsE,WAAA,wBAAAT,GAAA,CAAAmK,OAAA,CAAAC,QACe,CAAC;QAAA;MAAA;MAAAuJ,MAAA;QAAAxJ,OAAA;MAAA;MAAAyJ,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADlB3X,EAAE,CAAA4X,mBAAA;MAAAC,KAAA,EAAAzU,GAAA;MAAA0U,kBAAA,EAAAzU,GAAA;MAAA0U,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,8BAAAvU,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAwU,GAAA,GAAFpY,EAAE,CAAAoF,gBAAA;UAAFpF,EAAE,CAAAqY,eAAA;UAAFrY,EAAE,CAAA+D,cAAA,iBAC88B,CAAC;UADj9B/D,EAAE,CAAAqF,UAAA,oBAAAiT,sDAAA/S,MAAA;YAAFvF,EAAE,CAAAwF,aAAA,CAAA4S,GAAA;YAAA,OAAFpY,EAAE,CAAA0F,WAAA,CAC0sB7B,GAAA,CAAA0L,QAAA,CAAAhK,MAAe,CAAC;UAAA,CAAC,CAAC;UAD9tBvF,EAAE,CAAAiE,YAAA,CAC88B,CAAC;UADj9BjE,EAAE,CAAAsM,YAAA,EACy+B,CAAC;QAAA;QAAA,IAAA1I,EAAA;UAD5+B5D,EAAE,CAAAqL,WAAA,kBAC08B,CAAC;UAD78BrL,EAAE,CAAAyE,UAAA,aAAAZ,GAAA,CAAAmK,OAAA,CAAAuK,QAC46B,CAAC;UAD/6BvY,EAAE,CAAAgF,WAAA,WAAAnB,GAAA,CAAAmK,OAAA,CAAAoB,MAAA,eAAAvL,GAAA,CAAAmK,OAAA,CAAAU,SAAA,0CAAA7K,GAAA,CAAAmK,OAAA,CAAAU,SAAA;QAAA;MAAA;MAAA8J,aAAA;IAAA,EAC0hC;EAAE;AACloC;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGzY,EAAE,CAAA0Y,iBAAA,CAGX5K,oBAAoB,EAAc,CAAC;IAClHU,IAAI,EAAEvO,SAAS;IACfmT,IAAI,EAAE,CAAC;MAAEuF,QAAQ,EAAE,iBAAiB;MAAElB,QAAQ,EAAE,aAAa;MAAEmB,IAAI,EAAE;QACzDC,KAAK,EAAE,YAAY;QACnB,iBAAiB,EAAE,KAAK;QACxB,aAAa,EAAE,UAAU;QACzB,6BAA6B,EAAE,kBAAkB;QACjD,QAAQ,EAAE,oBAAoB;QAC9B,YAAY,EAAE;MAClB,CAAC;MAAEC,mBAAmB,EAAE,KAAK;MAAEN,aAAa,EAAEtY,iBAAiB,CAAC6Y,IAAI;MAAErB,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE;IAA+f,CAAC;EAC9mB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1J,IAAI,EAAExO,EAAE,CAACqW;EAAO,CAAC,EAAE;IAAE7H,IAAI,EAAE7O,EAAE,CAAC2W,UAAU;IAAE0C,UAAU,EAAE,CAAC;MACxExK,IAAI,EAAErO;IACV,CAAC;EAAE,CAAC,EAAE;IAAEqO,IAAI,EAAExO,EAAE,CAACuW;EAAW,CAAC,CAAC,EAAkB;IAAEpI,IAAI,EAAE,CAAC;MACzDK,IAAI,EAAEpO,SAAS;MACfgT,IAAI,EAAE,CAAC,MAAM,EAAE;QAAE6F,MAAM,EAAE;MAAK,CAAC;IACnC,CAAC,CAAC;IAAEjL,OAAO,EAAE,CAAC;MACVQ,IAAI,EAAEnO;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM6Y,eAAe,GAAI1K,IAAI,IAAK,CAAC,CAACA,IAAI,IAAIA,IAAI,CAAC2C,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;AACxE,MAAMgI,YAAY,GAAG,GAAG;AACxB,MAAMC,qBAAqB,CAAC;EACxB,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAClS,QAAQ,KAAK,SAAS,IAAI,IAAI,CAACA,QAAQ,KAAK,cAAc;EAC1E;EACA,IAAI0H,KAAKA,CAACoD,IAAI,EAAE;IACZ,IAAI,CAACA,IAAI,GAAGA,IAAI;EACpB;EACAqH,MAAMA,CAACnL,IAAI,EAAE;IACT,IAAIA,IAAI,CAACoL,QAAQ,IAAI,OAAOpL,IAAI,CAACoL,QAAQ,KAAK,QAAQ,EAAE;MACpD,OAAOpL,IAAI,CAACoL,QAAQ;IACxB;IACA,OAAQpL,IAAI,CAACqL,KAAK,IAAIrL,IAAI,CAACqL,KAAK,CAACC,UAAU,IAAK,IAAI,CAAC3S,MAAM,CAAC4S,WAAW;EAC3E;EACAC,OAAOA,CAAC7U,GAAG,EAAE;IACT,MAAM8U,IAAI,GAAG9U,GAAG,CAAC2L,KAAK,CAAC,GAAG,CAAC;IAC3B,MAAMoJ,QAAQ,GAAGD,IAAI,CAACA,IAAI,CAACvK,MAAM,GAAG,CAAC,CAAC;IACtC,MAAMyK,qBAAqB,GAAGD,QAAQ,CAACpJ,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IACvD,OAAO,CAAC,aAAa,CAACsJ,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACjE;EACAjU,UAAUA,CAACsI,IAAI,EAAE;IACb,IAAI+K,eAAe,CAAC/K,IAAI,CAACK,IAAI,CAAC,EAAE;MAC5B,OAAO,IAAI;IACf;IACA,MAAM1J,GAAG,GAAIqJ,IAAI,CAACtJ,QAAQ,IAAIsJ,IAAI,CAACrJ,GAAG,IAAI,EAAG;IAC7C,IAAI,CAACA,GAAG,EAAE;MACN,OAAO,KAAK;IAChB;IACA,MAAMkV,SAAS,GAAG,IAAI,CAACL,OAAO,CAAC7U,GAAG,CAAC;IACnC,IAAI,eAAe,CAACsM,IAAI,CAACtM,GAAG,CAAC,IAAI,4CAA4C,CAACsM,IAAI,CAAC4I,SAAS,CAAC,EAAE;MAC3F,OAAO,IAAI;IACf,CAAC,MACI,IAAI,QAAQ,CAAC5I,IAAI,CAACtM,GAAG,CAAC,EAAE;MACzB;MACA,OAAO,KAAK;IAChB,CAAC,MACI,IAAIkV,SAAS,EAAE;MAChB;MACA,OAAO,KAAK;IAChB;IACA,OAAO,IAAI;EACf;EACAC,WAAWA,CAAC9L,IAAI,EAAE;IACd,IAAI,CAAC,IAAI,CAACkL,OAAO,EAAE;MACf,OAAO,EAAE;IACb;IACA,IAAIlL,IAAI,CAAC5J,WAAW,IAAK,CAAC4J,IAAI,CAACtJ,QAAQ,IAAI,CAACsJ,IAAI,CAACrJ,GAAI,EAAE;MACnD,OAAO,WAAW;IACtB,CAAC,MACI;MACD,OAAO,WAAW;IACtB;EACJ;EACAoV,YAAYA,CAAC/L,IAAI,EAAE;IACf,IAAI,CAAC+K,eAAe,CAAC/K,IAAI,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC2L,QAAQ,CAACC,SAAS,EAAE;MACzD,OAAOzZ,EAAE,CAAC,EAAE,CAAC;IACjB;IACA,MAAM0Z,MAAM,GAAG,IAAI,CAACC,GAAG,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/CF,MAAM,CAACG,KAAK,GAAGrB,YAAY;IAC3BkB,MAAM,CAACI,MAAM,GAAGtB,YAAY;IAC5BkB,MAAM,CAAC7Y,KAAK,CAACkZ,OAAO,GAAI,4CAA2CvB,YAAa,eAAcA,YAAa,mCAAkC;IAC7I,IAAI,CAACmB,GAAG,CAACnF,IAAI,CAACwF,WAAW,CAACN,MAAM,CAAC;IACjC,MAAMxW,GAAG,GAAGwW,MAAM,CAACO,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IACvB,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAAC9M,IAAI,CAAC;IAC3C0M,GAAG,CAACK,GAAG,GAAGH,SAAS;IACnB,OAAOha,SAAS,CAAC8Z,GAAG,EAAE,MAAM,CAAC,CAAC7I,IAAI,CAAC/Q,GAAG,CAAC,MAAM;MACzC,MAAM;QAAEuZ,KAAK;QAAEC;MAAO,CAAC,GAAGI,GAAG;MAC7B,IAAIM,SAAS,GAAGhC,YAAY;MAC5B,IAAIiC,UAAU,GAAGjC,YAAY;MAC7B,IAAIkC,OAAO,GAAG,CAAC;MACf,IAAIC,OAAO,GAAG,CAAC;MACf,IAAId,KAAK,GAAGC,MAAM,EAAE;QAChBW,UAAU,GAAGX,MAAM,IAAItB,YAAY,GAAGqB,KAAK,CAAC;QAC5Cc,OAAO,GAAG,EAAEF,UAAU,GAAGD,SAAS,CAAC,GAAG,CAAC;MAC3C,CAAC,MACI;QACDA,SAAS,GAAGX,KAAK,IAAIrB,YAAY,GAAGsB,MAAM,CAAC;QAC3CY,OAAO,GAAG,EAAEF,SAAS,GAAGC,UAAU,CAAC,GAAG,CAAC;MAC3C;MACA,IAAI;QACAvX,GAAG,CAAC0X,SAAS,CAACV,GAAG,EAAEQ,OAAO,EAAEC,OAAO,EAAEH,SAAS,EAAEC,UAAU,CAAC;MAC/D,CAAC,CACD,MAAM,CAAE;MACR,MAAMI,OAAO,GAAGnB,MAAM,CAACoB,SAAS,CAAC,CAAC;MAClC,IAAI,CAACnB,GAAG,CAACnF,IAAI,CAACuG,WAAW,CAACrB,MAAM,CAAC;MACjCW,GAAG,CAACW,eAAe,CAACZ,SAAS,CAAC;MAC9B,OAAOS,OAAO;IAClB,CAAC,CAAC,CAAC;EACP;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACzB,QAAQ,CAACC,SAAS,EAAE;MAC1B;IACJ;IACA,MAAMyB,GAAG,GAAGC,MAAM;IAClB,IAAI,CAAC,IAAI,CAACzC,OAAO,IACb,OAAO0C,QAAQ,KAAK,WAAW,IAC/B,OAAOF,GAAG,KAAK,WAAW,IAC1B,CAACA,GAAG,CAACG,UAAU,IACf,CAACH,GAAG,CAACI,IAAI,EAAE;MACX;IACJ;IACA,IAAI,CAAChK,IAAI,CACJ7Q,MAAM,CAAC+M,IAAI,IAAIA,IAAI,CAAC+N,aAAa,YAAYD,IAAI,IAAI9N,IAAI,CAACtJ,QAAQ,KAAKgG,SAAS,CAAC,CACjFiH,OAAO,CAAC3D,IAAI,IAAI;MACjBA,IAAI,CAACtJ,QAAQ,GAAG,EAAE;MAClB;MACA;MACA;MACA,MAAMsX,QAAQ,GAAG,CAAC,IAAI,CAACnS,WAAW,GAAG,IAAI,CAACA,WAAW,CAACmE,IAAI,CAAC,GAAG,IAAI,CAAC+L,YAAY,CAAC/L,IAAI,CAAC+N,aAAa,CAAC,EAAElK,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC;MACnI,IAAI,CAACF,MAAM,CAACK,iBAAiB,CAAC,MAAM;QAChCsG,QAAQ,CAAC/J,SAAS,CAACgK,OAAO,IAAI;UAC1B,IAAI,CAAC5G,MAAM,CAAC6G,GAAG,CAAC,MAAM;YAClBlO,IAAI,CAACtJ,QAAQ,GAAGuX,OAAO;YACvB,IAAI,CAACE,aAAa,CAAC,CAAC;UACxB,CAAC,CAAC;QACN,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,CAAC;EACN;EACA/T,YAAYA,CAAC4F,IAAI,EAAE;IACf,OAAO,CAAC,EAAE,IAAI,CAACpG,KAAK,CAACwU,gBAAgB,IAAIpO,IAAI,CAAC7D,MAAM,KAAK,MAAM,CAAC;EACpE;EACAkS,OAAOA,CAAA,EAAG;IACN,IAAI,CAACvK,IAAI,CAACH,OAAO,CAAC3D,IAAI,IAAI;MACtBA,IAAI,CAAC5J,WAAW,GAAG4J,IAAI,CAAC7D,MAAM,KAAK,WAAW;MAC9C6D,IAAI,CAACrD,OAAO,GAAG,IAAI,CAACwO,MAAM,CAACnL,IAAI,CAAC;MAChCA,IAAI,CAACjF,SAAS,GAAG,OAAOiF,IAAI,CAACjF,SAAS,KAAK,QAAQ,GAAGuT,IAAI,CAACC,KAAK,CAACvO,IAAI,CAACjF,SAAS,CAAC,GAAGiF,IAAI,CAACjF,SAAS;MACjGiF,IAAI,CAACtI,UAAU,GAAG,IAAI,CAAC8W,cAAc,GAAG,IAAI,CAACA,cAAc,CAACxO,IAAI,CAAC,GAAG,IAAI,CAACtI,UAAU,CAACsI,IAAI,CAAC;MACzFA,IAAI,CAAC7H,QAAQ,GAAG,IAAI,CAAC2T,WAAW,CAAC9L,IAAI,CAAC;MACtCA,IAAI,CAAC5F,YAAY,GAAG,IAAI,CAACA,YAAY,CAAC4F,IAAI,CAAC;IAC/C,CAAC,CAAC;EACN;EACAxI,aAAaA,CAACwI,IAAI,EAAEI,CAAC,EAAE;IACnB,IAAI,CAAC,IAAI,CAACqO,SAAS,EAAE;MACjB;IACJ;IACArO,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,OAAO,IAAI,CAACmO,SAAS,CAACzO,IAAI,CAAC;EAC/B;EACAvG,YAAYA,CAACuG,IAAI,EAAEI,CAAC,EAAE;IAClBA,CAAC,CAACE,cAAc,CAAC,CAAC;IAClB,IAAI,IAAI,CAAC5C,QAAQ,EAAE;MACf,IAAI,CAACA,QAAQ,CAACsC,IAAI,CAAC;IACvB;IACA;EACJ;EACA/F,cAAcA,CAAC+F,IAAI,EAAE;IACjB,IAAI,OAAO,IAAI,CAAC0O,UAAU,KAAK,UAAU,EAAE;MACvC,IAAI,CAACA,UAAU,CAAC1O,IAAI,CAAC;IACzB,CAAC,MACI,IAAIA,IAAI,CAACrJ,GAAG,EAAE;MACfgX,MAAM,CAACgB,IAAI,CAAC3O,IAAI,CAACrJ,GAAG,CAAC;IACzB;EACJ;EACA;EACAyQ,WAAWA,CAACwH,GAAG,EAAEzC,GAAG,EAAE9E,MAAM,EAAE2E,QAAQ,EAAE;IACpC,IAAI,CAAC4C,GAAG,GAAGA,GAAG;IACd,IAAI,CAACzC,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC9E,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC2E,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAClI,IAAI,GAAG,EAAE;IACd,IAAI,CAACnL,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAACU,UAAU,GAAG,IAAI;IACtB,IAAI,CAACuE,GAAG,GAAG,KAAK;IAChB,IAAI,CAAC2J,QAAQ,GAAG,IAAI5U,OAAO,CAAC,CAAC;EACjC;EACAwb,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACE,OAAO,CAAC,CAAC;IACd,IAAI,CAACO,GAAG,CAACT,aAAa,CAAC,CAAC;EAC5B;EACAU,WAAWA,CAAA,EAAG;IACV,IAAI,CAACR,OAAO,CAAC,CAAC;IACd,IAAI,CAACZ,QAAQ,CAAC,CAAC;EACnB;EACA7F,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC;EACxB;EACA;IAAS,IAAI,CAACC,IAAI,YAAAgH,8BAAA9G,CAAA;MAAA,YAAAA,CAAA,IAAwFiD,qBAAqB,EA7M/BpZ,EAAE,CAAAoW,iBAAA,CA6M+CpW,EAAE,CAACkd,iBAAiB,GA7MrEld,EAAE,CAAAoW,iBAAA,CA6MgF1U,QAAQ,GA7M1F1B,EAAE,CAAAoW,iBAAA,CA6MqGpW,EAAE,CAACqW,MAAM,GA7MhHrW,EAAE,CAAAoW,iBAAA,CA6M2HzT,IAAI,CAACwa,QAAQ;IAAA,CAA4C;EAAE;EACxR;IAAS,IAAI,CAAC3G,IAAI,kBA9M8ExW,EAAE,CAAAyW,iBAAA;MAAAjI,IAAA,EA8MJ4K,qBAAqB;MAAA1C,SAAA;MAAAQ,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAgG,mCAAAxZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9MnB5D,EAAE,CAAAsE,WAAA,wBAAAT,GAAA,CAAAkI,GAAA,KA8MI,KAAY,CAAC,yBAAAlI,GAAA,CAAAsD,QAAA,KAAR,MAAO,CAAC,4BAAAtD,GAAA,CAAAsD,QAAA,KAAR,SAAO,CAAC,iCAAAtD,GAAA,CAAAsD,QAAA,KAAR,cAAO,CAAC;QAAA;MAAA;MAAAqQ,MAAA;QAAA1Q,MAAA;QAAAK,QAAA;QAAA0H,KAAA;QAAA9G,KAAA;QAAA6U,SAAA;QAAA/Q,QAAA;QAAAgR,UAAA;QAAA7S,WAAA;QAAA2S,cAAA;QAAAnV,UAAA;QAAAuE,GAAA;MAAA;MAAA0L,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA9MnB3X,EAAE,CAAAqd,oBAAA,EAAFrd,EAAE,CAAA4X,mBAAA;MAAAG,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoF,+BAAA1Z,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAgE,UAAA,IAAAyG,oCAAA,kBA8MgwB,CAAC;QAAA;QAAA,IAAA7G,EAAA;UA9MnwB5D,EAAE,CAAAyE,UAAA,YAAAZ,GAAA,CAAAoO,IA8M0sB,CAAC;QAAA;MAAA;MAAAsL,YAAA,GAA4+L5b,OAAO,EAAkHe,eAAe,EAA+BD,EAAE,CAAC+a,kBAAkB,EAAqc5b,QAAQ,EAA6EC,gBAAgB,EAAoJC,IAAI,EAA6FC,eAAe,EAA8DC,YAAY,EAAoFM,YAAY,EAA+BD,EAAE,CAACob,eAAe,EAAgKrb,cAAc,EAA+BD,EAAE,CAACub,iBAAiB,EAAgO9a,EAAE,CAAC+a,2BAA2B,EAAoM1b,OAAO,EAA0EO,gBAAgB,EAA+BD,EAAE,CAACqb,mBAAmB;MAAApF,aAAA;MAAAvF,IAAA;QAAA4K,SAAA,EAAoR,CACniSvc,OAAO,CAAC,WAAW,EAAE,CACjBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;UAAEiZ,MAAM,EAAE,GAAG;UAAED,KAAK,EAAE,GAAG;UAAE9W,OAAO,EAAE;QAAE,CAAC,CAAC,EAC9CjC,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;UAAEiZ,MAAM,EAAE,GAAG;UAAED,KAAK,EAAE,GAAG;UAAE9W,OAAO,EAAE;QAAE,CAAC,CAAC,CAAC,CAC/D,CAAC,EACFnC,UAAU,CAAC,QAAQ,EAAE,CAACE,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;UAAEiZ,MAAM,EAAE,GAAG;UAAED,KAAK,EAAE,GAAG;UAAE9W,OAAO,EAAE;QAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACvF,CAAC;MACL;MAAAoa,eAAA;IAAA,EAAiG;EAAE;AAC5G;AACA;EAAA,QAAArF,SAAA,oBAAAA,SAAA,KAxNoGzY,EAAE,CAAA0Y,iBAAA,CAwNXU,qBAAqB,EAAc,CAAC;IACnH5K,IAAI,EAAEvO,SAAS;IACfmT,IAAI,EAAE,CAAC;MAAEuF,QAAQ,EAAE,gBAAgB;MAAElB,QAAQ,EAAE,cAAc;MAAEsG,UAAU,EAAE,CAC/Dzc,OAAO,CAAC,WAAW,EAAE,CACjBC,UAAU,CAAC,QAAQ,EAAE,CACjBC,KAAK,CAAC;QAAEiZ,MAAM,EAAE,GAAG;QAAED,KAAK,EAAE,GAAG;QAAE9W,OAAO,EAAE;MAAE,CAAC,CAAC,EAC9CjC,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;QAAEiZ,MAAM,EAAE,GAAG;QAAED,KAAK,EAAE,GAAG;QAAE9W,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAC/D,CAAC,EACFnC,UAAU,CAAC,QAAQ,EAAE,CAACE,OAAO,CAAC,GAAG,EAAED,KAAK,CAAC;QAAEiZ,MAAM,EAAE,GAAG;QAAED,KAAK,EAAE,GAAG;QAAE9W,OAAO,EAAE;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CACvF,CAAC,CACL;MAAEkV,IAAI,EAAE;QACLC,KAAK,EAAE,iBAAiB;QACxB,6BAA6B,EAAG,eAAc;QAC9C,8BAA8B,EAAG,qBAAoB;QACrD,iCAAiC,EAAG,wBAAuB;QAC3D,sCAAsC,EAAG;MAC7C,CAAC;MAAEC,mBAAmB,EAAE,KAAK;MAAEN,aAAa,EAAEtY,iBAAiB,CAAC6Y,IAAI;MAAE+E,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAEC,OAAO,EAAE,CAC5Htc,OAAO,EACPe,eAAe,EACfd,QAAQ,EACRC,gBAAgB,EAChBC,IAAI,EACJC,eAAe,EACfC,YAAY,EACZM,YAAY,EACZF,cAAc,EACdH,OAAO,EACPO,gBAAgB,CACnB;MAAEkV,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE;IAAg+L,CAAC;EAC5gM,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1J,IAAI,EAAExO,EAAE,CAACkd;EAAkB,CAAC,EAAE;IAAE1O,IAAI,EAAE3D,SAAS;IAAEmO,UAAU,EAAE,CAAC;MAC/ExK,IAAI,EAAEjO,MAAM;MACZ6S,IAAI,EAAE,CAAC1R,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8M,IAAI,EAAExO,EAAE,CAACqW;EAAO,CAAC,EAAE;IAAE7H,IAAI,EAAE7L,IAAI,CAACwa;EAAS,CAAC,CAAC,EAAkB;IAAErW,MAAM,EAAE,CAAC;MAChF0H,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE8G,QAAQ,EAAE,CAAC;MACXqH,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEwO,KAAK,EAAE,CAAC;MACRL,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE0H,KAAK,EAAE,CAAC;MACRyG,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEuc,SAAS,EAAE,CAAC;MACZpO,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEwL,QAAQ,EAAE,CAAC;MACX2C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEwc,UAAU,EAAE,CAAC;MACbrO,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE2J,WAAW,EAAE,CAAC;MACdwE,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEsc,cAAc,EAAE,CAAC;MACjBnO,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEmH,UAAU,EAAE,CAAC;MACbgH,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE0L,GAAG,EAAE,CAAC;MACNyC,IAAI,EAAEnO;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM6d,iBAAiB,CAAC;EACpB,IAAI5S,gBAAgBA,CAACoE,KAAK,EAAE;IACxB,IAAI,CAACyO,eAAe,GAAG,OAAOzO,KAAK,KAAK,SAAS,GAAG5M,SAAS,CAAC4M,KAAK,CAAC,GAAGA,KAAK;EAChF;EACA,IAAIpE,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAAC6S,eAAe;EAC/B;EACAC,UAAUA,CAAA,EAAG;IACT,IAAI,OAAO,IAAI,CAAC9S,gBAAgB,KAAK,SAAS,IAAI,IAAI,CAACA,gBAAgB,EAAE;MACrE,IAAI,CAACA,gBAAgB,GAAG;QACpBjB,eAAe,EAAE,IAAI;QACrBrC,cAAc,EAAE,IAAI;QACpBuU,gBAAgB,EAAE;MACtB,CAAC;IACL;IACA;IACA,MAAM1K,OAAO,GAAG,IAAI,CAACwM,QAAQ,CAACpP,KAAK,CAAC,CAAC;IACrC,IAAI,IAAI,CAACqP,UAAU,IAAI,IAAI,CAACC,OAAO,GAAG,CAAC,IAAI1M,OAAO,CAAC2M,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxZ,IAAI,KAAK,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;MAC1F4M,OAAO,CAAC6M,IAAI,CAAC;QACTzZ,IAAI,EAAE,OAAO;QACbkN,EAAE,EAAGR,QAAQ,IAAKA,QAAQ,CAAC1C,KAAK,CAAC,CAAC,IAAI,CAACsP,OAAO;MAClD,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACI,MAAM,GAAG,CAAC,IAAI9M,OAAO,CAAC2M,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxZ,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MACrE4M,OAAO,CAAC6M,IAAI,CAAC;QACTzZ,IAAI,EAAE,MAAM;QACZkN,EAAE,EAAGR,QAAQ,IAAKA,QAAQ,CAACvQ,MAAM,CAACqd,CAAC,IAAIA,CAAC,CAACG,IAAI,GAAG,IAAI,IAAI,IAAI,CAACD,MAAM;MACvE,CAAC,CAAC;IACN;IACA,IAAI,IAAI,CAACE,UAAU,IAAI,IAAI,CAACA,UAAU,CAACxP,MAAM,GAAG,CAAC,IAAIwC,OAAO,CAAC2M,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxZ,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MACnG,MAAM6Z,KAAK,GAAG,IAAI,CAACD,UAAU,CAACpO,KAAK,CAAC,GAAG,CAAC;MACxCoB,OAAO,CAAC6M,IAAI,CAAC;QACTzZ,IAAI,EAAE,MAAM;QACZkN,EAAE,EAAGR,QAAQ,IAAKA,QAAQ,CAACvQ,MAAM,CAACqd,CAAC,IAAI,CAACK,KAAK,CAAC3N,OAAO,CAACsN,CAAC,CAACjQ,IAAI,CAAC;MACjE,CAAC,CAAC;IACN;IACA,IAAI,CAAC5B,WAAW,GAAG;MACfqB,QAAQ,EAAE,IAAI,CAAC8Q,UAAU;MACzB3P,MAAM,EAAE,IAAI,CAAC4P,QAAQ;MACrBhM,MAAM,EAAE,IAAI,CAACiM,QAAQ;MACrBvQ,SAAS,EAAE,IAAI,CAACwQ,WAAW;MAC3BhR,qBAAqB,EAAE,IAAI,CAACiR,uBAAuB;MACnD7M,YAAY,EAAE,IAAI,CAAC8M,cAAc;MACjChL,aAAa,EAAE,IAAI,CAACiL,eAAe;MACnCpM,IAAI,EAAE,IAAI,CAACqM,MAAM;MACjBpM,OAAO,EAAE,IAAI,CAACqM,SAAS;MACvBta,IAAI,EAAE,IAAI,CAACua,MAAM;MACjBjH,QAAQ,EAAE,IAAI,CAAC+F,UAAU;MACzBhL,eAAe,EAAE,IAAI,CAACmM,iBAAiB;MACvC5N,OAAO;MACPsB,aAAa,EAAE,IAAI,CAACuM,eAAe;MACnCpL,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBf,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BC,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBI,OAAO,EAAE,IAAI,CAACA;IAClB,CAAC;IACD,OAAO,IAAI;EACf;EACA;EACA2B,WAAWA,CAACC,MAAM,EAAEuG,QAAQ,EAAEgB,GAAG,EAAE4C,IAAI,EAAEC,cAAc,EAAE;IACrD,IAAI,CAACpK,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACuG,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACgB,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC4C,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAClK,QAAQ,GAAG,IAAI5U,OAAO,CAAC,CAAC;IAC7B,IAAI,CAACiL,GAAG,GAAG,KAAK;IAChB;IACA,IAAI,CAAC8T,MAAM,GAAG,QAAQ;IACtB,IAAI,CAACtB,OAAO,GAAG,CAAC;IAChB,IAAI,CAACI,MAAM,GAAG,CAAC;IACf,IAAI,CAACO,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,uBAAuB,GAAG,IAAI;IACnC,IAAI,CAACd,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAC7S,UAAU,GAAG,EAAE;IACpB,IAAI,CAACuT,UAAU,GAAG,KAAK;IACvB,IAAI,CAACxT,UAAU,GAAG,MAAM;IACxB,IAAI,CAAC+S,UAAU,GAAG,KAAK;IACvB,IAAI,CAACkB,MAAM,GAAG,MAAM;IACpB,IAAI,CAACrB,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACzR,YAAY,GAAG,IAAI;IACxB,IAAI,CAAC+S,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAAChU,YAAY,GAAG,IAAI;IACxB,IAAI,CAACU,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAAC2T,QAAQ,GAAG,IAAItf,YAAY,CAAC,CAAC;IAClC,IAAI,CAACuf,gBAAgB,GAAG,IAAIvf,YAAY,CAAC,CAAC;IAC1C,IAAI,CAAC8T,OAAO,GAAInG,IAAI,IAAK;MACrB,IAAI,CAAC,IAAI,CAAC3C,UAAU,EAAE;QAClB,IAAI,CAACA,UAAU,GAAG,EAAE;MACxB;MACA,MAAMwU,UAAU,GAAG,IAAI,CAACC,YAAY,CAAC9R,IAAI,CAAC;MAC1C6R,UAAU,CAAC1V,MAAM,GAAG,WAAW;MAC/B,IAAI,CAACkB,UAAU,GAAG,IAAI,CAACA,UAAU,CAAC0U,MAAM,CAACF,UAAU,CAAC;MACpD,IAAI,CAACD,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC3U,UAAU,CAAC;MAC3C,IAAI,CAACsU,QAAQ,CAACK,IAAI,CAAC;QAAEhS,IAAI,EAAE6R,UAAU;QAAErO,QAAQ,EAAE,IAAI,CAACnG,UAAU;QAAEgD,IAAI,EAAE;MAAQ,CAAC,CAAC;MAClF,IAAI,CAAC4R,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC7M,UAAU,GAAG,CAAChF,CAAC,EAAEJ,IAAI,KAAK;MAC3B,MAAMwD,QAAQ,GAAG,IAAI,CAACnG,UAAU;MAChC,MAAMwU,UAAU,GAAG,IAAI,CAACK,WAAW,CAAClS,IAAI,EAAEwD,QAAQ,CAAC;MACnDqO,UAAU,CAACxV,OAAO,GAAG+D,CAAC,CAAC/D,OAAO;MAC9B,IAAI,CAACsV,QAAQ,CAACK,IAAI,CAAC;QACfpL,KAAK,EAAExG,CAAC;QACRJ,IAAI,EAAE;UAAE,GAAG6R;QAAW,CAAC;QACvBrO,QAAQ,EAAE,IAAI,CAACnG,UAAU;QACzBgD,IAAI,EAAE;MACV,CAAC,CAAC;MACF,IAAI,CAAC4R,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAAC5M,SAAS,GAAG,CAACM,GAAG,EAAE3F,IAAI,KAAK;MAC5B,MAAMwD,QAAQ,GAAG,IAAI,CAACnG,UAAU;MAChC,MAAMwU,UAAU,GAAG,IAAI,CAACK,WAAW,CAAClS,IAAI,EAAEwD,QAAQ,CAAC;MACnDqO,UAAU,CAAC1V,MAAM,GAAG,MAAM;MAC1B0V,UAAU,CAACzG,QAAQ,GAAGzF,GAAG;MACzB,IAAI,CAACgM,QAAQ,CAACK,IAAI,CAAC;QACfhS,IAAI,EAAE;UAAE,GAAG6R;QAAW,CAAC;QACvBrO,QAAQ;QACRnD,IAAI,EAAE;MACV,CAAC,CAAC;MACF,IAAI,CAAC4R,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAACxM,OAAO,GAAG,CAACwB,GAAG,EAAEjH,IAAI,KAAK;MAC1B,MAAMwD,QAAQ,GAAG,IAAI,CAACnG,UAAU;MAChC,MAAMwU,UAAU,GAAG,IAAI,CAACK,WAAW,CAAClS,IAAI,EAAEwD,QAAQ,CAAC;MACnDqO,UAAU,CAACxG,KAAK,GAAGpE,GAAG;MACtB4K,UAAU,CAAC1V,MAAM,GAAG,OAAO;MAC3B,IAAI,CAACwV,QAAQ,CAACK,IAAI,CAAC;QACfhS,IAAI,EAAE;UAAE,GAAG6R;QAAW,CAAC;QACvBrO,QAAQ;QACRnD,IAAI,EAAE;MACV,CAAC,CAAC;MACF,IAAI,CAAC4R,iBAAiB,CAAC,CAAC;IAC5B,CAAC;IACD,IAAI,CAACvU,QAAQ,GAAIsC,IAAI,IAAK;MACtB,IAAI,CAACmS,UAAU,CAACjL,KAAK,CAAClH,IAAI,CAAC;MAC3BA,IAAI,CAAC7D,MAAM,GAAG,SAAS;MACvB,MAAM4H,KAAK,GAAG,OAAO,IAAI,CAACqO,QAAQ,KAAK,UAAU,GAAG,IAAI,CAACA,QAAQ,CAACpS,IAAI,CAAC,GAAG,IAAI,CAACoS,QAAQ,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAACA,QAAQ;MACtH,CAACrO,KAAK,YAAYtR,UAAU,GAAGsR,KAAK,GAAGvR,EAAE,CAACuR,KAAK,CAAC,EAAEF,IAAI,CAAC5Q,MAAM,CAAE0S,GAAG,IAAKA,GAAG,CAAC,CAAC,CAAC1B,SAAS,CAAC,MAAM;QACzF,IAAI,CAAC5G,UAAU,GAAG,IAAI,CAACgV,cAAc,CAACrS,IAAI,EAAE,IAAI,CAAC3C,UAAU,CAAC;QAC5D,IAAI,CAACsU,QAAQ,CAACK,IAAI,CAAC;UACfhS,IAAI;UACJwD,QAAQ,EAAE,IAAI,CAACnG,UAAU;UACzBgD,IAAI,EAAE;QACV,CAAC,CAAC;QACF,IAAI,CAACuR,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC3U,UAAU,CAAC;QAC3C,IAAI,CAACuR,GAAG,CAACT,aAAa,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN,CAAC;IACD;IACA;IACA,IAAI,CAACmE,SAAS,GAAG,YAAY;IAC7B,IAAI,CAAC9T,SAAS,GAAG,EAAE;EACvB;EACA;EACAsT,YAAYA,CAAC9R,IAAI,EAAE;IACf,OAAO;MACHuS,YAAY,EAAEvS,IAAI,CAACuS,YAAY;MAC/BC,gBAAgB,EAAExS,IAAI,CAACwS,gBAAgB;MACvC1b,IAAI,EAAEkJ,IAAI,CAAC0L,QAAQ,IAAI1L,IAAI,CAAClJ,IAAI;MAChC2Z,IAAI,EAAEzQ,IAAI,CAACyQ,IAAI;MACfpQ,IAAI,EAAEL,IAAI,CAACK,IAAI;MACf8C,GAAG,EAAEnD,IAAI,CAACmD,GAAG;MACbiI,QAAQ,EAAEpL,IAAI,CAACoL,QAAQ;MACvBC,KAAK,EAAErL,IAAI,CAACqL,KAAK;MACjBhP,OAAO,EAAE,CAAC;MACV0R,aAAa,EAAE/N;IACnB,CAAC;EACL;EACAkS,WAAWA,CAAClS,IAAI,EAAEwD,QAAQ,EAAE;IACxB,OAAOA,QAAQ,CAACvQ,MAAM,CAACwO,IAAI,IAAIA,IAAI,CAAC0B,GAAG,KAAKnD,IAAI,CAACmD,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5D;EACAkP,cAAcA,CAACrS,IAAI,EAAEwD,QAAQ,EAAE;IAC3B,OAAOA,QAAQ,CAACvQ,MAAM,CAACwO,IAAI,IAAIA,IAAI,CAAC0B,GAAG,KAAKnD,IAAI,CAACmD,GAAG,CAAC;EACzD;EACA;EACArE,QAAQA,CAACsB,CAAC,EAAE;IACR,IAAIA,CAAC,CAACC,IAAI,KAAK,IAAI,CAACoS,SAAS,EAAE;MAC3B;IACJ;IACA,IAAI,CAACA,SAAS,GAAGrS,CAAC,CAACC,IAAI;IACvB,IAAI,CAACqS,WAAW,CAAC,CAAC;EACtB;EACA;EACA;EACAT,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACrD,GAAG,CAACT,aAAa,CAAC,CAAC;IACxB,IAAI,CAACwE,QAAQ,EAAExE,aAAa,CAAC,CAAC;EAClC;EACAuE,WAAWA,CAAA,EAAG;IACV,IAAIE,MAAM,GAAG,EAAE;IACf,IAAI,IAAI,CAAClB,MAAM,KAAK,MAAM,EAAE;MACxB,IAAI,IAAI,CAACrU,UAAU,CAACsF,IAAI,CAAC3C,IAAI,IAAIA,IAAI,CAAC7D,MAAM,KAAK,WAAW,CAAC,EAAE;QAC3DyW,MAAM,CAACrC,IAAI,CAAE,GAAE,IAAI,CAAC+B,SAAU,iBAAgB,CAAC;MACnD;MACA,IAAI,IAAI,CAACG,SAAS,KAAK,UAAU,EAAE;QAC/BG,MAAM,CAACrC,IAAI,CAAE,GAAE,IAAI,CAAC+B,SAAU,aAAY,CAAC;MAC/C;IACJ,CAAC,MACI;MACDM,MAAM,GAAG,CAAE,GAAE,IAAI,CAACN,SAAU,WAAU,IAAI,CAAClV,UAAW,EAAC,CAAC;IAC5D;IACA,IAAI,CAACoB,SAAS,GAAG,CACb,IAAI,CAAC8T,SAAS,EACb,GAAE,IAAI,CAACA,SAAU,IAAG,IAAI,CAACZ,MAAO,EAAC,EAClC,GAAGkB,MAAM,EACR,IAAI,CAAChC,UAAU,IAAK,GAAE,IAAI,CAAC0B,SAAU,WAAU,IAAK,EAAE,EACtD,IAAI,CAAC1U,GAAG,KAAK,KAAK,IAAK,GAAE,IAAI,CAAC0U,SAAU,MAAK,IAAK,EAAE,CACxD,CAACrf,MAAM,CAACwO,IAAI,IAAI,CAAC,CAACA,IAAI,CAAC;IACxB,IAAI,CAACmN,GAAG,CAACT,aAAa,CAAC,CAAC;EAC5B;EACA;EACA1G,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC7J,GAAG,GAAG,IAAI,CAAC6T,cAAc,CAAClQ,KAAK;IACpC,IAAI,CAACkQ,cAAc,CAACoB,MAAM,EAAEhP,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC,CAACtD,SAAS,CAAE6O,SAAS,IAAK;MAChF,IAAI,CAAClV,GAAG,GAAGkV,SAAS;MACpB,IAAI,CAACJ,WAAW,CAAC,CAAC;MAClB,IAAI,CAAC9D,GAAG,CAACT,aAAa,CAAC,CAAC;IAC5B,CAAC,CAAC;IACF,IAAI,CAACqD,IAAI,CAACuB,YAAY,CAAClP,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC,CAACtD,SAAS,CAAC,MAAM;MAClE,IAAI,CAACtL,MAAM,GAAG,IAAI,CAAC6Y,IAAI,CAACwB,aAAa,CAAC,QAAQ,CAAC;MAC/C,IAAI,CAACf,iBAAiB,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACAgB,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAAC5L,MAAM,CAACK,iBAAiB,CAAC,MAAM9U,SAAS,CAAC,IAAI,CAACgb,QAAQ,CAAC5G,IAAI,EAAE,MAAM,CAAC,CACpEnD,IAAI,CAAC7Q,SAAS,CAAC,IAAI,CAACuU,QAAQ,CAAC,CAAC,CAC9BtD,SAAS,CAAC2C,KAAK,IAAI;MACpBA,KAAK,CAACtG,cAAc,CAAC,CAAC;MACtBsG,KAAK,CAACsM,eAAe,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC;EACP;EACArE,WAAWA,CAAA,EAAG;IACV,IAAI,CAACoB,UAAU,CAAC,CAAC,CAACyC,WAAW,CAAC,CAAC;EACnC;EACA9K,WAAWA,CAAA,EAAG;IACV,IAAI,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC;IACpB,IAAI,CAACN,QAAQ,CAAC4L,QAAQ,CAAC,CAAC;EAC5B;EACA;IAAS,IAAI,CAACrL,IAAI,YAAAsL,0BAAApL,CAAA;MAAA,YAAAA,CAAA,IAAwF+H,iBAAiB,EA/f3Ble,EAAE,CAAAoW,iBAAA,CA+f2CpW,EAAE,CAACqW,MAAM,GA/ftDrW,EAAE,CAAAoW,iBAAA,CA+fiE1U,QAAQ,GA/f3E1B,EAAE,CAAAoW,iBAAA,CA+fsFpW,EAAE,CAACkd,iBAAiB,GA/f5Gld,EAAE,CAAAoW,iBAAA,CA+fuHnT,IAAI,CAACue,aAAa,GA/f3IxhB,EAAE,CAAAoW,iBAAA,CA+fsJlT,IAAI,CAACue,cAAc;IAAA,CAA4D;EAAE;EACzU;IAAS,IAAI,CAACjL,IAAI,kBAhgB8ExW,EAAE,CAAAyW,iBAAA;MAAAjI,IAAA,EAggBJ0P,iBAAiB;MAAAxH,SAAA;MAAAC,SAAA,WAAA+K,wBAAA9d,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhgBf5D,EAAE,CAAA6W,WAAA,CAAA7L,GAAA;UAAFhL,EAAE,CAAA6W,WAAA,CAAA5L,GAAA;QAAA;QAAA,IAAArH,EAAA;UAAA,IAAAkT,EAAA;UAAF9W,EAAE,CAAA+W,cAAA,CAAAD,EAAA,GAAF9W,EAAE,CAAAgX,WAAA,QAAAnT,GAAA,CAAAyc,UAAA,GAAAxJ,EAAA,CAAAG,KAAA;UAAFjX,EAAE,CAAA+W,cAAA,CAAAD,EAAA,GAAF9W,EAAE,CAAAgX,WAAA,QAAAnT,GAAA,CAAAid,QAAA,GAAAhK,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAE,QAAA;MAAAC,YAAA,WAAAuK,+BAAA/d,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAsE,WAAA,oCAAAT,GAAA,CAAA0H,UAAA,KAggBW,cAAC,CAAC;QAAA;MAAA;MAAAiM,MAAA;QAAAqI,MAAA;QAAAtB,OAAA;QAAAI,MAAA;QAAAE,UAAA;QAAAG,QAAA;QAAAC,QAAA;QAAAC,WAAA;QAAAC,uBAAA;QAAAC,cAAA;QAAAC,eAAA;QAAAC,MAAA;QAAAjB,QAAA;QAAA7S,UAAA;QAAAuT,UAAA;QAAAQ,SAAA;QAAAhU,UAAA;QAAA+S,UAAA;QAAAkB,MAAA;QAAAlU,gBAAA;QAAAoB,YAAA;QAAA+S,iBAAA;QAAAc,QAAA;QAAA3U,SAAA;QAAAF,aAAA;QAAAC,gBAAA;QAAA+T,eAAA;QAAA5T,UAAA;QAAAL,YAAA;QAAAU,gBAAA;MAAA;MAAAyV,OAAA;QAAA9B,QAAA;QAAAC,gBAAA;MAAA;MAAAtI,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAhgBf3X,EAAE,CAAAqd,oBAAA,EAAFrd,EAAE,CAAA4X,mBAAA;MAAAE,kBAAA,EAAAzU,GAAA;MAAA0U,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2J,2BAAAje,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5D,EAAE,CAAAqY,eAAA;UAAFrY,EAAE,CAAAgE,UAAA,IAAAoI,wCAAA,gCAAFpM,EAAE,CAAAqG,sBAggB8zC,CAAC,IAAAgG,wCAAA,gCAhgBj0CrM,EAAE,CAAAqG,sBAggBghE,CAAC,IAAAmG,wCAAA,gCAhgBnhExM,EAAE,CAAAqG,sBAggB2kE,CAAC,IAAA0G,yCAAA,yBAA4S,CAAC,IAAAU,wCAAA,gCAhgB33EzN,EAAE,CAAAqG,sBAggBu0F,CAAC,IAAAwH,wCAAA,gCAhgB10F7N,EAAE,CAAAqG,sBAggB2jG,CAAC;QAAA;QAAA,IAAAzC,EAAA;UAAA,MAAAke,SAAA,GAhgB9jG9hB,EAAE,CAAAqE,WAAA;UAAFrE,EAAE,CAAAwE,SAAA,EAggB02E,CAAC;UAhgB72ExE,EAAE,CAAAyE,UAAA,SAAAZ,GAAA,CAAAgc,MAAA,WAggB02E,CAAC,aAAAiC,SAAU,CAAC;QAAA;MAAA;MAAAvE,YAAA,GAA03BnE,qBAAqB,EAAsOtX,IAAI,EAA6FD,gBAAgB,EAAoJK,OAAO,EAAoF4L,oBAAoB;MAAA0K,aAAA;MAAAsF,eAAA;IAAA,EAAiL;EAAE;AACxnI;AACAjb,UAAU,CAAC,CACPE,WAAW,CAAC,CAAC,CAChB,EAAEmb,iBAAiB,CAAClP,SAAS,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;AAClDnM,UAAU,CAAC,CACPE,WAAW,CAAC,CAAC,CAChB,EAAEmb,iBAAiB,CAAClP,SAAS,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;AACjDnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC;AACtDnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,yBAAyB,EAAE,KAAK,CAAC,CAAC;AAClEnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACrDnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;AACrDnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,cAAc,EAAE,KAAK,CAAC,CAAC;AACvDnM,UAAU,CAAC,CACPG,YAAY,CAAC,CAAC,CACjB,EAAEkb,iBAAiB,CAAClP,SAAS,EAAE,mBAAmB,EAAE,KAAK,CAAC,CAAC;AAC5D;EAAA,QAAAyJ,SAAA,oBAAAA,SAAA,KA1hBoGzY,EAAE,CAAA0Y,iBAAA,CA0hBXwF,iBAAiB,EAAc,CAAC;IAC/G1P,IAAI,EAAEvO,SAAS;IACfmT,IAAI,EAAE,CAAC;MAAEuF,QAAQ,EAAE,WAAW;MAAElB,QAAQ,EAAE,UAAU;MAAEqB,mBAAmB,EAAE,KAAK;MAAEN,aAAa,EAAEtY,iBAAiB,CAAC6Y,IAAI;MAAE+E,eAAe,EAAExd,uBAAuB,CAAC0d,MAAM;MAAEpF,IAAI,EAAE;QACpK,yCAAyC,EAAE;MAC/C,CAAC;MAAEqF,OAAO,EAAE,CAAC7E,qBAAqB,EAAEtX,IAAI,EAAED,gBAAgB,EAAEK,OAAO,EAAE4L,oBAAoB,CAAC;MAAE4J,UAAU,EAAE,IAAI;MAAEQ,QAAQ,EAAE;IAA05D,CAAC;EAC/hE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE1J,IAAI,EAAExO,EAAE,CAACqW;EAAO,CAAC,EAAE;IAAE7H,IAAI,EAAE3D,SAAS;IAAEmO,UAAU,EAAE,CAAC;MACpExK,IAAI,EAAEjO,MAAM;MACZ6S,IAAI,EAAE,CAAC1R,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAE8M,IAAI,EAAExO,EAAE,CAACkd;EAAkB,CAAC,EAAE;IAAE1O,IAAI,EAAEvL,IAAI,CAACue;EAAc,CAAC,EAAE;IAAEhT,IAAI,EAAEtL,IAAI,CAACue,cAAc;IAAEzI,UAAU,EAAE,CAAC;MAC1GxK,IAAI,EAAErO;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEmgB,UAAU,EAAE,CAAC;MACtC9R,IAAI,EAAEpO,SAAS;MACfgT,IAAI,EAAE,CAAC,YAAY,EAAE;QAAE6F,MAAM,EAAE;MAAM,CAAC;IAC1C,CAAC,CAAC;IAAE6H,QAAQ,EAAE,CAAC;MACXtS,IAAI,EAAEpO,SAAS;MACfgT,IAAI,EAAE,CAAC,UAAU,EAAE;QAAE6F,MAAM,EAAE;MAAM,CAAC;IACxC,CAAC,CAAC;IAAE4G,MAAM,EAAE,CAAC;MACTrR,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEke,OAAO,EAAE,CAAC;MACV/P,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEse,MAAM,EAAE,CAAC;MACTnQ,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEwe,UAAU,EAAE,CAAC;MACbrQ,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE2e,QAAQ,EAAE,CAAC;MACXxQ,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE4e,QAAQ,EAAE,CAAC;MACXzQ,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE6e,WAAW,EAAE,CAAC;MACd1Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE8e,uBAAuB,EAAE,CAAC;MAC1B3Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE+e,cAAc,EAAE,CAAC;MACjB5Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEgf,eAAe,EAAE,CAAC;MAClB7Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEif,MAAM,EAAE,CAAC;MACT9Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEge,QAAQ,EAAE,CAAC;MACX7P,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEmL,UAAU,EAAE,CAAC;MACbgD,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE0e,UAAU,EAAE,CAAC;MACbvQ,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEkf,SAAS,EAAE,CAAC;MACZ/Q,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEkL,UAAU,EAAE,CAAC;MACbiD,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEie,UAAU,EAAE,CAAC;MACb9P,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEmf,MAAM,EAAE,CAAC;MACThR,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEiL,gBAAgB,EAAE,CAAC;MACnBkD,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEqM,YAAY,EAAE,CAAC;MACf8B,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEof,iBAAiB,EAAE,CAAC;MACpBjR,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEkgB,QAAQ,EAAE,CAAC;MACX/R,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEuL,SAAS,EAAE,CAAC;MACZ4C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEqL,aAAa,EAAE,CAAC;MAChB8C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEsL,gBAAgB,EAAE,CAAC;MACnB6C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEqf,eAAe,EAAE,CAAC;MAClBlR,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEyL,UAAU,EAAE,CAAC;MACb0C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEoL,YAAY,EAAE,CAAC;MACf+C,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAE8L,gBAAgB,EAAE,CAAC;MACnBqC,IAAI,EAAEnO;IACV,CAAC,CAAC;IAAEyf,QAAQ,EAAE,CAAC;MACXtR,IAAI,EAAE/N;IACV,CAAC,CAAC;IAAEsf,gBAAgB,EAAE,CAAC;MACnBvR,IAAI,EAAE/N;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMshB,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC9L,IAAI,YAAA+L,uBAAA7L,CAAA;MAAA,YAAAA,CAAA,IAAwF4L,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACE,IAAI,kBAhnB8EjiB,EAAE,CAAAkiB,gBAAA;MAAA1T,IAAA,EAgnBSuT,cAAc;MAAA9D,OAAA,GAAYC,iBAAiB,EAAEpQ,oBAAoB,EAAEsL,qBAAqB;MAAA+I,OAAA,GAAajE,iBAAiB;IAAA,EAAI;EAAE;EACvO;IAAS,IAAI,CAACkE,IAAI,kBAjnB8EpiB,EAAE,CAAAqiB,gBAAA;MAAApE,OAAA,GAinBmCC,iBAAiB,EAAE9E,qBAAqB;IAAA,EAAI;EAAE;AACvL;AACA;EAAA,QAAAX,SAAA,oBAAAA,SAAA,KAnnBoGzY,EAAE,CAAA0Y,iBAAA,CAmnBXqJ,cAAc,EAAc,CAAC;IAC5GvT,IAAI,EAAE9N,QAAQ;IACd0S,IAAI,EAAE,CAAC;MACC6K,OAAO,EAAE,CAACC,iBAAiB,EAAEpQ,oBAAoB,EAAEsL,qBAAqB,CAAC;MACzE+I,OAAO,EAAE,CAACjE,iBAAiB;IAC/B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,SAASpQ,oBAAoB,EAAEoQ,iBAAiB,EAAE9E,qBAAqB,EAAE2I,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}