{"ast": null, "code": "/**\n * Creates an array with all falsey values removed. The values `false`, `null`,\n * `0`, `\"\"`, `undefined`, and `NaN` are falsey.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to compact.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.compact([0, 1, false, 2, '', 3]);\n * // => [1, 2, 3]\n */\nfunction compact(array) {\n  var index = -1,\n    length = array == null ? 0 : array.length,\n    resIndex = 0,\n    result = [];\n  while (++index < length) {\n    var value = array[index];\n    if (value) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\nexport default compact;", "map": {"version": 3, "names": ["compact", "array", "index", "length", "resIndex", "result", "value"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/compact.js"], "sourcesContent": ["/**\n * Creates an array with all falsey values removed. The values `false`, `null`,\n * `0`, `\"\"`, `undefined`, and `NaN` are falsey.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to compact.\n * @returns {Array} Returns the new array of filtered values.\n * @example\n *\n * _.compact([0, 1, false, 2, '', 3]);\n * // => [1, 2, 3]\n */\nfunction compact(array) {\n  var index = -1,\n      length = array == null ? 0 : array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index];\n    if (value) {\n      result[resIndex++] = value;\n    }\n  }\n  return result;\n}\n\nexport default compact;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,KAAK,EAAE;EACtB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;IACzCC,QAAQ,GAAG,CAAC;IACZC,MAAM,GAAG,EAAE;EAEf,OAAO,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGL,KAAK,CAACC,KAAK,CAAC;IACxB,IAAII,KAAK,EAAE;MACTD,MAAM,CAACD,QAAQ,EAAE,CAAC,GAAGE,KAAK;IAC5B;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}