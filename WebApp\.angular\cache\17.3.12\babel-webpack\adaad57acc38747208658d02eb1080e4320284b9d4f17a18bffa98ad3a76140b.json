{"ast": null, "code": "// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\n// it's a very new API right now.\n// Not exported from index.\n/** @private */\nexport class AbortController {\n  constructor() {\n    this._isAborted = false;\n    this.onabort = null;\n  }\n  abort() {\n    if (!this._isAborted) {\n      this._isAborted = true;\n      if (this.onabort) {\n        this.onabort();\n      }\n    }\n  }\n  get signal() {\n    return this;\n  }\n  get aborted() {\n    return this._isAborted;\n  }\n}", "map": {"version": 3, "names": ["AbortController", "constructor", "_isAborted", "<PERSON>ab<PERSON>", "abort", "signal", "aborted"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/AbortController.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController {\r\n    constructor() {\r\n        this._isAborted = false;\r\n        this.onabort = null;\r\n    }\r\n    abort() {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n    get signal() {\r\n        return this;\r\n    }\r\n    get aborted() {\r\n        return this._isAborted;\r\n    }\r\n}\r\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,eAAe,CAAC;EACzBC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACF,UAAU,EAAE;MAClB,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,IAAI,CAACC,OAAO,EAAE;QACd,IAAI,CAACA,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA,IAAIE,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI;EACf;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACJ,UAAU;EAC1B;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}