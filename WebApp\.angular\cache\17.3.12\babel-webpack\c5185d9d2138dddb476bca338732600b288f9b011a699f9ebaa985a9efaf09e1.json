{"ast": null, "code": "import baseIsArrayBuffer from './_baseIsArrayBuffer.js';\nimport baseUnary from './_baseUnary.js';\nimport nodeUtil from './_nodeUtil.js';\n\n/* Node.js helper references. */\nvar nodeIsArrayBuffer = nodeUtil && nodeUtil.isArrayBuffer;\n\n/**\n * Checks if `value` is classified as an `ArrayBuffer` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array buffer, else `false`.\n * @example\n *\n * _.isArrayBuffer(new ArrayBuffer(2));\n * // => true\n *\n * _.isArrayBuffer(new Array(2));\n * // => false\n */\nvar isArrayBuffer = nodeIsArrayBuffer ? baseUnary(nodeIsArrayBuffer) : baseIsArrayBuffer;\nexport default isArrayBuffer;", "map": {"version": 3, "names": ["baseIsArrayBuffer", "baseUnary", "nodeUtil", "nodeIsArrayBuffer", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/isArrayBuffer.js"], "sourcesContent": ["import baseIsArrayBuffer from './_baseIsArrayBuffer.js';\nimport baseUnary from './_baseUnary.js';\nimport nodeUtil from './_nodeUtil.js';\n\n/* Node.js helper references. */\nvar nodeIsArrayBuffer = nodeUtil && nodeUtil.isArrayBuffer;\n\n/**\n * Checks if `value` is classified as an `ArrayBuffer` object.\n *\n * @static\n * @memberOf _\n * @since 4.3.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an array buffer, else `false`.\n * @example\n *\n * _.isArrayBuffer(new ArrayBuffer(2));\n * // => true\n *\n * _.isArrayBuffer(new Array(2));\n * // => false\n */\nvar isArrayBuffer = nodeIsArrayBuffer ? baseUnary(nodeIsArrayBuffer) : baseIsArrayBuffer;\n\nexport default isArrayBuffer;\n"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;;AAErC;AACA,IAAIC,iBAAiB,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,aAAa;;AAE1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIA,aAAa,GAAGD,iBAAiB,GAAGF,SAAS,CAACE,iBAAiB,CAAC,GAAGH,iBAAiB;AAExF,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}