{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// Licensed to the .NET Foundation under one or more agreements.\n// The .NET Foundation licenses this file to you under the MIT license.\nimport { AbortController } from \"./AbortController\";\nimport { HttpError, TimeoutError } from \"./Errors\";\nimport { LogLevel } from \"./ILogger\";\nimport { TransferFormat } from \"./ITransport\";\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\n// Not exported from 'index', this type is internal.\n/** @private */\nexport class LongPollingTransport {\n  // This is an internal type, not exported from 'index' so this is really just internal.\n  get pollAborted() {\n    return this._pollAbort.aborted;\n  }\n  constructor(httpClient, logger, options) {\n    this._httpClient = httpClient;\n    this._logger = logger;\n    this._pollAbort = new AbortController();\n    this._options = options;\n    this._running = false;\n    this.onreceive = null;\n    this.onclose = null;\n  }\n  connect(url, transferFormat) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      Arg.isRequired(url, \"url\");\n      Arg.isRequired(transferFormat, \"transferFormat\");\n      Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\n      _this._url = url;\n      _this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\n      // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\n      if (transferFormat === TransferFormat.Binary && typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\") {\n        throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\n      }\n      const [name, value] = getUserAgentHeader();\n      const headers = {\n        [name]: value,\n        ..._this._options.headers\n      };\n      const pollOptions = {\n        abortSignal: _this._pollAbort.signal,\n        headers,\n        timeout: 100000,\n        withCredentials: _this._options.withCredentials\n      };\n      if (transferFormat === TransferFormat.Binary) {\n        pollOptions.responseType = \"arraybuffer\";\n      }\n      // Make initial long polling request\n      // Server uses first long polling request to finish initializing connection and it returns without data\n      const pollUrl = `${url}&_=${Date.now()}`;\n      _this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n      const response = yield _this._httpClient.get(pollUrl, pollOptions);\n      if (response.statusCode !== 200) {\n        _this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\n        // Mark running as false so that the poll immediately ends and runs the close logic\n        _this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n        _this._running = false;\n      } else {\n        _this._running = true;\n      }\n      _this._receiving = _this._poll(_this._url, pollOptions);\n    })();\n  }\n  _poll(url, pollOptions) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        while (_this2._running) {\n          try {\n            const pollUrl = `${url}&_=${Date.now()}`;\n            _this2._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\n            const response = yield _this2._httpClient.get(pollUrl, pollOptions);\n            if (response.statusCode === 204) {\n              _this2._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\n              _this2._running = false;\n            } else if (response.statusCode !== 200) {\n              _this2._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\n              // Unexpected status code\n              _this2._closeError = new HttpError(response.statusText || \"\", response.statusCode);\n              _this2._running = false;\n            } else {\n              // Process the response\n              if (response.content) {\n                _this2._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, _this2._options.logMessageContent)}.`);\n                if (_this2.onreceive) {\n                  _this2.onreceive(response.content);\n                }\n              } else {\n                // This is another way timeout manifest.\n                _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n              }\n            }\n          } catch (e) {\n            if (!_this2._running) {\n              // Log but disregard errors that occur after stopping\n              _this2._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\n            } else {\n              if (e instanceof TimeoutError) {\n                // Ignore timeouts and reissue the poll.\n                _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\n              } else {\n                // Close the connection with the error as the result.\n                _this2._closeError = e;\n                _this2._running = false;\n              }\n            }\n          }\n        }\n      } finally {\n        _this2._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\n        // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\n        // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\n        if (!_this2.pollAborted) {\n          _this2._raiseOnClose();\n        }\n      }\n    })();\n  }\n  send(data) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3._running) {\n        return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\n      }\n      return sendMessage(_this3._logger, \"LongPolling\", _this3._httpClient, _this3._url, data, _this3._options);\n    })();\n  }\n  stop() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\n      // Tell receiving loop to stop, abort any current request, and then wait for it to finish\n      _this4._running = false;\n      _this4._pollAbort.abort();\n      try {\n        yield _this4._receiving;\n        // Send DELETE to clean up long polling on the server\n        _this4._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${_this4._url}.`);\n        const headers = {};\n        const [name, value] = getUserAgentHeader();\n        headers[name] = value;\n        const deleteOptions = {\n          headers: {\n            ...headers,\n            ..._this4._options.headers\n          },\n          timeout: _this4._options.timeout,\n          withCredentials: _this4._options.withCredentials\n        };\n        let error;\n        try {\n          yield _this4._httpClient.delete(_this4._url, deleteOptions);\n        } catch (err) {\n          error = err;\n        }\n        if (error) {\n          if (error instanceof HttpError) {\n            if (error.statusCode === 404) {\n              _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\n            } else {\n              _this4._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\n            }\n          }\n        } else {\n          _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\n        }\n      } finally {\n        _this4._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\n        // Raise close event here instead of in polling\n        // It needs to happen after the DELETE request is sent\n        _this4._raiseOnClose();\n      }\n    })();\n  }\n  _raiseOnClose() {\n    if (this.onclose) {\n      let logMessage = \"(LongPolling transport) Firing onclose event.\";\n      if (this._closeError) {\n        logMessage += \" Error: \" + this._closeError;\n      }\n      this._logger.log(LogLevel.Trace, logMessage);\n      this.onclose(this._closeError);\n    }\n  }\n}", "map": {"version": 3, "names": ["AbortController", "HttpError", "TimeoutError", "LogLevel", "TransferFormat", "Arg", "getDataDetail", "getUserAgentHeader", "sendMessage", "LongPollingTransport", "pollAborted", "_pollAbort", "aborted", "constructor", "httpClient", "logger", "options", "_httpClient", "_logger", "_options", "_running", "onreceive", "onclose", "connect", "url", "transferFormat", "_this", "_asyncToGenerator", "isRequired", "isIn", "_url", "log", "Trace", "Binary", "XMLHttpRequest", "responseType", "Error", "name", "value", "headers", "pollOptions", "abortSignal", "signal", "timeout", "withCredentials", "pollUrl", "Date", "now", "response", "get", "statusCode", "_closeError", "statusText", "_receiving", "_poll", "_this2", "Information", "content", "logMessageContent", "e", "message", "_raiseOnClose", "send", "data", "_this3", "Promise", "reject", "stop", "_this4", "abort", "deleteOptions", "error", "delete", "err", "logMessage"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/@microsoft/signalr/dist/esm/LongPollingTransport.js"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport {\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    get pollAborted() {\r\n        return this._pollAbort.aborted;\r\n    }\r\n    constructor(httpClient, logger, options) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n        this._running = false;\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n    async connect(url, transferFormat) {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._url = url;\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n        const pollOptions = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        }\r\n        else {\r\n            this._running = true;\r\n        }\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n    async _poll(url, pollOptions) {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n                        this._running = false;\r\n                    }\r\n                    else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    }\r\n                    else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        }\r\n                        else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    }\r\n                    else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                        else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n    async send(data) {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url, data, this._options);\r\n    }\r\n    async stop() {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n        try {\r\n            await this._receiving;\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n            const headers = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n            const deleteOptions = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            let error;\r\n            try {\r\n                await this._httpClient.delete(this._url, deleteOptions);\r\n            }\r\n            catch (err) {\r\n                error = err;\r\n            }\r\n            if (error) {\r\n                if (error instanceof HttpError) {\r\n                    if (error.statusCode === 404) {\r\n                        this._logger.log(LogLevel.Trace, \"(LongPolling transport) A 404 response was returned from sending a DELETE request.\");\r\n                    }\r\n                    else {\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Error sending a DELETE request: ${error}`);\r\n                    }\r\n                }\r\n            }\r\n            else {\r\n                this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request accepted.\");\r\n            }\r\n        }\r\n        finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n    _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n"], "mappings": ";AAAA;AACA;AACA,SAASA,eAAe,QAAQ,mBAAmB;AACnD,SAASC,SAAS,EAAEC,YAAY,QAAQ,UAAU;AAClD,SAASC,QAAQ,QAAQ,WAAW;AACpC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,GAAG,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,WAAW,QAAQ,SAAS;AAC7E;AACA;AACA,OAAO,MAAMC,oBAAoB,CAAC;EAC9B;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,UAAU,CAACC,OAAO;EAClC;EACAC,WAAWA,CAACC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAE;IACrC,IAAI,CAACC,WAAW,GAAGH,UAAU;IAC7B,IAAI,CAACI,OAAO,GAAGH,MAAM;IACrB,IAAI,CAACJ,UAAU,GAAG,IAAIX,eAAe,CAAC,CAAC;IACvC,IAAI,CAACmB,QAAQ,GAAGH,OAAO;IACvB,IAAI,CAACI,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,OAAO,GAAG,IAAI;EACvB;EACMC,OAAOA,CAACC,GAAG,EAAEC,cAAc,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAC/BtB,GAAG,CAACuB,UAAU,CAACJ,GAAG,EAAE,KAAK,CAAC;MAC1BnB,GAAG,CAACuB,UAAU,CAACH,cAAc,EAAE,gBAAgB,CAAC;MAChDpB,GAAG,CAACwB,IAAI,CAACJ,cAAc,EAAErB,cAAc,EAAE,gBAAgB,CAAC;MAC1DsB,KAAI,CAACI,IAAI,GAAGN,GAAG;MACfE,KAAI,CAACR,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,qCAAqC,CAAC;MACvE;MACA,IAAIP,cAAc,KAAKrB,cAAc,CAAC6B,MAAM,IACvC,OAAOC,cAAc,KAAK,WAAW,IAAI,OAAO,IAAIA,cAAc,CAAC,CAAC,CAACC,YAAY,KAAK,QAAS,EAAE;QAClG,MAAM,IAAIC,KAAK,CAAC,4FAA4F,CAAC;MACjH;MACA,MAAM,CAACC,IAAI,EAAEC,KAAK,CAAC,GAAG/B,kBAAkB,CAAC,CAAC;MAC1C,MAAMgC,OAAO,GAAG;QAAE,CAACF,IAAI,GAAGC,KAAK;QAAE,GAAGZ,KAAI,CAACP,QAAQ,CAACoB;MAAQ,CAAC;MAC3D,MAAMC,WAAW,GAAG;QAChBC,WAAW,EAAEf,KAAI,CAACf,UAAU,CAAC+B,MAAM;QACnCH,OAAO;QACPI,OAAO,EAAE,MAAM;QACfC,eAAe,EAAElB,KAAI,CAACP,QAAQ,CAACyB;MACnC,CAAC;MACD,IAAInB,cAAc,KAAKrB,cAAc,CAAC6B,MAAM,EAAE;QAC1CO,WAAW,CAACL,YAAY,GAAG,aAAa;MAC5C;MACA;MACA;MACA,MAAMU,OAAO,GAAI,GAAErB,GAAI,MAAKsB,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;MACxCrB,KAAI,CAACR,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,oCAAmCa,OAAQ,GAAE,CAAC;MAChF,MAAMG,QAAQ,SAAStB,KAAI,CAACT,WAAW,CAACgC,GAAG,CAACJ,OAAO,EAAEL,WAAW,CAAC;MACjE,IAAIQ,QAAQ,CAACE,UAAU,KAAK,GAAG,EAAE;QAC7BxB,KAAI,CAACR,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAACiC,KAAK,EAAG,qDAAoDY,QAAQ,CAACE,UAAW,GAAE,CAAC;QAC7G;QACAxB,KAAI,CAACyB,WAAW,GAAG,IAAIlD,SAAS,CAAC+C,QAAQ,CAACI,UAAU,IAAI,EAAE,EAAEJ,QAAQ,CAACE,UAAU,CAAC;QAChFxB,KAAI,CAACN,QAAQ,GAAG,KAAK;MACzB,CAAC,MACI;QACDM,KAAI,CAACN,QAAQ,GAAG,IAAI;MACxB;MACAM,KAAI,CAAC2B,UAAU,GAAG3B,KAAI,CAAC4B,KAAK,CAAC5B,KAAI,CAACI,IAAI,EAAEU,WAAW,CAAC;IAAC;EACzD;EACMc,KAAKA,CAAC9B,GAAG,EAAEgB,WAAW,EAAE;IAAA,IAAAe,MAAA;IAAA,OAAA5B,iBAAA;MAC1B,IAAI;QACA,OAAO4B,MAAI,CAACnC,QAAQ,EAAE;UAClB,IAAI;YACA,MAAMyB,OAAO,GAAI,GAAErB,GAAI,MAAKsB,IAAI,CAACC,GAAG,CAAC,CAAE,EAAC;YACxCQ,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,oCAAmCa,OAAQ,GAAE,CAAC;YAChF,MAAMG,QAAQ,SAASO,MAAI,CAACtC,WAAW,CAACgC,GAAG,CAACJ,OAAO,EAAEL,WAAW,CAAC;YACjE,IAAIQ,QAAQ,CAACE,UAAU,KAAK,GAAG,EAAE;cAC7BK,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAACqD,WAAW,EAAE,oDAAoD,CAAC;cAC5FD,MAAI,CAACnC,QAAQ,GAAG,KAAK;YACzB,CAAC,MACI,IAAI4B,QAAQ,CAACE,UAAU,KAAK,GAAG,EAAE;cAClCK,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAACiC,KAAK,EAAG,qDAAoDY,QAAQ,CAACE,UAAW,GAAE,CAAC;cAC7G;cACAK,MAAI,CAACJ,WAAW,GAAG,IAAIlD,SAAS,CAAC+C,QAAQ,CAACI,UAAU,IAAI,EAAE,EAAEJ,QAAQ,CAACE,UAAU,CAAC;cAChFK,MAAI,CAACnC,QAAQ,GAAG,KAAK;YACzB,CAAC,MACI;cACD;cACA,IAAI4B,QAAQ,CAACS,OAAO,EAAE;gBAClBF,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,0CAAyC1B,aAAa,CAAC0C,QAAQ,CAACS,OAAO,EAAEF,MAAI,CAACpC,QAAQ,CAACuC,iBAAiB,CAAE,GAAE,CAAC;gBAC/I,IAAIH,MAAI,CAAClC,SAAS,EAAE;kBAChBkC,MAAI,CAAClC,SAAS,CAAC2B,QAAQ,CAACS,OAAO,CAAC;gBACpC;cACJ,CAAC,MACI;gBACD;gBACAF,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,oDAAoD,CAAC;cAC1F;YACJ;UACJ,CAAC,CACD,OAAO2B,CAAC,EAAE;YACN,IAAI,CAACJ,MAAI,CAACnC,QAAQ,EAAE;cAChB;cACAmC,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,wDAAuD2B,CAAC,CAACC,OAAQ,EAAC,CAAC;YACzG,CAAC,MACI;cACD,IAAID,CAAC,YAAYzD,YAAY,EAAE;gBAC3B;gBACAqD,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,oDAAoD,CAAC;cAC1F,CAAC,MACI;gBACD;gBACAuB,MAAI,CAACJ,WAAW,GAAGQ,CAAC;gBACpBJ,MAAI,CAACnC,QAAQ,GAAG,KAAK;cACzB;YACJ;UACJ;QACJ;MACJ,CAAC,SACO;QACJmC,MAAI,CAACrC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,2CAA2C,CAAC;QAC7E;QACA;QACA,IAAI,CAACuB,MAAI,CAAC7C,WAAW,EAAE;UACnB6C,MAAI,CAACM,aAAa,CAAC,CAAC;QACxB;MACJ;IAAC;EACL;EACMC,IAAIA,CAACC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAArC,iBAAA;MACb,IAAI,CAACqC,MAAI,CAAC5C,QAAQ,EAAE;QAChB,OAAO6C,OAAO,CAACC,MAAM,CAAC,IAAI9B,KAAK,CAAC,8CAA8C,CAAC,CAAC;MACpF;MACA,OAAO5B,WAAW,CAACwD,MAAI,CAAC9C,OAAO,EAAE,aAAa,EAAE8C,MAAI,CAAC/C,WAAW,EAAE+C,MAAI,CAAClC,IAAI,EAAEiC,IAAI,EAAEC,MAAI,CAAC7C,QAAQ,CAAC;IAAC;EACtG;EACMgD,IAAIA,CAAA,EAAG;IAAA,IAAAC,MAAA;IAAA,OAAAzC,iBAAA;MACTyC,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,2CAA2C,CAAC;MAC7E;MACAoC,MAAI,CAAChD,QAAQ,GAAG,KAAK;MACrBgD,MAAI,CAACzD,UAAU,CAAC0D,KAAK,CAAC,CAAC;MACvB,IAAI;QACA,MAAMD,MAAI,CAACf,UAAU;QACrB;QACAe,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,qDAAoDoC,MAAI,CAACtC,IAAK,GAAE,CAAC;QACnG,MAAMS,OAAO,GAAG,CAAC,CAAC;QAClB,MAAM,CAACF,IAAI,EAAEC,KAAK,CAAC,GAAG/B,kBAAkB,CAAC,CAAC;QAC1CgC,OAAO,CAACF,IAAI,CAAC,GAAGC,KAAK;QACrB,MAAMgC,aAAa,GAAG;UAClB/B,OAAO,EAAE;YAAE,GAAGA,OAAO;YAAE,GAAG6B,MAAI,CAACjD,QAAQ,CAACoB;UAAQ,CAAC;UACjDI,OAAO,EAAEyB,MAAI,CAACjD,QAAQ,CAACwB,OAAO;UAC9BC,eAAe,EAAEwB,MAAI,CAACjD,QAAQ,CAACyB;QACnC,CAAC;QACD,IAAI2B,KAAK;QACT,IAAI;UACA,MAAMH,MAAI,CAACnD,WAAW,CAACuD,MAAM,CAACJ,MAAI,CAACtC,IAAI,EAAEwC,aAAa,CAAC;QAC3D,CAAC,CACD,OAAOG,GAAG,EAAE;UACRF,KAAK,GAAGE,GAAG;QACf;QACA,IAAIF,KAAK,EAAE;UACP,IAAIA,KAAK,YAAYtE,SAAS,EAAE;YAC5B,IAAIsE,KAAK,CAACrB,UAAU,KAAK,GAAG,EAAE;cAC1BkB,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,oFAAoF,CAAC;YAC1H,CAAC,MACI;cACDoC,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAG,2DAA0DuC,KAAM,EAAC,CAAC;YACxG;UACJ;QACJ,CAAC,MACI;UACDH,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,kDAAkD,CAAC;QACxF;MACJ,CAAC,SACO;QACJoC,MAAI,CAAClD,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE,wCAAwC,CAAC;QAC1E;QACA;QACAoC,MAAI,CAACP,aAAa,CAAC,CAAC;MACxB;IAAC;EACL;EACAA,aAAaA,CAAA,EAAG;IACZ,IAAI,IAAI,CAACvC,OAAO,EAAE;MACd,IAAIoD,UAAU,GAAG,+CAA+C;MAChE,IAAI,IAAI,CAACvB,WAAW,EAAE;QAClBuB,UAAU,IAAI,UAAU,GAAG,IAAI,CAACvB,WAAW;MAC/C;MACA,IAAI,CAACjC,OAAO,CAACa,GAAG,CAAC5B,QAAQ,CAAC6B,KAAK,EAAE0C,UAAU,CAAC;MAC5C,IAAI,CAACpD,OAAO,CAAC,IAAI,CAAC6B,WAAW,CAAC;IAClC;EACJ;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}