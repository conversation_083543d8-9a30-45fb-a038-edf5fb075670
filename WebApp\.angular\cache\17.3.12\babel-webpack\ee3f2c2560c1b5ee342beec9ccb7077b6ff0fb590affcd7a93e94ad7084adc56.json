{"ast": null, "code": "import apply from './_apply.js';\nimport arrayPush from './_arrayPush.js';\nimport baseRest from './_baseRest.js';\nimport castSlice from './_castSlice.js';\nimport toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates a function that invokes `func` with the `this` binding of the\n * create function and an array of arguments much like\n * [`Function#apply`](http://www.ecma-international.org/ecma-262/7.0/#sec-function.prototype.apply).\n *\n * **Note:** This method is based on the\n * [spread operator](https://mdn.io/spread_operator).\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Function\n * @param {Function} func The function to spread arguments over.\n * @param {number} [start=0] The start position of the spread.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var say = _.spread(function(who, what) {\n *   return who + ' says ' + what;\n * });\n *\n * say(['fred', 'hello']);\n * // => 'fred says hello'\n *\n * var numbers = Promise.all([\n *   Promise.resolve(40),\n *   Promise.resolve(36)\n * ]);\n *\n * numbers.then(_.spread(function(x, y) {\n *   return x + y;\n * }));\n * // => a Promise of 76\n */\nfunction spread(func, start) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  start = start == null ? 0 : nativeMax(toInteger(start), 0);\n  return baseRest(function (args) {\n    var array = args[start],\n      otherArgs = castSlice(args, 0, start);\n    if (array) {\n      arrayPush(otherArgs, array);\n    }\n    return apply(func, this, otherArgs);\n  });\n}\nexport default spread;", "map": {"version": 3, "names": ["apply", "arrayPush", "baseRest", "castSlice", "toInteger", "FUNC_ERROR_TEXT", "nativeMax", "Math", "max", "spread", "func", "start", "TypeError", "args", "array", "otherArgs"], "sources": ["C:/Users/<USER>/source/ai-hub/WebApp/node_modules/lodash-es/spread.js"], "sourcesContent": ["import apply from './_apply.js';\nimport arrayPush from './_arrayPush.js';\nimport baseRest from './_baseRest.js';\nimport castSlice from './_castSlice.js';\nimport toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max;\n\n/**\n * Creates a function that invokes `func` with the `this` binding of the\n * create function and an array of arguments much like\n * [`Function#apply`](http://www.ecma-international.org/ecma-262/7.0/#sec-function.prototype.apply).\n *\n * **Note:** This method is based on the\n * [spread operator](https://mdn.io/spread_operator).\n *\n * @static\n * @memberOf _\n * @since 3.2.0\n * @category Function\n * @param {Function} func The function to spread arguments over.\n * @param {number} [start=0] The start position of the spread.\n * @returns {Function} Returns the new function.\n * @example\n *\n * var say = _.spread(function(who, what) {\n *   return who + ' says ' + what;\n * });\n *\n * say(['fred', 'hello']);\n * // => 'fred says hello'\n *\n * var numbers = Promise.all([\n *   Promise.resolve(40),\n *   Promise.resolve(36)\n * ]);\n *\n * numbers.then(_.spread(function(x, y) {\n *   return x + y;\n * }));\n * // => a Promise of 76\n */\nfunction spread(func, start) {\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  start = start == null ? 0 : nativeMax(toInteger(start), 0);\n  return baseRest(function(args) {\n    var array = args[start],\n        otherArgs = castSlice(args, 0, start);\n\n    if (array) {\n      arrayPush(otherArgs, array);\n    }\n    return apply(func, this, otherArgs);\n  });\n}\n\nexport default spread;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,aAAa;AAC/B,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,IAAI,EAAEC,KAAK,EAAE;EAC3B,IAAI,OAAOD,IAAI,IAAI,UAAU,EAAE;IAC7B,MAAM,IAAIE,SAAS,CAACP,eAAe,CAAC;EACtC;EACAM,KAAK,GAAGA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGL,SAAS,CAACF,SAAS,CAACO,KAAK,CAAC,EAAE,CAAC,CAAC;EAC1D,OAAOT,QAAQ,CAAC,UAASW,IAAI,EAAE;IAC7B,IAAIC,KAAK,GAAGD,IAAI,CAACF,KAAK,CAAC;MACnBI,SAAS,GAAGZ,SAAS,CAACU,IAAI,EAAE,CAAC,EAAEF,KAAK,CAAC;IAEzC,IAAIG,KAAK,EAAE;MACTb,SAAS,CAACc,SAAS,EAAED,KAAK,CAAC;IAC7B;IACA,OAAOd,KAAK,CAACU,IAAI,EAAE,IAAI,EAAEK,SAAS,CAAC;EACrC,CAAC,CAAC;AACJ;AAEA,eAAeN,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}