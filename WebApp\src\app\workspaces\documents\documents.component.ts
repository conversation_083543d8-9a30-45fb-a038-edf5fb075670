import { CommonModule } from '@angular/common';
import { Component, OnInit, OnD<PERSON>roy, inject, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink, ActivatedRoute } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { Observable, Observer, firstValueFrom } from 'rxjs';
import { ThemeService } from '../../../shared/services/theam.service';
import { DocumentSyncService } from '../../shared/services/document-sync.service';
import { ActiveDocumentService } from '../../shared/services/active-document.service';

// EditorJS imports
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Link from '@editorjs/link';
import Table from '@editorjs/table';
import ImageTool from '@editorjs/image';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CreateDocsDto, DocsServiceProxy, FileServiceProxy, ResponseMessage, TrackDocumentOpenDto, UpdateFavoriteDto } from '../../../shared/service-proxies/service-proxies';
import { AddOrEditDocumentComponent } from './add-or-edit-document/add-or-edit-document.component';

// Document interface
interface Document {
  id: string;
  name: string;
  content?: string;
  files?: File[];
}

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzBreadCrumbModule,
    NzIconModule,
    NzUploadModule,
    AddOrEditDocumentComponent,
  ],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.css',
  providers: [NzMessageService, NzModalService],
})
export class DocumentsComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('chatContainer') chatContainer!: ElementRef;

  showAddDocumentDialog = false;
  loading = false;
  avatarUrl?: string;
  showViewDocumentDialog = false;
  selectedDocument: any = null;
  documents: any[] = [];
  isAddingOrEditing: boolean = false;
  documentToEdit: any = null;
  isNewDocument: boolean = false;
  showingDocumentsList: boolean = true;

  // EditorJS properties
  private editor: EditorJS | null = null;
  private editorInitialized = false;
  private pendingContent: any = null;
  hasContent = false;
  wordCount = 0;
  workspaceName: string = '';
  isPublicRoute: boolean = false;

  // For file uploads
  selectedFiles: File[] = [];
  filesToDelete: any[] = [];

  // Document Chat Properties
  showDocumentChat: boolean = false;
  showChatOptions: boolean = false;
  documentChatMessages: Array<{
    id: string;
    sender: string;
    content: string;
    timestamp: Date;
    liked?: boolean;
  }> = [];
  documentChatInput: string = '';
  isProcessingDocumentChat: boolean = false;
  unreadDocumentChatCount: number = 0;

  // Inject services
  themeService = inject(ThemeService);
  documentSyncService = inject(DocumentSyncService);
  activeDocumentService = inject(ActiveDocumentService);

  constructor(
    private messageService: NzMessageService,
    private modalService: NzModalService,
    private docsService: DocsServiceProxy,
    private documentService: DocsServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private fileService: FileServiceProxy,
  ) { }

  ngOnInit() {
    // Get the current URL and split it into segments
    const url = this.router.url;
    const urlSegments = url.split('/');

    // Check if we're on the public notes route
    if (url.startsWith('/notes')) {
      // For public notes route, use 'GlobalNotes' as the workspace name
      this.workspaceName = 'GlobalNotes';
      this.isPublicRoute = true;
    }
    // Check if we're in a workspace route
    else if (url.includes('/workspaces/')) {
      // For workspace routes, get the workspace name from the URL
      this.workspaceName = urlSegments[2];
      this.workspaceName = decodeURIComponent(this.workspaceName);
    }
    // Fallback to GlobalNotes for any other case
    else {
      this.workspaceName = 'GlobalNotes';
    }

    console.log('Using workspace name:', this.workspaceName);

    // Load documents with the determined workspace name
    this.loadDocuments().then(() => {
      // Handle query parameters after documents are loaded
      this.handleQueryParameters();
    });
  }

  private handleQueryParameters() {
    this.route.queryParams.subscribe(params => {
      const docId = params['docId'];
      const action = params['action'];

      if (docId) {
        // Find and select the document by ID
        const document = this.documents.find(doc => doc.id == docId);
        if (document) {
          // Set the active document using the service before selecting
          this.activeDocumentService.setActiveDocument(document, {
            workspaceName: this.workspaceName,
            route: this.isPublicRoute ? 'notes' : 'workspace-documents'
          });

          this.selectDocument(document);
          // Clear the query parameter to avoid issues with navigation
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: {},
            replaceUrl: true
          });
        }
      } else if (action === 'add') {
        // Trigger add document mode
        this.addDocument(new Event('click'));
        // Clear the query parameter
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      }
    });
  }

  ngAfterViewInit() {
    // Remove the automatic initialization for now
    // We'll initialize it when the user starts editing
  }

  ngOnDestroy() {
    this.destroyEditor();
  }

  private destroyEditor() {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
      this.editorInitialized = false;
    }
  }

  private initializeEditor() {
    // First destroy any existing editor instance
    this.destroyEditor();

    // Add a short delay to ensure the DOM has updated
    setTimeout(() => {
      const editorElement = document.getElementById('editor');
      if (!editorElement) {
        console.error('Element with ID "editor" is missing.');
        return;
      }

      this.editor = new EditorJS({
        holder: 'editor',
        minHeight: 200,
        placeholder: 'Write your description here...',
        onChange: () => {
          this.editor?.save().then((data) => {
            this.hasContent = data.blocks.length > 0;
            this.updateWordCount(data);
            if (this.documentToEdit) {
              this.documentToEdit.content = JSON.stringify(data);
            }
          });
        },
        onReady: () => {
          this.editorInitialized = true;
          console.log('Editor.js is ready');
          if (this.pendingContent) {
            this.updateEditorContent(this.pendingContent);
            this.pendingContent = null;
          }
        },
        tools: {
          header: {
            class: Header as unknown as ToolConstructable,
            inlineToolbar: true,
            config: {
              levels: [1, 2, 3, 4],
              defaultLevel: 1,
            },
          },
          list: List as unknown as ToolConstructable,
          checklist: Checklist as unknown as ToolConstructable,
          quote: Quote as unknown as ToolConstructable,
          marker: Marker as unknown as ToolConstructable,
          code: CodeTool as unknown as ToolConstructable,
          delimiter: Delimiter as unknown as ToolConstructable,
          inlineCode: InlineCode as unknown as ToolConstructable,
          table: Table as unknown as ToolConstructable,
          image: {
            class: ImageTool as unknown as { new(): any },
            config: {
              uploader: {
                uploadByFile: (file: File) => {
                  return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                      resolve({
                        success: 1,
                        file: {
                          url: reader.result as string,
                        },
                      });
                    };
                    reader.onerror = () => {
                      reject({
                        success: 0,
                        message: 'Could not read file',
                      });
                    };
                    reader.readAsDataURL(file);
                  });
                },
              },
            },
          },
        },
      });
    }, 100);
  }

  private ensureEditorInitialized() {
    if (!this.editor && this.isAddingOrEditing) {
      this.initializeEditor();
    }
  }

  private async updateEditorContent(newContent: any) {
    try {
      if (!this.editor) {
        console.warn('Editor not initialized yet, storing content for later');
        this.pendingContent = newContent;
        this.ensureEditorInitialized(); // Try to initialize the editor
        return;
      }

      await this.editor.isReady;
      await this.editor.blocks.clear();

      // Make sure content is in the correct format for EditorJS
      if (newContent && typeof newContent === 'object') {
        // If it's already in the EditorJS format with blocks
        if (Array.isArray(newContent.blocks)) {
          await this.editor.render(newContent);
        }
        // If it's some other object format, create a text block
        else {
          await this.editor.render({
            blocks: [
              {
                type: "paragraph",
                data: {
                  text: JSON.stringify(newContent)
                }
              }
            ]
          });
        }
      }
      // If it's a string, create a text block
      else if (typeof newContent === 'string') {
        await this.editor.render({
          blocks: [
            {
              type: "paragraph",
              data: {
                text: newContent
              }
            }
          ]
        });
      }
    } catch (error) {
      console.error('Error updating editor content:', error);
    }
  }

  private async saveEditorContent() {
    if (!this.editor) return;

    try {
      const editorData = await this.editor.save();
      if (this.documentToEdit) {
        this.documentToEdit.content = JSON.stringify(editorData);
      }
    } catch (error) {
      console.error('Error saving editor content:', error);
    }
  }

  private updateWordCount(data: any) {
    const text = data.blocks
      .map((block: any) =>
        block.data && block.data.text ? block.data.text : ''
      )
      .join(' ');
    this.wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
  }

  async loadDocuments() {
    console.log(this.workspaceName);

    try {
      const data = await firstValueFrom(this.documentService.getByWorkspaceName(this.workspaceName));
      this.documents = data;
    } catch (error) {
      console.error('Error loading documents:', error);
    }
  }

  selectDocument(document: any) {
    this.selectedDocument = document;
    this.showingDocumentsList = false;
    this.isAddingOrEditing = false;

    console.log('📖 Document selected from list:', document.title);

    // Set the active document using the service
    this.activeDocumentService.setActiveDocument(document, {
      workspaceName: this.workspaceName,
      route: this.isPublicRoute ? 'notes' : 'workspace-documents'
    });

    // Track document as recently opened when selected from the main documents list
    this.trackDocumentOpen(document);

    // For viewing document content, we don't need to initialize the editor
    // but we can prepare the content for display in the HTML
    if (document.content) {
      try {
        let parsedContent = JSON.parse(document.content);

        // Handle nested content structure
        if (parsedContent.desc) {
          try {
            // Try parsing the desc field if it's a string representation of JSON
            const descContent = JSON.parse(parsedContent.desc);

            // Set a formatted HTML version for display
            document.formattedContent = this.formatEditorContent(descContent);
          } catch {
            // If desc is not JSON, use it directly
            document.formattedContent = parsedContent.desc;
          }
        } else if (parsedContent.blocks) {
          // If it's already in EditorJS format
          document.formattedContent = this.formatEditorContent(parsedContent);
        } else {
          // Fallback
          document.formattedContent = document.content;
        }
      } catch (error) {
        console.error('Error parsing document content:', error);
        document.formattedContent = document.content;
      }
    }
  }

  addDocument($event: Event) {
    $event.stopImmediatePropagation();

    this.isAddingOrEditing = true;
    this.isNewDocument = true;
    this.documentToEdit = { title: '', content: '', description: '' };
    this.selectedDocument = null;
    this.selectedFiles = [];

    // Destroy existing editor and initialize a new one
    this.destroyEditor();

    // Initialize the editor after setting isAddingOrEditing to true
    setTimeout(() => {
      this.initializeEditor();
    }, 100);
  }

  editDocument(document: any) {
    this.isAddingOrEditing = true;
    this.isNewDocument = false;
    this.documentToEdit = { ...document };

    // Reset file tracking arrays
    this.selectedFiles = [];
    this.filesToDelete = [];

    // Destroy existing editor and initialize a new one
    this.destroyEditor();

    // Initialize the editor after setting isAddingOrEditing to true
    setTimeout(() => {
      this.initializeEditor();

      // Handle document content for the editor
      if (document.content) {
        try {
          let parsedContent;

          // Try to parse the content as JSON
          try {
            parsedContent = JSON.parse(document.content);

            // If content is in the format {title: "...", desc: "..."}
            if (parsedContent.desc && typeof parsedContent.desc === 'string') {
              // Handle case where desc is a string but needs to be parsed as EditorJS data
              try {
                parsedContent = JSON.parse(parsedContent.desc);
              } catch {
                // If desc is not JSON, create a simple text block
                parsedContent = {
                  blocks: [
                    {
                      type: "paragraph",
                      data: {
                        text: parsedContent.desc
                      }
                    }
                  ]
                };
              }
            }
            // If the content is already in EditorJS format with blocks
            else if (!parsedContent.blocks) {
              // Create a simple text block for plain JSON that's not in EditorJS format
              parsedContent = {
                blocks: [
                  {
                    type: "paragraph",
                    data: {
                      text: document.content
                    }
                  }
                ]
              };
            }
          } catch (jsonError) {
            // If content is not JSON at all, create a simple text block
            parsedContent = {
              blocks: [
                {
                  type: "paragraph",
                  data: {
                    text: document.content
                  }
                }
              ]
            };
          }

          // Update the editor with the parsed content
          if (this.editorInitialized) {
            this.updateEditorContent(parsedContent);
          } else {
            this.pendingContent = parsedContent;
          }
        } catch (error) {
          console.error('Error parsing or setting document content:', error);
        }
      } else {
        // Clear the editor if no content
        if (this.editor) {
          this.editor.blocks.clear();
        }
      }
    }, 100);
  }

  deleteDocument(document: any) {
    if (confirm('Are you sure you want to delete this document?')) {
      this.documentService.delete(document.id).subscribe(
        () => {
          this.loadDocuments();
          if (this.selectedDocument && this.selectedDocument.id === document.id) {
            this.selectedDocument = null;
            this.showingDocumentsList = true;
          }

          // Notify sidebar to refresh data
          this.documentSyncService.notifyDocumentDeleted(document, this.workspaceName);
        },
        (error: any) => {
          console.error('Error deleting document:', error);
        }
      );
    }
  }

  async saveDocument() {
    await this.saveEditorContent();

    if (!this.documentToEdit.title) {
      this.messageService.error('Please fill in title');
      return;
    }

    // Ensure content is properly formatted when saving
    let contentToSave = this.documentToEdit.content || '';

    // Format content properly
    try {
      const parsedContent = JSON.parse(contentToSave);
      if (!parsedContent.title && !parsedContent.desc) {
        contentToSave = JSON.stringify({
          title: this.documentToEdit.title,
          desc: contentToSave
        });
      }
    } catch (e) {
      contentToSave = JSON.stringify({
        title: this.documentToEdit.title,
        desc: contentToSave
      });
    }

    try {
      // Step 1: Upload files first
      let fileToAddResponse: ResponseMessage = new ResponseMessage();

      if (this.selectedFiles.length > 0) {
        // Convert files to FileParameter array
        const fileParams = this.selectedFiles.map(file => ({
          data: file,
          fileName: file.name
        }));

        // Upload files using Promise
        try {
          fileToAddResponse = await firstValueFrom(this.fileService.upload(undefined,fileParams));
          console.log('Files uploaded successfully:', fileToAddResponse);
        } catch (error) {
          console.error('Error uploading files:', error);
          // Continue with empty file response
        }
      }

      // Step 2: Create the document DTO
      const docsCreate = new CreateDocsDto({
        id: this.isNewDocument ? 0 : this.documentToEdit.id,
        title: this.documentToEdit.title,
        content: contentToSave,
        workspaceName: this.workspaceName || 'GlobalNotes', // Ensure we always have a workspace name
        filesToAdd: fileToAddResponse.message,
        filesToDelete: this.filesToDelete
      });

      // Step 3: Save the document using Promise
      const savedDoc = await firstValueFrom(this.documentService.createOrUpdate(docsCreate));

      // Handle success
      this.messageService.success(`Document ${this.isNewDocument ? 'created' : 'updated'} successfully`);

      console.log('📄 Document saved successfully:', savedDoc);
      console.log('📁 Current workspace:', this.workspaceName);
      console.log('🆕 Is new document:', this.isNewDocument);

      this.onDocumentSaved(savedDoc);
      this.clearFileSelections();

      // For new documents, track them as recently opened to ensure they appear in sidebar
      if (this.isNewDocument && savedDoc) {
        console.log('📌 Tracking new document as recently opened...');
        this.trackNewDocumentAsRecentlyOpened(savedDoc);
      }

      // Notify sidebar immediately for new documents, with delay for updates
      if (this.isNewDocument) {
        console.log('🔔 Notifying sidebar of new document creation...');
        // Immediate notification for new documents
        this.documentSyncService.notifyDocumentCreated(savedDoc, this.workspaceName);

        // Also send a delayed notification to ensure backend sync
        setTimeout(() => {
          console.log('🔔 Sending delayed notification for new document...');
          this.documentSyncService.notifyDocumentCreated(savedDoc, this.workspaceName);
        }, 1500);
      } else {
        console.log('🔔 Notifying sidebar of document update...');
        setTimeout(() => {
          this.documentSyncService.notifyDocumentUpdated({ type: 'updated', document: savedDoc, workspaceName: this.workspaceName });
        }, 500);
      }
    } catch (error) {
      this.messageService.error(`Error ${this.isNewDocument ? 'creating' : 'updating'} document`);
      console.error(`Error ${this.isNewDocument ? 'creating' : 'updating'} document:`, error);
    }
  }

  onDocumentSaved(savedDocument: any) {
    this.loadDocuments();
    this.selectedDocument = savedDocument;
    this.isAddingOrEditing = false;
    this.showingDocumentsList = false;

    // Format the content immediately after saving
    if (savedDocument.content) {
      try {
        let parsedContent = JSON.parse(savedDocument.content);

        // Handle nested content structure
        if (parsedContent.desc) {
          try {
            // Try parsing the desc field if it's a string representation of JSON
            const descContent = JSON.parse(parsedContent.desc);
            // Set a formatted HTML version for display
            savedDocument.formattedContent = this.formatEditorContent(descContent);
          } catch {
            // If desc is not JSON, use it directly
            savedDocument.formattedContent = parsedContent.desc;
          }
        } else if (parsedContent.blocks) {
          // If it's already in EditorJS format
          savedDocument.formattedContent = this.formatEditorContent(parsedContent);
        } else {
          // Fallback
          savedDocument.formattedContent = savedDocument.content;
        }
      } catch (error) {
        console.error('Error parsing document content:', error);
        savedDocument.formattedContent = savedDocument.content;
      }
    }
  }

  onCancel() {
    this.isAddingOrEditing = false;
    if (this.isNewDocument && !this.selectedDocument) {
      this.showingDocumentsList = true;
    }
    this.selectedFiles = [];
    this.destroyEditor();
  }

  openAddDocumentDialog() {
    this.showAddDocumentDialog = true;
    setTimeout(() => {
      if (!this.editor) {
        this.initializeEditor();
      }
    }, 0);
  }

  closeAddDocumentDialog() {
    this.showAddDocumentDialog = false;
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  }

  beforeUpload = (
    file: NzUploadFile,
    _fileList: NzUploadFile[]
  ): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng =
        file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        this.messageService.error('You can only upload JPG or PNG files!');
        observer.complete();
        return;
      }
      const isLt2M = file.size! / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.messageService.error('Image must be smaller than 2MB!');
        observer.complete();
        return;
      }
      observer.next(isJpgOrPng && isLt2M);
      observer.complete();
    });

  private getBase64(img: File, callback: (img: string) => void): void {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result!.toString()));
    reader.readAsDataURL(img);
  }

  handleChange(info: { file: NzUploadFile }): void {
    switch (info.file.status) {
      case 'uploading':
        this.loading = true;
        break;
      case 'done':
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          this.loading = false;
          this.avatarUrl = img;
        });
        break;
      case 'error':
        this.messageService.error('Network error');
        this.loading = false;
        break;
    }
  }



  showDocumentsList() {
    this.showingDocumentsList = true;
    this.selectedDocument = null;
    this.isAddingOrEditing = false;
  }

  createNewNote($event: Event) {
    $event.stopImmediatePropagation()

  }
  backToList() {
    this.selectedDocument = null;
    this.showingDocumentsList = true;
  }



  onFileSelect(event: any): void {
    if (event.target.files.length > 0) {
      const files = event.target.files;
      for (let i = 0; i < files.length; i++) {
        // Check for duplicates by name
        const fileName = files[i].name;
        const isDuplicate = this.selectedFiles.some(file => file.name === fileName);

        if (!isDuplicate) {
          this.selectedFiles.push(files[i]);
        }
      }
    }

    // Clear the input value to allow selecting the same file again
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
  }

  isImageFile(fileName: string): boolean {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() ?? '';
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension);
  }

  showDocumentDetails(document: Document) {
    this.selectedDocument = document;
  }

  // Add a helper method to format EditorJS content for display
  private formatEditorContent(content: any): string {
    if (!content || !content.blocks) return '';

    let html = '';

    content.blocks.forEach((block: any) => {
      switch (block.type) {
        case 'header':
          const level = block.data.level || 2;
          html += `<h${level}>${block.data.text}</h${level}>`;
          break;
        case 'paragraph':
          html += `<p>${block.data.text}</p>`;
          break;
        case 'list':
          const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';
          html += `<${listTag}>`;
          block.data.items.forEach((item: string) => {
            html += `<li>${item}</li>`;
          });
          html += `</${listTag}>`;
          break;
        // Add more cases for other block types as needed
        default:
          if (block.data.text) {
            html += `<p>${block.data.text}</p>`;
          }
      }
    });

    return html;
  }

  getFilePreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  markFileForDeletion(file: any, index: number): void {
    // Add to files to delete array (only once)
    this.filesToDelete.push(file);

    // Remove from UI display - properly update the string
    if (this.documentToEdit && this.documentToEdit.files) {
      const filesArray = this.documentToEdit.files.split(', ');
      filesArray.splice(index, 1);
      this.documentToEdit.files = filesArray.join(', ');
    }

    // Mark file as deleted for UI rendering
    file.markedForDeletion = true;

    // Trigger change detection
    this.changeDetectorRef.detectChanges();
  }

  removeSelectedFile(index: number): void {
    if (index >= 0 && index < this.selectedFiles.length) {
      this.selectedFiles.splice(index, 1);
    }
  }

  clearFileSelections() {
    this.selectedFiles = [];
    this.filesToDelete = [];
  }

  isDocumentFavorite(document: any): boolean {
    // Check if the document has a favorite property or if it exists in a favorites list
    return document && (document.isFavorite === true || document.isFavourite === true);
  }

  toggleFavorite(document: any) {
    if (!document || !document.id) {
      console.error('Invalid document for favorite toggle');
      return;
    }

    const isFavorite = this.isDocumentFavorite(document);
    console.log('🌟 Toggling favorite for document:', document.title, 'Current status:', isFavorite);

    // Use the API to update favorite status
    this.documentService.updateFavoriteStatus({
      id: document.id,
      isFavorite: !isFavorite
    } as UpdateFavoriteDto).subscribe(
      (response: any) => {
        console.log('✅ Favorite status updated:', response);

        // Update local document state
        document.isFavorite = !isFavorite;
        document.isFavourite = !isFavorite; // Handle both property names

        // If this is the selected document, update it as well
        if (this.selectedDocument && this.selectedDocument.id === document.id) {
          this.selectedDocument.isFavorite = !isFavorite;
          this.selectedDocument.isFavourite = !isFavorite;
        }

        // Notify sidebar to refresh favorites list
        this.documentSyncService.notifyDocumentFavorited(document, this.workspaceName);

        console.log('🔔 Notified sidebar of favorite change');
      },
      (error: any) => {
        console.error('❌ Error updating favorite status:', error);
      }
    );
  }

  trackDocumentOpen(document: any) {
    // Track document as recently opened and notify sidebar
    if (document && document.id) {
      console.log('📌 Tracking document open from main list:', {
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      });

      // Use the API to track document opens
      this.documentService.trackDocumentOpen({
        id: document.id,
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      } as any).subscribe(
        (response: any) => {
          console.log('✅ Document tracked as recently opened:', response);

          // Notify sidebar to refresh recent documents list
          this.documentSyncService.notifyDocumentUpdated({
            type: 'updated',
            document: document,
            workspaceName: this.workspaceName
          });

          console.log('🔔 Notified sidebar of document open');
        },
        (error: any) => {
          console.error('❌ Error tracking document open:', error);

          // Try alternative API call format if the first one fails
          this.documentService.trackDocumentOpen({
            id: document.id
          } as any).subscribe(
            (response2: any) => {
              console.log('✅ Document tracked with alternative format:', response2);

              // Still notify sidebar even with alternative format
              this.documentSyncService.notifyDocumentUpdated({
                type: 'updated',
                document: document,
                workspaceName: this.workspaceName
              });
            },
            (error2: any) => {
              console.error('❌ Alternative tracking also failed:', error2);
            }
          );
        }
      );
    }
  }

  private trackNewDocumentAsRecentlyOpened(document: any) {
    // Track the newly created document as recently opened so it appears in the sidebar
    if (document && document.id) {
      console.log('📌 Attempting to track document as recently opened:', {
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      });

      // Use the correct API call format
      this.documentService.trackDocumentOpen({
        id: document.id,
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      } as any).subscribe(
        (response: any) => {
          console.log('✅ New document tracked as recently opened:', response);
        },
        (error: any) => {
          console.error('❌ Error tracking new document as recently opened:', error);

          // Try alternative API call format if the first one fails
          this.documentService.trackDocumentOpen({
            id: document.id
          } as any).subscribe(
            (response2: any) => {
              console.log('✅ New document tracked with alternative format:', response2);
            },
            (error2: any) => {
              console.error('❌ Alternative tracking also failed:', error2);
            }
          );
        }
      );
    }
  }

  // Document Chat Methods
  toggleDocumentChat(): void {
    console.log('Toggle document chat clicked. Current state:', this.showDocumentChat);
    this.showDocumentChat = !this.showDocumentChat;
    console.log('New state:', this.showDocumentChat);

    if (this.showDocumentChat) {
      // Reset unread count when opening chat
      this.unreadDocumentChatCount = 0;
      // Close chat options if open
      this.showChatOptions = false;

      // Add welcome message if no messages exist
      if (this.documentChatMessages.length === 0) {
        const welcomeMessage = {
          id: this.generateMessageId(),
          sender: 'agent',
          content: `Hello! I'm your Document AI Assistant. I can help you analyze, edit, and understand your documents. ${this.selectedDocument ? `I can see you're working on "${this.selectedDocument.title}". ` : ''}How can I assist you today?`,
          timestamp: new Date()
        };
        this.documentChatMessages.push(welcomeMessage);
      }

      // Scroll to bottom after opening
      setTimeout(() => this.scrollChatToBottom(), 300);
      console.log('Document chat opened');
    } else {
      console.log('Document chat closed');
    }
  }

  toggleChatOptions(): void {
    this.showChatOptions = !this.showChatOptions;
  }

  clearChatHistory(): void {
    this.documentChatMessages = [];
    this.showChatOptions = false;
    this.messageService.success('Chat history cleared');
  }

  exportChatHistory(): void {
    const chatData = {
      exportDate: new Date().toISOString(),
      documentTitle: this.selectedDocument?.title || 'No document selected',
      workspaceName: this.workspaceName,
      messages: this.documentChatMessages
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `document-chat-history-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);

    this.showChatOptions = false;
    this.messageService.success('Chat history exported');
  }

  analyzeCurrentDocument(): void {
    this.showChatOptions = false;
    if (this.selectedDocument) {
      this.sendQuickMessage(`Please analyze the document "${this.selectedDocument.title}" and provide insights about its content, structure, and key points.`);
    } else {
      this.messageService.warning('Please select a document first to analyze');
    }
  }

  sendQuickMessage(message: string): void {
    this.documentChatInput = message;
    this.sendDocumentChatMessage();
  }

  sendDocumentChatMessage(): void {
    if (!this.documentChatInput.trim()) {
      return;
    }

    this.isProcessingDocumentChat = true;

    const userMessage = {
      id: this.generateMessageId(),
      sender: 'user',
      content: this.documentChatInput.trim(),
      timestamp: new Date()
    };

    this.documentChatMessages.push(userMessage);

    // Store the question
    const question = this.documentChatInput.trim();

    // Clear input field
    this.documentChatInput = '';

    // Add loading message
    const loadingMessage = {
      id: this.generateMessageId(),
      sender: 'loading',
      content: '',
      timestamp: new Date()
    };
    this.documentChatMessages.push(loadingMessage);

    // Scroll to bottom
    setTimeout(() => this.scrollChatToBottom(), 100);

    // Simulate AI response (replace with actual API call)
    setTimeout(() => {
      // Remove loading message
      this.documentChatMessages.pop();

      // Generate response based on document context
      const agentMessage = {
        id: this.generateMessageId(),
        sender: 'agent',
        content: this.generateDocumentAIResponse(question),
        timestamp: new Date()
      };

      this.documentChatMessages.push(agentMessage);
      this.isProcessingDocumentChat = false;

      // Update unread count if chat is not visible
      if (!this.showDocumentChat) {
        this.unreadDocumentChatCount++;
      }

      setTimeout(() => this.scrollChatToBottom(), 100);
    }, Math.random() * 2000 + 1000); // Random delay between 1-3 seconds
  }

  // Message Interaction Methods
  copyMessage(content: string): void {
    navigator.clipboard.writeText(content).then(() => {
      this.messageService.success('Message copied to clipboard');
    }).catch(() => {
      this.messageService.error('Failed to copy message');
    });
  }

  likeMessage(message: any): void {
    message.liked = !message.liked;
    const action = message.liked ? 'liked' : 'unliked';
    this.messageService.success(`Message ${action}`);
  }

  regenerateResponse(message: any): void {
    const messageIndex = this.documentChatMessages.findIndex(m => m.id === message.id);
    if (messageIndex > -1) {
      this.isProcessingDocumentChat = true;

      setTimeout(() => {
        this.isProcessingDocumentChat = false;
        this.documentChatMessages[messageIndex] = {
          ...message,
          content: this.generateDocumentAIResponse('regenerate'),
          timestamp: new Date()
        };
        setTimeout(() => this.scrollChatToBottom(), 100);
      }, 1500);
    }
  }

  // Utility Methods
  generateMessageId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }

  generateDocumentAIResponse(userInput: string): string {
    const documentContext = this.selectedDocument ? `document "${this.selectedDocument.title}"` : 'your documents';

    if (userInput.toLowerCase().includes('summarize')) {
      return `I can help you summarize ${documentContext}. Based on the content, here are the key points: This document contains important information that covers the main topics and their relevance. The structure follows a logical flow with clear sections and actionable insights.`;
    } else if (userInput.toLowerCase().includes('key points')) {
      return `Here are the key points from ${documentContext}:\n\n• Main topic and purpose\n• Important details and data\n• Actionable recommendations\n• Conclusion and next steps\n\nWould you like me to elaborate on any of these points?`;
    } else if (userInput.toLowerCase().includes('grammar') || userInput.toLowerCase().includes('style')) {
      return `I've analyzed ${documentContext} for grammar and style. Here are my suggestions:\n\n• Overall writing quality is good\n• Consider improving sentence variety\n• Some sections could be more concise\n• Technical terms are well-explained\n\nWould you like specific recommendations for any section?`;
    } else if (userInput.toLowerCase().includes('analyze')) {
      return `Analysis of ${documentContext}:\n\n**Content Structure:** Well-organized with clear sections\n**Key Themes:** Covers main topics comprehensively\n**Readability:** Appropriate for target audience\n**Recommendations:** Consider adding more examples and visual elements\n\nWhat specific aspect would you like me to focus on?`;
    } else if (userInput === 'regenerate') {
      const responses = [
        `Let me provide an alternative perspective on ${documentContext}...`,
        `Here's another way to look at the content in ${documentContext}...`,
        `I can offer additional insights about ${documentContext}...`,
        `From a different angle, ${documentContext} shows...`
      ];
      return responses[Math.floor(Math.random() * responses.length)];
    }

    const generalResponses = [
      `I understand your question about ${documentContext}. Let me help you with that.`,
      `That's an interesting question regarding ${documentContext}. Based on the content, I can provide some insights.`,
      `I've analyzed your request about ${documentContext}. Here's what I found.`,
      `Great question! Let me break this down for you based on ${documentContext}.`,
      `I can help you with that. Regarding ${documentContext}, here are my thoughts.`
    ];

    return generalResponses[Math.floor(Math.random() * generalResponses.length)];
  }

  trackByMessageId(_index: number, message: any): string {
    return message.id;
  }

  scrollChatToBottom(): void {
    if (this.chatContainer?.nativeElement) {
      const container = this.chatContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

}
