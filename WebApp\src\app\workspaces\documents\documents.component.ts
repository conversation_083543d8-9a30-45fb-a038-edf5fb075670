import { CommonModule } from '@angular/common';
import { Component, OnInit, OnD<PERSON>roy, inject, ViewChild, ElementRef, ChangeDetectorRef } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router, RouterLink, ActivatedRoute } from '@angular/router';
import { NzBreadCrumbModule } from 'ng-zorro-antd/breadcrumb';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzUploadFile, NzUploadModule } from 'ng-zorro-antd/upload';
import { Observable, Observer, firstValueFrom } from 'rxjs';
import { ThemeService } from '../../../shared/services/theam.service';
import { DocumentSyncService } from '../../shared/services/document-sync.service';
import { ActiveDocumentService } from '../../shared/services/active-document.service';

// EditorJS imports
import EditorJS, { ToolConstructable } from '@editorjs/editorjs';
import Header from '@editorjs/header';
import List from '@editorjs/list';
import Checklist from '@editorjs/checklist';
import Quote from '@editorjs/quote';
import Warning from '@editorjs/warning';
import Marker from '@editorjs/marker';
import CodeTool from '@editorjs/code';
import Delimiter from '@editorjs/delimiter';
import InlineCode from '@editorjs/inline-code';
import Link from '@editorjs/link';
import Table from '@editorjs/table';
import ImageTool from '@editorjs/image';
import { NzModalService } from 'ng-zorro-antd/modal';
import { CreateDocsDto, DocsServiceProxy, FileServiceProxy, ResponseMessage, TrackDocumentOpenDto, UpdateFavoriteDto } from '../../../shared/service-proxies/service-proxies';

// Document interface
interface Document {
  id: string;
  name: string;
  content?: string;
  files?: File[];
}

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    FormsModule,
    CommonModule,
    NzBreadCrumbModule,
    NzIconModule,
    NzUploadModule,
  ],
  templateUrl: './documents.component.html',
  styleUrl: './documents.component.css',
  providers: [NzMessageService, NzModalService],
})
export class DocumentsComponent implements OnInit, OnDestroy {
  @ViewChild('fileInput') fileInput!: ElementRef;
  @ViewChild('chatMessagesContainer') chatMessagesContainer!: ElementRef;
  @ViewChild('chatTextarea') chatTextarea!: ElementRef;

  showAddDocumentDialog = false;
  loading = false;
  avatarUrl?: string;
  showViewDocumentDialog = false;
  selectedDocument: any = null;
  documents: any[] = [];
  isAddingOrEditing: boolean = false;
  documentToEdit: any = null;
  isNewDocument: boolean = false;
  showingDocumentsList: boolean = true;

  // EditorJS properties
  private editor: EditorJS | null = null;
  private editorInitialized = false;
  private pendingContent: any = null;
  hasContent = false;
  wordCount = 0;
  workspaceName: string = '';
  isPublicRoute: boolean = false;

  // For file uploads
  selectedFiles: File[] = [];
  filesToDelete: any[] = [];

  // Enhanced Chat properties
  showChat: boolean = false;
  chatMessages: Array<{
    id: string;
    sender: string;
    content: string;
    timestamp: Date;
    liked?: boolean;
    type?: 'text' | 'file' | 'image';
  }> = [];
  chatInput: string = '';
  isTestingAgent: boolean = false;
  isTyping: boolean = false;
  isSendingMessage: boolean = false;
  showChatOptions: boolean = false;
  unreadChatCount: number = 0;

  // Voice input properties
  isRecording: boolean = false;
  recognition: any = null;

  // Chat file upload
  chatFiles: File[] = [];

  // Quick actions for chat
  quickActions = [
    { label: 'Summarize document', action: 'summarize' },
    { label: 'Ask questions', action: 'questions' },
    { label: 'Get insights', action: 'insights' },
    { label: 'Translate', action: 'translate' },
    { label: 'Improve writing', action: 'improve' }
  ];



  // Inject services
  themeService = inject(ThemeService);
  documentSyncService = inject(DocumentSyncService);
  activeDocumentService = inject(ActiveDocumentService);

  constructor(
    private messageService: NzMessageService,
    private modalService: NzModalService,
    private docsService: DocsServiceProxy,
    private documentService: DocsServiceProxy,
    private router: Router,
    private route: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private fileService: FileServiceProxy,
  ) { }

  ngOnInit() {
    // Get the current URL and split it into segments
    const url = this.router.url;
    const urlSegments = url.split('/');

    // Check if we're on the public notes route
    if (url.startsWith('/notes')) {
      // For public notes route, use 'GlobalNotes' as the workspace name
      this.workspaceName = 'GlobalNotes';
      this.isPublicRoute = true;
    }
    // Check if we're in a workspace route
    else if (url.includes('/workspaces/')) {
      // For workspace routes, get the workspace name from the URL
      this.workspaceName = urlSegments[2];
      this.workspaceName = decodeURIComponent(this.workspaceName);
    }
    // Fallback to GlobalNotes for any other case
    else {
      this.workspaceName = 'GlobalNotes';
    }

    console.log('Using workspace name:', this.workspaceName);

    // Load documents with the determined workspace name
    this.loadDocuments().then(() => {
      // Handle query parameters after documents are loaded
      this.handleQueryParameters();
    });
  }

  private handleQueryParameters() {
    this.route.queryParams.subscribe(params => {
      const docId = params['docId'];
      const action = params['action'];

      if (docId) {
        // Find and select the document by ID
        const document = this.documents.find(doc => doc.id == docId);
        if (document) {
          // Set the active document using the service before selecting
          this.activeDocumentService.setActiveDocument(document, {
            workspaceName: this.workspaceName,
            route: this.isPublicRoute ? 'notes' : 'workspace-documents'
          });

          this.selectDocument(document);
          // Clear the query parameter to avoid issues with navigation
          this.router.navigate([], {
            relativeTo: this.route,
            queryParams: {},
            replaceUrl: true
          });
        }
      } else if (action === 'add') {
        // Trigger add document mode
        this.addDocument(new Event('click'));
        // Clear the query parameter
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      }
    });
  }

  ngAfterViewInit() {
    // Remove the automatic initialization for now
    // We'll initialize it when the user starts editing
  }

  ngOnDestroy() {
    this.destroyEditor();
  }

  private destroyEditor() {
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
      this.editorInitialized = false;
    }
  }

  private initializeEditor() {
    // First destroy any existing editor instance
    this.destroyEditor();

    // Add a short delay to ensure the DOM has updated
    setTimeout(() => {
      const editorElement = document.getElementById('editor');
      if (!editorElement) {
        console.error('Element with ID "editor" is missing.');
        return;
      }

      this.editor = new EditorJS({
        holder: 'editor',
        minHeight: 200,
        placeholder: 'Write your description here...',
        onChange: () => {
          this.editor?.save().then((data) => {
            this.hasContent = data.blocks.length > 0;
            this.updateWordCount(data);
            if (this.documentToEdit) {
              this.documentToEdit.content = JSON.stringify(data);
            }
          });
        },
        onReady: () => {
          this.editorInitialized = true;
          console.log('Editor.js is ready');
          if (this.pendingContent) {
            this.updateEditorContent(this.pendingContent);
            this.pendingContent = null;
          }
        },
        tools: {
          header: {
            class: Header as unknown as ToolConstructable,
            inlineToolbar: true,
            config: {
              levels: [1, 2, 3, 4],
              defaultLevel: 1,
            },
          },
          list: List as unknown as ToolConstructable,
          checklist: Checklist as unknown as ToolConstructable,
          quote: Quote as unknown as ToolConstructable,
          marker: Marker as unknown as ToolConstructable,
          code: CodeTool as unknown as ToolConstructable,
          delimiter: Delimiter as unknown as ToolConstructable,
          inlineCode: InlineCode as unknown as ToolConstructable,
          table: Table as unknown as ToolConstructable,
          image: {
            class: ImageTool as unknown as { new(): any },
            config: {
              uploader: {
                uploadByFile: (file: File) => {
                  return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => {
                      resolve({
                        success: 1,
                        file: {
                          url: reader.result as string,
                        },
                      });
                    };
                    reader.onerror = () => {
                      reject({
                        success: 0,
                        message: 'Could not read file',
                      });
                    };
                    reader.readAsDataURL(file);
                  });
                },
              },
            },
          },
        },
      });
    }, 100);
  }

  private ensureEditorInitialized() {
    if (!this.editor && this.isAddingOrEditing) {
      this.initializeEditor();
    }
  }

  private async updateEditorContent(newContent: any) {
    try {
      if (!this.editor) {
        console.warn('Editor not initialized yet, storing content for later');
        this.pendingContent = newContent;
        this.ensureEditorInitialized(); // Try to initialize the editor
        return;
      }

      await this.editor.isReady;
      await this.editor.blocks.clear();

      // Make sure content is in the correct format for EditorJS
      if (newContent && typeof newContent === 'object') {
        // If it's already in the EditorJS format with blocks
        if (Array.isArray(newContent.blocks)) {
          await this.editor.render(newContent);
        }
        // If it's some other object format, create a text block
        else {
          await this.editor.render({
            blocks: [
              {
                type: "paragraph",
                data: {
                  text: JSON.stringify(newContent)
                }
              }
            ]
          });
        }
      }
      // If it's a string, create a text block
      else if (typeof newContent === 'string') {
        await this.editor.render({
          blocks: [
            {
              type: "paragraph",
              data: {
                text: newContent
              }
            }
          ]
        });
      }
    } catch (error) {
      console.error('Error updating editor content:', error);
    }
  }

  private async saveEditorContent() {
    if (!this.editor) return;

    try {
      const editorData = await this.editor.save();
      if (this.documentToEdit) {
        this.documentToEdit.content = JSON.stringify(editorData);
      }
    } catch (error) {
      console.error('Error saving editor content:', error);
    }
  }

  private updateWordCount(data: any) {
    const text = data.blocks
      .map((block: any) =>
        block.data && block.data.text ? block.data.text : ''
      )
      .join(' ');
    this.wordCount = text.trim() ? text.trim().split(/\s+/).length : 0;
  }

  async loadDocuments() {
    console.log(this.workspaceName);

    try {
      const data = await firstValueFrom(this.documentService.getByWorkspaceName(this.workspaceName));
      this.documents = data;
    } catch (error) {
      console.error('Error loading documents:', error);
    }
  }

  selectDocument(document: any) {
    this.selectedDocument = document;
    this.showingDocumentsList = false;
    this.isAddingOrEditing = false;

    console.log('📖 Document selected from list:', document.title);

    // Set the active document using the service
    this.activeDocumentService.setActiveDocument(document, {
      workspaceName: this.workspaceName,
      route: this.isPublicRoute ? 'notes' : 'workspace-documents'
    });

    // Track document as recently opened when selected from the main documents list
    this.trackDocumentOpen(document);

    // For viewing document content, we don't need to initialize the editor
    // but we can prepare the content for display in the HTML
    if (document.content) {
      try {
        let parsedContent = JSON.parse(document.content);

        // Handle nested content structure
        if (parsedContent.desc) {
          try {
            // Try parsing the desc field if it's a string representation of JSON
            const descContent = JSON.parse(parsedContent.desc);

            // Set a formatted HTML version for display
            document.formattedContent = this.formatEditorContent(descContent);
          } catch {
            // If desc is not JSON, use it directly
            document.formattedContent = parsedContent.desc;
          }
        } else if (parsedContent.blocks) {
          // If it's already in EditorJS format
          document.formattedContent = this.formatEditorContent(parsedContent);
        } else {
          // Fallback
          document.formattedContent = document.content;
        }
      } catch (error) {
        console.error('Error parsing document content:', error);
        document.formattedContent = document.content;
      }
    }
  }

  addDocument($event: Event) {
    $event.stopImmediatePropagation();

    this.isAddingOrEditing = true;
    this.isNewDocument = true;
    this.documentToEdit = { title: '', content: '', description: '' };
    this.selectedDocument = null;
    this.selectedFiles = [];

    // Destroy existing editor and initialize a new one
    this.destroyEditor();

    // Initialize the editor after setting isAddingOrEditing to true
    setTimeout(() => {
      this.initializeEditor();
    }, 100);
  }

  editDocument(document: any) {
    this.isAddingOrEditing = true;
    this.isNewDocument = false;
    this.documentToEdit = { ...document };

    // Reset file tracking arrays
    this.selectedFiles = [];
    this.filesToDelete = [];

    // Destroy existing editor and initialize a new one
    this.destroyEditor();

    // Initialize the editor after setting isAddingOrEditing to true
    setTimeout(() => {
      this.initializeEditor();

      // Handle document content for the editor
      if (document.content) {
        try {
          let parsedContent;

          // Try to parse the content as JSON
          try {
            parsedContent = JSON.parse(document.content);

            // If content is in the format {title: "...", desc: "..."}
            if (parsedContent.desc && typeof parsedContent.desc === 'string') {
              // Handle case where desc is a string but needs to be parsed as EditorJS data
              try {
                parsedContent = JSON.parse(parsedContent.desc);
              } catch {
                // If desc is not JSON, create a simple text block
                parsedContent = {
                  blocks: [
                    {
                      type: "paragraph",
                      data: {
                        text: parsedContent.desc
                      }
                    }
                  ]
                };
              }
            }
            // If the content is already in EditorJS format with blocks
            else if (!parsedContent.blocks) {
              // Create a simple text block for plain JSON that's not in EditorJS format
              parsedContent = {
                blocks: [
                  {
                    type: "paragraph",
                    data: {
                      text: document.content
                    }
                  }
                ]
              };
            }
          } catch (jsonError) {
            // If content is not JSON at all, create a simple text block
            parsedContent = {
              blocks: [
                {
                  type: "paragraph",
                  data: {
                    text: document.content
                  }
                }
              ]
            };
          }

          // Update the editor with the parsed content
          if (this.editorInitialized) {
            this.updateEditorContent(parsedContent);
          } else {
            this.pendingContent = parsedContent;
          }
        } catch (error) {
          console.error('Error parsing or setting document content:', error);
        }
      } else {
        // Clear the editor if no content
        if (this.editor) {
          this.editor.blocks.clear();
        }
      }
    }, 100);
  }

  deleteDocument(document: any) {
    if (confirm('Are you sure you want to delete this document?')) {
      this.documentService.delete(document.id).subscribe(
        () => {
          this.loadDocuments();
          if (this.selectedDocument && this.selectedDocument.id === document.id) {
            this.selectedDocument = null;
            this.showingDocumentsList = true;
          }

          // Notify sidebar to refresh data
          this.documentSyncService.notifyDocumentDeleted(document, this.workspaceName);
        },
        (error: any) => {
          console.error('Error deleting document:', error);
        }
      );
    }
  }

  async saveDocument() {
    await this.saveEditorContent();

    if (!this.documentToEdit.title) {
      this.messageService.error('Please fill in title');
      return;
    }

    // Ensure content is properly formatted when saving
    let contentToSave = this.documentToEdit.content || '';

    // Format content properly
    try {
      const parsedContent = JSON.parse(contentToSave);
      if (!parsedContent.title && !parsedContent.desc) {
        contentToSave = JSON.stringify({
          title: this.documentToEdit.title,
          desc: contentToSave
        });
      }
    } catch (e) {
      contentToSave = JSON.stringify({
        title: this.documentToEdit.title,
        desc: contentToSave
      });
    }

    try {
      // Step 1: Upload files first
      let fileToAddResponse: ResponseMessage = new ResponseMessage();

      if (this.selectedFiles.length > 0) {
        // Convert files to FileParameter array
        const fileParams = this.selectedFiles.map(file => ({
          data: file,
          fileName: file.name
        }));

        // Upload files using Promise
        try {
          fileToAddResponse = await firstValueFrom(this.fileService.upload(undefined,fileParams));
          console.log('Files uploaded successfully:', fileToAddResponse);
        } catch (error) {
          console.error('Error uploading files:', error);
          // Continue with empty file response
        }
      }

      // Step 2: Create the document DTO
      const docsCreate = new CreateDocsDto({
        id: this.isNewDocument ? 0 : this.documentToEdit.id,
        title: this.documentToEdit.title,
        content: contentToSave,
        workspaceName: this.workspaceName || 'GlobalNotes', // Ensure we always have a workspace name
        filesToAdd: fileToAddResponse.message,
        filesToDelete: this.filesToDelete
      });

      // Step 3: Save the document using Promise
      const savedDoc = await firstValueFrom(this.documentService.createOrUpdate(docsCreate));

      // Handle success
      this.messageService.success(`Document ${this.isNewDocument ? 'created' : 'updated'} successfully`);

      console.log('📄 Document saved successfully:', savedDoc);
      console.log('📁 Current workspace:', this.workspaceName);
      console.log('🆕 Is new document:', this.isNewDocument);

      this.onDocumentSaved(savedDoc);
      this.clearFileSelections();

      // For new documents, track them as recently opened to ensure they appear in sidebar
      if (this.isNewDocument && savedDoc) {
        console.log('📌 Tracking new document as recently opened...');
        this.trackNewDocumentAsRecentlyOpened(savedDoc);
      }

      // Notify sidebar immediately for new documents, with delay for updates
      if (this.isNewDocument) {
        console.log('🔔 Notifying sidebar of new document creation...');
        // Immediate notification for new documents
        this.documentSyncService.notifyDocumentCreated(savedDoc, this.workspaceName);

        // Also send a delayed notification to ensure backend sync
        setTimeout(() => {
          console.log('🔔 Sending delayed notification for new document...');
          this.documentSyncService.notifyDocumentCreated(savedDoc, this.workspaceName);
        }, 1500);
      } else {
        console.log('🔔 Notifying sidebar of document update...');
        setTimeout(() => {
          this.documentSyncService.notifyDocumentUpdated({ type: 'updated', document: savedDoc, workspaceName: this.workspaceName });
        }, 500);
      }
    } catch (error) {
      this.messageService.error(`Error ${this.isNewDocument ? 'creating' : 'updating'} document`);
      console.error(`Error ${this.isNewDocument ? 'creating' : 'updating'} document:`, error);
    }
  }

  onDocumentSaved(savedDocument: any) {
    this.loadDocuments();
    this.selectedDocument = savedDocument;
    this.isAddingOrEditing = false;
    this.showingDocumentsList = false;

    // Format the content immediately after saving
    if (savedDocument.content) {
      try {
        let parsedContent = JSON.parse(savedDocument.content);

        // Handle nested content structure
        if (parsedContent.desc) {
          try {
            // Try parsing the desc field if it's a string representation of JSON
            const descContent = JSON.parse(parsedContent.desc);
            // Set a formatted HTML version for display
            savedDocument.formattedContent = this.formatEditorContent(descContent);
          } catch {
            // If desc is not JSON, use it directly
            savedDocument.formattedContent = parsedContent.desc;
          }
        } else if (parsedContent.blocks) {
          // If it's already in EditorJS format
          savedDocument.formattedContent = this.formatEditorContent(parsedContent);
        } else {
          // Fallback
          savedDocument.formattedContent = savedDocument.content;
        }
      } catch (error) {
        console.error('Error parsing document content:', error);
        savedDocument.formattedContent = savedDocument.content;
      }
    }
  }

  onCancel() {
    this.isAddingOrEditing = false;
    if (this.isNewDocument && !this.selectedDocument) {
      this.showingDocumentsList = true;
    }
    this.selectedFiles = [];
    this.destroyEditor();
  }

  openAddDocumentDialog() {
    this.showAddDocumentDialog = true;
    setTimeout(() => {
      if (!this.editor) {
        this.initializeEditor();
      }
    }, 0);
  }

  closeAddDocumentDialog() {
    this.showAddDocumentDialog = false;
    if (this.editor) {
      this.editor.destroy();
      this.editor = null;
    }
  }

  beforeUpload = (
    file: NzUploadFile,
    _fileList: NzUploadFile[]
  ): Observable<boolean> =>
    new Observable((observer: Observer<boolean>) => {
      const isJpgOrPng =
        file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        this.messageService.error('You can only upload JPG or PNG files!');
        observer.complete();
        return;
      }
      const isLt2M = file.size! / 1024 / 1024 < 2;
      if (!isLt2M) {
        this.messageService.error('Image must be smaller than 2MB!');
        observer.complete();
        return;
      }
      observer.next(isJpgOrPng && isLt2M);
      observer.complete();
    });

  private getBase64(img: File, callback: (img: string) => void): void {
    const reader = new FileReader();
    reader.addEventListener('load', () => callback(reader.result!.toString()));
    reader.readAsDataURL(img);
  }

  handleChange(info: { file: NzUploadFile }): void {
    switch (info.file.status) {
      case 'uploading':
        this.loading = true;
        break;
      case 'done':
        this.getBase64(info.file!.originFileObj!, (img: string) => {
          this.loading = false;
          this.avatarUrl = img;
        });
        break;
      case 'error':
        this.messageService.error('Network error');
        this.loading = false;
        break;
    }
  }



  showDocumentsList() {
    this.showingDocumentsList = true;
    this.selectedDocument = null;
    this.isAddingOrEditing = false;
  }

  createNewNote($event: Event) {
    $event.stopImmediatePropagation()

  }
  backToList() {
    this.selectedDocument = null;
    this.showingDocumentsList = true;
  }



  onFileSelect(event: any): void {
    if (event.target.files.length > 0) {
      const files = event.target.files;
      for (let i = 0; i < files.length; i++) {
        // Check for duplicates by name
        const fileName = files[i].name;
        const isDuplicate = this.selectedFiles.some(file => file.name === fileName);

        if (!isDuplicate) {
          this.selectedFiles.push(files[i]);
        }
      }
    }

    // Clear the input value to allow selecting the same file again
    if (this.fileInput) {
      this.fileInput.nativeElement.value = '';
    }
  }

  removeFile(index: number) {
    this.selectedFiles.splice(index, 1);
  }

  isImageFile(fileName: string): boolean {
    if (!fileName) return false;
    const extension = fileName.split('.').pop()?.toLowerCase() ?? '';
    return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension);
  }

  showDocumentDetails(document: Document) {
    this.selectedDocument = document;
  }

  // Add a helper method to format EditorJS content for display
  private formatEditorContent(content: any): string {
    if (!content || !content.blocks) return '';

    let html = '';

    content.blocks.forEach((block: any) => {
      switch (block.type) {
        case 'header':
          const level = block.data.level || 2;
          html += `<h${level}>${block.data.text}</h${level}>`;
          break;
        case 'paragraph':
          html += `<p>${block.data.text}</p>`;
          break;
        case 'list':
          const listTag = block.data.style === 'ordered' ? 'ol' : 'ul';
          html += `<${listTag}>`;
          block.data.items.forEach((item: string) => {
            html += `<li>${item}</li>`;
          });
          html += `</${listTag}>`;
          break;
        // Add more cases for other block types as needed
        default:
          if (block.data.text) {
            html += `<p>${block.data.text}</p>`;
          }
      }
    });

    return html;
  }

  getFilePreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  markFileForDeletion(file: any, index: number): void {
    // Add to files to delete array (only once)
    this.filesToDelete.push(file);

    // Remove from UI display - properly update the string
    if (this.documentToEdit && this.documentToEdit.files) {
      const filesArray = this.documentToEdit.files.split(', ');
      filesArray.splice(index, 1);
      this.documentToEdit.files = filesArray.join(', ');
    }

    // Mark file as deleted for UI rendering
    file.markedForDeletion = true;

    // Trigger change detection
    this.changeDetectorRef.detectChanges();
  }

  removeSelectedFile(index: number): void {
    if (index >= 0 && index < this.selectedFiles.length) {
      this.selectedFiles.splice(index, 1);
    }
  }

  clearFileSelections() {
    this.selectedFiles = [];
    this.filesToDelete = [];
  }

  isDocumentFavorite(document: any): boolean {
    // Check if the document has a favorite property or if it exists in a favorites list
    return document && (document.isFavorite === true || document.isFavourite === true);
  }

  toggleFavorite(document: any) {
    if (!document || !document.id) {
      console.error('Invalid document for favorite toggle');
      return;
    }

    const isFavorite = this.isDocumentFavorite(document);
    console.log('🌟 Toggling favorite for document:', document.title, 'Current status:', isFavorite);

    // Use the API to update favorite status
    this.documentService.updateFavoriteStatus({
      id: document.id,
      isFavorite: !isFavorite
    } as UpdateFavoriteDto).subscribe(
      (response: any) => {
        console.log('✅ Favorite status updated:', response);

        // Update local document state
        document.isFavorite = !isFavorite;
        document.isFavourite = !isFavorite; // Handle both property names

        // If this is the selected document, update it as well
        if (this.selectedDocument && this.selectedDocument.id === document.id) {
          this.selectedDocument.isFavorite = !isFavorite;
          this.selectedDocument.isFavourite = !isFavorite;
        }

        // Notify sidebar to refresh favorites list
        this.documentSyncService.notifyDocumentFavorited(document, this.workspaceName);

        console.log('🔔 Notified sidebar of favorite change');
      },
      (error: any) => {
        console.error('❌ Error updating favorite status:', error);
      }
    );
  }

  trackDocumentOpen(document: any) {
    // Track document as recently opened and notify sidebar
    if (document && document.id) {
      console.log('📌 Tracking document open from main list:', {
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      });

      // Use the API to track document opens
      this.documentService.trackDocumentOpen({
        id: document.id,
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      } as any).subscribe(
        (response: any) => {
          console.log('✅ Document tracked as recently opened:', response);

          // Notify sidebar to refresh recent documents list
          this.documentSyncService.notifyDocumentUpdated({
            type: 'updated',
            document: document,
            workspaceName: this.workspaceName
          });

          console.log('🔔 Notified sidebar of document open');
        },
        (error: any) => {
          console.error('❌ Error tracking document open:', error);

          // Try alternative API call format if the first one fails
          this.documentService.trackDocumentOpen({
            id: document.id
          } as any).subscribe(
            (response2: any) => {
              console.log('✅ Document tracked with alternative format:', response2);

              // Still notify sidebar even with alternative format
              this.documentSyncService.notifyDocumentUpdated({
                type: 'updated',
                document: document,
                workspaceName: this.workspaceName
              });
            },
            (error2: any) => {
              console.error('❌ Alternative tracking also failed:', error2);
            }
          );
        }
      );
    }
  }

  private trackNewDocumentAsRecentlyOpened(document: any) {
    // Track the newly created document as recently opened so it appears in the sidebar
    if (document && document.id) {
      console.log('📌 Attempting to track document as recently opened:', {
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      });

      // Use the correct API call format
      this.documentService.trackDocumentOpen({
        id: document.id,
        documentId: document.id,
        workspaceName: this.workspaceName || 'GlobalNotes'
      } as any).subscribe(
        (response: any) => {
          console.log('✅ New document tracked as recently opened:', response);
        },
        (error: any) => {
          console.error('❌ Error tracking new document as recently opened:', error);

          // Try alternative API call format if the first one fails
          this.documentService.trackDocumentOpen({
            id: document.id
          } as any).subscribe(
            (response2: any) => {
              console.log('✅ New document tracked with alternative format:', response2);
            },
            (error2: any) => {
              console.error('❌ Alternative tracking also failed:', error2);
            }
          );
        }
      );
    }
  }

  // Enhanced Chat Methods
  sendChatMessage(): void {
    if (this.chatInput.trim() || this.chatFiles.length > 0) {
      this.isSendingMessage = true;

      const userMessage = {
        id: this.generateMessageId(),
        sender: 'user',
        content: this.chatInput,
        timestamp: new Date(),
        type: this.chatFiles.length > 0 ? 'file' : 'text' as 'text' | 'file' | 'image'
      };

      this.chatMessages.push(userMessage);
      this.chatInput = '';
      this.chatFiles = [];

      // Auto-resize textarea
      this.resetTextareaHeight();

      // Scroll to bottom
      setTimeout(() => this.scrollToBottom(), 100);

      // Show typing indicator
      this.isTyping = true;

      // Simulate agent response with more realistic delay
      setTimeout(() => {
        this.isTyping = false;
        this.isSendingMessage = false;

        const agentMessage = {
          id: this.generateMessageId(),
          sender: 'agent',
          content: this.generateAgentResponse(userMessage.content),
          timestamp: new Date(),
          type: 'text' as 'text' | 'file' | 'image'
        };

        this.chatMessages.push(agentMessage);

        // Update unread count if chat is not visible
        if (!this.showChat) {
          this.unreadChatCount++;
        }

        setTimeout(() => this.scrollToBottom(), 100);
      }, Math.random() * 2000 + 1000); // Random delay between 1-3 seconds
    }
  }

  toggleChat(): void {
    this.showChat = !this.showChat;

    if (this.showChat) {
      // Reset unread count when opening chat
      this.unreadChatCount = 0;
      // Close chat options if open
      this.showChatOptions = false;
      // Focus on input after a short delay
      setTimeout(() => {
        if (this.chatTextarea?.nativeElement) {
          this.chatTextarea.nativeElement.focus();
        }
      }, 300);
      console.log('Chat section opened');
    } else {
      console.log('Chat section closed');
    }

    this.changeDetectorRef.detectChanges();
  }

  // Chat Options Methods
  toggleChatOptions(): void {
    this.showChatOptions = !this.showChatOptions;
  }

  clearChatHistory(): void {
    this.chatMessages = [];
    this.showChatOptions = false;
    this.messageService.success('Chat history cleared');
  }

  exportChatHistory(): void {
    const chatData = {
      exportDate: new Date().toISOString(),
      messages: this.chatMessages
    };

    const blob = new Blob([JSON.stringify(chatData, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `chat-history-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    window.URL.revokeObjectURL(url);

    this.showChatOptions = false;
    this.messageService.success('Chat history exported');
  }

  toggleChatSettings(): void {
    // Placeholder for chat settings
    this.showChatOptions = false;
    this.messageService.info('Chat settings coming soon');
  }

  // Message Interaction Methods
  copyMessage(content: string): void {
    navigator.clipboard.writeText(content).then(() => {
      this.messageService.success('Message copied to clipboard');
    }).catch(() => {
      this.messageService.error('Failed to copy message');
    });
  }

  likeMessage(message: any): void {
    message.liked = !message.liked;
    const action = message.liked ? 'liked' : 'unliked';
    this.messageService.success(`Message ${action}`);
  }

  regenerateResponse(message: any): void {
    const messageIndex = this.chatMessages.findIndex(m => m.id === message.id);
    if (messageIndex > -1) {
      this.isTyping = true;

      setTimeout(() => {
        this.isTyping = false;
        this.chatMessages[messageIndex] = {
          ...message,
          content: this.generateAgentResponse('regenerate'),
          timestamp: new Date()
        };
        setTimeout(() => this.scrollToBottom(), 100);
      }, 1500);
    }
  }

  // Quick Actions
  sendQuickAction(action: any): void {
    this.chatInput = `Please ${action.action} this document.`;
    this.sendChatMessage();
  }

  // File Upload Methods
  onChatFileSelect(event: any): void {
    const files = Array.from(event.target.files) as File[];
    this.chatFiles = [...this.chatFiles, ...files];

    // Clear the input
    event.target.value = '';
  }

  removeChatFile(index: number): void {
    this.chatFiles.splice(index, 1);
  }

  isImageFile(filename: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
  }

  getFilePreviewUrl(file: File): string {
    return URL.createObjectURL(file);
  }

  // Input Handling Methods
  onChatKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      this.sendChatMessage();
    }
  }

  onChatInputChange(): void {
    this.autoResizeTextarea();
  }

  autoResizeTextarea(): void {
    if (this.chatTextarea?.nativeElement) {
      const textarea = this.chatTextarea.nativeElement;
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
    }
  }

  resetTextareaHeight(): void {
    if (this.chatTextarea?.nativeElement) {
      this.chatTextarea.nativeElement.style.height = '40px';
    }
  }

  // Voice Input Methods
  toggleVoiceInput(): void {
    if (this.isRecording) {
      this.stopRecording();
    } else {
      this.startRecording();
    }
  }

  startRecording(): void {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition;
      this.recognition = new SpeechRecognition();

      this.recognition.continuous = false;
      this.recognition.interimResults = false;
      this.recognition.lang = 'en-US';

      this.recognition.onstart = () => {
        this.isRecording = true;
      };

      this.recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        this.chatInput = transcript;
        this.autoResizeTextarea();
      };

      this.recognition.onerror = () => {
        this.messageService.error('Voice recognition failed');
        this.isRecording = false;
      };

      this.recognition.onend = () => {
        this.isRecording = false;
      };

      this.recognition.start();
    } else {
      this.messageService.error('Voice recognition not supported in this browser');
    }
  }

  stopRecording(): void {
    if (this.recognition) {
      this.recognition.stop();
      this.isRecording = false;
    }
  }

  // Utility Methods
  generateMessageId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  generateAgentResponse(userInput: string): string {
    const responses = [
      "I understand your request. Let me help you with that.",
      "That's an interesting question. Based on the document content, I can provide some insights.",
      "I've analyzed your request and here's what I found.",
      "Let me break this down for you step by step.",
      "Based on the context of your document, here's my analysis.",
      "I can help you with that. Here are some suggestions.",
      "That's a great question! Let me provide you with a detailed response.",
      "I've processed your request and here's what I recommend."
    ];

    if (userInput.toLowerCase().includes('summarize')) {
      return "Here's a summary of your document: This document contains important information that I've analyzed. The key points include the main topics discussed and their relevance to your work.";
    } else if (userInput.toLowerCase().includes('translate')) {
      return "I can help you translate this content. Please specify which language you'd like me to translate to.";
    } else if (userInput.toLowerCase().includes('improve')) {
      return "I can suggest improvements to your writing. Here are some recommendations for better clarity and engagement.";
    } else if (userInput === 'regenerate') {
      return "Here's an alternative response: " + responses[Math.floor(Math.random() * responses.length)];
    }

    return responses[Math.floor(Math.random() * responses.length)];
  }

  formatMessageContent(content: string): string {
    // Simple formatting for links, bold text, etc.
    return content
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" class="text-blue-500 underline">$1</a>');
  }

  formatMessageTime(timestamp: Date): string {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / 60000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (minutes < 1440) return `${Math.floor(minutes / 60)}h ago`;
    return timestamp.toLocaleDateString();
  }

  trackByMessageId(index: number, message: any): string {
    return message.id;
  }

  scrollToBottom(): void {
    if (this.chatMessagesContainer?.nativeElement) {
      const container = this.chatMessagesContainer.nativeElement;
      container.scrollTop = container.scrollHeight;
    }
  }

}
